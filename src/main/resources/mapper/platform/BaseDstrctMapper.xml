<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.platform.mapper.BaseDstrctMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.platform.entity.BaseDstrct">
    <!--@mbg.generated-->
    <!--@Table GDGS.BASE_DSTRCT-->
    <id column="DSTRCT_ID" jdbcType="VARCHAR" property="dstrctId" />
    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="DSTRCT_CODE" jdbcType="VARCHAR" property="dstrctCode" />
    <result column="DSTRCT_NAME" jdbcType="VARCHAR" property="dstrctName" />
    <result column="PARENT_CODE" jdbcType="VARCHAR" property="parentCode" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="GIS_X" jdbcType="DECIMAL" property="gisX" />
    <result column="GIS_Y" jdbcType="DECIMAL" property="gisY" />
    <result column="DSTRCT_CODE_BAK" jdbcType="VARCHAR" property="dstrctCodeBak" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DSTRCT_ID, PARENT_ID, DSTRCT_CODE, DSTRCT_NAME, PARENT_CODE, REMARK, CREATE_USER_ID, 
    CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, GIS_X, GIS_Y, DSTRCT_CODE_BAK
  </sql>
</mapper>