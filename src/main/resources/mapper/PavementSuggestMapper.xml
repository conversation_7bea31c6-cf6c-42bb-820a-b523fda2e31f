<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.PavementSuggestMapper">

    <select id="getPavementSuggest"
            resultType="com.hualu.highwaymaintenance.module.national.entity.PavementSuggest">
        select *
        from MEMSDB.PAVEMENT_SUGGEST
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="target != null">
                and TARGET = #{target,jdbcType=INTEGER}
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
            <if test="lineType != null and lineType != ''">
                and line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            order by orders
        </where>
    </select>

    <select id="getPavementSuggestDecision"
            resultType="com.hualu.highwaymaintenance.module.national.entity.PavementSuggest">
        select *
        from MEMSDB.PAVEMENT_SUGGEST_T
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)

            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>

            <if test="lineType != null and lineType != ''">
                and line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            order by orders
        </where>
    </select>

    <select id="getPavementSuggestPage"
            resultType="com.hualu.highwaymaintenance.module.national.entity.PavementSuggest">
        select *
        from MEMSDB.PAVEMENT_SUGGEST
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="target != null">
                and TARGET = #{target,jdbcType=INTEGER}
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
            <if test="lineType != null and lineType != ''">
                and line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            order by orders
        </where>
    </select>

    <select id="getPavementSuggestDecisionPage"
            resultType="com.hualu.highwaymaintenance.module.national.entity.PavementSuggest">
        select *
        from MEMSDB.PAVEMENT_SUGGEST_T
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)

            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
            <if test="lineType != null and lineType != ''">
                and line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            order by orders
        </where>
    </select>
</mapper>
