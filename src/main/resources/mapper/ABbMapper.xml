<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.ABbMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.ABb">
    <!--@mbg.generated-->
    <!--@Table A_BB-->
    <result column="SER_ORG" jdbcType="VARCHAR" property="serOrg" />
    <result column="ORG" jdbcType="VARCHAR" property="org" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LINE_NAME" jdbcType="VARCHAR" property="lineName" />
    <result column="START_STAKE" jdbcType="VARCHAR" property="startStake" />
    <result column="END_STAKE" jdbcType="VARCHAR" property="endStake" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SER_ORG, ORG, LINE_CODE, LINE_NAME, START_STAKE, END_STAKE, "TYPE"
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridge.ABb">
    <!--@mbg.generated-->
    insert into A_BB (SER_ORG, ORG, LINE_CODE, 
      LINE_NAME, START_STAKE, END_STAKE, 
      "TYPE")
    values (#{serOrg,jdbcType=VARCHAR}, #{org,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, 
      #{lineName,jdbcType=VARCHAR}, #{startStake,jdbcType=VARCHAR}, #{endStake,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridge.ABb">
    <!--@mbg.generated-->
    insert into A_BB
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serOrg != null">
        SER_ORG,
      </if>
      <if test="org != null">
        ORG,
      </if>
      <if test="lineCode != null">
        LINE_CODE,
      </if>
      <if test="lineName != null">
        LINE_NAME,
      </if>
      <if test="startStake != null">
        START_STAKE,
      </if>
      <if test="endStake != null">
        END_STAKE,
      </if>
      <if test="type != null">
        "TYPE",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serOrg != null">
        #{serOrg,jdbcType=VARCHAR},
      </if>
      <if test="org != null">
        #{org,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="lineName != null">
        #{lineName,jdbcType=VARCHAR},
      </if>
      <if test="startStake != null">
        #{startStake,jdbcType=VARCHAR},
      </if>
      <if test="endStake != null">
        #{endStake,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectAbList" resultType="java.util.Map">
    select * from BCTCMSDB.A_BB where type=#{type}
  </select>

  <select id="listBrdgSpanKind" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select d.ATTRIBUTE_VALUE as CAT_NAME,count(1) as CAT_VAL from T_BRDG_RECOGMANAGE m inner join T_BRDG_BRDGRECOG br on m.MAIN_BRDGRECOG_ID=br.BRDGRECOG_ID
    and br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
    from GDGS.FW_RIGHT_ORG f
    where f.ID = #{orgId}
    or exists(select 1
    from GDGS.FW_RIGHT_ORG p
    where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)
    inner join GDGS.BASE_DATATHIRD_DIC d on d.ATTRIBUTE_ITEM='BRDG_SPAN_KIND' and  m.MAX_SPAN_KIND = d.ATTRIBUTE_CODE
    where m.VALID_FLAG=1 and br.VALID_FLAG=1 and m.BRDGRECOG_ID=m.MAIN_BRDGRECOG_ID
    group by d.ATTRIBUTE_CODE,d.ATTRIBUTE_VALUE
    order by  d.ATTRIBUTE_CODE
    </select>

  <select id="stsBrdgTechByOrgCodeAndYear" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select n.TC_GRADE as CAT_NAME,count(1) as CAT_VAL from T_BRDG_RECOGMANAGE m inner join T_BRDG_BRDGRECOG br on m.MAIN_BRDGRECOG_ID=br.BRDGRECOG_ID
      and br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
                                                                                                       from GDGS.FW_RIGHT_ORG f
                                                                                                       where f.ID = #{orgId}
                                                                                                          or exists(select 1
                                                                                                                    from GDGS.FW_RIGHT_ORG p
                                                                                                                    where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)
                                                                                inner join ONEMAP.TS_BRIDGE_TECHNICAL n on n.BRIDGE_ID=m.MAIN_BRDGRECOG_ID
    where m.VALID_FLAG=1 and br.VALID_FLAG=1 and m.BRDGRECOG_ID=m.MAIN_BRDGRECOG_ID and n.DATA_YEAR=#{year}
    group by n.TC_GRADE
    order by n.TC_GRADE
  </select>

  <select id="stsCulvertType" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select t.CLVRT_TYPE_NAME as CAT_NAME,count(1) as CAT_VAL from
      (select br.CLVRTRECOG_ID,max(d.STRUCT_FORM) as STRUCT_FORM from  T_CLVRT_CLVRTRECOG br
                                                                         inner join T_CLVRT_CONSSHAPE d on d.CLVRTRECOG_ID=br.CLVRTRECOG_ID

       where br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
                                                                                                          from GDGS.FW_RIGHT_ORG f
                                                                                                          where f.ID = #{orgId}
                                                                                                             or exists(select 1
                                                                                                                       from GDGS.FW_RIGHT_ORG p
                                                                                                                       where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)
         and  br.VALID_FLAG=1 and d.VALID_FLAG=1 group by br.CLVRTRECOG_ID) x
        inner join T_CLVRT_CLVRTTYPE t on x.STRUCT_FORM = t.CLVRT_TYPE_CODE
    group by t.CLVRT_TYPE_CODE,t.CLVRT_TYPE_NAME
    order by  t.CLVRT_TYPE_CODE
  </select>

  <select id="stsCulvertTechByOrgCodeAndYear" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select z.TC_GRADE as CAT_NAME,count(1) as CAT_VAL from (
                                                             select max(n.TC_GRADE) as TC_GRADE from  T_CLVRT_CLVRTRECOG br
                                                                                                        inner join ONEMAP.TS_CULVERT_TECHNICAL n on n.CULVERT_ID=br.CLVRTRECOG_ID
                                                             where br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
                                                                                                                                                                from GDGS.FW_RIGHT_ORG f
                                                                                                                                                                where f.ID = #{orgId}
                                                                                                                                                                   or exists(select 1
                                                                                                                                                                             from GDGS.FW_RIGHT_ORG p
                                                                                                                                                                             where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)
                                                               and   br.VALID_FLAG=1  and  n.DATA_YEAR=#{year}
                                                             group by br.CLVRTRECOG_ID) z group by z.TC_GRADE
    order by z.TC_GRADE
  </select>

  <select id="stsTunnelType" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select d.ATTRIBUTE_VALUE as CAT_NAME,count(1) as CAT_VAL from   MTMSDB.MTMS_TUNNEL_BASIC br
                                                                      inner join GDGS.BASE_DATATHIRD_DIC d on d.ATTRIBUTE_ITEM='TUNNEL_CLASSIFIED' and  br.TUNNEL_CLASSIFIED = d.ATTRIBUTE_CODE
    where  br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
                                                                                                        from GDGS.FW_RIGHT_ORG f
                                                                                                        where f.ID = #{orgId}
                                                                                                           or exists(select 1
                                                                                                                     from GDGS.FW_RIGHT_ORG p
                                                                                                                     where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)

      and  br.IS_ENABLE=1 and br.IS_DELETED=0
    group by d.ATTRIBUTE_CODE,d.ATTRIBUTE_VALUE
    order by  d.ATTRIBUTE_CODE
  </select>

  <select id="stsTunnelTechByOrgCodeAndYear" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select n.TC_GRADE as CAT_NAME,count(1) as CAT_VAL from  MTMSDB.MTMS_TUNNEL_BASIC br
    inner join ONEMAP.TS_TUNNEL_TECHNICAL n on n.TUNNEL_ID=br.TUNNEL_ID
    where br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
    from GDGS.FW_RIGHT_ORG f
    where f.ID = #{orgId}
    or exists(select 1
    from GDGS.FW_RIGHT_ORG p
    where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)

    and  br.IS_DELETED=0 and n.DATA_YEAR=#{year}
    group by n.TC_GRADE
    order by n.TC_GRADE
  </select>

  <select id="stsSlopeType" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select d.ATTRIBUTE_VALUE as CAT_NAME,count(1) as CAT_VAL from   HSMSDB.HSMS_SLOPE_INFO br
                                                                      inner join GDGS.BASE_DATATHIRD_DIC d on d.ATTRIBUTE_ITEM='SLOPE_LEVEL' and  br.SLOPE_LEVEL = d.ATTRIBUTE_CODE
    where  br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
                                                                                                        from GDGS.FW_RIGHT_ORG f
                                                                                                        where f.ID = 'N000003'
                                                                                                           or exists(select 1
                                                                                                                     from GDGS.FW_RIGHT_ORG p
                                                                                                                     where p.PARENT_ID = 'N000003' and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)

      and  br.IS_DELETED=0 and br.IS_DELETED=0
    group by d.ATTRIBUTE_CODE,d.ATTRIBUTE_VALUE
    order by  d.ATTRIBUTE_CODE
  </select>

  <select id="stsSlopeTechByOrgCodeAndYear" resultType="com.hualu.highwaymaintenance.module.bridge.vo.StructTechSts">
    select n.TS_GRADE as CAT_NAME,count(1) as CAT_VAL from  HSMSDB.HSMS_SLOPE_INFO br
    inner join ONEMAP.TS_SLOPE_TECHNICAL n on n.SLOPE_ID=br.SLOPE_ID
    where br.ROUTE_CODE in (select t.ROUTE_CODE from GDGS.BASE_ROUTE_LOGIC t where t.OPRT_ORG_CODE in (select f.ID
    from GDGS.FW_RIGHT_ORG f
    where f.ID = #{orgId}
    or exists(select 1
    from GDGS.FW_RIGHT_ORG p
    where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1)) and t.IS_ENABLE=1)

    and  br.IS_DELETED=0 and n.DATA_YEAR=#{year}
    group by n.TS_GRADE
    order by n.TS_GRADE
  </select>

  <update id="updateBridgeMoney">
    update BCTCMSDB.T_BRDG_BRDGRECOG br set br.MONEY_2025=#{money},br.customized=#{customized} where br.BRDGRECOG_ID=#{structId}
    </update>

  <select id="findCulvertDssByOprtOrgCode" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo">
    select di.*
    from BCTCMSDB.dss_info di
           inner join
         BCTCMSDB.T_CLVRT_CLVRTRECOG br on di.STRUCT_ID=br.CLVRTRECOG_ID and di.FACILITY_CAT='HD'
           inner join BCTCMSDB.EVAL_CULVERT_NEW t on br.CLVRTRECOG_ID = t.CLVRTRECOG_ID and di.REL_TASK_CODE=t.PRJ_ID
    where br.OPRT_ORG_CODE = #{oprtOrgCode}
      and br.VALID_FLAG = 1
    </select>

  <select id="findMapBySql" resultType="java.util.Map">
    ${reSql}
  </select>
</mapper>