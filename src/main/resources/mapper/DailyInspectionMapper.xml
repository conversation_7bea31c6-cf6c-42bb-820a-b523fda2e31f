<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintenance.mapper.DailyInspectionMapper">
	<select id="getDailyInspectionAllDataByLevelTwo" resultType="com.hualu.highwaymaintenance.module.maintenance.entity.DailyInspection">
		with orgtable as (
		    select o.ID,o.ORG_NAME,o.PARENT_ID
                  from GDGS.FW_RIGHT_ORG o
                  where o.IS_ENABLE = 1
                    and o.IS_DELETED = 0 and o.PARENT_ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
                  start with o.ID = #{orgId}
                  connect by prior o.ID = o.PARENT_ID
		)
		select org.SECOND_ORG_NAME as ORG_NAME,org.SECOND_ORG_CODE AS ORG_ID,sum(JAN)|| '/' || count(*) AS JAN, sum(FEB)|| '/' || count(*) AS FEB,
        sum(MAR)|| '/' || count(*) AS MAR, sum(APR) || '/' || count(*) AS APR,
        sum(MAY) || '/' || count(*) AS MAY, sum(JUN) || '/' || count(*) AS JUN, sum(JUL) || '/' || count(*) AS JUL,
        sum(AUG) || '/' || count(*) AS AUG, sum(SEPT) || '/' || count(*) AS SEPT,sum(OCT) || '/' || count(*) AS OCT,
        sum(NOV) || '/' || count(*) AS NOV,sum(DEC)|| '/' || count(*) AS DEC
		from (
		select distinct op.ID         SECOND_ORG_CODE,
                                        o.ID          oprt_org_code,
                                        op.ORG_NAME   SECOND_ORG_NAME,
                                        o.ORG_NAME as OPRT_ORG_NAME,
			l.PRJ_ORG_CODE
                        from orgtable o
                                 inner join gdgs.FW_RIGHT_ORG op on o.PARENT_ID = op.ID
                                 inner join gdgs.BASE_ROUTE_LOGIC l on l.OPRT_ORG_CODE = o.ID
		) org
		left join
		(select MNT_ORG_ID,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '01' then TOTAL end,0))
		    &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-01','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS JAN,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '02' then TOTAL end,0))
		    &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-02','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS FEB,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '03' then TOTAL end,0))
		    &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-03','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS MAR,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '04' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-04','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS APR,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '05' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-05','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS MAY,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '06' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-06','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS JUN,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '07' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-07','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS JUL,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '08' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-08','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS AUG,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '09' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-09','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS SEPT,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '10' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-10','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS OCT,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '11' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-11','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS NOV,
		case when sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '12' then TOTAL end,0))
		   &gt;= (SELECT TO_CHAR(LAST_DAY(TO_DATE(#{year} || '-12','yyyy-MM')),'dd') FROM dual) then 1 else 0 end AS DEC
		from (select MNT_ORG_ID,
		to_char(to_date(INSP_DATE,'yyyy-MM-dd'),'yyyy-MM') AS INSP_DATE,nvl(count(*),0) AS TOTAL
		from (select MNT_ORG_ID,to_char(INSP_DATE,'yyyy-MM-dd') AS INSP_DATE
		from MEMSDB.DM_DINSP where to_char(INSP_DATE,'yyyy') = #{year} and type = '1'
		group by MNT_ORG_ID,to_char(INSP_DATE,'yyyy-MM-dd')
		order by INSP_DATE) dd
		where INSP_DATE is not null
		group by to_char(to_date(INSP_DATE,'yyyy-MM-dd'),'yyyy-MM'),MNT_ORG_ID) group by MNT_ORG_ID) b
		on org.oprt_org_code = b.MNT_ORG_ID or (org.PRJ_ORG_CODE = b.MNT_ORG_ID and org.oprt_org_code = '38b22f19-891d-4075-811a-12482d403586')
		group by org.SECOND_ORG_NAME ,org.SECOND_ORG_CODE
	</select>
	<select id="getDailyInspectionAllDataByLevelThree" resultType="com.hualu.highwaymaintenance.module.maintenance.entity.DailyInspection">
		with orgtable as (
		    select o.ID
		    from GDGS.FW_RIGHT_ORG o
		    where o.IS_ENABLE = 1
		    and o.IS_DELETED = 0 
		    start with o.ID = #{orgId}
		    connect by prior o.ID = o.PARENT_ID
		)
		select org.OPRT_ORG_NAME,org.ORG_CODE AS ORG_ID,org.ORG_NAME, JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEPT, OCT, NOV, DEC
			from (select distinct o.ORG_FULLNAME OPRT_ORG_NAME,r.OPRT_ORG_CODE,r.PRJ_ORG_CODE as ORG_CODE,r.ROUTE_NAME as ORG_NAME
			from gdgs.BASE_ROUTE_LOGIC r
			inner join gdgs.FW_RIGHT_ORG o on r.OPRT_ORG_CODE = o.ORG_CODE
			where r.line_direct = 1 and r.IS_NEW = 1
			and r.IS_ENABLE = 1
			and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID or p.PRJ_ORG_CODE = uo.id
	        where p.ROUTE_CODE = r.ROUTE_CODE)
			) org
		left join
		(select MNT_ORG_ID,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '01' then TOTAL end,0)) AS JAN,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '02' then TOTAL end,0)) AS FEB,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '03' then TOTAL end,0)) AS MAR,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '04' then TOTAL end,0)) AS APR,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '05' then TOTAL end,0)) AS MAY,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '06' then TOTAL end,0)) AS JUN,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '07' then TOTAL end,0)) AS JUL,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '08' then TOTAL end,0)) AS AUG,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '09' then TOTAL end,0)) AS SEPT,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '10' then TOTAL end,0)) AS OCT,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '11' then TOTAL end,0))  AS NOV,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '12' then TOTAL end,0)) AS DEC
		from (select MNT_ORG_ID,
		to_char(to_date(INSP_DATE,'yyyy-MM-dd'),'yyyy-MM') AS INSP_DATE,count(*) AS TOTAL
		from (select MNT_ORG_ID,to_char(INSP_DATE,'yyyy-MM-dd') AS INSP_DATE
		from MEMSDB.DM_DINSP where to_char(INSP_DATE,'yyyy') = #{year} and type = '1'
		group by MNT_ORG_ID,to_char(INSP_DATE,'yyyy-MM-dd')
		order by INSP_DATE) dd
		where INSP_DATE is not null
		group by to_char(to_date(INSP_DATE,'yyyy-MM-dd'),'yyyy-MM'),MNT_ORG_ID) group by MNT_ORG_ID) b
		on org.OPRT_ORG_CODE = b.MNT_ORG_ID
	</select>
	<select id="getDailyInspectionOrgDataByLevelTwo" resultType="com.hualu.highwaymaintenance.module.maintenance.entity.DailyInspection">
		with orgtable as (
		    select o.ID,o.ORG_NAME,o.PARENT_ID
                  from GDGS.FW_RIGHT_ORG o
                  where o.IS_ENABLE = 1
                    and o.IS_DELETED = 0 and o.PARENT_ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
                  start with o.ID = #{orgId} 
                  connect by prior o.ID = o.PARENT_ID
		)
		select org.OPRT_ORG_NAME,case when (org.PRJ_ORG_CODE = b.MNT_ORG_ID and org.oprt_org_code = '38b22f19-891d-4075-811a-12482d403586') then org.PRJ_ORG_CODE else org.oprt_org_code end AS ORG_ID,org.ORG_NAME, JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEPT, OCT, NOV, DEC
		from ( select distinct op.ID         SECOND_ORG_CODE,
                                        o.ID          oprt_org_code,
                                        op.ORG_NAME   SECOND_ORG_NAME,
                                        o.ORG_NAME as OPRT_ORG_NAME,
                                        l.ROUTE_NAME as org_name,
			l.PRJ_ORG_CODE
                        from orgtable o
                                 inner join gdgs.FW_RIGHT_ORG op on o.PARENT_ID = op.ID
                                 inner join gdgs.BASE_ROUTE_LOGIC l on l.OPRT_ORG_CODE = o.ID
			) org
		left join
		(select MNT_ORG_ID,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '01' then TOTAL end,0)) AS JAN,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '02' then TOTAL end,0)) AS FEB,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '03' then TOTAL end,0)) AS MAR,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '04' then TOTAL end,0)) AS APR,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '05' then TOTAL end,0)) AS MAY,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '06' then TOTAL end,0)) AS JUN,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '07' then TOTAL end,0)) AS JUL,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '08' then TOTAL end,0)) AS AUG,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '09' then TOTAL end,0)) AS SEPT,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '10' then TOTAL end,0)) AS OCT,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '11' then TOTAL end,0))  AS NOV,
		sum(nvl(case when to_char(to_date(INSP_DATE,'yyyy-MM'),'MM') = '12' then TOTAL end,0)) AS DEC
		from (select MNT_ORG_ID,
		to_char(to_date(INSP_DATE,'yyyy-MM-dd'),'yyyy-MM') AS INSP_DATE,count(*) AS TOTAL
		from (select MNT_ORG_ID,to_char(INSP_DATE,'yyyy-MM-dd') AS INSP_DATE
		from MEMSDB.DM_DINSP where to_char(INSP_DATE,'yyyy') = #{year} and type = '1'
		group by MNT_ORG_ID,to_char(INSP_DATE,'yyyy-MM-dd')
		order by INSP_DATE) dd
		where INSP_DATE is not null
		group by to_char(to_date(INSP_DATE,'yyyy-MM-dd'),'yyyy-MM'),MNT_ORG_ID) group by MNT_ORG_ID)b
		on org.oprt_org_code = b.MNT_ORG_ID or (org.PRJ_ORG_CODE = b.MNT_ORG_ID and org.oprt_org_code = '38b22f19-891d-4075-811a-12482d403586')
		order by OPRT_ORG_CODE
	</select>
	
	<select id="getAppTrack" resultType="com.hualu.highwaymaintenance.module.platform.entity.AppTrack">
		select to_char(row_number() over (order by dd.INSP_DATE asc)) as orders,
		dd.DINSP_CODE as code,dd.PATROLUSERNAME as person,to_char(dd.INSP_DATE,'yyyy-MM-dd') as dateTime,
        at.TRACK_LENGTH as length from gdgs.APP_TRACK at
		inner join memsdb.DM_DINSP dd on at.ID = dd.DINSP_ID
		where 1 = 1 
		<if test="orgCode != null and orgCode != '' and orgCode != 'N000001'">
			and MNT_ORG_ID = #{orgCode}
		</if>
		<if test="startDate != null and startDate != ''">
			and to_char(INSP_DATE,'yyyy-MM-dd') &gt;= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			and to_char(INSP_DATE,'yyyy-MM-dd') &lt;= #{endDate}
		</if>
		union all
		select '合计' as orders,
		'' as code,'' as person,'' as dateTime,
        sum(at.TRACK_LENGTH) as length from gdgs.APP_TRACK at
		inner join memsdb.DM_DINSP dd on at.ID = dd.DINSP_ID
		where 1 = 1 
		<if test="orgCode != null and orgCode != '' and orgCode != 'N000001'">
			and MNT_ORG_ID = #{orgCode}
		</if>
		<if test="startDate != null and startDate != ''">
			and to_char(INSP_DATE,'yyyy-MM-dd') &gt;= #{startDate}
		</if>
		<if test="endDate != null and endDate != ''">
			and to_char(INSP_DATE,'yyyy-MM-dd') &lt;= #{endDate}
		</if>
	</select>

	<select id="getYearDailyInspectionStsByYearAndOrgCode"
			resultType="com.hualu.highwaymaintenance.module.maintenance.vo.YearDailyInspectionSts">
		select
			sum(at.TRACK_LENGTH)/1000 as mileage,count(distinct to_char(dd.INSP_DATE,'yyyy-MM-dd')) as inspectDayOfMonth from gdgs.APP_TRACK at
		right join memsdb.DM_DINSP dd on at.ID = dd.DINSP_ID
		where  MNT_ORG_ID = #{orgId}
		  and to_char(INSP_DATE,'yyyy') = #{year}
		and INSP_DATE &lt;=sysdate
	</select>

	<select id="getYearPlanStsOfItemBByYearAndOrgCode"
			resultType="com.hualu.highwaymaintenance.module.maintenance.vo.YearPlanCompletedSts">
		select sum(d.ACCEPT_AMOUNT*c.UNIT_PRICE) as "COMPLETED_NUM",sum(c.UNIT_PRICE*c.UNIT_COUNT) as "SHOULD_COMPLETE_OF_YEAR" from MEMSDB.DM_TASK_ACCPT t inner join MEMSDB.DM_TASK_ACCPT_DETAIL d on t.MTASK_ACCPT_ID=d.MTASK_ACCPT_ID
																											inner join MEMSDB.DM_TASK k on t.MTASK_ID=k.MTASK_ID
																											inner join MEMSDB.MPC_MPITEM_PRICE c on c.MP_ITEM_ID = d.MPITEM_ID and c.CONTR_ID=k.CONTR_ID
		where t.MNT_ORG_ID=#{orgId}
		  and to_char(t.ACCEPT_DATE,'yyyy')=#{year}
	</select>

	<select id="getYearPlanDtlBByYearAndOrgCode"
			resultType="com.hualu.highwaymaintenance.module.maintenance.vo.YearPlanCompletedSts">
<!--		select j.MPITEM_NAME as "ORG_NAME",sum(d.ACCEPT_AMOUNT*c.UNIT_PRICE) as "COMPLETED_NUM",sum(c.UNIT_PRICE*c.UNIT_COUNT) as "SHOULD_COMPLETE_OF_YEAR" from MEMSDB.DM_TASK_ACCPT t inner join MEMSDB.DM_TASK_ACCPT_DETAIL d on t.MTASK_ACCPT_ID=d.MTASK_ACCPT_ID-->
<!--																														  inner join MEMSDB.DM_TASK k on t.MTASK_ID=k.MTASK_ID-->
<!--																														  inner join MEMSDB.MPC_MPITEM_PRICE c on c.MP_ITEM_ID = d.MPITEM_ID and c.CONTR_ID=k.CONTR_ID-->
<!--																														  inner join memsdb.MPC_MPITEM s on s.MPITEM_ID=c.MP_ITEM_ID-->
<!--																														  inner join MEMSDB.MPC_MPITEM p on p.MPITEM_ID=s.P_MPITEM_ID-->
<!--																														  inner join MEMSDB.MPC_MPITEM g on g.MPITEM_ID=p.P_MPITEM_ID-->
<!--																														  inner join MEMSDB.MPC_MPITEM j on j.MPITEM_ID=g.P_MPITEM_ID-->
<!--		where t.MNT_ORG_ID=#{orgId}-->
<!--		  and to_char(t.ACCEPT_DATE,'yyyy')=#{year}-->
<!--		group by j.MPITEM_NAME-->
		select
		(select j.MPITEM_NAME from MEMSDB.MPC_MPITEM  j where j.MPITEM_CODE= r.MPITEM_CODE and j.ORG_ID=#{orgId} and ROWNUM=1) as "ORG_NAME",r.MPITEM_CODE,
		sum(r.COMPLETED_NUM) as "COMPLETED_NUM",
		sum(r.SHOULD_COMPLETE_OF_YEAR)    as "SHOULD_COMPLETE_OF_YEAR" from
		(
		select (select min(j.MPITEM_CODE) from  MEMSDB.MPC_MPITEM j start with j.MPITEM_ID=c.MP_ITEM_ID
		connect by prior P_MPITEM_ID=MPITEM_ID ) as MPITEM_CODE,
		sum(d.ACCEPT_AMOUNT * c.UNIT_PRICE) as "COMPLETED_NUM",
		sum(c.UNIT_PRICE * c.UNIT_COUNT)    as "SHOULD_COMPLETE_OF_YEAR"
		from MEMSDB.DM_TASK_ACCPT t
		inner join MEMSDB.DM_TASK_ACCPT_DETAIL d on t.MTASK_ACCPT_ID = d.MTASK_ACCPT_ID
		inner join MEMSDB.DM_TASK k on t.MTASK_ID = k.MTASK_ID
		inner join MEMSDB.MPC_MPITEM_PRICE c on c.MP_ITEM_ID = d.MPITEM_ID and c.CONTR_ID = k.CONTR_ID

		where t.MNT_ORG_ID = #{orgId}
		and to_char(t.ACCEPT_DATE, 'yyyy') = #{year}
		group by c.MP_ITEM_ID) r group by  r.MPITEM_CODE
		order by ORG_NAME
	</select>

	<select id="stsSpecialPrjByOrgAndYear"
			resultType="com.hualu.highwaymaintenance.module.maintenance.vo.SpecialPrjSts">
		select d.cat_name,sum(d.c) as SUM,sum(decode(d.PROCESS,1,0,2,0,1)) as DONE from (
			select decode(sp.FACILITY_CAT,'LM','路面','QL','桥梁','SD','隧道','BP','边坡','HD','涵洞') as cat_name, sp.process,count(1) as c from memsdb.special_project sp where sp.PROJECT_TYPE=3
			and sp.PLAN_YEAR=#{year}
			and sp.OPRT_ORG_CODE
			in (select f.ID from GDGS.FW_RIGHT_ORG  f where f.ID=#{orgId} or exists(select 1 from GDGS.FW_RIGHT_ORG p where p.PARENT_ID=#{orgId} and p.ID=f.ID and p.IS_ENABLE=1))
			group by sp.FACILITY_CAT, sp.process) d
		group by d.cat_name
		order by d.cat_name
	</select>
</mapper>