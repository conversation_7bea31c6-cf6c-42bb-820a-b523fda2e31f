<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.RepositoryAdjustLogMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.national.entity.RepositoryAdjustLog">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="content" column="CONTENT" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
            <result property="propertyName" column="PROPERTY_NAME" jdbcType="VARCHAR"/>
            <result property="beforeVal" column="BEFORE_VAL" jdbcType="VARCHAR"/>
            <result property="afterVal" column="AFTER_VAL" jdbcType="VARCHAR"/>
            <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
            <result property="time" column="TIME" jdbcType="TIMESTAMP"/>
            <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
            <result property="packageId" column="PACKAGE_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CONTENT,TYPE,
        PROPERTY_NAME,BEFORE_VAL,AFTER_VAL,
        USER_CODE,TIME,ORG_ID,
        PACKAGE_ID
    </sql>
    <select id="queryBridgeLog"
            resultType="com.hualu.highwaymaintenance.module.national.vo.BridgeRepositoryLog">
        with tt as (
            select oprt_org_name, year, target, prevent_quantity, prevent_mileage, prevent_fee, repair_quantity, repair_mileage, repair_fee,prevent_fee+repair_fee as fee from
            (select distinct OPRT_ORG_NAME,year,target,
            sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.QUANTITY else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) prevent_quantity,
            sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.MILEAGE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) prevent_mileage,
            sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.FEE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) prevent_fee,
            sum(case when br.MAINTENANCE_TYPE = 'repair' then br.QUANTITY else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repair_quantity,
            sum(case when br.MAINTENANCE_TYPE = 'repair' then br.MILEAGE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repair_mileage,
            sum(case when br.MAINTENANCE_TYPE = 'repair' then br.FEE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repair_fee
            from MEMSDB.BRIDGE_REPOSITORY_NEW br
            where exists (select 1 from MEMSDB.REPOSITORY_PACKAGE b where id = #{packageId}
            and br.OPRT_ORG_CODE = b.ORG_CODE)
            )),yeart as (select VERSION from MEMSDB.REPOSITORY_PACKAGE b where id = #{packageId}),
            beforet as (select OPRT_ORG_NAME,TARGET,year,sum(prevent_quantity) prevent_quantity,sum(repair_quantity) repair_quantity,
            sum(prevent_mileage) prevent_mileage,sum(prevent_fee) prevent_fee,
            sum(repair_mileage) repair_mileage,sum(repair_fee) repair_fee, sum(fee) fee from tt a
        where exists(select 1 from yeart b where a.TARGET = b.VERSION - 1) group by OPRT_ORG_NAME,TARGET,year),
            aftert as (select OPRT_ORG_NAME,TARGET,year,sum(prevent_quantity) prevent_quantity,sum(repair_quantity) repair_quantity,
            sum(prevent_mileage) prevent_mileage,sum(prevent_fee) prevent_fee,
            sum(repair_mileage) repair_mileage,sum(repair_fee) repair_fee, sum(fee) fee
        from tt a where exists(select 1 from yeart b where a.TARGET = b.VERSION) group by OPRT_ORG_NAME,TARGET,year)
        select a.OPRT_ORG_NAME org_name,
               case when b.OPRT_ORG_NAME is null then  a.TARGET || '版本(' || a.YEAR || '年)新增' else b.TARGET || '版本(' || b.year || '年)于' || a.TARGET || '版本(' || a.YEAR || '年)调整' end as year,
           b.prevent_mileage before_prevent_mileage,
               b.prevent_fee before_prevent_fee,
               b.prevent_quantity before_prevent_quantity,
               b.repair_quantity before_repair_quantity,
               b.repair_mileage before_repair_mileage,
               b.repair_fee before_repair_fee,
               b.fee before_fee,
               a.prevent_mileage after_prevent_mileage,
               a.prevent_fee after_prevent_fee,
               a.prevent_quantity after_prevent_quantity,
               a.repair_quantity after_repair_quantity,
               a.repair_mileage after_repair_mileage,
               a.repair_fee after_repair_fee,
               a.fee after_fee
        from aftert a left join beforet b on a.OPRT_ORG_NAME = b.OPRT_ORG_NAME and a.year = b.year
    </select>
    <select id="queryPavementLog"
            resultType="com.hualu.highwaymaintenance.module.national.vo.PavementRepositoryLog">
        with tt as (
            select oprt_org_name, prevent_mileage, prevent_fee, repair_mileage, repair_fee,prevent_fee+repair_fee as fee,year,target from
            (select distinct
            OPRT_ORG_NAME,year,target,
            sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.MILEAGE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) prevent_mileage,
            sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.FEE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) prevent_fee,
            sum(case when br.MAINTENANCE_TYPE = 'repair' then br.MILEAGE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repair_mileage,
            sum(case when br.MAINTENANCE_TYPE = 'repair' then br.FEE else 0 end)
            over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repair_fee
            from MEMSDB.PAVEMENT_REPOSITORY_NEW br
            where exists (select 1 from MEMSDB.REPOSITORY_PACKAGE b where id = #{packageId}
            and br.OPRT_ORG_CODE = b.ORG_CODE)
            )),yeart as (select VERSION from MEMSDB.REPOSITORY_PACKAGE b where id = #{packageId}),
            beforet as (select OPRT_ORG_NAME,TARGET,year, sum(prevent_mileage) prevent_mileage,sum(prevent_fee) prevent_fee,
            sum(repair_mileage) repair_mileage,sum(repair_fee) repair_fee, sum(fee) fee from tt a
        where exists(select 1 from yeart b where a.TARGET = b.VERSION - 1) group by OPRT_ORG_NAME,TARGET,year),
            aftert as (select OPRT_ORG_NAME,TARGET,year, sum(prevent_mileage) prevent_mileage,sum(prevent_fee) prevent_fee,
            sum(repair_mileage) repair_mileage,sum(repair_fee) repair_fee, sum(fee) fee
        from tt a where exists(select 1 from yeart b where a.TARGET = b.VERSION) group by OPRT_ORG_NAME,TARGET,year)
        select a.OPRT_ORG_NAME org_name,
               case when b.OPRT_ORG_NAME is null then  a.TARGET || '版本(' || a.YEAR || '年)新增' else b.TARGET || '版本(' || b.year || '年)于' || a.TARGET || '版本(' || a.YEAR || '年)调整' end as year,
               b.prevent_mileage before_prevent_mileage,
               b.prevent_fee before_prevent_fee,
               b.repair_mileage before_repair_mileage,
               b.repair_fee before_repair_fee,
               b.fee before_fee,
               a.prevent_mileage after_prevent_mileage,
               a.prevent_fee after_prevent_fee,
               a.repair_mileage after_repair_mileage,
               a.repair_fee after_repair_fee,
               a.fee after_fee
        from aftert a left join beforet b on a.OPRT_ORG_NAME = b.OPRT_ORG_NAME and a.year = b.year
    </select>
    <select id="queryTunnelLog"
            resultType="com.hualu.highwaymaintenance.module.national.vo.TunnelRepositoryLog">
        with tt as (
            select oprt_org_name, year, prevent_tunnel_count, repair_tunnel_count, prevent_mileage, repair_mileage,
            prevent_civil_const, repair_civil_const, civil_total_const, ele_total_const, total_const, version, target
        from MEMSDB.TUNNEL_REPOSITORY_NEW br
        where exists (select 1 from MEMSDB.REPOSITORY_PACKAGE b where id = #{packageId}
          and br.OPRT_ORG_CODE = b.ORG_CODE)
            ),yeart as (select VERSION from MEMSDB.REPOSITORY_PACKAGE b where id = #{packageId}),
            beforet as (select OPRT_ORG_NAME,TARGET,year,sum(prevent_tunnel_count) prevent_tunnel_count,sum(repair_tunnel_count) repair_tunnel_count,
            sum(prevent_mileage) prevent_mileage,sum(repair_mileage) repair_mileage,sum(prevent_civil_const) prevent_civil_const,
            sum(repair_civil_const) repair_civil_const,
            sum(civil_total_const) civil_total_const,sum(ele_total_const) ele_total_const,sum(total_const) total_const from tt a
        where exists(select 1 from yeart b where a.TARGET = b.VERSION - 1) group by OPRT_ORG_NAME,TARGET,year),
            aftert as (select OPRT_ORG_NAME,TARGET,year,sum(prevent_tunnel_count) prevent_tunnel_count,sum(repair_tunnel_count) repair_tunnel_count,
            sum(prevent_mileage) prevent_mileage,sum(repair_mileage) repair_mileage,sum(prevent_civil_const) prevent_civil_const,
            sum(repair_civil_const) repair_civil_const,
            sum(civil_total_const) civil_total_const,sum(ele_total_const) ele_total_const,sum(total_const) total_const
        from tt a where exists(select 1 from yeart b where a.TARGET = b.VERSION) group by OPRT_ORG_NAME,TARGET,year)
        select a.OPRT_ORG_NAME org_name,
               case when b.OPRT_ORG_NAME is null then a.TARGET || '版本(' || a.YEAR || '年)新增' else b.TARGET || '版本(' || b.year || '年)于' || a.TARGET || '版本(' || a.YEAR || '年)调整' end as year,
               b.prevent_tunnel_count before_prevent_tunnel_count,
               b.repair_tunnel_count before_repair_tunnel_count,
               b.prevent_mileage before_prevent_mileage,
               b.repair_mileage before_repair_mileage,
               b.prevent_civil_const before_prevent_civil_const,
               b.repair_civil_const before_repair_civil_const,
               b.civil_total_const before_civil_total_const,
               b.ele_total_const before_ele_total_const,
               b.total_const before_total_const,
               a.prevent_tunnel_count after_prevent_tunnel_count,
               a.repair_tunnel_count after_repair_tunnel_count,
               a.prevent_mileage after_prevent_mileage,
               a.repair_mileage after_repair_mileage,
               a.prevent_civil_const after_prevent_civil_const,
               a.repair_civil_const after_repair_civil_const,
               a.civil_total_const after_civil_total_const,
               a.ele_total_const after_ele_total_const,
               a.total_const after_total_const
        from aftert a left join beforet b on a.OPRT_ORG_NAME = b.OPRT_ORG_NAME and a.year = b.year
    </select>
</mapper>
