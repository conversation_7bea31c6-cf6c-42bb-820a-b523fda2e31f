<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dsstype.mapper.DssTypeMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dsstype.domain.DssType">
            <result property="dssTypeId" column="DSS_TYPE_ID" jdbcType="VARCHAR"/>
            <result property="dssType" column="DSS_TYPE" jdbcType="VARCHAR"/>
            <result property="dssTypeName" column="DSS_TYPE_NAME" jdbcType="VARCHAR"/>
            <result property="structCompId" column="STRUCT_COMP_ID" jdbcType="VARCHAR"/>
            <result property="structPosition" column="STRUCT_POSITION" jdbcType="VARCHAR"/>
            <result property="qultDescWay" column="QULT_DESC_WAY" jdbcType="VARCHAR"/>
            <result property="dssRefImg" column="DSS_REF_IMG" jdbcType="VARCHAR"/>
            <result property="dssDrawIcon" column="DSS_DRAW_ICON" jdbcType="VARCHAR"/>
            <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
            <result property="maxDegree" column="MAX_DEGREE" jdbcType="DECIMAL"/>
            <result property="isSys" column="IS_SYS" jdbcType="DECIMAL"/>
            <result property="pDssTypeId" column="P_DSS_TYPE_ID" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="haveDssL" column="HAVE_DSS_L" jdbcType="DECIMAL"/>
            <result property="dssLUnit" column="DSS_L_UNIT" jdbcType="VARCHAR"/>
            <result property="haveDssW" column="HAVE_DSS_W" jdbcType="DECIMAL"/>
            <result property="dssWUnit" column="DSS_W_UNIT" jdbcType="VARCHAR"/>
            <result property="haveDssD" column="HAVE_DSS_D" jdbcType="DECIMAL"/>
            <result property="dssDUnit" column="DSS_D_UNIT" jdbcType="VARCHAR"/>
            <result property="haveDssN" column="HAVE_DSS_N" jdbcType="DECIMAL"/>
            <result property="dssNUnit" column="DSS_N_UNIT" jdbcType="VARCHAR"/>
            <result property="haveDssA" column="HAVE_DSS_A" jdbcType="DECIMAL"/>
            <result property="dssAUnit" column="DSS_A_UNIT" jdbcType="VARCHAR"/>
            <result property="haveDssV" column="HAVE_DSS_V" jdbcType="DECIMAL"/>
            <result property="dssVUnit" column="DSS_V_UNIT" jdbcType="VARCHAR"/>
            <result property="haveDssP" column="HAVE_DSS_P" jdbcType="DECIMAL"/>
            <result property="haveDssG" column="HAVE_DSS_G" jdbcType="DECIMAL"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="haveDssColom" column="HAVE_DSS_COLOM" jdbcType="VARCHAR"/>
            <result property="haveDssUnit" column="HAVE_DSS_UNIT" jdbcType="VARCHAR"/>
            <result property="dssOrderNumber" column="DSS_ORDER_NUMBER" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        DSS_TYPE_ID,DSS_TYPE,DSS_TYPE_NAME,
        STRUCT_COMP_ID,STRUCT_POSITION,QULT_DESC_WAY,
        DSS_REF_IMG,DSS_DRAW_ICON,ORG_ID,
        MAX_DEGREE,IS_SYS,P_DSS_TYPE_ID,
        IS_DELETED,HAVE_DSS_L,DSS_L_UNIT,
        HAVE_DSS_W,DSS_W_UNIT,HAVE_DSS_D,
        DSS_D_UNIT,HAVE_DSS_N,DSS_N_UNIT,
        HAVE_DSS_A,DSS_A_UNIT,HAVE_DSS_V,
        DSS_V_UNIT,HAVE_DSS_P,HAVE_DSS_G,
        CREATE_USER_ID,CREATE_TIME,UPDATE_USER_ID,
        UPDATE_TIME,HAVE_DSS_COLOM,HAVE_DSS_UNIT,
        DSS_ORDER_NUMBER
    </sql>
    <select id="findBySql" resultType="com.hualu.highwaymaintenance.module.dsstype.domain.DssType">
        ${sql}
    </select>
    <select id="findStringBySql" resultType="java.lang.String">
        ${sql}
    </select>
    <select id="findMapBySql" resultType="java.util.Map">
        ${sql}
    </select>
    <select id="findStructBySql" resultType="com.hualu.highwaymaintenance.module.dic.BaseStruct">
        ${sql}
    </select>
    <select id="findTuunelBySql" resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseTunnel">
        ${sql}
    </select>
    <select id="findSlopeBySql" resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseSlope">
        ${sql}
    </select>
    <select id="findBaseServiceAreaBySql"
            resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseServiceArea">
        ${sql}
    </select>
    <select id="findBaseTollGateBySql"
            resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseTollGate">
        ${sql}
    </select>
    <select id="findBrdgBySql" resultType="com.hualu.highwaymaintenance.module.dic.domain.TBrdgBrdgrecog">
        ${sql}
    </select>
    <select id="findClvrtBySql" resultType="com.hualu.highwaymaintenance.module.dic.domain.TClvrtClvrtrecog">
        ${sql}
    </select>
    <select id="findSlopeById" resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseSlope">
        ${sql}
    </select>
    <select id="findTuunelById" resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseTunnel">
        ${sql}
    </select>
    <select id="findBaseServiceAreaById"
            resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseServiceArea">
        ${sql}
    </select>
    <select id="findTollGateId" resultType="com.hualu.highwaymaintenance.module.dic.domain.BaseTollGate">
        ${sql}
    </select>

    <select id="loadDssDegree" resultType="com.hualu.highwaymaintenance.module.dsstype.domain.DssDegree">
      SELECT * FROM memsdb.DSSTYPE_DSSDEGREE D WHERE D.DSS_TYPE= #{dssType}
    </select>
</mapper>
