<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqInitialInspectionMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqInitialInspection">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_INITIAL_INSPECTION-->
    <id column="INITIAL_INSPECTION_ID" jdbcType="VARCHAR" property="initialInspectionId" />
    <result column="BRIDGE_IDENTITY_CODE" jdbcType="VARCHAR" property="bridgeIdentityCode" />
    <result column="CONSTRUCTION_METHOD" jdbcType="VARCHAR" property="constructionMethod" />
    <result column="CONSTRUCTION_PROCESS" jdbcType="VARCHAR" property="constructionProcess" />
    <result column="REINFORCE_AND_RECONSTRUCTION" jdbcType="VARCHAR" property="reinforceAndReconstruction" />
    <result column="INITIAL_INSPECTION_DATE" jdbcType="VARCHAR" property="initialInspectionDate" />
    <result column="HANDOVER_DATE" jdbcType="VARCHAR" property="handoverDate" />
    <result column="CLIMATE_AND_TEMPERATURE" jdbcType="VARCHAR" property="climateAndTemperature" />
    <result column="RECORDER" jdbcType="VARCHAR" property="recorder" />
    <result column="BRIDGE_ENGINEER" jdbcType="VARCHAR" property="bridgeEngineer" />
    <result column="INSPECTION_ORGANIZATION" jdbcType="VARCHAR" property="inspectionOrganization" />
    <result column="DATA_INCOMPLETE" jdbcType="VARCHAR" property="dataIncomplete" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INITIAL_INSPECTION_ID, BRIDGE_IDENTITY_CODE, CONSTRUCTION_METHOD, CONSTRUCTION_PROCESS, 
    REINFORCE_AND_RECONSTRUCTION, INITIAL_INSPECTION_DATE, HANDOVER_DATE, CLIMATE_AND_TEMPERATURE, 
    RECORDER, BRIDGE_ENGINEER, INSPECTION_ORGANIZATION, DATA_INCOMPLETE, SECOND_COMPANY, 
    ORG_CODE, REPORT_ID
  </sql>
</mapper>