<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.task.mapper.DmTaskDetailMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.task.domain.DmTaskDetail">
            <id property="mtaskDtlId" column="MTASK_DTL_ID" jdbcType="VARCHAR"/>
            <result property="mtaskId" column="MTASK_ID" jdbcType="VARCHAR"/>
            <result property="dssId" column="DSS_ID" jdbcType="VARCHAR"/>
            <result property="mpitemId" column="MPITEM_ID" jdbcType="VARCHAR"/>
            <result property="measureUnit" column="MEASURE_UNIT" jdbcType="VARCHAR"/>
            <result property="mpitemAccount" column="MPITEM_ACCOUNT" jdbcType="DECIMAL"/>
            <result property="isLump" column="IS_LUMP" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="acceptStatus" column="ACCEPT_STATUS" jdbcType="DECIMAL"/>
            <result property="isAdd" column="IS_ADD" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        MTASK_DTL_ID,MTASK_ID,DSS_ID,
        MPITEM_ID,MEASURE_UNIT,MPITEM_ACCOUNT,
        IS_LUMP,REMARK,ACCEPT_STATUS,
        IS_ADD
    </sql>
    <select id="loadDmTaskDetailInfo"
            resultType="com.hualu.highwaymaintenance.module.task.domain.DmTaskDetail">

        select de.*,mmp.MPITEM_NAME as item_name,mmp.UNIT_PRICE,(de.MPITEM_ACCOUNT*mmp.UNIT_PRICE) as sum_money from MEMSDB.DM_TASK dk inner join memsdb.DM_TASK_DETAIL de on dk.MTASK_ID=de.MTASK_ID
                                                                                                                            inner join MEMSDB.MPC_MPITEM t on de.MPITEM_ID=t.MPITEM_ID
                                                                                                                            left join MEMSDB.MPC_MPITEM_PRICE mmp on mmp.contr_id =dk.contr_id and   mmp.mp_item_id = t.MPITEM_ID
        where de.DSS_ID=#{dssId}

    </select>
</mapper>
