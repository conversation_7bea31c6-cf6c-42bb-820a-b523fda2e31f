<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintainbase.mapper.BaseRampLineMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRampLine">
    <!--@mbg.generated-->
    <!--@Table GDGS.BASE_RAMP_LINE-->
    <id column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LINE_ALLNAME" jdbcType="VARCHAR" property="lineAllname" />
    <result column="LINE_SNAME" jdbcType="VARCHAR" property="lineSname" />
    <result column="LINE_DESC" jdbcType="VARCHAR" property="lineDesc" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="LINE_COUNTER_CODE" jdbcType="VARCHAR" property="lineCounterCode" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
    <result column="STAKE_TYPE" jdbcType="VARCHAR" property="stakeType" />
    <result column="LINE_LENGTH" jdbcType="DECIMAL" property="lineLength" />
    <result column="RAMP_LINETYPE" jdbcType="VARCHAR" property="rampLinetype" />
    <result column="S_MRPID" jdbcType="VARCHAR" property="sMrpid" />
    <result column="E_MRPID" jdbcType="VARCHAR" property="eMrpid" />
    <result column="S_ROUTE_CODE" jdbcType="VARCHAR" property="sRouteCode" />
    <result column="E_ROUTE_CODE" jdbcType="VARCHAR" property="eRouteCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LINE_ID, LINE_CODE, LINE_ALLNAME, LINE_SNAME, LINE_DESC, REMARK, CREATE_USER_ID, 
    CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, LINE_COUNTER_CODE, IS_ENABLE, IS_DELETED, 
    STAKE_TYPE, LINE_LENGTH, RAMP_LINETYPE, S_MRPID, E_MRPID, S_ROUTE_CODE, E_ROUTE_CODE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from GDGS.BASE_RAMP_LINE
    where LINE_ID = #{lineId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from GDGS.BASE_RAMP_LINE
    where LINE_ID = #{lineId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRampLine">
    <!--@mbg.generated-->
    insert into GDGS.BASE_RAMP_LINE (LINE_ID, LINE_CODE, LINE_ALLNAME, 
      LINE_SNAME, LINE_DESC, REMARK, 
      CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, 
      UPDATE_TIME, LINE_COUNTER_CODE, IS_ENABLE, 
      IS_DELETED, STAKE_TYPE, LINE_LENGTH, 
      RAMP_LINETYPE, S_MRPID, E_MRPID, 
      S_ROUTE_CODE, E_ROUTE_CODE)
    values (#{lineId,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{lineAllname,jdbcType=VARCHAR}, 
      #{lineSname,jdbcType=VARCHAR}, #{lineDesc,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{lineCounterCode,jdbcType=VARCHAR}, #{isEnable,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=VARCHAR}, #{stakeType,jdbcType=VARCHAR}, #{lineLength,jdbcType=DECIMAL}, 
      #{rampLinetype,jdbcType=VARCHAR}, #{sMrpid,jdbcType=VARCHAR}, #{eMrpid,jdbcType=VARCHAR}, 
      #{sRouteCode,jdbcType=VARCHAR}, #{eRouteCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRampLine">
    <!--@mbg.generated-->
    insert into GDGS.BASE_RAMP_LINE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="lineId != null">
        LINE_ID,
      </if>
      <if test="lineCode != null">
        LINE_CODE,
      </if>
      <if test="lineAllname != null">
        LINE_ALLNAME,
      </if>
      <if test="lineSname != null">
        LINE_SNAME,
      </if>
      <if test="lineDesc != null">
        LINE_DESC,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="lineCounterCode != null">
        LINE_COUNTER_CODE,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="stakeType != null">
        STAKE_TYPE,
      </if>
      <if test="lineLength != null">
        LINE_LENGTH,
      </if>
      <if test="rampLinetype != null">
        RAMP_LINETYPE,
      </if>
      <if test="sMrpid != null">
        S_MRPID,
      </if>
      <if test="eMrpid != null">
        E_MRPID,
      </if>
      <if test="sRouteCode != null">
        S_ROUTE_CODE,
      </if>
      <if test="eRouteCode != null">
        E_ROUTE_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="lineId != null">
        #{lineId,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="lineAllname != null">
        #{lineAllname,jdbcType=VARCHAR},
      </if>
      <if test="lineSname != null">
        #{lineSname,jdbcType=VARCHAR},
      </if>
      <if test="lineDesc != null">
        #{lineDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lineCounterCode != null">
        #{lineCounterCode,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="stakeType != null">
        #{stakeType,jdbcType=VARCHAR},
      </if>
      <if test="lineLength != null">
        #{lineLength,jdbcType=DECIMAL},
      </if>
      <if test="rampLinetype != null">
        #{rampLinetype,jdbcType=VARCHAR},
      </if>
      <if test="sMrpid != null">
        #{sMrpid,jdbcType=VARCHAR},
      </if>
      <if test="eMrpid != null">
        #{eMrpid,jdbcType=VARCHAR},
      </if>
      <if test="sRouteCode != null">
        #{sRouteCode,jdbcType=VARCHAR},
      </if>
      <if test="eRouteCode != null">
        #{eRouteCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRampLine">
    <!--@mbg.generated-->
    update GDGS.BASE_RAMP_LINE
    <set>
      <if test="lineCode != null">
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="lineAllname != null">
        LINE_ALLNAME = #{lineAllname,jdbcType=VARCHAR},
      </if>
      <if test="lineSname != null">
        LINE_SNAME = #{lineSname,jdbcType=VARCHAR},
      </if>
      <if test="lineDesc != null">
        LINE_DESC = #{lineDesc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lineCounterCode != null">
        LINE_COUNTER_CODE = #{lineCounterCode,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="stakeType != null">
        STAKE_TYPE = #{stakeType,jdbcType=VARCHAR},
      </if>
      <if test="lineLength != null">
        LINE_LENGTH = #{lineLength,jdbcType=DECIMAL},
      </if>
      <if test="rampLinetype != null">
        RAMP_LINETYPE = #{rampLinetype,jdbcType=VARCHAR},
      </if>
      <if test="sMrpid != null">
        S_MRPID = #{sMrpid,jdbcType=VARCHAR},
      </if>
      <if test="eMrpid != null">
        E_MRPID = #{eMrpid,jdbcType=VARCHAR},
      </if>
      <if test="sRouteCode != null">
        S_ROUTE_CODE = #{sRouteCode,jdbcType=VARCHAR},
      </if>
      <if test="eRouteCode != null">
        E_ROUTE_CODE = #{eRouteCode,jdbcType=VARCHAR},
      </if>
    </set>
    where LINE_ID = #{lineId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRampLine">
    <!--@mbg.generated-->
    update GDGS.BASE_RAMP_LINE
    set LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      LINE_ALLNAME = #{lineAllname,jdbcType=VARCHAR},
      LINE_SNAME = #{lineSname,jdbcType=VARCHAR},
      LINE_DESC = #{lineDesc,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      LINE_COUNTER_CODE = #{lineCounterCode,jdbcType=VARCHAR},
      IS_ENABLE = #{isEnable,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=VARCHAR},
      STAKE_TYPE = #{stakeType,jdbcType=VARCHAR},
      LINE_LENGTH = #{lineLength,jdbcType=DECIMAL},
      RAMP_LINETYPE = #{rampLinetype,jdbcType=VARCHAR},
      S_MRPID = #{sMrpid,jdbcType=VARCHAR},
      E_MRPID = #{eMrpid,jdbcType=VARCHAR},
      S_ROUTE_CODE = #{sRouteCode,jdbcType=VARCHAR},
      E_ROUTE_CODE = #{eRouteCode,jdbcType=VARCHAR}
    where LINE_ID = #{lineId,jdbcType=VARCHAR}
  </update>
</mapper>