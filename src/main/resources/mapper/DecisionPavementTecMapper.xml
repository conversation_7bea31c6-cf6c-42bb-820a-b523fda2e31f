<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.DecisionPavementTecMapper">

    <select id="loadDecisionPavementTecDatas" resultType="com.hualu.highwaymaintenance.module.decision.domain.PavementTecBean">
        select sum(case when d.PCI &gt;= 90 then d.LENGTH else 0 end)/1000 as PCI1,
        sum(case when d.PCI &gt;= 80 and d.PCI &lt; 90 then d.LENGTH else 0 end)/1000 as PCI2,
        sum(case when d.PCI &gt;= 70 and d.PCI &lt; 80 then d.LENGTH else 0 end)/1000 as PCI3,
        sum(case when d.PCI &gt;= 60 and d.PCI &lt; 70 then d.LENGTH else 0 end)/1000 as PCI4,
        sum(case when d.PCI &lt; 60 then d.LENGTH else 0 end)/1000 as PCI5,
        sum(case when d.RQI &gt;= 90 then d.LENGTH else 0 end)/1000 as RQI1,
        sum(case when d.RQI &gt;= 80 and d.RQI &lt; 90 then d.LENGTH else 0 end)/1000 as RQI2,
        sum(case when d.RQI &gt;= 70 and d.RQI &lt; 80 then d.LENGTH else 0 end)/1000 as RQI3,
        sum(case when d.RQI &gt;= 60 and d.RQI &lt; 70 then d.LENGTH else 0 end)/1000 as RQI4,
        sum(case when d.RQI &lt; 60 then d.LENGTH else 0 end)/1000 as RQI5,
        sum(case when d.RDI &gt;= 90 then d.LENGTH else 0 end)/1000 as RDI1,
        sum(case when d.RDI &gt;= 80 and d.RDI &lt; 90 then d.LENGTH else 0 end)/1000 as RDI2,
        sum(case when d.RDI &gt;= 70 and d.RDI &lt; 80 then d.LENGTH else 0 end)/1000 as RDI3,
        sum(case when d.RDI &gt;= 60 and d.RDI &lt; 70 then d.LENGTH else 0 end)/1000 as RDI4,
        sum(case when d.RDI &lt; 60 then d.LENGTH else 0 end)/1000 as RDI5,
        sum(case when d.SRI &gt;= 90 then d.LENGTH else 0 end)/1000 as SRI1,
        sum(case when d.SRI &gt;= 80 and d.SRI &lt; 90 then d.LENGTH else 0 end)/1000 as SRI2,
        sum(case when d.SRI &gt;= 70 and d.SRI &lt; 80 then d.LENGTH else 0 end)/1000 as SRI3,
        sum(case when d.SRI &gt;= 60 and d.SRI &lt; 70 then d.LENGTH else 0 end)/1000 as SRI4,
        sum(case when d.SRI &lt; 60 then d.LENGTH else 0 end)/1000 as SRI5,
        sum(case when d.PSSI &gt;= 90 then d.LENGTH else 0 end)/1000 as PSSI1,
        sum(case when d.PSSI &gt;= 80 and d.PSSI &lt; 90 then d.LENGTH else 0 end)/1000 as PSSI2,
        sum(case when d.PSSI &gt;= 70 and d.PSSI &lt; 80 then d.LENGTH else 0 end)/1000 as PSSI3,
        sum(case when d.PSSI &gt;= 60 and d.PSSI &lt; 70 then d.LENGTH else 0 end)/1000 as PSSI4,
        sum(case when d.PSSI &lt; 60 then d.LENGTH else 0 end)/1000 as PSSI5
        FROM PTCMSDB.PTCD_TC_DETAIL_PROVIDE_1000 d
        inner join gdgs.BASE_ROUTE_LOGIC l on d.ROUTE_CODE = l.ROUTE_CODE
        inner join GDGS.PRO_ROUTE p on l.OPRT_ORG_CODE = p.OPRT_ORG_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON l.PRJ_ORG_CODE=ORG.ID
        WHERE d.YEAR = #{year} and org.PARENT_ID = #{orgCode}
    </select>

    <select id="loadDecisionPavementTecTotalDatas" resultType="com.hualu.highwaymaintenance.module.decision.domain.PavementTecTotalBean">
        select ROUND(AVG(nvl(d.PCI,0)), 2) AS PCI,
               ROUND(AVG(nvl(d.RQI,0)), 2) RQI,
               ROUND(AVG(nvl(d.RDI,0)), 2) AS RDI,
               ROUND(AVG(nvl(d.SRI,0)), 2) AS SRI,
               ROUND(AVG(nvl(d.PSSI,0)), 2) AS PSSI
        FROM PTCMSDB.PTCD_TC_DETAIL_PROVIDE_1000 d
                 inner join gdgs.BASE_ROUTE_LOGIC l on d.ROUTE_CODE = l.ROUTE_CODE
                 inner join GDGS.PRO_ROUTE p on l.OPRT_ORG_CODE = p.OPRT_ORG_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON l.PRJ_ORG_CODE=ORG.ID
        WHERE d.YEAR = #{year} and org.PARENT_ID = #{orgCode}
    </select>

</mapper>
