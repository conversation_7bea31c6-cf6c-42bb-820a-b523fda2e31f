<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.PavementPlanImpMapper">

    <select id="getPavementPlanImp"
            resultType="com.hualu.highwaymaintenance.module.national.entity.PavementPlanImp">
        select *
        from MEMSDB.PAVEMENT_PLAN_IMP a
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="lineType != null and lineType != ''">
                and a.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <select id="getPavementPlanImpPage"
            resultType="com.hualu.highwaymaintenance.module.national.entity.PavementPlanImp">
        select a.*,b.total from MEMSDB.PAVEMENT_PLAN_IMP a
        left join (select NATIONAL_ID,count(1) as total from MEMSDB.NATIONAL_ANNEX
        where NATIONAL_SOURCE = 'LM_PLAN'
        group by NATIONAL_ID) b
        on a.id = b.NATIONAL_ID
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="lineType != null and lineType != ''">
                and a.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
        </where>
    </select>
</mapper>
