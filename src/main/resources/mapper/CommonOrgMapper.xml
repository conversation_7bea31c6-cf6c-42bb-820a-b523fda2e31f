<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.CommonOrgMapper">
  <select id="getSecondOrgForManageRoute" resultType="com.hualu.highwaymaintenance.module.user.domain.FwRightOrg">
    select distinct op.*
    from GDGS.BASE_ROUTE_LOGIC l
    inner join GDGS.FW_RIGHT_ORG o on o.ID = l.OPRT_ORG_CODE
    inner join GDGS.FW_RIGHT_ORG op on op.ID = o.PARENT_ID
    where l.ROUTE_CODE in
    (select f.ROUTE_CODE
    from GDGS.FW_RIGHT_DATA_PERMISSION f
    where f.OPRT_ORG_CODE in
    (select o.ID
    from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID))
    and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and op.ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
  </select>

  <select id="getThreeOrgForManageRoute" resultType="com.hualu.highwaymaintenance.module.user.domain.FwRightOrg">
    select distinct o.*
    from GDGS.BASE_ROUTE_LOGIC l
    inner join GDGS.FW_RIGHT_ORG o on o.ID = l.OPRT_ORG_CODE
    where l.ROUTE_CODE in
    (select f.ROUTE_CODE
    from GDGS.FW_RIGHT_DATA_PERMISSION f
    where f.OPRT_ORG_CODE in
    (select o.ID
    from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID))
    and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and o.PARENT_ID = #{secondOrgCode}
  </select>

  <select id="getRouteForThreeOrg" resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    select l.*
    from GDGS.BASE_ROUTE_LOGIC l
    where l.ROUTE_CODE in
          (select f.ROUTE_CODE
           from GDGS.FW_RIGHT_DATA_PERMISSION f
           where f.OPRT_ORG_CODE = #{threeOrgCode})
  </select>

  <select id="getThreeOrgForUserCode" resultType="com.hualu.highwaymaintenance.module.user.domain.FwRightOrg">
    with ORG AS (
    select o.*
    from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1 and o.PARENT_ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID
    )
    SELECT o.* FROM ORG o where exists(
        select 1 from GDGS.FW_RIGHT_DATA_PERMISSION p where p.OPRT_ORG_CODE = o.ID
    )
  </select>

  <select id="getManageLine" resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    with ORG AS (
    select o.*
    from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1 and o.PARENT_ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID
    )
    select distinct l.LINE_CODE,l.LINE_ID from GDGS.BASE_ROUTE_LOGIC l where exists(
    select 1 from ORG o inner join GDGS.FW_RIGHT_DATA_PERMISSION p on o.ID = p.OPRT_ORG_CODE
    where p.ROUTE_CODE = l.ROUTE_CODE
    <if test="orgCode != null and orgCode != ''">
      and o.ID = #{orgCode}
    </if>
    )
  </select>

  <select id="getManageRoute" resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    with ORG AS (
    select o.*
    from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1 and o.PARENT_ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID
    )
    select distinct l.* from GDGS.BASE_ROUTE_LOGIC l where exists(
    select 1 from ORG o inner join GDGS.FW_RIGHT_DATA_PERMISSION p on o.ID = p.OPRT_ORG_CODE
    where p.ROUTE_CODE = l.ROUTE_CODE
    )
  </select>

  <select id="getOrgIdFroUserCode" resultType="java.lang.String">
    select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode}
  </select>

  <select id="getRoute" resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    select * from GDGS.BASE_ROUTE_LOGIC l where l.ROUTE_CODE = #{routeCode} and ROWNUM = 1
  </select>

  <select id="getStakeRange" resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    with ORG AS (
    select o.*
    from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1 and o.PARENT_ID != '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID
    )
    select
    max(greatest(l.START_STAKE, l.END_STAKE)) endStake,
    min(least(l.START_STAKE, l.END_STAKE)) startStake,
    min((select line.LINE_SNAME from GDGS.BASE_LINE line where line.LINE_ID = l.LINE_ID and line.IS_ENABLE = 1 and line.IS_DELETED = 0 and ROWNUM = 1)) routeName
    from GDGS.BASE_ROUTE_LOGIC l
    where l.OPRT_ORG_CODE = #{orgId} and l.LINE_CODE = #{lineCode} and exists(
    select 1 from ORG o inner join GDGS.FW_RIGHT_DATA_PERMISSION p on o.ID = p.OPRT_ORG_CODE
    where p.ROUTE_CODE = l.ROUTE_CODE
    )
  </select>

  <select id="getBaseLineByLineCode" resultType="com.hualu.highwaymaintenance.module.baseline.domain.BaseLine">
    select * from GDGS.BASE_LINE l where l.LINE_CODE = #{lineCode} and l.IS_ENABLE = 1 and l.IS_DELETED = 0 and ROWNUM = 1
  </select>

  <select id="getUserName" resultType="java.lang.String">
    select u.USER_NAME from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode} and u.IS_ENABLE = 1 and u.IS_DELETED = 0 and ROWNUM = 1
  </select>
</mapper>