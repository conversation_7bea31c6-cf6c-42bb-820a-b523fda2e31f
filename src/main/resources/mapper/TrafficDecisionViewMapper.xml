<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.TrafficDecisionViewMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.TrafficDecisionView">
    <!--@mbg.generated-->
    <!--@Table MEMSDB.TRAFFIC_DECISION_VIEW-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="OPRT_ORG_CODE" jdbcType="VARCHAR" property="oprtOrgCode" />
    <result column="OPRT_ORG_NAME" jdbcType="VARCHAR" property="oprtOrgName" />
    <result column="PRJ_ORG_CODE" jdbcType="VARCHAR" property="prjOrgCode" />
    <result column="PRJ_ORG_NAME" jdbcType="VARCHAR" property="prjOrgName" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="YEAR" jdbcType="VARCHAR" property="year" />
    <result column="TOLL_STATION" jdbcType="VARCHAR" property="tollStation" />
    <result column="START_STAKE" jdbcType="VARCHAR" property="startStake" />
    <result column="END_STAKE" jdbcType="VARCHAR" property="endStake" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="CREATE_ORG_CODE" jdbcType="VARCHAR" property="createOrgCode" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="START_STAKE_NUM" jdbcType="DECIMAL" property="startStakeNum" />
    <result column="END_STAKE_NUM" jdbcType="DECIMAL" property="endStakeNum" />
    <result column="TYPE1" jdbcType="DECIMAL" property="type1" />
    <result column="TYPE2" jdbcType="DECIMAL" property="type2" />
    <result column="TYPE3" jdbcType="DECIMAL" property="type3" />
    <result column="TYPE4" jdbcType="DECIMAL" property="type4" />
    <result column="TYPE5" jdbcType="DECIMAL" property="type5" />
    <result column="TYPE6" jdbcType="DECIMAL" property="type6" />
    <result column="TYPE_OTHER" jdbcType="DECIMAL" property="typeOther" />
    <result column="TYPE_TOTAL" jdbcType="DECIMAL" property="typeTotal" />
    <result column="TYPE_TOTAL_DAY" jdbcType="DECIMAL" property="typeTotalDay" />
    <result column="AADT" jdbcType="DECIMAL" property="aadt" />
    <result column="TYPE_TOTAL_LAST" jdbcType="DECIMAL" property="typeTotalLast" />
    <result column="INCREASE" jdbcType="DECIMAL" property="increase" />
    <result column="INCREASE_RATE" jdbcType="VARCHAR" property="increaseRate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, OPRT_ORG_CODE, OPRT_ORG_NAME, PRJ_ORG_CODE, PRJ_ORG_NAME, LINE_CODE, "YEAR", 
    TOLL_STATION, START_STAKE, END_STAKE, LINE_DIRECT, CREATE_DATE, CREATE_ORG_CODE, 
    CREATE_USER_ID, START_STAKE_NUM, END_STAKE_NUM, TYPE1, TYPE2, TYPE3, TYPE4, TYPE5, 
    TYPE6, TYPE_OTHER, TYPE_TOTAL, TYPE_TOTAL_DAY, AADT, TYPE_TOTAL_LAST, INCREASE, INCREASE_RATE
  </sql>
</mapper>