<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.SupportFormQualifiedMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.SupportFormQualified">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.SUPPORT_FORM_QUALIFIED-->
    <result column="PRODUCER" jdbcType="VARCHAR" property="producer" />
    <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName" />
    <result column="SHEAR_RATE" jdbcType="DECIMAL" property="shearRate" />
    <result column="RESIST_RATE" jdbcType="DECIMAL" property="resistRate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PRODUCER, ROUTE_NAME, SHEAR_RATE, RESIST_RATE
  </sql>
</mapper>