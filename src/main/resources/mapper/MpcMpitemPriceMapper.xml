<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.mpItem.mapper.MpcMpitemPriceMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.mpItem.entity.MpcMpitemPrice">
            <id property="contrMpitemId" column="CONTR_MPITEM_ID" jdbcType="VARCHAR"/>
            <result property="contrId" column="CONTR_ID" jdbcType="VARCHAR"/>
            <result property="mpitemCode" column="MPITEM_CODE" jdbcType="VARCHAR"/>
            <result property="mpitemName" column="MPITEM_NAME" jdbcType="VARCHAR"/>
            <result property="unitPrice" column="UNIT_PRICE" jdbcType="DECIMAL"/>
            <result property="lumpPrice" column="LUMP_PRICE" jdbcType="DECIMAL"/>
            <result property="unitCount" column="UNIT_COUNT" jdbcType="DECIMAL"/>
            <result property="measureUnit" column="MEASURE_UNIT" jdbcType="VARCHAR"/>
            <result property="isLump" column="IS_LUMP" jdbcType="DECIMAL"/>
            <result property="rate" column="RATE" jdbcType="VARCHAR"/>
            <result property="mmpCode" column="MMP_CODE" jdbcType="VARCHAR"/>
            <result property="mpItemId" column="MP_ITEM_ID" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="preventPrice" column="PREVENT_PRICE" jdbcType="DECIMAL"/>
            <result property="effectPrice" column="EFFECT_PRICE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONTR_MPITEM_ID,CONTR_ID,MPITEM_CODE,
        MPITEM_NAME,UNIT_PRICE,LUMP_PRICE,
        UNIT_COUNT,MEASURE_UNIT,IS_LUMP,
        RATE,MMP_CODE,MP_ITEM_ID,
        REMARK,PREVENT_PRICE,EFFECT_PRICE
    </sql>
    <select id="queryContrAndPrice"
            resultType="com.hualu.highwaymaintenance.module.mpItem.entity.MpcMpitemPrice">
        select b.*
        from MEMSDB.MPC_CONTRACT a
                 inner join MEMSDB.MPC_MPITEM_PRICE b on a.CONTR_ID = b.CONTR_ID
        where a.ENABLED = '1' and b.MPITEM_CODE = 'Y1101-2-1-3' and MPITEM_NAME = '大件垃圾清捡'
          and a.MNT_ORG_ID = #{orgId}
    </select>
</mapper>
