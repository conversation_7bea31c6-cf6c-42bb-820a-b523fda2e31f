<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgTechindexMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgTechindex">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.T_BRDG_TECHINDEX-->
    <id column="TECHINDEX_ID" jdbcType="VARCHAR" property="techindexId" />
    <result column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="BRDG_LEN" jdbcType="DECIMAL" property="brdgLen" />
    <result column="BRDG_SPAN_KIND" jdbcType="DECIMAL" property="brdgSpanKind" />
    <result column="CARRY_NUM" jdbcType="VARCHAR" property="carryNum" />
    <result column="GUARDRAIL_WIDTH" jdbcType="DECIMAL" property="guardrailWidth" />
    <result column="SIDEWALK_WIDTH" jdbcType="DECIMAL" property="sidewalkWidth" />
    <result column="MEDIAN_WIDTH" jdbcType="DECIMAL" property="medianWidth" />
    <result column="CARRY_WIDTH" jdbcType="DECIMAL" property="carryWidth" />
    <result column="DECK_TOTAL_WIDTH" jdbcType="DECIMAL" property="deckTotalWidth" />
    <result column="NAVIGABLE_LVL" jdbcType="DECIMAL" property="navigableLvl" />
    <result column="DECK_ELEVA" jdbcType="DECIMAL" property="deckEleva" />
    <result column="BRDGUP_HIGH" jdbcType="DECIMAL" property="brdgupHigh" />
    <result column="BRDGDOWN_HIGH" jdbcType="DECIMAL" property="brdgdownHigh" />
    <result column="LDR_TOTAL_WIDTH" jdbcType="DECIMAL" property="ldrTotalWidth" />
    <result column="LDR_WIDTH" jdbcType="DECIMAL" property="ldrWidth" />
    <result column="LDR_LINEAR" jdbcType="DECIMAL" property="ldrLinear" />
    <result column="EXPNSN_JOINT_TYPE" jdbcType="VARCHAR" property="expnsnJointType" />
    <result column="BEAR_TYPE" jdbcType="VARCHAR" property="bearType" />
    <result column="EQDPAC" jdbcType="DECIMAL" property="eqdpac" />
    <result column="ABUTMENT_SLOPE" jdbcType="VARCHAR" property="abutmentSlope" />
    <result column="BODY_GUARD_PIER" jdbcType="VARCHAR" property="bodyGuardPier" />
    <result column="MODULATED_STRUCT" jdbcType="VARCHAR" property="modulatedStruct" />
    <result column="NORMAL_WATER_LVL" jdbcType="DECIMAL" property="normalWaterLvl" />
    <result column="DSGN_WATER_LVL" jdbcType="VARCHAR" property="dsgnWaterLvl" />
    <result column="HIS_FLOOD_LVL" jdbcType="DECIMAL" property="hisFloodLvl" />
    <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_CODE" jdbcType="VARCHAR" property="updateUserCode" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="MAX_SPAN" jdbcType="DECIMAL" property="maxSpan" />
    <result column="SPAN_LEN" jdbcType="DECIMAL" property="spanLen" />
    <result column="CROSS_NUM" jdbcType="DECIMAL" property="crossNum" />
    <result column="GUARDRAIL_HIGH" jdbcType="DECIMAL" property="guardrailHigh" />
    <result column="SEISMICLVL" jdbcType="DECIMAL" property="seismiclvl" />
    <result column="OVERPASS" jdbcType="DECIMAL" property="overpass" />
    <result column="ACRADIUS" jdbcType="DECIMAL" property="acradius" />
    <result column="MBG" jdbcType="VARCHAR" property="mbg" />
    <result column="SBG" jdbcType="VARCHAR" property="sbg" />
    <result column="SCBD" jdbcType="DECIMAL" property="scbd" />
    <result column="ACBD" jdbcType="DECIMAL" property="acbd" />
    <result column="ACUB" jdbcType="DECIMAL" property="acub" />
    <result column="TOP_TYPE" jdbcType="VARCHAR" property="topType" />
    <result column="PIER_TYPE" jdbcType="VARCHAR" property="pierType" />
    <result column="PIER_MTRL" jdbcType="VARCHAR" property="pierMtrl" />
    <result column="QT_TYPE" jdbcType="VARCHAR" property="qtType" />
    <result column="QT_MTRL" jdbcType="VARCHAR" property="qtMtrl" />
    <result column="BASE_TYPE" jdbcType="VARCHAR" property="baseType" />
    <result column="BASE_MTRL" jdbcType="VARCHAR" property="baseMtrl" />
    <result column="BRIDGE_FLAT_RADIUS" jdbcType="VARCHAR" property="bridgeFlatRadius" />
    <result column="DESIGN_FLOOD_FREQUENCY" jdbcType="VARCHAR" property="designFloodFrequency" />
    <result column="GSNUBE" jdbcType="DECIMAL" property="gsnube" />
    <result column="LDR_LINEAR_TEXT" jdbcType="VARCHAR" property="ldrLinearText" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TECHINDEX_ID, BRDGRECOG_ID, BRDG_LEN, BRDG_SPAN_KIND, CARRY_NUM, GUARDRAIL_WIDTH, 
    SIDEWALK_WIDTH, MEDIAN_WIDTH, CARRY_WIDTH, DECK_TOTAL_WIDTH, NAVIGABLE_LVL, DECK_ELEVA, 
    BRDGUP_HIGH, BRDGDOWN_HIGH, LDR_TOTAL_WIDTH, LDR_WIDTH, LDR_LINEAR, EXPNSN_JOINT_TYPE, 
    BEAR_TYPE, EQDPAC, ABUTMENT_SLOPE, BODY_GUARD_PIER, MODULATED_STRUCT, NORMAL_WATER_LVL, 
    DSGN_WATER_LVL, HIS_FLOOD_LVL, CREATE_USER_CODE, CREATE_TIME, UPDATE_USER_CODE, UPDATE_TIME, 
    REMARK, VALID_FLAG, MAX_SPAN, SPAN_LEN, CROSS_NUM, GUARDRAIL_HIGH, SEISMICLVL, OVERPASS, 
    ACRADIUS, MBG, SBG, SCBD, ACBD, ACUB, TOP_TYPE, PIER_TYPE, PIER_MTRL, QT_TYPE, QT_MTRL, 
    BASE_TYPE, BASE_MTRL, BRIDGE_FLAT_RADIUS, DESIGN_FLOOD_FREQUENCY, GSNUBE, LDR_LINEAR_TEXT
  </sql>
</mapper>