<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.BaseDatathirdDicMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.BaseDatathirdDic">
    <!--@mbg.generated-->
    <!--@Table GDGS.BASE_DATATHIRD_DIC-->
    <id column="DATATHIRD_DIC_ID" jdbcType="VARCHAR" property="datathirdDicId" />
    <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
    <result column="ATTRIBUTE_ITEM" jdbcType="VARCHAR" property="attributeItem" />
    <result column="ATTRIBUTE_DESC" jdbcType="VARCHAR" property="attributeDesc" />
    <result column="ATTRIBUTE_CODE" jdbcType="VARCHAR" property="attributeCode" />
    <result column="ATTRIBUTE_VALUE" jdbcType="VARCHAR" property="attributeValue" />
    <result column="ATTRIBUTE_ACTIVE" jdbcType="DECIMAL" property="attributeActive" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="SEQ_NO" jdbcType="DECIMAL" property="seqNo" />
    <result column="XQ_VALUE" jdbcType="VARCHAR" property="xqValue" />
    <result column="YR_CODE" jdbcType="VARCHAR" property="yrCode" />
    <result column="YR_VALUE" jdbcType="VARCHAR" property="yrValue" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DATATHIRD_DIC_ID, PARENT_ID, ATTRIBUTE_ITEM, ATTRIBUTE_DESC, ATTRIBUTE_CODE, ATTRIBUTE_VALUE, 
    ATTRIBUTE_ACTIVE, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, SEQ_NO, 
    XQ_VALUE, YR_CODE, YR_VALUE
  </sql>

  <select id="queryUnCheckSlopeItem" resultType="com.hualu.highwaymaintenance.module.bridge.entity.BaseDatathirdDic">
    select * from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM='SLOPE_CHECK'
                                              and not exists(select 1 from HSMSDB.DM_EMERGENCE_FINSP_RESULT r
                                                             where r.FINSP_ID=#{finspId}
                                                               and r.VALID_FLAG=1
                                                               and r.FINSP_ITEM_ID = d.ATTRIBUTE_CODE)
                                              and d.ATTRIBUTE_ACTIVE=0
    </select>
</mapper>