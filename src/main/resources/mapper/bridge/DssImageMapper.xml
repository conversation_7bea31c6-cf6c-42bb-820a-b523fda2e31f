<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.file.mapper.DssImageMapper">

  <select id="getDssImageList" resultType="com.hualu.highwaymaintenance.module.file.entity.DssImage">
    select *
    from MEMSDB.DSS_IMAGE i
    where exists(
      select 1
      from DSS_INFO d
      where d.REL_TASK_CODE = #{projectId}
        and FACILITY_CAT = 'QL'
        and d.DSS_ID = i.DSS_ID
    )
  </select>
</mapper>