<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.DssInfoMapper">

  <select id="getDssInfo" resultType="com.hualu.highwaymaintenance.module.bridge.entity.DssInfo">
    select
      (select br.BRDG_NAME from T_BRDG_BRDGRECOG br where br.BRDGRECOG_ID = d.STRUCT_ID) bridgeName,
      nvl((select ps.PARTSTYPE_NAME from T_BRDG_PARTSTYPE ps where ps.PARTSTYPE_ID = d.STRUCT_PART_ID and ROWNUM = 1),d.DSS_PART_NAME) partName,
      d.STRUCT_COMP_ID componentId,
      nvl((select p.POST_NAME from T_BRDG_PARTPOST p where p.PARTPOST_D = d.DSS_POSITION and ROWNUM = 1), d.DSS_POSITION_NAME) postName,
      (select dt.DSS_TYPE_NAME from MEMSDB.DSS_TYPE_NEW dt where dt.DSS_TYPE = d.DSS_TYPE and ROWNUM = 1) dssTypeName,
      decode(d.REPAIR_STATUS, 0, '待修复', 1, '修复中', 2, '已修复', 3, '历史修复', 4, '新增修复') repairStatusName,
      d.*
    from DSS_INFO d
    where d.REL_TASK_CODE = #{projectId} and FACILITY_CAT = 'QL'
    order by d.STRUCT_ID, d.STRUCT_PART_ID
  </select>

</mapper>