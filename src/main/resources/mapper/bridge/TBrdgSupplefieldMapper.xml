<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgSupplefieldMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgSupplefield">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.T_BRDG_SUPPLEFIELD-->
    <id column="SUPPLEFIELD_ID" jdbcType="VARCHAR" property="supplefieldId" />
    <result column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="START_STAKE" jdbcType="VARCHAR" property="startStake" />
    <result column="END_STAKE" jdbcType="VARCHAR" property="endStake" />
    <result column="CHRG_NATURE" jdbcType="DECIMAL" property="chrgNature" />
    <result column="PRJ_ORG_CODE" jdbcType="VARCHAR" property="prjOrgCode" />
    <result column="DSGN_LEADER" jdbcType="VARCHAR" property="dsgnLeader" />
    <result column="CNSTRCT_LEADER" jdbcType="VARCHAR" property="cnstrctLeader" />
    <result column="SUPER_LEADER" jdbcType="VARCHAR" property="superLeader" />
    <result column="DSGN_BASIS_DATE" jdbcType="TIMESTAMP" property="dsgnBasisDate" />
    <result column="ATTACH_PIPE_FLAG" jdbcType="DECIMAL" property="attachPipeFlag" />
    <result column="BRDG_HEIGHT_LIMIT" jdbcType="DECIMAL" property="brdgHeightLimit" />
    <result column="DECK_WIDTH" jdbcType="DECIMAL" property="deckWidth" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="COMMUT_DATE" jdbcType="TIMESTAMP" property="commutDate" />
    <result column="TOWN_NAME" jdbcType="VARCHAR" property="townName" />
    <result column="USE_AGE" jdbcType="DECIMAL" property="useAge" />
    <result column="TECH_GRADE" jdbcType="DECIMAL" property="techGrade" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SUPPLEFIELD_ID, BRDGRECOG_ID, START_STAKE, END_STAKE, CHRG_NATURE, PRJ_ORG_CODE, 
    DSGN_LEADER, CNSTRCT_LEADER, SUPER_LEADER, DSGN_BASIS_DATE, ATTACH_PIPE_FLAG, BRDG_HEIGHT_LIMIT, 
    DECK_WIDTH, VALID_FLAG, COMMUT_DATE, TOWN_NAME, USE_AGE, TECH_GRADE, ROUTE_CODE
  </sql>
</mapper>