<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgToptypeMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgToptype">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.T_BRDG_TOPTYPE-->
    <id column="TOPTYPE_ID" jdbcType="VARCHAR" property="toptypeId" />
    <result column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="BRDG_TYPE" jdbcType="VARCHAR" property="brdgType" />
    <result column="SHAPE_NAME" jdbcType="VARCHAR" property="shapeName" />
    <result column="SHAPE_CODE" jdbcType="VARCHAR" property="shapeCode" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="BUSS_TYPE" jdbcType="DECIMAL" property="bussType" />
    <result column="JUMP_PAEAM" jdbcType="VARCHAR" property="jumpPaeam" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TOPTYPE_ID, BRDGRECOG_ID, BRDG_TYPE, SHAPE_NAME, SHAPE_CODE, VALID_FLAG, REMARK, 
    BUSS_TYPE, JUMP_PAEAM
  </sql>
</mapper>