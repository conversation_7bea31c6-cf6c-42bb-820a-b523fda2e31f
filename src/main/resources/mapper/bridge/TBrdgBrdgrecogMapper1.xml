<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgBrdgrecogMapper1">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgBrdgrecog">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.T_BRDG_BRDGRECOG-->
    <id column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="ROAD_NUM" jdbcType="VARCHAR" property="roadNum" />
    <result column="ROAD_NAME" jdbcType="VARCHAR" property="roadName" />
    <result column="ROUTE_LVL" jdbcType="DECIMAL" property="routeLvl" />
    <result column="SEQ_NO" jdbcType="DECIMAL" property="seqNo" />
    <result column="OLD_HIGHWAY_STAKE" jdbcType="VARCHAR" property="oldHighwayStake" />
    <result column="FUNC_TYPE" jdbcType="DECIMAL" property="funcType" />
    <result column="CNSTRCT_STAKE" jdbcType="VARCHAR" property="cnstrctStake" />
    <result column="DESIGN_STAKE" jdbcType="VARCHAR" property="designStake" />
    <result column="BRDG_NUM" jdbcType="DECIMAL" property="brdgNum" />
    <result column="BRDG_NATURE" jdbcType="VARCHAR" property="brdgNature" />
    <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude" />
    <result column="ACROSS_FTR_TYPE" jdbcType="VARCHAR" property="acrossFtrType" />
    <result column="BRDG_SPAN_GROUP" jdbcType="VARCHAR" property="brdgSpanGroup" />
    <result column="BRDG_STATUS" jdbcType="VARCHAR" property="brdgStatus" />
    <result column="LATITUDE" jdbcType="DECIMAL" property="latitude" />
    <result column="ACROSS_FTR_NAME" jdbcType="VARCHAR" property="acrossFtrName" />
    <result column="PASSAGE_NAME" jdbcType="VARCHAR" property="passageName" />
    <result column="PASSAGE_NUM" jdbcType="VARCHAR" property="passageNum" />
    <result column="DSGN_LOAD" jdbcType="VARCHAR" property="dsgnLoad" />
    <result column="TRAFFIC_LOAD" jdbcType="VARCHAR" property="trafficLoad" />
    <result column="BEND_SLOPE" jdbcType="VARCHAR" property="bendSlope" />
    <result column="BRDG_DECK" jdbcType="VARCHAR" property="brdgDeck" />
    <result column="SEC_ROUTE" jdbcType="VARCHAR" property="secRoute" />
    <result column="COMP_TIME" jdbcType="TIMESTAMP" property="compTime" />
    <result column="DSGN_ORG" jdbcType="VARCHAR" property="dsgnOrg" />
    <result column="CNSTRCT_ORG" jdbcType="VARCHAR" property="cnstrctOrg" />
    <result column="SUPER_ORG" jdbcType="VARCHAR" property="superOrg" />
    <result column="CBMS_ORG" jdbcType="VARCHAR" property="cbmsOrg" />
    <result column="REGULATORY_ORG" jdbcType="VARCHAR" property="regulatoryOrg" />
    <result column="BRDG_RATING" jdbcType="DECIMAL" property="brdgRating" />
    <result column="APPR_TIME" jdbcType="TIMESTAMP" property="apprTime" />
    <result column="RATING_ID" jdbcType="VARCHAR" property="ratingId" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="RP_INTRVL_ID" jdbcType="VARCHAR" property="rpIntrvlId" />
    <result column="BRDG_CODE" jdbcType="VARCHAR" property="brdgCode" />
    <result column="BRDG_NAME" jdbcType="VARCHAR" property="brdgName" />
    <result column="CNTR_STAKE" jdbcType="VARCHAR" property="cntrStake" />
    <result column="CNTR_STAKE_NUM" jdbcType="DECIMAL" property="cntrStakeNum" />
    <result column="CNTR_SENSSION_NUM" jdbcType="DECIMAL" property="cntrSenssionNum" />
    <result column="OPRT_ORG_CODE" jdbcType="VARCHAR" property="oprtOrgCode" />
    <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_CODE" jdbcType="VARCHAR" property="updateUserCode" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
    <result column="PLACE" jdbcType="VARCHAR" property="place" />
    <result column="BRDG_LINE_TYPE" jdbcType="VARCHAR" property="brdgLineType" />
    <result column="FRAME_NUM" jdbcType="VARCHAR" property="frameNum" />
    <result column="ROAD_TYPE" jdbcType="DECIMAL" property="roadType" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="TRAFFIC_CODE" jdbcType="VARCHAR" property="trafficCode" />
    <result column="BOXROOM_TYPE" jdbcType="VARCHAR" property="boxroomType" />
    <result column="BOXROOM_TYPE_VALUE" jdbcType="VARCHAR" property="boxroomTypeValue" />
    <result column="MAIN_RAMP_LINE_ID" jdbcType="VARCHAR" property="mainRampLineId" />
    <result column="START_OFFSET" jdbcType="DECIMAL" property="startOffset" />
    <result column="RAMP_STAKE" jdbcType="VARCHAR" property="rampStake" />
    <result column="EXCEL_RESULT" jdbcType="DECIMAL" property="excelResult" />
    <result column="START_STAKE" jdbcType="DECIMAL" property="startStake" />
    <result column="END_STAKE" jdbcType="DECIMAL" property="endStake" />
    <result column="LAST_PRJ_ID" jdbcType="VARCHAR" property="lastPrjId" />
    <result column="IN_CATALOGUE" jdbcType="DECIMAL" property="inCatalogue" />
    <result column="LOGIC_CNTR_STAKE" jdbcType="VARCHAR" property="logicCntrStake" />
    <result column="LOGIC_CNTR_STAKE_NUM" jdbcType="DECIMAL" property="logicCntrStakeNum" />
    <result column="OPRT_ORG_NAME" jdbcType="VARCHAR" property="oprtOrgName" />
    <result column="MAIN_ID" jdbcType="VARCHAR" property="mainId" />
    <result column="MAIN_NAME" jdbcType="VARCHAR" property="mainName" />
    <result column="JD" jdbcType="DECIMAL" property="jd" />
    <result column="WD" jdbcType="DECIMAL" property="wd" />
    <result column="SSF_SET" jdbcType="VARCHAR" property="ssfSet" />
    <result column="NEW_ID" jdbcType="VARCHAR" property="newId" />
    <result column="YHID" jdbcType="VARCHAR" property="yhid" />
    <result column="GRADE_2020" jdbcType="DECIMAL" property="grade2020" />
    <result column="GRADE_2019" jdbcType="DECIMAL" property="grade2019" />
    <result column="GRADE_2018" jdbcType="DECIMAL" property="grade2018" />
    <result column="GRADE_2017" jdbcType="DECIMAL" property="grade2017" />
    <result column="GRADE_2016" jdbcType="DECIMAL" property="grade2016" />
    <result column="BRDG_TYPE" jdbcType="VARCHAR" property="brdgType" />
    <result column="PRJ_CODE" jdbcType="VARCHAR" property="prjCode" />
    <result column="PRJ_NAME" jdbcType="VARCHAR" property="prjName" />
    <result column="OLD_BRDGCODE" jdbcType="VARCHAR" property="oldBrdgcode" />
    <result column="KUANLZQ" jdbcType="DECIMAL" property="kuanlzq" />
    <result column="SFXZDM" jdbcType="DECIMAL" property="sfxzdm" />
    <result column="F039" jdbcType="DECIMAL" property="f039" />
    <result column="GJXZDM" jdbcType="DECIMAL" property="gjxzdm" />
    <result column="F042" jdbcType="DECIMAL" property="f042" />
    <result column="BZXM" jdbcType="DECIMAL" property="bzxm" />
    <result column="GCXZDM" jdbcType="DECIMAL" property="gcxzdm" />
    <result column="F099" jdbcType="VARCHAR" property="f099" />
    <result column="F103" jdbcType="DECIMAL" property="f103" />
    <result column="F107" jdbcType="DECIMAL" property="f107" />
    <result column="F109" jdbcType="DECIMAL" property="f109" />
    <result column="F105" jdbcType="DECIMAL" property="f105" />
    <result column="HAS_THREE_PART" jdbcType="DECIMAL" property="hasThreePart" />
    <result column="LAST_PRJ_DATE" jdbcType="TIMESTAMP" property="lastPrjDate" />
    <result column="WSG" jdbcType="DECIMAL" property="wsg" />
    <result column="TRANS_TIME" jdbcType="TIMESTAMP" property="transTime" />
    <result column="TRANS_REMARK" jdbcType="VARCHAR" property="transRemark" />
    <result column="TRANS_FILE" jdbcType="VARCHAR" property="transFile" />
    <result column="LAST_DISP_COMP_DATE" jdbcType="TIMESTAMP" property="lastDispCompDate" />
    <result column="LAST_DISP_ID" jdbcType="VARCHAR" property="lastDispId" />
    <result column="ROUTE_CODE_VERSION" jdbcType="VARCHAR" property="routeCodeVersion" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
    <result column="BRDG_CODE_BAK" jdbcType="VARCHAR" property="brdgCodeBak" />
    <result column="PHYSICS_CNTR_STAKE_NUM_NEW" jdbcType="DECIMAL" property="physicsCntrStakeNumNew" />
    <result column="WIDEN_BRIDGE" jdbcType="DECIMAL" property="widenBridge" />
    <result column="RADIUS" jdbcType="DECIMAL" property="radius" />
    <result column="HAS_HLA" jdbcType="DECIMAL" property="hasHla" />
    <result column="HAS_SCP" jdbcType="DECIMAL" property="hasScp" />
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName" />
    <result column="HLA_DESC" jdbcType="VARCHAR" property="hlaDesc" />
    <result column="JGJC" jdbcType="DECIMAL" property="jgjc" />
    <result column="CZJC" jdbcType="DECIMAL" property="czjc" />
    <result column="HZJC" jdbcType="DECIMAL" property="hzjc" />
    <result column="UNDERLOADWARN" jdbcType="DECIMAL" property="underloadwarn" />
    <result column="HUMIDITYWARN" jdbcType="DECIMAL" property="humiditywarn" />
    <result column="STRAINWARN" jdbcType="DECIMAL" property="strainwarn" />
    <result column="ACCELERATIONWARN" jdbcType="DECIMAL" property="accelerationwarn" />
    <result column="ROUTE_CODE_BAK" jdbcType="VARCHAR" property="routeCodeBak" />
    <result column="DEL_TIME" jdbcType="TIMESTAMP" property="delTime" />
    <result column="DEL_PERSON" jdbcType="VARCHAR" property="delPerson" />
    <result column="RECOVER_TIME" jdbcType="TIMESTAMP" property="recoverTime" />
    <result column="RECOVER_PERSON" jdbcType="VARCHAR" property="recoverPerson" />
    <result column="XQ_CODE" jdbcType="VARCHAR" property="xqCode" />
    <result column="BRDG_CODE_20210911" jdbcType="VARCHAR" property="brdgCode20210911" />
    <result column="DRIVING_DIRECTION" jdbcType="VARCHAR" property="drivingDirection" />
    <result column="BRDG_CODE_20210914" jdbcType="VARCHAR" property="brdgCode20210914" />
    <result column="BRDG_CODE_20210915" jdbcType="VARCHAR" property="brdgCode20210915" />
    <result column="BW" jdbcType="DECIMAL" property="bw" />
    <result column="JC_CODE" jdbcType="VARCHAR" property="jcCode" />
    <result column="REPORT_ROAD_NUM" jdbcType="VARCHAR" property="reportRoadNum" />
    <result column="LATITUE_BAK" jdbcType="DECIMAL" property="latitueBak" />
    <result column="LONGTITUE_BAK" jdbcType="DECIMAL" property="longtitueBak" />
    <result column="TRANS_PROVINCIAL" jdbcType="DECIMAL" property="transProvincial" />
    <result column="PLACE_BAK" jdbcType="VARCHAR" property="placeBak" />
    <result column="OLD_YHID" jdbcType="VARCHAR" property="oldYhid" />
    <result column="DATA_INTEGRITY" jdbcType="DECIMAL" property="dataIntegrity" />
    <result column="NEVER_DJ" jdbcType="DECIMAL" property="neverDj" />
    <result column="NEVER_DJ_3" jdbcType="DECIMAL" property="neverDj3" />
    <result column="TRAFFIC_CODE_BAK" jdbcType="VARCHAR" property="trafficCodeBak" />
    <result column="TRAFFIC_CODE_BAK_20220120" jdbcType="VARCHAR" property="trafficCodeBak20220120" />
    <result column="TRAFFIC_CODE_BAK_20220221" jdbcType="VARCHAR" property="trafficCodeBak20220221" />
    <result column="MORTARRUBBLE" jdbcType="DECIMAL" property="mortarrubble" />
    <result column="REINFORCEMENTFORM" jdbcType="VARCHAR" property="reinforcementform" />
    <result column="REINFORCEMENTFORMEXTRA" jdbcType="VARCHAR" property="reinforcementformextra" />
    <result column="LGHL_MTRL" jdbcType="DECIMAL" property="lghlMtrl" />
    <result column="MAINTAIN_GRADE" jdbcType="DECIMAL" property="maintainGrade" />
    <result column="XIAOYU40" jdbcType="DECIMAL" property="xiaoyu40" />
    <result column="XIAOYU100" jdbcType="DECIMAL" property="xiaoyu100" />
    <result column="DAYUDENGYU100" jdbcType="DECIMAL" property="dayudengyu100" />
    <result column="HASAV" jdbcType="VARCHAR" property="hasav" />
    <result column="BSE" jdbcType="VARCHAR" property="bse" />
    <result column="INSPECT_COMPANY" jdbcType="VARCHAR" property="inspectCompany" />
    <result column="IS_MAIN" jdbcType="DECIMAL" property="isMain" />
    <result column="OLD_ROADBM" jdbcType="VARCHAR" property="oldRoadbm" />
    <result column="JWDGX" jdbcType="DECIMAL" property="jwdgx" />
    <result column="JWDGXSJ" jdbcType="VARCHAR" property="jwdgxsj" />
    <result column="JD_GIS" jdbcType="DECIMAL" property="jdGis" />
    <result column="WD_GIS" jdbcType="DECIMAL" property="wdGis" />
    <result column="JD_STAKE" jdbcType="DECIMAL" property="jdStake" />
    <result column="WD_STAKE" jdbcType="DECIMAL" property="wdStake" />
    <result column="DIS_XT" jdbcType="DECIMAL" property="disXt" />
    <result column="DIS_STAKE" jdbcType="DECIMAL" property="disStake" />
    <result column="XQ_AREA_CODE" jdbcType="VARCHAR" property="xqAreaCode" />
    <result column="OLD_ROADBM_20221128" jdbcType="VARCHAR" property="oldRoadbm20221128" />
    <result column="TRAFFIC_CODE_20221213" jdbcType="VARCHAR" property="trafficCode20221213" />
    <result column="TRAFFIC_CODE_2023523" jdbcType="VARCHAR" property="trafficCode2023523" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRDGRECOG_ID, ROAD_NUM, ROAD_NAME, ROUTE_LVL, SEQ_NO, OLD_HIGHWAY_STAKE, FUNC_TYPE, 
    CNSTRCT_STAKE, DESIGN_STAKE, BRDG_NUM, BRDG_NATURE, LONGITUDE, ACROSS_FTR_TYPE, BRDG_SPAN_GROUP, 
    BRDG_STATUS, LATITUDE, ACROSS_FTR_NAME, PASSAGE_NAME, PASSAGE_NUM, DSGN_LOAD, TRAFFIC_LOAD, 
    BEND_SLOPE, BRDG_DECK, SEC_ROUTE, COMP_TIME, DSGN_ORG, CNSTRCT_ORG, SUPER_ORG, CBMS_ORG, 
    REGULATORY_ORG, BRDG_RATING, APPR_TIME, RATING_ID, VALID_FLAG, RP_INTRVL_ID, BRDG_CODE, 
    BRDG_NAME, CNTR_STAKE, CNTR_STAKE_NUM, CNTR_SENSSION_NUM, OPRT_ORG_CODE, CREATE_USER_CODE, 
    CREATE_TIME, UPDATE_USER_CODE, UPDATE_TIME, LINE_ID, PLACE, BRDG_LINE_TYPE, FRAME_NUM, 
    ROAD_TYPE, REMARK, TRAFFIC_CODE, BOXROOM_TYPE, BOXROOM_TYPE_VALUE, MAIN_RAMP_LINE_ID, 
    START_OFFSET, RAMP_STAKE, EXCEL_RESULT, START_STAKE, END_STAKE, LAST_PRJ_ID, IN_CATALOGUE, 
    LOGIC_CNTR_STAKE, LOGIC_CNTR_STAKE_NUM, OPRT_ORG_NAME, MAIN_ID, MAIN_NAME, JD, WD, 
    SSF_SET, NEW_ID, YHID, GRADE_2020, GRADE_2019, GRADE_2018, GRADE_2017, GRADE_2016, 
    BRDG_TYPE, PRJ_CODE, PRJ_NAME, OLD_BRDGCODE, KUANLZQ, SFXZDM, F039, GJXZDM, F042, 
    BZXM, GCXZDM, F099, F103, F107, F109, F105, HAS_THREE_PART, LAST_PRJ_DATE, WSG, TRANS_TIME, 
    TRANS_REMARK, TRANS_FILE, LAST_DISP_COMP_DATE, LAST_DISP_ID, ROUTE_CODE_VERSION, 
    ROUTE_CODE, BRDG_CODE_BAK, PHYSICS_CNTR_STAKE_NUM_NEW, WIDEN_BRIDGE, RADIUS, HAS_HLA, 
    HAS_SCP, ID, ROUTE_NAME, HLA_DESC, JGJC, CZJC, HZJC, UNDERLOADWARN, HUMIDITYWARN, 
    STRAINWARN, ACCELERATIONWARN, ROUTE_CODE_BAK, DEL_TIME, DEL_PERSON, RECOVER_TIME, 
    RECOVER_PERSON, XQ_CODE, BRDG_CODE_20210911, DRIVING_DIRECTION, BRDG_CODE_20210914, 
    BRDG_CODE_20210915, BW, JC_CODE, REPORT_ROAD_NUM, LATITUE_BAK, LONGTITUE_BAK, TRANS_PROVINCIAL, 
    PLACE_BAK, OLD_YHID, DATA_INTEGRITY, NEVER_DJ, NEVER_DJ_3, TRAFFIC_CODE_BAK, TRAFFIC_CODE_BAK_20220120, 
    TRAFFIC_CODE_BAK_20220221, MORTARRUBBLE, REINFORCEMENTFORM, REINFORCEMENTFORMEXTRA, 
    LGHL_MTRL, MAINTAIN_GRADE, XIAOYU40, XIAOYU100, DAYUDENGYU100, HASAV, BSE, INSPECT_COMPANY, 
    IS_MAIN, OLD_ROADBM, JWDGX, JWDGXSJ, JD_GIS, WD_GIS, JD_STAKE, WD_STAKE, DIS_XT, 
    DIS_STAKE, XQ_AREA_CODE, OLD_ROADBM_20221128, TRAFFIC_CODE_20221213, TRAFFIC_CODE_2023523
  </sql>
</mapper>