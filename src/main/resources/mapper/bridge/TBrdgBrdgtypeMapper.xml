<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgBrdgtypeMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgBrdgtype">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.T_BRDG_BRDGTYPE-->
    <id column="BRDGTYPE_ID" jdbcType="VARCHAR" property="brdgtypeId" />
    <result column="BRDG_TYPE_NAME" jdbcType="VARCHAR" property="brdgTypeName" />
    <result column="BRDG_CODE" jdbcType="VARCHAR" property="brdgCode" />
    <result column="P_BRDGTYPE_ID" jdbcType="VARCHAR" property="pBrdgtypeId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode" />
    <result column="FLAG" jdbcType="DECIMAL" property="flag" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="XQ_BRDG_TYPE_NAME" jdbcType="VARCHAR" property="xqBrdgTypeName" />
    <result column="YR_CODE" jdbcType="VARCHAR" property="yrCode" />
    <result column="YEAR_VALUE" jdbcType="VARCHAR" property="yearValue" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRDGTYPE_ID, BRDG_TYPE_NAME, BRDG_CODE, P_BRDGTYPE_ID, CREATE_TIME, CREATE_USER_CODE, 
    FLAG, REMARK, VALID_FLAG, XQ_BRDG_TYPE_NAME, YR_CODE, YEAR_VALUE
  </sql>
</mapper>