<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintainbase.mapper.ExpresswayMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.maintainbase.domain.Expressway">
    <!--@mbg.generated-->
    <!--@Table EXPRESSWAY-->
    <result column="OBJECTID" jdbcType="DECIMAL" property="objectid" />
    <result column="RP_INTRVL_ID" jdbcType="VARCHAR" property="rpIntrvlId" />
    <result column="ROADNAME" jdbcType="VARCHAR" property="roadname" />
    <result column="DIRECTION" jdbcType="DECIMAL" property="direction" />
    <result column="START_STAKE_NUM" jdbcType="DECIMAL" property="startStakeNum" />
    <result column="END_STAKE_NUM" jdbcType="DECIMAL" property="endStakeNum" />
    <result column="MAINLINE_ID" jdbcType="VARCHAR" property="mainlineId" />
    <result column="OPRT_ORG_CODE" jdbcType="VARCHAR" property="oprtOrgCode" />
    <result column="PRJ_ORG_CODE" jdbcType="VARCHAR" property="prjOrgCode" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="START_SENSSION_NUM" jdbcType="DECIMAL" property="startSenssionNum" />
    <result column="END_SENSSION_NUM" jdbcType="DECIMAL" property="endSenssionNum" />
    <result column="START_PLACE" jdbcType="VARCHAR" property="startPlace" />
    <result column="END_PLACE" jdbcType="VARCHAR" property="endPlace" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="UPDA_USER_ID" jdbcType="VARCHAR" property="updaUserId" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
    <result column="GIS_ID" jdbcType="VARCHAR" property="gisId" />
    <result column="ROUTENO" jdbcType="VARCHAR" property="routeno" />
    <result column="LENGTH" jdbcType="DECIMAL" property="length" />
    <result column="RAMPTYPE" jdbcType="VARCHAR" property="ramptype" />
    <result column="E_QUERY_RPID" jdbcType="VARCHAR" property="eQueryRpid" />
    <result column="LINENO" jdbcType="DECIMAL" property="lineno" />
    <result column="ROADCD" jdbcType="VARCHAR" property="roadcd" />
    <result column="SX" jdbcType="DECIMAL" property="sx" />
    <result column="SY" jdbcType="DECIMAL" property="sy" />
    <result column="EX" jdbcType="DECIMAL" property="ex" />
    <result column="EY" jdbcType="DECIMAL" property="ey" />
    <result column="DSTRCT_CODE" jdbcType="VARCHAR" property="dstrctCode" />
    <result column="PRO_DISC_IDEN" jdbcType="VARCHAR" property="proDiscIden" />
    <result column="CHA_NATURE" jdbcType="VARCHAR" property="chaNature" />
    <result column="REPEAT_ROAD_LINE_TYPE" jdbcType="VARCHAR" property="repeatRoadLineType" />
    <result column="RAMP_NAME" jdbcType="VARCHAR" property="rampName" />
    <result column="RAMP_TOW_CLASS" jdbcType="VARCHAR" property="rampTowClass" />
    <result column="ROAD_NUM" jdbcType="VARCHAR" property="roadNum" />
    <result column="COLUMN_IS" jdbcType="VARCHAR" property="columnIs" />
    <result column="COLUMN_RAISE_SOURCE" jdbcType="VARCHAR" property="columnRaiseSource" />
    <result column="CURING_DATE" jdbcType="VARCHAR" property="curingDate" />
    <result column="START_DEMAR_CLASS" jdbcType="VARCHAR" property="startDemarClass" />
    <result column="END_DEMAR_CLASS" jdbcType="VARCHAR" property="endDemarClass" />
    <result column="RAMP_CODE" jdbcType="VARCHAR" property="rampCode" />
    <result column="ROAD_NATURE" jdbcType="VARCHAR" property="roadNature" />
    <result column="BUILT_DATE" jdbcType="VARCHAR" property="builtDate" />
    <result column="REBUIT_DATE" jdbcType="VARCHAR" property="rebuitDate" />
    <result column="GREEN_MILE" jdbcType="VARCHAR" property="greenMile" />
    <result column="REPEAT_ROAD_CODE" jdbcType="VARCHAR" property="repeatRoadCode" />
    <result column="REPEAT_ROAD_NUM" jdbcType="VARCHAR" property="repeatRoadNum" />
    <result column="SURFACE_THICK" jdbcType="DECIMAL" property="surfaceThick" />
    <result column="LAST_REPAIR_DATE" jdbcType="VARCHAR" property="lastRepairDate" />
    <result column="BUILT_NATURE" jdbcType="VARCHAR" property="builtNature" />
    <result column="GBMWMYBL" jdbcType="VARCHAR" property="gbmwmybl" />
    <result column="LANDFROM" jdbcType="VARCHAR" property="landfrom" />
    <result column="OWNED_TOLL" jdbcType="VARCHAR" property="ownedToll" />
    <result column="CULVERT_NUM" jdbcType="DECIMAL" property="culvertNum" />
    <result column="TECHNICAL_GRADE" jdbcType="VARCHAR" property="technicalGrade" />
    <result column="SURFACE_TYPE" jdbcType="VARCHAR" property="surfaceType" />
    <result column="ROAD_WIDTH" jdbcType="DECIMAL" property="roadWidth" />
    <result column="ROADBED_WIDTH" jdbcType="VARCHAR" property="roadbedWidth" />
    <result column="LANE_FEATURES" jdbcType="VARCHAR" property="laneFeatures" />
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="COMMUT_DATE" jdbcType="DECIMAL" property="commutDate" />
    <result column="EXCHANGE" jdbcType="VARCHAR" property="exchange" />
    <result column="DESIGN_SPEED" jdbcType="DECIMAL" property="designSpeed" />
    <result column="EVENTID" jdbcType="VARCHAR" property="eventid" />
    <result column="F12" jdbcType="VARCHAR" property="f12" />
    <result column="IS_JITUAN" jdbcType="VARCHAR" property="isJituan" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="LDLX" jdbcType="VARCHAR" property="ldlx" />
    <result column="STARTZH" jdbcType="DECIMAL" property="startzh" />
    <result column="ENDZH" jdbcType="DECIMAL" property="endzh" />
    <result column="ROADCODE" jdbcType="VARCHAR" property="roadcode" />
    <result column="FIRSTEVENTID" jdbcType="VARCHAR" property="firsteventid" />
    <result column="ENDEVENTID" jdbcType="VARCHAR" property="endeventid" />
    <result column="LINK_MAINLINE" jdbcType="DECIMAL" property="linkMainline" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
    <result column="SHAPE" jdbcType="OTHER" property="shape" />
    <result column="ORG_FULLNAME" jdbcType="VARCHAR" property="orgFullname" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    OBJECTID, RP_INTRVL_ID, ROADNAME, DIRECTION, START_STAKE_NUM, END_STAKE_NUM, MAINLINE_ID, 
    OPRT_ORG_CODE, PRJ_ORG_CODE, LINE_DIRECT, START_SENSSION_NUM, END_SENSSION_NUM, START_PLACE, 
    END_PLACE, CREATE_USER_ID, UPDA_USER_ID, IS_ENABLE, IS_DELETED, GIS_ID, ROUTENO, 
    "LENGTH", RAMPTYPE, E_QUERY_RPID, "LINENO", ROADCD, SX, SY, EX, EY, DSTRCT_CODE, 
    PRO_DISC_IDEN, CHA_NATURE, REPEAT_ROAD_LINE_TYPE, RAMP_NAME, RAMP_TOW_CLASS, ROAD_NUM, 
    COLUMN_IS, COLUMN_RAISE_SOURCE, CURING_DATE, START_DEMAR_CLASS, END_DEMAR_CLASS, 
    RAMP_CODE, ROAD_NATURE, BUILT_DATE, REBUIT_DATE, GREEN_MILE, REPEAT_ROAD_CODE, REPEAT_ROAD_NUM, 
    SURFACE_THICK, LAST_REPAIR_DATE, BUILT_NATURE, GBMWMYBL, LANDFROM, OWNED_TOLL, CULVERT_NUM, 
    TECHNICAL_GRADE, SURFACE_TYPE, ROAD_WIDTH, ROADBED_WIDTH, LANE_FEATURES, ID, COMMUT_DATE, 
    EXCHANGE, DESIGN_SPEED, EVENTID, F12, IS_JITUAN, REMARK, LDLX, STARTZH, ENDZH, ROADCODE, 
    FIRSTEVENTID, ENDEVENTID, LINK_MAINLINE, ROUTE_CODE, SHAPE, ORG_FULLNAME
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.Expressway">
    <!--@mbg.generated-->
    insert into EXPRESSWAY (OBJECTID, RP_INTRVL_ID, ROADNAME, 
      DIRECTION, START_STAKE_NUM, END_STAKE_NUM, 
      MAINLINE_ID, OPRT_ORG_CODE, PRJ_ORG_CODE, 
      LINE_DIRECT, START_SENSSION_NUM, END_SENSSION_NUM, 
      START_PLACE, END_PLACE, CREATE_USER_ID, 
      UPDA_USER_ID, IS_ENABLE, IS_DELETED, 
      GIS_ID, ROUTENO, "LENGTH", 
      RAMPTYPE, E_QUERY_RPID, "LINENO", 
      ROADCD, SX, SY, EX, 
      EY, DSTRCT_CODE, PRO_DISC_IDEN, 
      CHA_NATURE, REPEAT_ROAD_LINE_TYPE, RAMP_NAME, 
      RAMP_TOW_CLASS, ROAD_NUM, COLUMN_IS, 
      COLUMN_RAISE_SOURCE, CURING_DATE, START_DEMAR_CLASS, 
      END_DEMAR_CLASS, RAMP_CODE, ROAD_NATURE, 
      BUILT_DATE, REBUIT_DATE, GREEN_MILE, 
      REPEAT_ROAD_CODE, REPEAT_ROAD_NUM, SURFACE_THICK, 
      LAST_REPAIR_DATE, BUILT_NATURE, GBMWMYBL, 
      LANDFROM, OWNED_TOLL, CULVERT_NUM, 
      TECHNICAL_GRADE, SURFACE_TYPE, ROAD_WIDTH, 
      ROADBED_WIDTH, LANE_FEATURES, ID, 
      COMMUT_DATE, EXCHANGE, DESIGN_SPEED, 
      EVENTID, F12, IS_JITUAN, 
      REMARK, LDLX, STARTZH, 
      ENDZH, ROADCODE, FIRSTEVENTID, 
      ENDEVENTID, LINK_MAINLINE, ROUTE_CODE
      , ORG_FULLNAME)
    values (#{objectid,jdbcType=DECIMAL}, #{rpIntrvlId,jdbcType=VARCHAR}, #{roadname,jdbcType=VARCHAR}, 
      #{direction,jdbcType=DECIMAL}, #{startStakeNum,jdbcType=DECIMAL}, #{endStakeNum,jdbcType=DECIMAL}, 
      #{mainlineId,jdbcType=VARCHAR}, #{oprtOrgCode,jdbcType=VARCHAR}, #{prjOrgCode,jdbcType=VARCHAR}, 
      #{lineDirect,jdbcType=VARCHAR}, #{startSenssionNum,jdbcType=DECIMAL}, #{endSenssionNum,jdbcType=DECIMAL}, 
      #{startPlace,jdbcType=VARCHAR}, #{endPlace,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{updaUserId,jdbcType=VARCHAR}, #{isEnable,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR}, 
      #{gisId,jdbcType=VARCHAR}, #{routeno,jdbcType=VARCHAR}, #{length,jdbcType=DECIMAL}, 
      #{ramptype,jdbcType=VARCHAR}, #{eQueryRpid,jdbcType=VARCHAR}, #{lineno,jdbcType=DECIMAL}, 
      #{roadcd,jdbcType=VARCHAR}, #{sx,jdbcType=DECIMAL}, #{sy,jdbcType=DECIMAL}, #{ex,jdbcType=DECIMAL}, 
      #{ey,jdbcType=DECIMAL}, #{dstrctCode,jdbcType=VARCHAR}, #{proDiscIden,jdbcType=VARCHAR}, 
      #{chaNature,jdbcType=VARCHAR}, #{repeatRoadLineType,jdbcType=VARCHAR}, #{rampName,jdbcType=VARCHAR}, 
      #{rampTowClass,jdbcType=VARCHAR}, #{roadNum,jdbcType=VARCHAR}, #{columnIs,jdbcType=VARCHAR}, 
      #{columnRaiseSource,jdbcType=VARCHAR}, #{curingDate,jdbcType=VARCHAR}, #{startDemarClass,jdbcType=VARCHAR}, 
      #{endDemarClass,jdbcType=VARCHAR}, #{rampCode,jdbcType=VARCHAR}, #{roadNature,jdbcType=VARCHAR}, 
      #{builtDate,jdbcType=VARCHAR}, #{rebuitDate,jdbcType=VARCHAR}, #{greenMile,jdbcType=VARCHAR}, 
      #{repeatRoadCode,jdbcType=VARCHAR}, #{repeatRoadNum,jdbcType=VARCHAR}, #{surfaceThick,jdbcType=DECIMAL}, 
      #{lastRepairDate,jdbcType=VARCHAR}, #{builtNature,jdbcType=VARCHAR}, #{gbmwmybl,jdbcType=VARCHAR}, 
      #{landfrom,jdbcType=VARCHAR}, #{ownedToll,jdbcType=VARCHAR}, #{culvertNum,jdbcType=DECIMAL}, 
      #{technicalGrade,jdbcType=VARCHAR}, #{surfaceType,jdbcType=VARCHAR}, #{roadWidth,jdbcType=DECIMAL}, 
      #{roadbedWidth,jdbcType=VARCHAR}, #{laneFeatures,jdbcType=VARCHAR}, #{id,jdbcType=VARCHAR}, 
      #{commutDate,jdbcType=DECIMAL}, #{exchange,jdbcType=VARCHAR}, #{designSpeed,jdbcType=DECIMAL}, 
      #{eventid,jdbcType=VARCHAR}, #{f12,jdbcType=VARCHAR}, #{isJituan,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{ldlx,jdbcType=VARCHAR}, #{startzh,jdbcType=DECIMAL}, 
      #{endzh,jdbcType=DECIMAL}, #{roadcode,jdbcType=VARCHAR}, #{firsteventid,jdbcType=VARCHAR}, 
      #{endeventid,jdbcType=VARCHAR}, #{linkMainline,jdbcType=DECIMAL}, #{routeCode,jdbcType=VARCHAR},
       #{orgFullname,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.Expressway">
    <!--@mbg.generated-->
    insert into EXPRESSWAY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="objectid != null">
        OBJECTID,
      </if>
      <if test="rpIntrvlId != null">
        RP_INTRVL_ID,
      </if>
      <if test="roadname != null">
        ROADNAME,
      </if>
      <if test="direction != null">
        DIRECTION,
      </if>
      <if test="startStakeNum != null">
        START_STAKE_NUM,
      </if>
      <if test="endStakeNum != null">
        END_STAKE_NUM,
      </if>
      <if test="mainlineId != null">
        MAINLINE_ID,
      </if>
      <if test="oprtOrgCode != null">
        OPRT_ORG_CODE,
      </if>
      <if test="prjOrgCode != null">
        PRJ_ORG_CODE,
      </if>
      <if test="lineDirect != null">
        LINE_DIRECT,
      </if>
      <if test="startSenssionNum != null">
        START_SENSSION_NUM,
      </if>
      <if test="endSenssionNum != null">
        END_SENSSION_NUM,
      </if>
      <if test="startPlace != null">
        START_PLACE,
      </if>
      <if test="endPlace != null">
        END_PLACE,
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID,
      </if>
      <if test="updaUserId != null">
        UPDA_USER_ID,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="gisId != null">
        GIS_ID,
      </if>
      <if test="routeno != null">
        ROUTENO,
      </if>
      <if test="length != null">
        "LENGTH",
      </if>
      <if test="ramptype != null">
        RAMPTYPE,
      </if>
      <if test="eQueryRpid != null">
        E_QUERY_RPID,
      </if>
      <if test="lineno != null">
        "LINENO",
      </if>
      <if test="roadcd != null">
        ROADCD,
      </if>
      <if test="sx != null">
        SX,
      </if>
      <if test="sy != null">
        SY,
      </if>
      <if test="ex != null">
        EX,
      </if>
      <if test="ey != null">
        EY,
      </if>
      <if test="dstrctCode != null">
        DSTRCT_CODE,
      </if>
      <if test="proDiscIden != null">
        PRO_DISC_IDEN,
      </if>
      <if test="chaNature != null">
        CHA_NATURE,
      </if>
      <if test="repeatRoadLineType != null">
        REPEAT_ROAD_LINE_TYPE,
      </if>
      <if test="rampName != null">
        RAMP_NAME,
      </if>
      <if test="rampTowClass != null">
        RAMP_TOW_CLASS,
      </if>
      <if test="roadNum != null">
        ROAD_NUM,
      </if>
      <if test="columnIs != null">
        COLUMN_IS,
      </if>
      <if test="columnRaiseSource != null">
        COLUMN_RAISE_SOURCE,
      </if>
      <if test="curingDate != null">
        CURING_DATE,
      </if>
      <if test="startDemarClass != null">
        START_DEMAR_CLASS,
      </if>
      <if test="endDemarClass != null">
        END_DEMAR_CLASS,
      </if>
      <if test="rampCode != null">
        RAMP_CODE,
      </if>
      <if test="roadNature != null">
        ROAD_NATURE,
      </if>
      <if test="builtDate != null">
        BUILT_DATE,
      </if>
      <if test="rebuitDate != null">
        REBUIT_DATE,
      </if>
      <if test="greenMile != null">
        GREEN_MILE,
      </if>
      <if test="repeatRoadCode != null">
        REPEAT_ROAD_CODE,
      </if>
      <if test="repeatRoadNum != null">
        REPEAT_ROAD_NUM,
      </if>
      <if test="surfaceThick != null">
        SURFACE_THICK,
      </if>
      <if test="lastRepairDate != null">
        LAST_REPAIR_DATE,
      </if>
      <if test="builtNature != null">
        BUILT_NATURE,
      </if>
      <if test="gbmwmybl != null">
        GBMWMYBL,
      </if>
      <if test="landfrom != null">
        LANDFROM,
      </if>
      <if test="ownedToll != null">
        OWNED_TOLL,
      </if>
      <if test="culvertNum != null">
        CULVERT_NUM,
      </if>
      <if test="technicalGrade != null">
        TECHNICAL_GRADE,
      </if>
      <if test="surfaceType != null">
        SURFACE_TYPE,
      </if>
      <if test="roadWidth != null">
        ROAD_WIDTH,
      </if>
      <if test="roadbedWidth != null">
        ROADBED_WIDTH,
      </if>
      <if test="laneFeatures != null">
        LANE_FEATURES,
      </if>
      <if test="id != null">
        ID,
      </if>
      <if test="commutDate != null">
        COMMUT_DATE,
      </if>
      <if test="exchange != null">
        EXCHANGE,
      </if>
      <if test="designSpeed != null">
        DESIGN_SPEED,
      </if>
      <if test="eventid != null">
        EVENTID,
      </if>
      <if test="f12 != null">
        F12,
      </if>
      <if test="isJituan != null">
        IS_JITUAN,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="ldlx != null">
        LDLX,
      </if>
      <if test="startzh != null">
        STARTZH,
      </if>
      <if test="endzh != null">
        ENDZH,
      </if>
      <if test="roadcode != null">
        ROADCODE,
      </if>
      <if test="firsteventid != null">
        FIRSTEVENTID,
      </if>
      <if test="endeventid != null">
        ENDEVENTID,
      </if>
      <if test="linkMainline != null">
        LINK_MAINLINE,
      </if>
      <if test="routeCode != null">
        ROUTE_CODE,
      </if>
      <if test="shape != null">
        SHAPE,
      </if>
      <if test="orgFullname != null">
        ORG_FULLNAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="objectid != null">
        #{objectid,jdbcType=DECIMAL},
      </if>
      <if test="rpIntrvlId != null">
        #{rpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="roadname != null">
        #{roadname,jdbcType=VARCHAR},
      </if>
      <if test="direction != null">
        #{direction,jdbcType=DECIMAL},
      </if>
      <if test="startStakeNum != null">
        #{startStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="endStakeNum != null">
        #{endStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="mainlineId != null">
        #{mainlineId,jdbcType=VARCHAR},
      </if>
      <if test="oprtOrgCode != null">
        #{oprtOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="prjOrgCode != null">
        #{prjOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="lineDirect != null">
        #{lineDirect,jdbcType=VARCHAR},
      </if>
      <if test="startSenssionNum != null">
        #{startSenssionNum,jdbcType=DECIMAL},
      </if>
      <if test="endSenssionNum != null">
        #{endSenssionNum,jdbcType=DECIMAL},
      </if>
      <if test="startPlace != null">
        #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="endPlace != null">
        #{endPlace,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updaUserId != null">
        #{updaUserId,jdbcType=VARCHAR},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="gisId != null">
        #{gisId,jdbcType=VARCHAR},
      </if>
      <if test="routeno != null">
        #{routeno,jdbcType=VARCHAR},
      </if>
      <if test="length != null">
        #{length,jdbcType=DECIMAL},
      </if>
      <if test="ramptype != null">
        #{ramptype,jdbcType=VARCHAR},
      </if>
      <if test="eQueryRpid != null">
        #{eQueryRpid,jdbcType=VARCHAR},
      </if>
      <if test="lineno != null">
        #{lineno,jdbcType=DECIMAL},
      </if>
      <if test="roadcd != null">
        #{roadcd,jdbcType=VARCHAR},
      </if>
      <if test="sx != null">
        #{sx,jdbcType=DECIMAL},
      </if>
      <if test="sy != null">
        #{sy,jdbcType=DECIMAL},
      </if>
      <if test="ex != null">
        #{ex,jdbcType=DECIMAL},
      </if>
      <if test="ey != null">
        #{ey,jdbcType=DECIMAL},
      </if>
      <if test="dstrctCode != null">
        #{dstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="proDiscIden != null">
        #{proDiscIden,jdbcType=VARCHAR},
      </if>
      <if test="chaNature != null">
        #{chaNature,jdbcType=VARCHAR},
      </if>
      <if test="repeatRoadLineType != null">
        #{repeatRoadLineType,jdbcType=VARCHAR},
      </if>
      <if test="rampName != null">
        #{rampName,jdbcType=VARCHAR},
      </if>
      <if test="rampTowClass != null">
        #{rampTowClass,jdbcType=VARCHAR},
      </if>
      <if test="roadNum != null">
        #{roadNum,jdbcType=VARCHAR},
      </if>
      <if test="columnIs != null">
        #{columnIs,jdbcType=VARCHAR},
      </if>
      <if test="columnRaiseSource != null">
        #{columnRaiseSource,jdbcType=VARCHAR},
      </if>
      <if test="curingDate != null">
        #{curingDate,jdbcType=VARCHAR},
      </if>
      <if test="startDemarClass != null">
        #{startDemarClass,jdbcType=VARCHAR},
      </if>
      <if test="endDemarClass != null">
        #{endDemarClass,jdbcType=VARCHAR},
      </if>
      <if test="rampCode != null">
        #{rampCode,jdbcType=VARCHAR},
      </if>
      <if test="roadNature != null">
        #{roadNature,jdbcType=VARCHAR},
      </if>
      <if test="builtDate != null">
        #{builtDate,jdbcType=VARCHAR},
      </if>
      <if test="rebuitDate != null">
        #{rebuitDate,jdbcType=VARCHAR},
      </if>
      <if test="greenMile != null">
        #{greenMile,jdbcType=VARCHAR},
      </if>
      <if test="repeatRoadCode != null">
        #{repeatRoadCode,jdbcType=VARCHAR},
      </if>
      <if test="repeatRoadNum != null">
        #{repeatRoadNum,jdbcType=VARCHAR},
      </if>
      <if test="surfaceThick != null">
        #{surfaceThick,jdbcType=DECIMAL},
      </if>
      <if test="lastRepairDate != null">
        #{lastRepairDate,jdbcType=VARCHAR},
      </if>
      <if test="builtNature != null">
        #{builtNature,jdbcType=VARCHAR},
      </if>
      <if test="gbmwmybl != null">
        #{gbmwmybl,jdbcType=VARCHAR},
      </if>
      <if test="landfrom != null">
        #{landfrom,jdbcType=VARCHAR},
      </if>
      <if test="ownedToll != null">
        #{ownedToll,jdbcType=VARCHAR},
      </if>
      <if test="culvertNum != null">
        #{culvertNum,jdbcType=DECIMAL},
      </if>
      <if test="technicalGrade != null">
        #{technicalGrade,jdbcType=VARCHAR},
      </if>
      <if test="surfaceType != null">
        #{surfaceType,jdbcType=VARCHAR},
      </if>
      <if test="roadWidth != null">
        #{roadWidth,jdbcType=DECIMAL},
      </if>
      <if test="roadbedWidth != null">
        #{roadbedWidth,jdbcType=VARCHAR},
      </if>
      <if test="laneFeatures != null">
        #{laneFeatures,jdbcType=VARCHAR},
      </if>
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="commutDate != null">
        #{commutDate,jdbcType=DECIMAL},
      </if>
      <if test="exchange != null">
        #{exchange,jdbcType=VARCHAR},
      </if>
      <if test="designSpeed != null">
        #{designSpeed,jdbcType=DECIMAL},
      </if>
      <if test="eventid != null">
        #{eventid,jdbcType=VARCHAR},
      </if>
      <if test="f12 != null">
        #{f12,jdbcType=VARCHAR},
      </if>
      <if test="isJituan != null">
        #{isJituan,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ldlx != null">
        #{ldlx,jdbcType=VARCHAR},
      </if>
      <if test="startzh != null">
        #{startzh,jdbcType=DECIMAL},
      </if>
      <if test="endzh != null">
        #{endzh,jdbcType=DECIMAL},
      </if>
      <if test="roadcode != null">
        #{roadcode,jdbcType=VARCHAR},
      </if>
      <if test="firsteventid != null">
        #{firsteventid,jdbcType=VARCHAR},
      </if>
      <if test="endeventid != null">
        #{endeventid,jdbcType=VARCHAR},
      </if>
      <if test="linkMainline != null">
        #{linkMainline,jdbcType=DECIMAL},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="shape != null">
        #{shape,jdbcType=OTHER},
      </if>
      <if test="orgFullname != null">
        #{orgFullname,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectByRpId" resultType="com.hualu.highwaymaintenance.module.maintainbase.domain.Expressway">
    select * from expressway where rp_intrvl_id=#{id}
    </select>

  <select id="selectMaxObjectId" resultType="int">
    select max(objectId) from expressway
  </select>

  <update id="synchShape">
    update expressway e set e.shape=(
      select sde.ST_LineFromText(SDE.st_astext(t.SHAPE),4326) from GDGSSD t where t.ID=e.RP_INTRVL_ID
    ) where e.RP_INTRVL_ID=#{rpId} and exists(select 1 from GDGSSD t where t.ID=e.RP_INTRVL_ID and t.shape is not null)
  </update>
</mapper>