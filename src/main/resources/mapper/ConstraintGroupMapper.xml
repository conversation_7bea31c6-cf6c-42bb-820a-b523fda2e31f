<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.ConstraintGroupMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.ConstraintGroup">
            <id property="constraintGroupId" column="CONSTRAINT_GROUP_ID" jdbcType="VARCHAR"/>
            <result property="constraintGroupName" column="CONSTRAINT_GROUP_NAME" jdbcType="VARCHAR"/>
            <result property="projectId" column="PROJECT_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONSTRAINT_GROUP_ID,CONSTRAINT_GROUP_NAME,PROJECT_ID
    </sql>
</mapper>
