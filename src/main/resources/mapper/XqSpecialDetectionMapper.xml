<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqSpecialDetectionMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqSpecialDetection">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_SPECIAL_DETECTION-->
    <result column="BRIDGE_IDENTITY_CODE" jdbcType="VARCHAR" property="bridgeIdentityCode" />
    <result column="DETECTION_ID" jdbcType="VARCHAR" property="detectionId" />
    <result column="INSPECTION_DATE" jdbcType="TIMESTAMP" property="inspectionDate" />
    <result column="INSPECTION_TYPE" jdbcType="VARCHAR" property="inspectionType" />
    <result column="MAIN_CONCLUSIONS" jdbcType="VARCHAR" property="mainConclusions" />
    <result column="TREATE_MEASURE" jdbcType="VARCHAR" property="treateMeasure" />
    <result column="DETECTION_COMPANY_NAME" jdbcType="VARCHAR" property="detectionCompanyName" />
    <result column="DETECTION_REPORT_FILE_PATH" jdbcType="VARCHAR" property="detectionReportFilePath" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRIDGE_IDENTITY_CODE, DETECTION_ID, INSPECTION_DATE, INSPECTION_TYPE, MAIN_CONCLUSIONS, 
    TREATE_MEASURE, DETECTION_COMPANY_NAME, DETECTION_REPORT_FILE_PATH, SECOND_COMPANY, 
    ORG_CODE, REPORT_ID
  </sql>
</mapper>