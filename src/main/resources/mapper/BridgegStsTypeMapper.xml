<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridgeDecision.mapper.BridgegStsTypeMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgegStsType">
    <!--@mbg.generated-->
    <!--@Table BRIDGEG_STS_TYPE-->
    <result column="PARTNAME" jdbcType="VARCHAR" property="partname" />
    <result column="STRUCT_COMP_NAME" jdbcType="VARCHAR" property="structCompName" />
    <result column="PART_CODE" jdbcType="DECIMAL" property="partCode" />
    <result column="DSS_TYPE_NAME" jdbcType="VARCHAR" property="dssTypeName" />
    <result column="DSS_TYPE" jdbcType="VARCHAR" property="dssType" />
    <result column="STS_PART_NAME" jdbcType="VARCHAR" property="stsPartName" />
    <result column="STS_DSS_TYPE_NAME" jdbcType="VARCHAR" property="stsDssTypeName" />
    <result column="OBJ_FIELD" jdbcType="VARCHAR" property="objField" />
    <result column="STS_COLUMN" jdbcType="VARCHAR" property="stsColumn" />
    <result column="ID" jdbcType="VARCHAR" property="id" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PARTNAME, STRUCT_COMP_NAME, PART_CODE, DSS_TYPE_NAME, DSS_TYPE, STS_PART_NAME, STS_DSS_TYPE_NAME, 
    OBJ_FIELD, STS_COLUMN, ID
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgegStsType">
    <!--@mbg.generated-->
    insert into BRIDGEG_STS_TYPE (PARTNAME, STRUCT_COMP_NAME, PART_CODE, 
      DSS_TYPE_NAME, DSS_TYPE, STS_PART_NAME, 
      STS_DSS_TYPE_NAME, OBJ_FIELD, STS_COLUMN, 
      ID)
    values (#{partname,jdbcType=VARCHAR}, #{structCompName,jdbcType=VARCHAR}, #{partCode,jdbcType=DECIMAL}, 
      #{dssTypeName,jdbcType=VARCHAR}, #{dssType,jdbcType=VARCHAR}, #{stsPartName,jdbcType=VARCHAR}, 
      #{stsDssTypeName,jdbcType=VARCHAR}, #{objField,jdbcType=VARCHAR}, #{stsColumn,jdbcType=VARCHAR}, 
      #{id,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgegStsType">
    <!--@mbg.generated-->
    insert into BRIDGEG_STS_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="partname != null">
        PARTNAME,
      </if>
      <if test="structCompName != null">
        STRUCT_COMP_NAME,
      </if>
      <if test="partCode != null">
        PART_CODE,
      </if>
      <if test="dssTypeName != null">
        DSS_TYPE_NAME,
      </if>
      <if test="dssType != null">
        DSS_TYPE,
      </if>
      <if test="stsPartName != null">
        STS_PART_NAME,
      </if>
      <if test="stsDssTypeName != null">
        STS_DSS_TYPE_NAME,
      </if>
      <if test="objField != null">
        OBJ_FIELD,
      </if>
      <if test="stsColumn != null">
        STS_COLUMN,
      </if>
      <if test="id != null">
        ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="partname != null">
        #{partname,jdbcType=VARCHAR},
      </if>
      <if test="structCompName != null">
        #{structCompName,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        #{partCode,jdbcType=DECIMAL},
      </if>
      <if test="dssTypeName != null">
        #{dssTypeName,jdbcType=VARCHAR},
      </if>
      <if test="dssType != null">
        #{dssType,jdbcType=VARCHAR},
      </if>
      <if test="stsPartName != null">
        #{stsPartName,jdbcType=VARCHAR},
      </if>
      <if test="stsDssTypeName != null">
        #{stsDssTypeName,jdbcType=VARCHAR},
      </if>
      <if test="objField != null">
        #{objField,jdbcType=VARCHAR},
      </if>
      <if test="stsColumn != null">
        #{stsColumn,jdbcType=VARCHAR},
      </if>
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>