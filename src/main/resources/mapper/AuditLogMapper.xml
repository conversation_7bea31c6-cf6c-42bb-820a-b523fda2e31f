<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.platform.mapper.AuditLogMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.platform.entity.AuditLog">
        <result property="id" column="ID" jdbcType="DECIMAL"/>
        <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
        <result property="funcCode" column="FUNC_CODE" jdbcType="VARCHAR"/>
        <result property="funcName" column="FUNC_NAME" jdbcType="VARCHAR"/>
        <result property="visitTime" column="VISIT_TIME" jdbcType="TIMESTAMP"/>
        <result property="params" column="PARAMS" jdbcType="VARCHAR"/>
        <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
        <result property="ip" column="IP" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,USER_CODE,FUNC_CODE,
        FUNC_NAME,VISIT_TIME,PARAMS,
        REMARK,IP
    </sql>
    <select id="queryPage"
            resultType="com.hualu.highwaymaintenance.module.platform.entity.AuditLog">
    select t.ID, t.FUNC_CODE, t.FUNC_NAME, t.VISIT_TIME, t.PARAMS,t.REMARK, t.IP,(select f.USER_NAME from GDGS.fw_right_user f where f.USER_CODE=t.USER_CODE) as USER_CODE
    from GDGS.AUDIT_LOG t
    </select>
</mapper>
