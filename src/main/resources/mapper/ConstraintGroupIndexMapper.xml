<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.ConstraintGroupIndexMapper">
  <select id="listByGroupId"
    resultType="com.hualu.highwaymaintenance.module.decision.entity.ConstraintGroupIndex">
    select (select i.CONSTRAINT_INDEX_NAME
    from PMSDB.CONSTRAINT_INDEX i where i.CONSTRAINT_INDEX_ID = l.CONSTRAINT_INDEX_ID and rownum = 1) constraintIndexName,
    l.*
    from PMSDB.CONSTRAINT_GROUP_INDEX l
    where l.CONSTRAINT_GROUP_ID in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
</mapper>
