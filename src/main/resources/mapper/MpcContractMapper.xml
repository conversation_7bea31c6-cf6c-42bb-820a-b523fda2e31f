<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.contract.mapper.MpcContractMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.contract.domain.MpcContract">
            <id property="contrId" column="CONTR_ID" jdbcType="VARCHAR"/>
            <result property="contrCode" column="CONTR_CODE" jdbcType="VARCHAR"/>
            <result property="contrName" column="CONTR_NAME" jdbcType="VARCHAR"/>
            <result property="contrType" column="CONTR_TYPE" jdbcType="VARCHAR"/>
            <result property="spPrjId" column="SP_PRJ_ID" jdbcType="VARCHAR"/>
            <result property="spPrjCode" column="SP_PRJ_CODE" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="implOrgId" column="IMPL_ORG_ID" jdbcType="VARCHAR"/>
            <result property="implOrgName" column="IMPL_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="contrNo" column="CONTR_NO" jdbcType="VARCHAR"/>
            <result property="contrMoeny" column="CONTR_MOENY" jdbcType="DECIMAL"/>
            <result property="signDate" column="SIGN_DATE" jdbcType="TIMESTAMP"/>
            <result property="rptDate" column="RPT_DATE" jdbcType="TIMESTAMP"/>
            <result property="startDate" column="START_DATE" jdbcType="TIMESTAMP"/>
            <result property="endDate" column="END_DATE" jdbcType="TIMESTAMP"/>
            <result property="stlmtDesc" column="STLMT_DESC" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="submitTime" column="SUBMIT_TIME" jdbcType="TIMESTAMP"/>
            <result property="status" column="STATUS" jdbcType="VARCHAR"/>
            <result property="submitOpinion" column="SUBMIT_OPINION" jdbcType="VARCHAR"/>
            <result property="mpitemSysId" column="MPITEM_SYS_ID" jdbcType="VARCHAR"/>
            <result property="enabled" column="ENABLED" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONTR_ID,CONTR_CODE,CONTR_NAME,
        CONTR_TYPE,SP_PRJ_ID,SP_PRJ_CODE,
        MNT_ORG_ID,IMPL_ORG_ID,IMPL_ORG_NAME,
        CONTR_NO,CONTR_MOENY,SIGN_DATE,
        RPT_DATE,START_DATE,END_DATE,
        STLMT_DESC,REMARK,CREATE_USER_ID,
        CREATE_TIME,UPDATE_USER_ID,UPDATE_TIME,
        SUBMIT_TIME,STATUS,SUBMIT_OPINION,
        MPITEM_SYS_ID,ENABLED
    </sql>
    <select id="loadMpcContractByOrgId"
            resultMap="BaseResultMap">
        select c.contr_id,c.contr_name,o.org_fullname as mnt_org_id,c.IMPL_ORG_NAME,c.contr_code,
				 c.CONTR_MOENY from MEMSDB.MPC_CONTRACT c left join gdgs.FW_RIGHT_ORG o on c.MNT_ORG_ID = o.ID
				 where c.CONTR_TYPE='YH' and c.MNT_ORG_ID=#{org_id} and c.enabled=1 ORDER BY C.SIGN_DATE DESC
    </select>
    <select id="findDictionaryListByItemCode" resultType="com.hualu.highwaymaintenance.module.dic.BaseDataDic">
        select d.ATTRIBUTE_ITEM as itemCode,d.ATTRIBUTE_CODE as valueCode,d.ATTRIBUTE_VALUE as valueName from GDGS.BASE_DATATHIRD_DIC d
        where d.ATTRIBUTE_ITEM=#{itemCode} and d.ATTRIBUTE_ACTIVE='0'
        order by d.seq_no
    </select>
    <select id="loadPrjOrgCodeAndNameByUserCode"
            resultType="com.hualu.highwaymaintenance.module.dic.FwRightOrg">
        select rd.prj_org_code,org.org_fullname as ORG_NAME,org.org_fullname as ORG_FULLNAME,ORG.ORG_NAME as sname
         from GDGS.FW_RIGHT_DATA_PERMISSION rd 
         inner join GDGS.FW_RIGHT_ORG org on org.org_code = rd.PRJ_ORG_CODE 
         inner join gdgs.FW_RIGHT_USER ru on rd.PRJ_ORG_CODE = ru.ORG_ID or rd.OPRT_ORG_CODE = ru.ORG_ID 
         where ru.USER_CODE = #{userCode}
         and org.is_deleted = '0'
         group by rd.prj_org_code, org.org_fullname, ORG.ORG_NAME
         order by rd.prj_org_code
    </select>
    <select id="findDictionaryListByItemCode2ValueCode"
            resultType="com.hualu.highwaymaintenance.module.dic.BaseDataDic">
        select d.ATTRIBUTE_ITEM as itemCode,d.ATTRIBUTE_CODE as valueCode,d.ATTRIBUTE_VALUE as valueName from GDGS.BASE_DATATHIRD_DIC d
        where d.ATTRIBUTE_ITEM=#{itemCode} and d.ATTRIBUTE_CODE=#{valueCode} and d.ATTRIBUTE_ACTIVE='0' and rownum=1
    </select>
</mapper>
