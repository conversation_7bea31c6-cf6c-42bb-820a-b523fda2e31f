<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.emergenceCheck.mapper.HsmsSlopeInfoMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsSlopeInfo">
    <!--@mbg.generated-->
    <!--@Table HSMSDB.HSMS_SLOPE_INFO-->
    <id column="SLOPE_ID" jdbcType="VARCHAR" property="slopeId" />
    <result column="SLOPE_TYPE" jdbcType="VARCHAR" property="slopeType" />
    <result column="SLOPE_CODE" jdbcType="VARCHAR" property="slopeCode" />
    <result column="SLOPE_NAME" jdbcType="VARCHAR" property="slopeName" />
    <result column="MAINTAIN_SERIES" jdbcType="VARCHAR" property="maintainSeries" />
    <result column="IS_IMPORTANT" jdbcType="VARCHAR" property="isImportant" />
    <result column="SLOPE_POSITION" jdbcType="VARCHAR" property="slopePosition" />
    <result column="SLOPE_RATE" jdbcType="VARCHAR" property="slopeRate" />
    <result column="SLOPE_LEVEL" jdbcType="DECIMAL" property="slopeLevel" />
    <result column="SLOPE_LENGTH" jdbcType="DECIMAL" property="slopeLength" />
    <result column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
    <result column="LINE_DIRECTION" jdbcType="VARCHAR" property="lineDirection" />
    <result column="START_STAKE_OLD" jdbcType="DECIMAL" property="startStakeOld" />
    <result column="END_STAKE_OLD" jdbcType="DECIMAL" property="endStakeOld" />
    <result column="SLOPE_RETAINING_AND_REINFORCE" jdbcType="VARCHAR" property="slopeRetainingAndReinforce" />
    <result column="COMPLETION_TIME_DATE" jdbcType="TIMESTAMP" property="completionTimeDate" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
    <result column="GIS_X" jdbcType="DECIMAL" property="gisX" />
    <result column="GIS_Y" jdbcType="DECIMAL" property="gisY" />
    <result column="BD_GIS_X" jdbcType="DECIMAL" property="bdGisX" />
    <result column="BD_GIS_Y" jdbcType="DECIMAL" property="bdGisY" />
    <result column="OPT_ORG_ID" jdbcType="VARCHAR" property="optOrgId" />
    <result column="PRJ_ORG_ID" jdbcType="VARCHAR" property="prjOrgId" />
    <result column="IS_MAIN_LINE" jdbcType="VARCHAR" property="isMainLine" />
    <result column="RP_INTRVL_ID" jdbcType="VARCHAR" property="rpIntrvlId" />
    <result column="CNTR_STAKE_NUM_OLD" jdbcType="DECIMAL" property="cntrStakeNumOld" />
    <result column="EVALUATE_DATE" jdbcType="TIMESTAMP" property="evaluateDate" />
    <result column="EVALUATION_UNIT" jdbcType="VARCHAR" property="evaluationUnit" />
    <result column="PRJ_ID" jdbcType="VARCHAR" property="prjId" />
    <result column="SLOPE_TC_GRADE" jdbcType="VARCHAR" property="slopeTcGrade" />
    <result column="START_OFFSET_OLD" jdbcType="DECIMAL" property="startOffsetOld" />
    <result column="MAIN_RAMP_LINE_ID" jdbcType="VARCHAR" property="mainRampLineId" />
    <result column="THREAD_RPID" jdbcType="VARCHAR" property="threadRpid" />
    <result column="GIS_SX" jdbcType="DECIMAL" property="gisSx" />
    <result column="GIS_SY" jdbcType="DECIMAL" property="gisSy" />
    <result column="GIS_EX" jdbcType="DECIMAL" property="gisEx" />
    <result column="GIS_EY" jdbcType="DECIMAL" property="gisEy" />
    <result column="START_RP_INTRVL_ID" jdbcType="VARCHAR" property="startRpIntrvlId" />
    <result column="END_RP_INTRVL_ID" jdbcType="VARCHAR" property="endRpIntrvlId" />
    <result column="ROADWAY" jdbcType="VARCHAR" property="roadway" />
    <result column="IS_SPECIAL_SLOPE" jdbcType="VARCHAR" property="isSpecialSlope" />
    <result column="DESIGN_START_STAKE" jdbcType="DECIMAL" property="designStartStake" />
    <result column="DESIGN_END_STAKE" jdbcType="DECIMAL" property="designEndStake" />
    <result column="RED_SLOPE" jdbcType="VARCHAR" property="redSlope" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="PHYSICS_CNTR_STAKE" jdbcType="DECIMAL" property="physicsCntrStake" />
    <result column="LOGIC_CNTR_STAKE" jdbcType="DECIMAL" property="logicCntrStake" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
    <result column="ROUTE_VERSION" jdbcType="VARCHAR" property="routeVersion" />
    <result column="LOGIC_RAMP_CNTR_STAKE" jdbcType="DECIMAL" property="logicRampCntrStake" />
    <result column="LOGIC_END_STAKE" jdbcType="DECIMAL" property="logicEndStake" />
    <result column="LOGIC_START_STAKE" jdbcType="DECIMAL" property="logicStartStake" />
    <result column="START_STAKE" jdbcType="DECIMAL" property="startStake" />
    <result column="END_STAKE" jdbcType="DECIMAL" property="endStake" />
    <result column="CNTR_STAKE_NUM" jdbcType="DECIMAL" property="cntrStakeNum" />
    <result column="START_OFFSET" jdbcType="DECIMAL" property="startOffset" />
    <result column="LOGIC_END_STRSTAKE" jdbcType="VARCHAR" property="logicEndStrstake" />
    <result column="LOGIC_START_STRSTAKE" jdbcType="VARCHAR" property="logicStartStrstake" />
    <result column="LOGIC_CNTR_STRSTAKE" jdbcType="VARCHAR" property="logicCntrStrstake" />
    <result column="DESIGN_START_STRSTAKE" jdbcType="VARCHAR" property="designStartStrstake" />
    <result column="DESIGN_END_STRSTAKE" jdbcType="VARCHAR" property="designEndStrstake" />
    <result column="PLACE" jdbcType="VARCHAR" property="place" />
    <result column="ATTR1" jdbcType="VARCHAR" property="attr1" />
    <result column="COMPLETION_TIME" jdbcType="VARCHAR" property="completionTime" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="COL_START_LATITUDE" jdbcType="DECIMAL" property="colStartLatitude" />
    <result column="COL_START_LONGITUDE" jdbcType="DECIMAL" property="colStartLongitude" />
    <result column="COL_START_DATE" jdbcType="TIMESTAMP" property="colStartDate" />
    <result column="COL_START_PERSON" jdbcType="VARCHAR" property="colStartPerson" />
    <result column="COL_END_LATITUDE" jdbcType="DECIMAL" property="colEndLatitude" />
    <result column="COL_END_LONGITUDE" jdbcType="DECIMAL" property="colEndLongitude" />
    <result column="COL_END_DATE" jdbcType="TIMESTAMP" property="colEndDate" />
    <result column="COL_END_PERSON" jdbcType="VARCHAR" property="colEndPerson" />
    <result column="COL_START_ALTITUDE" jdbcType="DECIMAL" property="colStartAltitude" />
    <result column="COL_END_ALTITUDE" jdbcType="DECIMAL" property="colEndAltitude" />
    <result column="IN_EMERGENCY_INVEST" jdbcType="VARCHAR" property="inEmergencyInvest" />
    <result column="TYPICAL_SECTION_TYPE" jdbcType="VARCHAR" property="typicalSectionType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SLOPE_ID, SLOPE_TYPE, SLOPE_CODE, SLOPE_NAME, MAINTAIN_SERIES, IS_IMPORTANT, SLOPE_POSITION, 
    SLOPE_RATE, SLOPE_LEVEL, SLOPE_LENGTH, LINE_ID, LINE_DIRECTION, START_STAKE_OLD, 
    END_STAKE_OLD, SLOPE_RETAINING_AND_REINFORCE, COMPLETION_TIME_DATE, REMARK, CREATE_USER_ID, 
    UPDATE_USER_ID, CREATE_TIME, UPDATE_TIME, IS_DELETED, GIS_X, GIS_Y, BD_GIS_X, BD_GIS_Y, 
    OPT_ORG_ID, PRJ_ORG_ID, IS_MAIN_LINE, RP_INTRVL_ID, CNTR_STAKE_NUM_OLD, EVALUATE_DATE, 
    EVALUATION_UNIT, PRJ_ID, SLOPE_TC_GRADE, START_OFFSET_OLD, MAIN_RAMP_LINE_ID, THREAD_RPID, 
    GIS_SX, GIS_SY, GIS_EX, GIS_EY, START_RP_INTRVL_ID, END_RP_INTRVL_ID, ROADWAY, IS_SPECIAL_SLOPE, 
    DESIGN_START_STAKE, DESIGN_END_STAKE, RED_SLOPE, LINE_CODE, PHYSICS_CNTR_STAKE, LOGIC_CNTR_STAKE, 
    ROUTE_CODE, ROUTE_VERSION, LOGIC_RAMP_CNTR_STAKE, LOGIC_END_STAKE, LOGIC_START_STAKE, 
    START_STAKE, END_STAKE, CNTR_STAKE_NUM, START_OFFSET, LOGIC_END_STRSTAKE, LOGIC_START_STRSTAKE, 
    LOGIC_CNTR_STRSTAKE, DESIGN_START_STRSTAKE, DESIGN_END_STRSTAKE, PLACE, ATTR1, COMPLETION_TIME, 
    "STATUS", COL_START_LATITUDE, COL_START_LONGITUDE, COL_START_DATE, COL_START_PERSON, 
    COL_END_LATITUDE, COL_END_LONGITUDE, COL_END_DATE, COL_END_PERSON, COL_START_ALTITUDE, 
    COL_END_ALTITUDE, IN_EMERGENCY_INVEST, TYPICAL_SECTION_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from HSMSDB.HSMS_SLOPE_INFO
    where SLOPE_ID = #{slopeId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from HSMSDB.HSMS_SLOPE_INFO
    where SLOPE_ID = #{slopeId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsSlopeInfo">
    <!--@mbg.generated-->
    insert into HSMSDB.HSMS_SLOPE_INFO (SLOPE_ID, SLOPE_TYPE, SLOPE_CODE, 
      SLOPE_NAME, MAINTAIN_SERIES, IS_IMPORTANT, 
      SLOPE_POSITION, SLOPE_RATE, SLOPE_LEVEL, 
      SLOPE_LENGTH, LINE_ID, LINE_DIRECTION, 
      START_STAKE_OLD, END_STAKE_OLD, SLOPE_RETAINING_AND_REINFORCE, 
      COMPLETION_TIME_DATE, REMARK, CREATE_USER_ID, 
      UPDATE_USER_ID, CREATE_TIME, UPDATE_TIME, 
      IS_DELETED, GIS_X, GIS_Y, 
      BD_GIS_X, BD_GIS_Y, OPT_ORG_ID, 
      PRJ_ORG_ID, IS_MAIN_LINE, RP_INTRVL_ID, 
      CNTR_STAKE_NUM_OLD, EVALUATE_DATE, EVALUATION_UNIT, 
      PRJ_ID, SLOPE_TC_GRADE, START_OFFSET_OLD, 
      MAIN_RAMP_LINE_ID, THREAD_RPID, GIS_SX, 
      GIS_SY, GIS_EX, GIS_EY, 
      START_RP_INTRVL_ID, END_RP_INTRVL_ID, ROADWAY, 
      IS_SPECIAL_SLOPE, DESIGN_START_STAKE, DESIGN_END_STAKE, 
      RED_SLOPE, LINE_CODE, PHYSICS_CNTR_STAKE, 
      LOGIC_CNTR_STAKE, ROUTE_CODE, ROUTE_VERSION, 
      LOGIC_RAMP_CNTR_STAKE, LOGIC_END_STAKE, LOGIC_START_STAKE, 
      START_STAKE, END_STAKE, CNTR_STAKE_NUM, 
      START_OFFSET, LOGIC_END_STRSTAKE, LOGIC_START_STRSTAKE, 
      LOGIC_CNTR_STRSTAKE, DESIGN_START_STRSTAKE, 
      DESIGN_END_STRSTAKE, PLACE, ATTR1, 
      COMPLETION_TIME, "STATUS", COL_START_LATITUDE, 
      COL_START_LONGITUDE, COL_START_DATE, COL_START_PERSON, 
      COL_END_LATITUDE, COL_END_LONGITUDE, COL_END_DATE, 
      COL_END_PERSON, COL_START_ALTITUDE, COL_END_ALTITUDE, 
      IN_EMERGENCY_INVEST, TYPICAL_SECTION_TYPE)
    values (#{slopeId,jdbcType=VARCHAR}, #{slopeType,jdbcType=VARCHAR}, #{slopeCode,jdbcType=VARCHAR}, 
      #{slopeName,jdbcType=VARCHAR}, #{maintainSeries,jdbcType=VARCHAR}, #{isImportant,jdbcType=VARCHAR}, 
      #{slopePosition,jdbcType=VARCHAR}, #{slopeRate,jdbcType=VARCHAR}, #{slopeLevel,jdbcType=DECIMAL}, 
      #{slopeLength,jdbcType=DECIMAL}, #{lineId,jdbcType=VARCHAR}, #{lineDirection,jdbcType=VARCHAR}, 
      #{startStakeOld,jdbcType=DECIMAL}, #{endStakeOld,jdbcType=DECIMAL}, #{slopeRetainingAndReinforce,jdbcType=VARCHAR}, 
      #{completionTimeDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, 
      #{updateUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=VARCHAR}, #{gisX,jdbcType=DECIMAL}, #{gisY,jdbcType=DECIMAL}, 
      #{bdGisX,jdbcType=DECIMAL}, #{bdGisY,jdbcType=DECIMAL}, #{optOrgId,jdbcType=VARCHAR}, 
      #{prjOrgId,jdbcType=VARCHAR}, #{isMainLine,jdbcType=VARCHAR}, #{rpIntrvlId,jdbcType=VARCHAR}, 
      #{cntrStakeNumOld,jdbcType=DECIMAL}, #{evaluateDate,jdbcType=TIMESTAMP}, #{evaluationUnit,jdbcType=VARCHAR}, 
      #{prjId,jdbcType=VARCHAR}, #{slopeTcGrade,jdbcType=VARCHAR}, #{startOffsetOld,jdbcType=DECIMAL}, 
      #{mainRampLineId,jdbcType=VARCHAR}, #{threadRpid,jdbcType=VARCHAR}, #{gisSx,jdbcType=DECIMAL}, 
      #{gisSy,jdbcType=DECIMAL}, #{gisEx,jdbcType=DECIMAL}, #{gisEy,jdbcType=DECIMAL}, 
      #{startRpIntrvlId,jdbcType=VARCHAR}, #{endRpIntrvlId,jdbcType=VARCHAR}, #{roadway,jdbcType=VARCHAR}, 
      #{isSpecialSlope,jdbcType=VARCHAR}, #{designStartStake,jdbcType=DECIMAL}, #{designEndStake,jdbcType=DECIMAL}, 
      #{redSlope,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{physicsCntrStake,jdbcType=DECIMAL}, 
      #{logicCntrStake,jdbcType=DECIMAL}, #{routeCode,jdbcType=VARCHAR}, #{routeVersion,jdbcType=VARCHAR}, 
      #{logicRampCntrStake,jdbcType=DECIMAL}, #{logicEndStake,jdbcType=DECIMAL}, #{logicStartStake,jdbcType=DECIMAL}, 
      #{startStake,jdbcType=DECIMAL}, #{endStake,jdbcType=DECIMAL}, #{cntrStakeNum,jdbcType=DECIMAL}, 
      #{startOffset,jdbcType=DECIMAL}, #{logicEndStrstake,jdbcType=VARCHAR}, #{logicStartStrstake,jdbcType=VARCHAR}, 
      #{logicCntrStrstake,jdbcType=VARCHAR}, #{designStartStrstake,jdbcType=VARCHAR}, 
      #{designEndStrstake,jdbcType=VARCHAR}, #{place,jdbcType=VARCHAR}, #{attr1,jdbcType=VARCHAR}, 
      #{completionTime,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{colStartLatitude,jdbcType=DECIMAL}, 
      #{colStartLongitude,jdbcType=DECIMAL}, #{colStartDate,jdbcType=TIMESTAMP}, #{colStartPerson,jdbcType=VARCHAR}, 
      #{colEndLatitude,jdbcType=DECIMAL}, #{colEndLongitude,jdbcType=DECIMAL}, #{colEndDate,jdbcType=TIMESTAMP}, 
      #{colEndPerson,jdbcType=VARCHAR}, #{colStartAltitude,jdbcType=DECIMAL}, #{colEndAltitude,jdbcType=DECIMAL}, 
      #{inEmergencyInvest,jdbcType=VARCHAR}, #{typicalSectionType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsSlopeInfo">
    <!--@mbg.generated-->
    insert into HSMSDB.HSMS_SLOPE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="slopeId != null">
        SLOPE_ID,
      </if>
      <if test="slopeType != null">
        SLOPE_TYPE,
      </if>
      <if test="slopeCode != null">
        SLOPE_CODE,
      </if>
      <if test="slopeName != null">
        SLOPE_NAME,
      </if>
      <if test="maintainSeries != null">
        MAINTAIN_SERIES,
      </if>
      <if test="isImportant != null">
        IS_IMPORTANT,
      </if>
      <if test="slopePosition != null">
        SLOPE_POSITION,
      </if>
      <if test="slopeRate != null">
        SLOPE_RATE,
      </if>
      <if test="slopeLevel != null">
        SLOPE_LEVEL,
      </if>
      <if test="slopeLength != null">
        SLOPE_LENGTH,
      </if>
      <if test="lineId != null">
        LINE_ID,
      </if>
      <if test="lineDirection != null">
        LINE_DIRECTION,
      </if>
      <if test="startStakeOld != null">
        START_STAKE_OLD,
      </if>
      <if test="endStakeOld != null">
        END_STAKE_OLD,
      </if>
      <if test="slopeRetainingAndReinforce != null">
        SLOPE_RETAINING_AND_REINFORCE,
      </if>
      <if test="completionTimeDate != null">
        COMPLETION_TIME_DATE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="gisX != null">
        GIS_X,
      </if>
      <if test="gisY != null">
        GIS_Y,
      </if>
      <if test="bdGisX != null">
        BD_GIS_X,
      </if>
      <if test="bdGisY != null">
        BD_GIS_Y,
      </if>
      <if test="optOrgId != null">
        OPT_ORG_ID,
      </if>
      <if test="prjOrgId != null">
        PRJ_ORG_ID,
      </if>
      <if test="isMainLine != null">
        IS_MAIN_LINE,
      </if>
      <if test="rpIntrvlId != null">
        RP_INTRVL_ID,
      </if>
      <if test="cntrStakeNumOld != null">
        CNTR_STAKE_NUM_OLD,
      </if>
      <if test="evaluateDate != null">
        EVALUATE_DATE,
      </if>
      <if test="evaluationUnit != null">
        EVALUATION_UNIT,
      </if>
      <if test="prjId != null">
        PRJ_ID,
      </if>
      <if test="slopeTcGrade != null">
        SLOPE_TC_GRADE,
      </if>
      <if test="startOffsetOld != null">
        START_OFFSET_OLD,
      </if>
      <if test="mainRampLineId != null">
        MAIN_RAMP_LINE_ID,
      </if>
      <if test="threadRpid != null">
        THREAD_RPID,
      </if>
      <if test="gisSx != null">
        GIS_SX,
      </if>
      <if test="gisSy != null">
        GIS_SY,
      </if>
      <if test="gisEx != null">
        GIS_EX,
      </if>
      <if test="gisEy != null">
        GIS_EY,
      </if>
      <if test="startRpIntrvlId != null">
        START_RP_INTRVL_ID,
      </if>
      <if test="endRpIntrvlId != null">
        END_RP_INTRVL_ID,
      </if>
      <if test="roadway != null">
        ROADWAY,
      </if>
      <if test="isSpecialSlope != null">
        IS_SPECIAL_SLOPE,
      </if>
      <if test="designStartStake != null">
        DESIGN_START_STAKE,
      </if>
      <if test="designEndStake != null">
        DESIGN_END_STAKE,
      </if>
      <if test="redSlope != null">
        RED_SLOPE,
      </if>
      <if test="lineCode != null">
        LINE_CODE,
      </if>
      <if test="physicsCntrStake != null">
        PHYSICS_CNTR_STAKE,
      </if>
      <if test="logicCntrStake != null">
        LOGIC_CNTR_STAKE,
      </if>
      <if test="routeCode != null">
        ROUTE_CODE,
      </if>
      <if test="routeVersion != null">
        ROUTE_VERSION,
      </if>
      <if test="logicRampCntrStake != null">
        LOGIC_RAMP_CNTR_STAKE,
      </if>
      <if test="logicEndStake != null">
        LOGIC_END_STAKE,
      </if>
      <if test="logicStartStake != null">
        LOGIC_START_STAKE,
      </if>
      <if test="startStake != null">
        START_STAKE,
      </if>
      <if test="endStake != null">
        END_STAKE,
      </if>
      <if test="cntrStakeNum != null">
        CNTR_STAKE_NUM,
      </if>
      <if test="startOffset != null">
        START_OFFSET,
      </if>
      <if test="logicEndStrstake != null">
        LOGIC_END_STRSTAKE,
      </if>
      <if test="logicStartStrstake != null">
        LOGIC_START_STRSTAKE,
      </if>
      <if test="logicCntrStrstake != null">
        LOGIC_CNTR_STRSTAKE,
      </if>
      <if test="designStartStrstake != null">
        DESIGN_START_STRSTAKE,
      </if>
      <if test="designEndStrstake != null">
        DESIGN_END_STRSTAKE,
      </if>
      <if test="place != null">
        PLACE,
      </if>
      <if test="attr1 != null">
        ATTR1,
      </if>
      <if test="completionTime != null">
        COMPLETION_TIME,
      </if>
      <if test="status != null">
        "STATUS",
      </if>
      <if test="colStartLatitude != null">
        COL_START_LATITUDE,
      </if>
      <if test="colStartLongitude != null">
        COL_START_LONGITUDE,
      </if>
      <if test="colStartDate != null">
        COL_START_DATE,
      </if>
      <if test="colStartPerson != null">
        COL_START_PERSON,
      </if>
      <if test="colEndLatitude != null">
        COL_END_LATITUDE,
      </if>
      <if test="colEndLongitude != null">
        COL_END_LONGITUDE,
      </if>
      <if test="colEndDate != null">
        COL_END_DATE,
      </if>
      <if test="colEndPerson != null">
        COL_END_PERSON,
      </if>
      <if test="colStartAltitude != null">
        COL_START_ALTITUDE,
      </if>
      <if test="colEndAltitude != null">
        COL_END_ALTITUDE,
      </if>
      <if test="inEmergencyInvest != null">
        IN_EMERGENCY_INVEST,
      </if>
      <if test="typicalSectionType != null">
        TYPICAL_SECTION_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="slopeId != null">
        #{slopeId,jdbcType=VARCHAR},
      </if>
      <if test="slopeType != null">
        #{slopeType,jdbcType=VARCHAR},
      </if>
      <if test="slopeCode != null">
        #{slopeCode,jdbcType=VARCHAR},
      </if>
      <if test="slopeName != null">
        #{slopeName,jdbcType=VARCHAR},
      </if>
      <if test="maintainSeries != null">
        #{maintainSeries,jdbcType=VARCHAR},
      </if>
      <if test="isImportant != null">
        #{isImportant,jdbcType=VARCHAR},
      </if>
      <if test="slopePosition != null">
        #{slopePosition,jdbcType=VARCHAR},
      </if>
      <if test="slopeRate != null">
        #{slopeRate,jdbcType=VARCHAR},
      </if>
      <if test="slopeLevel != null">
        #{slopeLevel,jdbcType=DECIMAL},
      </if>
      <if test="slopeLength != null">
        #{slopeLength,jdbcType=DECIMAL},
      </if>
      <if test="lineId != null">
        #{lineId,jdbcType=VARCHAR},
      </if>
      <if test="lineDirection != null">
        #{lineDirection,jdbcType=VARCHAR},
      </if>
      <if test="startStakeOld != null">
        #{startStakeOld,jdbcType=DECIMAL},
      </if>
      <if test="endStakeOld != null">
        #{endStakeOld,jdbcType=DECIMAL},
      </if>
      <if test="slopeRetainingAndReinforce != null">
        #{slopeRetainingAndReinforce,jdbcType=VARCHAR},
      </if>
      <if test="completionTimeDate != null">
        #{completionTimeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="gisX != null">
        #{gisX,jdbcType=DECIMAL},
      </if>
      <if test="gisY != null">
        #{gisY,jdbcType=DECIMAL},
      </if>
      <if test="bdGisX != null">
        #{bdGisX,jdbcType=DECIMAL},
      </if>
      <if test="bdGisY != null">
        #{bdGisY,jdbcType=DECIMAL},
      </if>
      <if test="optOrgId != null">
        #{optOrgId,jdbcType=VARCHAR},
      </if>
      <if test="prjOrgId != null">
        #{prjOrgId,jdbcType=VARCHAR},
      </if>
      <if test="isMainLine != null">
        #{isMainLine,jdbcType=VARCHAR},
      </if>
      <if test="rpIntrvlId != null">
        #{rpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="cntrStakeNumOld != null">
        #{cntrStakeNumOld,jdbcType=DECIMAL},
      </if>
      <if test="evaluateDate != null">
        #{evaluateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="evaluationUnit != null">
        #{evaluationUnit,jdbcType=VARCHAR},
      </if>
      <if test="prjId != null">
        #{prjId,jdbcType=VARCHAR},
      </if>
      <if test="slopeTcGrade != null">
        #{slopeTcGrade,jdbcType=VARCHAR},
      </if>
      <if test="startOffsetOld != null">
        #{startOffsetOld,jdbcType=DECIMAL},
      </if>
      <if test="mainRampLineId != null">
        #{mainRampLineId,jdbcType=VARCHAR},
      </if>
      <if test="threadRpid != null">
        #{threadRpid,jdbcType=VARCHAR},
      </if>
      <if test="gisSx != null">
        #{gisSx,jdbcType=DECIMAL},
      </if>
      <if test="gisSy != null">
        #{gisSy,jdbcType=DECIMAL},
      </if>
      <if test="gisEx != null">
        #{gisEx,jdbcType=DECIMAL},
      </if>
      <if test="gisEy != null">
        #{gisEy,jdbcType=DECIMAL},
      </if>
      <if test="startRpIntrvlId != null">
        #{startRpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="endRpIntrvlId != null">
        #{endRpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="roadway != null">
        #{roadway,jdbcType=VARCHAR},
      </if>
      <if test="isSpecialSlope != null">
        #{isSpecialSlope,jdbcType=VARCHAR},
      </if>
      <if test="designStartStake != null">
        #{designStartStake,jdbcType=DECIMAL},
      </if>
      <if test="designEndStake != null">
        #{designEndStake,jdbcType=DECIMAL},
      </if>
      <if test="redSlope != null">
        #{redSlope,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="physicsCntrStake != null">
        #{physicsCntrStake,jdbcType=DECIMAL},
      </if>
      <if test="logicCntrStake != null">
        #{logicCntrStake,jdbcType=DECIMAL},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="routeVersion != null">
        #{routeVersion,jdbcType=VARCHAR},
      </if>
      <if test="logicRampCntrStake != null">
        #{logicRampCntrStake,jdbcType=DECIMAL},
      </if>
      <if test="logicEndStake != null">
        #{logicEndStake,jdbcType=DECIMAL},
      </if>
      <if test="logicStartStake != null">
        #{logicStartStake,jdbcType=DECIMAL},
      </if>
      <if test="startStake != null">
        #{startStake,jdbcType=DECIMAL},
      </if>
      <if test="endStake != null">
        #{endStake,jdbcType=DECIMAL},
      </if>
      <if test="cntrStakeNum != null">
        #{cntrStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="startOffset != null">
        #{startOffset,jdbcType=DECIMAL},
      </if>
      <if test="logicEndStrstake != null">
        #{logicEndStrstake,jdbcType=VARCHAR},
      </if>
      <if test="logicStartStrstake != null">
        #{logicStartStrstake,jdbcType=VARCHAR},
      </if>
      <if test="logicCntrStrstake != null">
        #{logicCntrStrstake,jdbcType=VARCHAR},
      </if>
      <if test="designStartStrstake != null">
        #{designStartStrstake,jdbcType=VARCHAR},
      </if>
      <if test="designEndStrstake != null">
        #{designEndStrstake,jdbcType=VARCHAR},
      </if>
      <if test="place != null">
        #{place,jdbcType=VARCHAR},
      </if>
      <if test="attr1 != null">
        #{attr1,jdbcType=VARCHAR},
      </if>
      <if test="completionTime != null">
        #{completionTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="colStartLatitude != null">
        #{colStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="colStartLongitude != null">
        #{colStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="colStartDate != null">
        #{colStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="colStartPerson != null">
        #{colStartPerson,jdbcType=VARCHAR},
      </if>
      <if test="colEndLatitude != null">
        #{colEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="colEndLongitude != null">
        #{colEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="colEndDate != null">
        #{colEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="colEndPerson != null">
        #{colEndPerson,jdbcType=VARCHAR},
      </if>
      <if test="colStartAltitude != null">
        #{colStartAltitude,jdbcType=DECIMAL},
      </if>
      <if test="colEndAltitude != null">
        #{colEndAltitude,jdbcType=DECIMAL},
      </if>
      <if test="inEmergencyInvest != null">
        #{inEmergencyInvest,jdbcType=VARCHAR},
      </if>
      <if test="typicalSectionType != null">
        #{typicalSectionType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsSlopeInfo">
    <!--@mbg.generated-->
    update HSMSDB.HSMS_SLOPE_INFO
    <set>
      <if test="slopeType != null">
        SLOPE_TYPE = #{slopeType,jdbcType=VARCHAR},
      </if>
      <if test="slopeCode != null">
        SLOPE_CODE = #{slopeCode,jdbcType=VARCHAR},
      </if>
      <if test="slopeName != null">
        SLOPE_NAME = #{slopeName,jdbcType=VARCHAR},
      </if>
      <if test="maintainSeries != null">
        MAINTAIN_SERIES = #{maintainSeries,jdbcType=VARCHAR},
      </if>
      <if test="isImportant != null">
        IS_IMPORTANT = #{isImportant,jdbcType=VARCHAR},
      </if>
      <if test="slopePosition != null">
        SLOPE_POSITION = #{slopePosition,jdbcType=VARCHAR},
      </if>
      <if test="slopeRate != null">
        SLOPE_RATE = #{slopeRate,jdbcType=VARCHAR},
      </if>
      <if test="slopeLevel != null">
        SLOPE_LEVEL = #{slopeLevel,jdbcType=DECIMAL},
      </if>
      <if test="slopeLength != null">
        SLOPE_LENGTH = #{slopeLength,jdbcType=DECIMAL},
      </if>
      <if test="lineId != null">
        LINE_ID = #{lineId,jdbcType=VARCHAR},
      </if>
      <if test="lineDirection != null">
        LINE_DIRECTION = #{lineDirection,jdbcType=VARCHAR},
      </if>
      <if test="startStakeOld != null">
        START_STAKE_OLD = #{startStakeOld,jdbcType=DECIMAL},
      </if>
      <if test="endStakeOld != null">
        END_STAKE_OLD = #{endStakeOld,jdbcType=DECIMAL},
      </if>
      <if test="slopeRetainingAndReinforce != null">
        SLOPE_RETAINING_AND_REINFORCE = #{slopeRetainingAndReinforce,jdbcType=VARCHAR},
      </if>
      <if test="completionTimeDate != null">
        COMPLETION_TIME_DATE = #{completionTimeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="gisX != null">
        GIS_X = #{gisX,jdbcType=DECIMAL},
      </if>
      <if test="gisY != null">
        GIS_Y = #{gisY,jdbcType=DECIMAL},
      </if>
      <if test="bdGisX != null">
        BD_GIS_X = #{bdGisX,jdbcType=DECIMAL},
      </if>
      <if test="bdGisY != null">
        BD_GIS_Y = #{bdGisY,jdbcType=DECIMAL},
      </if>
      <if test="optOrgId != null">
        OPT_ORG_ID = #{optOrgId,jdbcType=VARCHAR},
      </if>
      <if test="prjOrgId != null">
        PRJ_ORG_ID = #{prjOrgId,jdbcType=VARCHAR},
      </if>
      <if test="isMainLine != null">
        IS_MAIN_LINE = #{isMainLine,jdbcType=VARCHAR},
      </if>
      <if test="rpIntrvlId != null">
        RP_INTRVL_ID = #{rpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="cntrStakeNumOld != null">
        CNTR_STAKE_NUM_OLD = #{cntrStakeNumOld,jdbcType=DECIMAL},
      </if>
      <if test="evaluateDate != null">
        EVALUATE_DATE = #{evaluateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="evaluationUnit != null">
        EVALUATION_UNIT = #{evaluationUnit,jdbcType=VARCHAR},
      </if>
      <if test="prjId != null">
        PRJ_ID = #{prjId,jdbcType=VARCHAR},
      </if>
      <if test="slopeTcGrade != null">
        SLOPE_TC_GRADE = #{slopeTcGrade,jdbcType=VARCHAR},
      </if>
      <if test="startOffsetOld != null">
        START_OFFSET_OLD = #{startOffsetOld,jdbcType=DECIMAL},
      </if>
      <if test="mainRampLineId != null">
        MAIN_RAMP_LINE_ID = #{mainRampLineId,jdbcType=VARCHAR},
      </if>
      <if test="threadRpid != null">
        THREAD_RPID = #{threadRpid,jdbcType=VARCHAR},
      </if>
      <if test="gisSx != null">
        GIS_SX = #{gisSx,jdbcType=DECIMAL},
      </if>
      <if test="gisSy != null">
        GIS_SY = #{gisSy,jdbcType=DECIMAL},
      </if>
      <if test="gisEx != null">
        GIS_EX = #{gisEx,jdbcType=DECIMAL},
      </if>
      <if test="gisEy != null">
        GIS_EY = #{gisEy,jdbcType=DECIMAL},
      </if>
      <if test="startRpIntrvlId != null">
        START_RP_INTRVL_ID = #{startRpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="endRpIntrvlId != null">
        END_RP_INTRVL_ID = #{endRpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="roadway != null">
        ROADWAY = #{roadway,jdbcType=VARCHAR},
      </if>
      <if test="isSpecialSlope != null">
        IS_SPECIAL_SLOPE = #{isSpecialSlope,jdbcType=VARCHAR},
      </if>
      <if test="designStartStake != null">
        DESIGN_START_STAKE = #{designStartStake,jdbcType=DECIMAL},
      </if>
      <if test="designEndStake != null">
        DESIGN_END_STAKE = #{designEndStake,jdbcType=DECIMAL},
      </if>
      <if test="redSlope != null">
        RED_SLOPE = #{redSlope,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="physicsCntrStake != null">
        PHYSICS_CNTR_STAKE = #{physicsCntrStake,jdbcType=DECIMAL},
      </if>
      <if test="logicCntrStake != null">
        LOGIC_CNTR_STAKE = #{logicCntrStake,jdbcType=DECIMAL},
      </if>
      <if test="routeCode != null">
        ROUTE_CODE = #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="routeVersion != null">
        ROUTE_VERSION = #{routeVersion,jdbcType=VARCHAR},
      </if>
      <if test="logicRampCntrStake != null">
        LOGIC_RAMP_CNTR_STAKE = #{logicRampCntrStake,jdbcType=DECIMAL},
      </if>
      <if test="logicEndStake != null">
        LOGIC_END_STAKE = #{logicEndStake,jdbcType=DECIMAL},
      </if>
      <if test="logicStartStake != null">
        LOGIC_START_STAKE = #{logicStartStake,jdbcType=DECIMAL},
      </if>
      <if test="startStake != null">
        START_STAKE = #{startStake,jdbcType=DECIMAL},
      </if>
      <if test="endStake != null">
        END_STAKE = #{endStake,jdbcType=DECIMAL},
      </if>
      <if test="cntrStakeNum != null">
        CNTR_STAKE_NUM = #{cntrStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="startOffset != null">
        START_OFFSET = #{startOffset,jdbcType=DECIMAL},
      </if>
      <if test="logicEndStrstake != null">
        LOGIC_END_STRSTAKE = #{logicEndStrstake,jdbcType=VARCHAR},
      </if>
      <if test="logicStartStrstake != null">
        LOGIC_START_STRSTAKE = #{logicStartStrstake,jdbcType=VARCHAR},
      </if>
      <if test="logicCntrStrstake != null">
        LOGIC_CNTR_STRSTAKE = #{logicCntrStrstake,jdbcType=VARCHAR},
      </if>
      <if test="designStartStrstake != null">
        DESIGN_START_STRSTAKE = #{designStartStrstake,jdbcType=VARCHAR},
      </if>
      <if test="designEndStrstake != null">
        DESIGN_END_STRSTAKE = #{designEndStrstake,jdbcType=VARCHAR},
      </if>
      <if test="place != null">
        PLACE = #{place,jdbcType=VARCHAR},
      </if>
      <if test="attr1 != null">
        ATTR1 = #{attr1,jdbcType=VARCHAR},
      </if>
      <if test="completionTime != null">
        COMPLETION_TIME = #{completionTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        "STATUS" = #{status,jdbcType=VARCHAR},
      </if>
      <if test="colStartLatitude != null">
        COL_START_LATITUDE = #{colStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="colStartLongitude != null">
        COL_START_LONGITUDE = #{colStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="colStartDate != null">
        COL_START_DATE = #{colStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="colStartPerson != null">
        COL_START_PERSON = #{colStartPerson,jdbcType=VARCHAR},
      </if>
      <if test="colEndLatitude != null">
        COL_END_LATITUDE = #{colEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="colEndLongitude != null">
        COL_END_LONGITUDE = #{colEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="colEndDate != null">
        COL_END_DATE = #{colEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="colEndPerson != null">
        COL_END_PERSON = #{colEndPerson,jdbcType=VARCHAR},
      </if>
      <if test="colStartAltitude != null">
        COL_START_ALTITUDE = #{colStartAltitude,jdbcType=DECIMAL},
      </if>
      <if test="colEndAltitude != null">
        COL_END_ALTITUDE = #{colEndAltitude,jdbcType=DECIMAL},
      </if>
      <if test="inEmergencyInvest != null">
        IN_EMERGENCY_INVEST = #{inEmergencyInvest,jdbcType=VARCHAR},
      </if>
      <if test="typicalSectionType != null">
        TYPICAL_SECTION_TYPE = #{typicalSectionType,jdbcType=VARCHAR},
      </if>
    </set>
    where SLOPE_ID = #{slopeId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsSlopeInfo">
    <!--@mbg.generated-->
    update HSMSDB.HSMS_SLOPE_INFO
    set SLOPE_TYPE = #{slopeType,jdbcType=VARCHAR},
      SLOPE_CODE = #{slopeCode,jdbcType=VARCHAR},
      SLOPE_NAME = #{slopeName,jdbcType=VARCHAR},
      MAINTAIN_SERIES = #{maintainSeries,jdbcType=VARCHAR},
      IS_IMPORTANT = #{isImportant,jdbcType=VARCHAR},
      SLOPE_POSITION = #{slopePosition,jdbcType=VARCHAR},
      SLOPE_RATE = #{slopeRate,jdbcType=VARCHAR},
      SLOPE_LEVEL = #{slopeLevel,jdbcType=DECIMAL},
      SLOPE_LENGTH = #{slopeLength,jdbcType=DECIMAL},
      LINE_ID = #{lineId,jdbcType=VARCHAR},
      LINE_DIRECTION = #{lineDirection,jdbcType=VARCHAR},
      START_STAKE_OLD = #{startStakeOld,jdbcType=DECIMAL},
      END_STAKE_OLD = #{endStakeOld,jdbcType=DECIMAL},
      SLOPE_RETAINING_AND_REINFORCE = #{slopeRetainingAndReinforce,jdbcType=VARCHAR},
      COMPLETION_TIME_DATE = #{completionTimeDate,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      IS_DELETED = #{isDeleted,jdbcType=VARCHAR},
      GIS_X = #{gisX,jdbcType=DECIMAL},
      GIS_Y = #{gisY,jdbcType=DECIMAL},
      BD_GIS_X = #{bdGisX,jdbcType=DECIMAL},
      BD_GIS_Y = #{bdGisY,jdbcType=DECIMAL},
      OPT_ORG_ID = #{optOrgId,jdbcType=VARCHAR},
      PRJ_ORG_ID = #{prjOrgId,jdbcType=VARCHAR},
      IS_MAIN_LINE = #{isMainLine,jdbcType=VARCHAR},
      RP_INTRVL_ID = #{rpIntrvlId,jdbcType=VARCHAR},
      CNTR_STAKE_NUM_OLD = #{cntrStakeNumOld,jdbcType=DECIMAL},
      EVALUATE_DATE = #{evaluateDate,jdbcType=TIMESTAMP},
      EVALUATION_UNIT = #{evaluationUnit,jdbcType=VARCHAR},
      PRJ_ID = #{prjId,jdbcType=VARCHAR},
      SLOPE_TC_GRADE = #{slopeTcGrade,jdbcType=VARCHAR},
      START_OFFSET_OLD = #{startOffsetOld,jdbcType=DECIMAL},
      MAIN_RAMP_LINE_ID = #{mainRampLineId,jdbcType=VARCHAR},
      THREAD_RPID = #{threadRpid,jdbcType=VARCHAR},
      GIS_SX = #{gisSx,jdbcType=DECIMAL},
      GIS_SY = #{gisSy,jdbcType=DECIMAL},
      GIS_EX = #{gisEx,jdbcType=DECIMAL},
      GIS_EY = #{gisEy,jdbcType=DECIMAL},
      START_RP_INTRVL_ID = #{startRpIntrvlId,jdbcType=VARCHAR},
      END_RP_INTRVL_ID = #{endRpIntrvlId,jdbcType=VARCHAR},
      ROADWAY = #{roadway,jdbcType=VARCHAR},
      IS_SPECIAL_SLOPE = #{isSpecialSlope,jdbcType=VARCHAR},
      DESIGN_START_STAKE = #{designStartStake,jdbcType=DECIMAL},
      DESIGN_END_STAKE = #{designEndStake,jdbcType=DECIMAL},
      RED_SLOPE = #{redSlope,jdbcType=VARCHAR},
      LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      PHYSICS_CNTR_STAKE = #{physicsCntrStake,jdbcType=DECIMAL},
      LOGIC_CNTR_STAKE = #{logicCntrStake,jdbcType=DECIMAL},
      ROUTE_CODE = #{routeCode,jdbcType=VARCHAR},
      ROUTE_VERSION = #{routeVersion,jdbcType=VARCHAR},
      LOGIC_RAMP_CNTR_STAKE = #{logicRampCntrStake,jdbcType=DECIMAL},
      LOGIC_END_STAKE = #{logicEndStake,jdbcType=DECIMAL},
      LOGIC_START_STAKE = #{logicStartStake,jdbcType=DECIMAL},
      START_STAKE = #{startStake,jdbcType=DECIMAL},
      END_STAKE = #{endStake,jdbcType=DECIMAL},
      CNTR_STAKE_NUM = #{cntrStakeNum,jdbcType=DECIMAL},
      START_OFFSET = #{startOffset,jdbcType=DECIMAL},
      LOGIC_END_STRSTAKE = #{logicEndStrstake,jdbcType=VARCHAR},
      LOGIC_START_STRSTAKE = #{logicStartStrstake,jdbcType=VARCHAR},
      LOGIC_CNTR_STRSTAKE = #{logicCntrStrstake,jdbcType=VARCHAR},
      DESIGN_START_STRSTAKE = #{designStartStrstake,jdbcType=VARCHAR},
      DESIGN_END_STRSTAKE = #{designEndStrstake,jdbcType=VARCHAR},
      PLACE = #{place,jdbcType=VARCHAR},
      ATTR1 = #{attr1,jdbcType=VARCHAR},
      COMPLETION_TIME = #{completionTime,jdbcType=VARCHAR},
      "STATUS" = #{status,jdbcType=VARCHAR},
      COL_START_LATITUDE = #{colStartLatitude,jdbcType=DECIMAL},
      COL_START_LONGITUDE = #{colStartLongitude,jdbcType=DECIMAL},
      COL_START_DATE = #{colStartDate,jdbcType=TIMESTAMP},
      COL_START_PERSON = #{colStartPerson,jdbcType=VARCHAR},
      COL_END_LATITUDE = #{colEndLatitude,jdbcType=DECIMAL},
      COL_END_LONGITUDE = #{colEndLongitude,jdbcType=DECIMAL},
      COL_END_DATE = #{colEndDate,jdbcType=TIMESTAMP},
      COL_END_PERSON = #{colEndPerson,jdbcType=VARCHAR},
      COL_START_ALTITUDE = #{colStartAltitude,jdbcType=DECIMAL},
      COL_END_ALTITUDE = #{colEndAltitude,jdbcType=DECIMAL},
      IN_EMERGENCY_INVEST = #{inEmergencyInvest,jdbcType=VARCHAR},
      TYPICAL_SECTION_TYPE = #{typicalSectionType,jdbcType=VARCHAR}
    where SLOPE_ID = #{slopeId,jdbcType=VARCHAR}
  </update>
</mapper>