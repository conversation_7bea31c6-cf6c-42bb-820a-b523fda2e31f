<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridgeDecision.mapper.BridgeSuggestTMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgeSuggestT">
    <!--@mbg.generated-->
    <!--@Table BRIDGE_SUGGEST_T-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ORDERS" jdbcType="DECIMAL" property="orders" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LINE_NAME" jdbcType="VARCHAR" property="lineName" />
    <result column="STAKE" jdbcType="VARCHAR" property="stake" />
    <result column="PREVENT_COUNT" jdbcType="DECIMAL" property="preventCount" />
    <result column="REPAIR_COUNT" jdbcType="DECIMAL" property="repairCount" />
    <result column="PREVENT_QUANTITY" jdbcType="DECIMAL" property="preventQuantity" />
    <result column="REPAIR_QUANTITY" jdbcType="DECIMAL" property="repairQuantity" />
    <result column="PREVENT_FEE" jdbcType="DECIMAL" property="preventFee" />
    <result column="REPAIR_FEE" jdbcType="DECIMAL" property="repairFee" />
    <result column="TOTAL_FEE" jdbcType="DECIMAL" property="totalFee" />
    <result column="SECOND_ORG_NAME" jdbcType="VARCHAR" property="secondOrgName" />
    <result column="SECOND_ORG_CODE" jdbcType="VARCHAR" property="secondOrgCode" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="IMPORT_TIME" jdbcType="TIMESTAMP" property="importTime" />
    <result column="YEAR" jdbcType="DECIMAL" property="year" />
    <result column="TARGET" jdbcType="DECIMAL" property="target" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORDERS, ORG_NAME, ORG_CODE, LINE_CODE, LINE_NAME, STAKE, PREVENT_COUNT, REPAIR_COUNT, 
    PREVENT_QUANTITY, REPAIR_QUANTITY, PREVENT_FEE, REPAIR_FEE, TOTAL_FEE, SECOND_ORG_NAME, 
    SECOND_ORG_CODE, REMARK, IMPORT_TIME, "YEAR", TARGET
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgeSuggestT">
    <!--@mbg.generated-->
    insert into BRIDGE_SUGGEST_T (ID, ORDERS, ORG_NAME, 
      ORG_CODE, LINE_CODE, LINE_NAME, 
      STAKE, PREVENT_COUNT, REPAIR_COUNT, 
      PREVENT_QUANTITY, REPAIR_QUANTITY, PREVENT_FEE, 
      REPAIR_FEE, TOTAL_FEE, SECOND_ORG_NAME, 
      SECOND_ORG_CODE, REMARK, IMPORT_TIME, 
      "YEAR", TARGET)
    values (#{id,jdbcType=VARCHAR}, #{orders,jdbcType=DECIMAL}, #{orgName,jdbcType=VARCHAR}, 
      #{orgCode,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{lineName,jdbcType=VARCHAR}, 
      #{stake,jdbcType=VARCHAR}, #{preventCount,jdbcType=DECIMAL}, #{repairCount,jdbcType=DECIMAL}, 
      #{preventQuantity,jdbcType=DECIMAL}, #{repairQuantity,jdbcType=DECIMAL}, #{preventFee,jdbcType=DECIMAL}, 
      #{repairFee,jdbcType=DECIMAL}, #{totalFee,jdbcType=DECIMAL}, #{secondOrgName,jdbcType=VARCHAR}, 
      #{secondOrgCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{importTime,jdbcType=TIMESTAMP}, 
      #{year,jdbcType=DECIMAL}, #{target,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgeSuggestT">
    <!--@mbg.generated-->
    insert into BRIDGE_SUGGEST_T
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="orders != null">
        ORDERS,
      </if>
      <if test="orgName != null">
        ORG_NAME,
      </if>
      <if test="orgCode != null">
        ORG_CODE,
      </if>
      <if test="lineCode != null">
        LINE_CODE,
      </if>
      <if test="lineName != null">
        LINE_NAME,
      </if>
      <if test="stake != null">
        STAKE,
      </if>
      <if test="preventCount != null">
        PREVENT_COUNT,
      </if>
      <if test="repairCount != null">
        REPAIR_COUNT,
      </if>
      <if test="preventQuantity != null">
        PREVENT_QUANTITY,
      </if>
      <if test="repairQuantity != null">
        REPAIR_QUANTITY,
      </if>
      <if test="preventFee != null">
        PREVENT_FEE,
      </if>
      <if test="repairFee != null">
        REPAIR_FEE,
      </if>
      <if test="totalFee != null">
        TOTAL_FEE,
      </if>
      <if test="secondOrgName != null">
        SECOND_ORG_NAME,
      </if>
      <if test="secondOrgCode != null">
        SECOND_ORG_CODE,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="importTime != null">
        IMPORT_TIME,
      </if>
      <if test="year != null">
        "YEAR",
      </if>
      <if test="target != null">
        TARGET,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orders != null">
        #{orders,jdbcType=DECIMAL},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="lineName != null">
        #{lineName,jdbcType=VARCHAR},
      </if>
      <if test="stake != null">
        #{stake,jdbcType=VARCHAR},
      </if>
      <if test="preventCount != null">
        #{preventCount,jdbcType=DECIMAL},
      </if>
      <if test="repairCount != null">
        #{repairCount,jdbcType=DECIMAL},
      </if>
      <if test="preventQuantity != null">
        #{preventQuantity,jdbcType=DECIMAL},
      </if>
      <if test="repairQuantity != null">
        #{repairQuantity,jdbcType=DECIMAL},
      </if>
      <if test="preventFee != null">
        #{preventFee,jdbcType=DECIMAL},
      </if>
      <if test="repairFee != null">
        #{repairFee,jdbcType=DECIMAL},
      </if>
      <if test="totalFee != null">
        #{totalFee,jdbcType=DECIMAL},
      </if>
      <if test="secondOrgName != null">
        #{secondOrgName,jdbcType=VARCHAR},
      </if>
      <if test="secondOrgCode != null">
        #{secondOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="importTime != null">
        #{importTime,jdbcType=TIMESTAMP},
      </if>
      <if test="year != null">
        #{year,jdbcType=DECIMAL},
      </if>
      <if test="target != null">
        #{target,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
</mapper>