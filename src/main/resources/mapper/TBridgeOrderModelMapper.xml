<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeOrderModelMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderModel">
    <!--@mbg.generated-->
    <!--@Table T_BRIDGE_ORDER_MODEL-->
    <result column="MODEL_ID" jdbcType="VARCHAR" property="modelId" />
    <result column="MODEL_NAME" jdbcType="VARCHAR" property="modelName" />
    <result column="RC_PRICE" jdbcType="FLOAT" property="rcPrice" />
    <result column="PREVENT_PRICE" jdbcType="FLOAT" property="preventPrice" />
    <result column="MODEL_TYPE" jdbcType="DECIMAL" property="modelType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    MODEL_ID, MODEL_NAME, RC_PRICE, PREVENT_PRICE, MODEL_TYPE
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderModel">
    <!--@mbg.generated-->
    insert into T_BRIDGE_ORDER_MODEL (MODEL_ID, MODEL_NAME, RC_PRICE, 
      PREVENT_PRICE, MODEL_TYPE)
    values (#{modelId,jdbcType=VARCHAR}, #{modelName,jdbcType=VARCHAR}, #{rcPrice,jdbcType=FLOAT}, 
      #{preventPrice,jdbcType=FLOAT}, #{modelType,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderModel">
    <!--@mbg.generated-->
    insert into T_BRIDGE_ORDER_MODEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        MODEL_ID,
      </if>
      <if test="modelName != null">
        MODEL_NAME,
      </if>
      <if test="rcPrice != null">
        RC_PRICE,
      </if>
      <if test="preventPrice != null">
        PREVENT_PRICE,
      </if>
      <if test="modelType != null">
        MODEL_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="rcPrice != null">
        #{rcPrice,jdbcType=FLOAT},
      </if>
      <if test="preventPrice != null">
        #{preventPrice,jdbcType=FLOAT},
      </if>
      <if test="modelType != null">
        #{modelType,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
</mapper>