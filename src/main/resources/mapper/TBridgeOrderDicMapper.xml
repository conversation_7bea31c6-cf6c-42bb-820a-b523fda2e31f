<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeOrderDicMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderDic">
    <!--@mbg.generated-->
    <!--@Table T_BRIDGE_ORDER_DIC-->
    <result column="DIC_ID" jdbcType="DECIMAL" property="dicId" />
    <result column="DIC_NAME" jdbcType="VARCHAR" property="dicName" />
    <result column="P_DIC_ID" jdbcType="DECIMAL" property="pDicId" />
    <result column="DIC_TYPE" jdbcType="DECIMAL" property="dicType" />
    <result column="MEASURE_UNIT" jdbcType="DECIMAL" property="measureUnit" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DIC_ID, DIC_NAME, P_DIC_ID, DIC_TYPE, MEASURE_UNIT
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderDic">
    <!--@mbg.generated-->
    insert into T_BRIDGE_ORDER_DIC (DIC_ID, DIC_NAME, P_DIC_ID, 
      DIC_TYPE, MEASURE_UNIT)
    values (#{dicId,jdbcType=DECIMAL}, #{dicName,jdbcType=VARCHAR}, #{pDicId,jdbcType=DECIMAL}, 
      #{dicType,jdbcType=DECIMAL}, #{measureUnit,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderDic">
    <!--@mbg.generated-->
    insert into T_BRIDGE_ORDER_DIC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dicId != null">
        DIC_ID,
      </if>
      <if test="dicName != null">
        DIC_NAME,
      </if>
      <if test="pDicId != null">
        P_DIC_ID,
      </if>
      <if test="dicType != null">
        DIC_TYPE,
      </if>
      <if test="measureUnit != null">
        MEASURE_UNIT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dicId != null">
        #{dicId,jdbcType=DECIMAL},
      </if>
      <if test="dicName != null">
        #{dicName,jdbcType=VARCHAR},
      </if>
      <if test="pDicId != null">
        #{pDicId,jdbcType=DECIMAL},
      </if>
      <if test="dicType != null">
        #{dicType,jdbcType=DECIMAL},
      </if>
      <if test="measureUnit != null">
        #{measureUnit,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
</mapper>