<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.clearObstacles.mapper.ClearObstaclesMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.clearObstacles.entity.ClearObstacles">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="stake" column="STAKE" jdbcType="DECIMAL"/>
            <result property="times" column="TIMES" jdbcType="DECIMAL"/>
            <result property="beforeImages" column="BEFORE_IMAGES" jdbcType="VARCHAR"/>
            <result property="afterImages" column="AFTER_IMAGES" jdbcType="VARCHAR"/>
            <result property="description" column="DESCRIPTION" jdbcType="VARCHAR"/>
            <result property="orgCode" column="ORG_CODE" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="createUser" column="CREATE_USER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,LINE_CODE,STAKE,
        TIMES,BEFORE_IMAGES,AFTER_IMAGES,
        DESCRIPTION,ORG_CODE,CREATE_TIME,
        CREATE_USER
    </sql>
    <select id="selectSendUsers"
            resultType="com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesSendUserDto">
        select a.id,u.USER_NAME as send_user_name from MEMSDB.CLEAR_OBSTACLES a
              join memsdb.DM_TASK_ACCPT_DETAIL b on a.ID = b.MTASK_ACCPT_DTL_ID
              join memsdb.DM_TASK_ACCPT c on b.MTASK_ACCPT_ID = c.MTASK_ACCPT_ID
              join gdgs.FW_RIGHT_USER u on c.CREATE_USER_ID = u.USER_CODE
                where a.id in
        <foreach collection="idList" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getClearObstaclesStat"
            resultType="com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesStatDto">
        select o.ORG_NAME,count(b.MTASK_ACCPT_DTL_ID) as num from MEMSDB.CLEAR_OBSTACLES a
          join memsdb.DM_TASK_ACCPT_DETAIL b on a.ID = b.MTASK_ACCPT_DTL_ID
          join memsdb.DM_TASK_ACCPT c on b.MTASK_ACCPT_ID = c.MTASK_ACCPT_ID
          join gdgs.FW_RIGHT_USER u on c.CREATE_USER_ID = u.USER_CODE
          join gdgs.FW_RIGHT_ORG o on a.ORG_CODE = o.ORG_CODE
          where a.ORG_CODE in
        <foreach collection="orgCodes" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
          and to_char(a.UPDATE_TIME,'yyyy-MM') = #{month}
           group by  o.ORG_NAME
    </select>
</mapper>
