<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dic.mapper.BaseFileEntityMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dic.entity.BaseFileEntity">
            <id property="fileEntityId" column="FILE_ENTITY_ID" jdbcType="VARCHAR"/>
            <result property="fileEntityTag" column="FILE_ENTITY_TAG" jdbcType="VARCHAR"/>
            <result property="fileEntityCode" column="FILE_ENTITY_CODE" jdbcType="VARCHAR"/>
            <result property="fileEntityName" column="FILE_ENTITY_NAME" jdbcType="VARCHAR"/>
            <result property="fileEntityDuty" column="FILE_ENTITY_DUTY" jdbcType="VARCHAR"/>
            <result property="fileEntityDate" column="FILE_ENTITY_DATE" jdbcType="VARCHAR"/>
            <result property="fileEntityUser" column="FILE_ENTITY_USER" jdbcType="VARCHAR"/>
            <result property="fileEntityDept" column="FILE_ENTITY_DEPT" jdbcType="VARCHAR"/>
            <result property="creaSysdate" column="CREA_SYSDATE" jdbcType="VARCHAR"/>
            <result property="fileEntityPath" column="FILE_ENTITY_PATH" jdbcType="VARCHAR"/>
            <result property="fileEntityFormat" column="FILE_ENTITY_FORMAT" jdbcType="VARCHAR"/>
            <result property="fileEntitySize" column="FILE_ENTITY_SIZE" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="fileEntityMd5" column="FILE_ENTITY_MD5" jdbcType="VARCHAR"/>
            <result property="fileEntityVersionNum" column="FILE_ENTITY_VERSION_NUM" jdbcType="VARCHAR"/>
            <result property="isSwfConversion" column="IS_SWF_CONVERSION" jdbcType="VARCHAR"/>
            <result property="isPdfConversion" column="IS_PDF_CONVERSION" jdbcType="VARCHAR"/>
            <result property="fileEntityTitle" column="FILE_ENTITY_TITLE" jdbcType="VARCHAR"/>
            <result property="mgFileId" column="MG_FILE_ID" jdbcType="VARCHAR"/>
            <result property="mgIsExists" column="MG_IS_EXISTS" jdbcType="VARCHAR"/>
            <result property="mgUserCode" column="MG_USER_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FILE_ENTITY_ID,FILE_ENTITY_TAG,FILE_ENTITY_CODE,
        FILE_ENTITY_NAME,FILE_ENTITY_DUTY,FILE_ENTITY_DATE,
        FILE_ENTITY_USER,FILE_ENTITY_DEPT,CREA_SYSDATE,
        FILE_ENTITY_PATH,FILE_ENTITY_FORMAT,FILE_ENTITY_SIZE,
        REMARK,FILE_ENTITY_MD5,FILE_ENTITY_VERSION_NUM,
        IS_SWF_CONVERSION,IS_PDF_CONVERSION,FILE_ENTITY_TITLE,
        MG_FILE_ID,MG_IS_EXISTS,MG_USER_CODE
    </sql>
</mapper>
