<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintainbase.mapper.BaseMaintainbaseMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseMaintainbase">
    <!--@mbg.generated-->
    <!--@Table BASE_MAINTAINBASE-->
    <result column="MAINTAINBASE_ID" jdbcType="VARCHAR" property="maintainbaseId" />
    <result column="MAINTAINBASE_NAME" jdbcType="VARCHAR" property="maintainbaseName" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="PRINCIPAL" jdbcType="VARCHAR" property="principal" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="NEARCRK" jdbcType="VARCHAR" property="nearcrk" />
    <result column="X" jdbcType="VARCHAR" property="x" />
    <result column="Y" jdbcType="VARCHAR" property="y" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="BASE_TYPE" jdbcType="VARCHAR" property="baseType" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="TELPHONE" jdbcType="VARCHAR" property="telphone" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="MORE_NAME" jdbcType="VARCHAR" property="moreName" />
    <result column="STAKE" jdbcType="FLOAT" property="stake" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
    <result column="DISTANCE" jdbcType="VARCHAR" property="distance" />
    <result column="BELONG_MAINTAIN_AREA" jdbcType="VARCHAR" property="belongMaintainArea" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="GIS_X" jdbcType="VARCHAR" property="gisX" />
    <result column="GIS_Y" jdbcType="VARCHAR" property="gisY" />
    <result column="BELONG_LINE" jdbcType="VARCHAR" property="belongLine" />
    <result column="SER_ORGNAME" jdbcType="VARCHAR" property="serOrgname" />
    <result column="WRITE_DATE" jdbcType="TIMESTAMP" property="writeDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    MAINTAINBASE_ID, MAINTAINBASE_NAME, ORG_ID, PRINCIPAL, ADDRESS, NEARCRK, X, Y, REMARK, 
    BASE_TYPE, LINE_CODE, TELPHONE, AREA, MORE_NAME, STAKE, ROUTE_CODE, DISTANCE, BELONG_MAINTAIN_AREA, 
    HEIGHT, GIS_X, GIS_Y, BELONG_LINE, SER_ORGNAME, WRITE_DATE
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseMaintainbase">
    <!--@mbg.generated-->
    insert into onemap.BASE_MAINTAINBASE (MAINTAINBASE_ID, MAINTAINBASE_NAME,
      ORG_ID, PRINCIPAL, ADDRESS, 
      NEARCRK, X, Y, REMARK, 
      BASE_TYPE, LINE_CODE, TELPHONE, 
      AREA, MORE_NAME, STAKE, 
      ROUTE_CODE, DISTANCE, BELONG_MAINTAIN_AREA, 
      HEIGHT, GIS_X, GIS_Y, 
      BELONG_LINE, SER_ORGNAME, WRITE_DATE
      )
    values (#{maintainbaseId,jdbcType=VARCHAR}, #{maintainbaseName,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=VARCHAR}, #{principal,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{nearcrk,jdbcType=VARCHAR}, #{x,jdbcType=VARCHAR}, #{y,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{baseType,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{telphone,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{moreName,jdbcType=VARCHAR}, #{stake,jdbcType=FLOAT}, 
      #{routeCode,jdbcType=VARCHAR}, #{distance,jdbcType=VARCHAR}, #{belongMaintainArea,jdbcType=VARCHAR}, 
      #{height,jdbcType=VARCHAR}, #{gisX,jdbcType=VARCHAR}, #{gisY,jdbcType=VARCHAR}, 
      #{belongLine,jdbcType=VARCHAR}, #{serOrgname,jdbcType=VARCHAR}, #{writeDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseMaintainbase">
    <!--@mbg.generated-->
    insert into onemap.BASE_MAINTAINBASE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="maintainbaseId != null">
        MAINTAINBASE_ID,
      </if>
      <if test="maintainbaseName != null">
        MAINTAINBASE_NAME,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="principal != null">
        PRINCIPAL,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="nearcrk != null">
        NEARCRK,
      </if>
      <if test="x != null">
        X,
      </if>
      <if test="y != null">
        Y,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="baseType != null">
        BASE_TYPE,
      </if>
      <if test="lineCode != null">
        LINE_CODE,
      </if>
      <if test="telphone != null">
        TELPHONE,
      </if>
      <if test="area != null">
        AREA,
      </if>
      <if test="moreName != null">
        MORE_NAME,
      </if>
      <if test="stake != null">
        STAKE,
      </if>
      <if test="routeCode != null">
        ROUTE_CODE,
      </if>
      <if test="distance != null">
        DISTANCE,
      </if>
      <if test="belongMaintainArea != null">
        BELONG_MAINTAIN_AREA,
      </if>
      <if test="height != null">
        HEIGHT,
      </if>
      <if test="gisX != null">
        GIS_X,
      </if>
      <if test="gisY != null">
        GIS_Y,
      </if>
      <if test="belongLine != null">
        BELONG_LINE,
      </if>
      <if test="serOrgname != null">
        SER_ORGNAME,
      </if>
      <if test="writeDate != null">
        WRITE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="maintainbaseId != null">
        #{maintainbaseId,jdbcType=VARCHAR},
      </if>
      <if test="maintainbaseName != null">
        #{maintainbaseName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="principal != null">
        #{principal,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="nearcrk != null">
        #{nearcrk,jdbcType=VARCHAR},
      </if>
      <if test="x != null">
        #{x,jdbcType=VARCHAR},
      </if>
      <if test="y != null">
        #{y,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="baseType != null">
        #{baseType,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="telphone != null">
        #{telphone,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="moreName != null">
        #{moreName,jdbcType=VARCHAR},
      </if>
      <if test="stake != null">
        #{stake,jdbcType=FLOAT},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=VARCHAR},
      </if>
      <if test="belongMaintainArea != null">
        #{belongMaintainArea,jdbcType=VARCHAR},
      </if>
      <if test="height != null">
        #{height,jdbcType=VARCHAR},
      </if>
      <if test="gisX != null">
        #{gisX,jdbcType=VARCHAR},
      </if>
      <if test="gisY != null">
        #{gisY,jdbcType=VARCHAR},
      </if>
      <if test="belongLine != null">
        #{belongLine,jdbcType=VARCHAR},
      </if>
      <if test="serOrgname != null">
        #{serOrgname,jdbcType=VARCHAR},
      </if>
      <if test="writeDate != null">
        #{writeDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="getMaintainBasePage" resultType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseMaintainbase">
    select * from ONEMAP.BASE_MAINTAINBASE t
    where
    exists(
    select 1 from GDGS.FW_RIGHT_ORG p
    inner join GDGS.FW_RIGHT_ORG s on p.id=s.PARENT_ID and p.ORG_LEVEL is not null and s.ORG_LEVEL is not null
    inner join GDGS.FW_RIGHT_ORG c on s.id=c.PARENT_ID  and c.ORG_LEVEL is not null and s.ORG_LEVEL is not null
    where (p.id=#{orgId}
    or s.id=#{orgId}
    or c.id=#{orgId} ) and c.id=t.ORG_ID)
    <if test="baseType != null and baseType!=''">
      and t.BASE_TYPE=#{baseType}
    </if> order by t.SER_ORGNAME, t.ORG_ID,t.MAINTAINBASE_ID
  </select>

  <select id="getMaintainBaseStatistic"
            resultType="com.hualu.highwaymaintenance.module.maintainbase.domain.MaintainBaseTj">
    with orgIds as (select u.ID
                    from GDGS.FW_RIGHT_ORG u
                    where u.ID = #{orgId}
                    union
                    select c.ID
                    from GDGS.FW_RIGHT_ORG c
                    where c.PARENT_ID = #{orgId}
                      and c.IS_DELETED = 0
                      and c.IS_ENABLE = 1
                    union
                    select p.ID
                    from GDGS.FW_RIGHT_ORG p
                    where p.PARENT_ID in (select c.ID
                                          from GDGS.FW_RIGHT_ORG c
                                          where c.PARENT_ID = #{orgId}
                                            and c.IS_DELETED = 0
                                            and c.IS_ENABLE = 1)
                      and p.IS_ENABLE = 1
                      and p.IS_DELETED = 0)
    select  p.ID as ORG_ID,p.ORG_NAME as ORG_NAME,f.ID as CHILD_ORG_ID,f.org_name as CHILD_ORG_NAME,sum(decode(c.BASE_TYPE,'1',1,0)) as zhNum,sum(decode(c.BASE_TYPE,'2',1,0)) as rcNum,
            ( select sum(d.LINE_MILEAGE) from ONEMAP.BASE_ROUTE_DTL d where d.OPRT_ORG_CODE=f.id ) as line_length
    from
      GDGS.FW_RIGHT_ORG f   left join  ONEMAP.BASE_MAINTAINBASE c on f.ID=c.ORG_ID

                            inner join GDGS.FW_RIGHT_ORG p on f.PARENT_ID=p.ID
    where   f.id in
            (select * from orgIds) and f.ORG_LEVEL=3 and f.IS_OPRTORG=1
      and p.id!='90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
    and f.ORG_GRP_FLAG=1
    group by  p.ID,p.ORG_NAME,f.ID,f.org_name
    </select>
</mapper>