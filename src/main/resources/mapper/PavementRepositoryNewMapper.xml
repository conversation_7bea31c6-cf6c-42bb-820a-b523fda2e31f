<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.PavementRepositoryNewMapper">

  <select id="getPavementRepository" resultType="com.hualu.highwaymaintenance.module.national.entity.PavementRepositoryNew">
    select id,ROWNUM orders, oprt_org_code, oprt_org_name, line_code, line_name, stake, lane, maintenance_type, mileage, fee, second_org_code, second_org_name, remark, year, import_date, import_version_id, order_number, unit_id, total_fee, version, line_direct, preventmileage, preventfee, repairmileage, repairfee, rn from
    (select m.* from (
    select
    br.*,
    sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.MILEAGE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) preventMileage,
    sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.FEE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) preventFee,
    sum(case when br.MAINTENANCE_TYPE = 'repair' then br.MILEAGE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repairMileage,
    sum(case when br.MAINTENANCE_TYPE = 'repair' then br.FEE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repairFee,
    row_number() over (partition by br.UNIT_ID order by br.UNIT_ID) rn
    from MEMSDB.PAVEMENT_REPOSITORY_NEW br
    <where>
      <if test="secondCompanyOrg != null and secondCompanyOrg != ''">
        br.SECOND_ORG_CODE = #{secondCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="threeCompanyOrg != null and threeCompanyOrg != ''">
        and br.OPRT_ORG_CODE = #{threeCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="startYear != null">
        and br.YEAR >= #{startYear,jdbcType=INTEGER}
      </if>

      <if test="endYear != null">
        and br.YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
      </if>

      <if test="userOrgId != null and userOrgId != ''">
        and exists(
        select 1 from (select ORG_CODE
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{userOrgId,jdbcType=VARCHAR}
        connect by prior o.ID = o.PARENT_ID) a
        where a.ORG_CODE = br.OPRT_ORG_CODE)
      </if>
      <if test="version != null and version != ''">
        and br.version is null
      </if>

      <if test="target != null">
        and br.target = #{target,jdbcType=INTEGER}
      </if>

      <if test="lineType != null and lineType != ''">
        and br.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
      </if>
    </where>
    ) m where rn = 1 order by ORDER_NUMBER)
  </select>

  <select id="getPavementRepositoryPage" resultType="com.hualu.highwaymaintenance.module.national.entity.PavementRepositoryNew">
    select id,ROWNUM orders, oprt_org_code, oprt_org_name, line_code, line_name, stake, lane, maintenance_type, mileage, fee, second_org_code, second_org_name, remark, year, import_date, import_version_id, order_number, unit_id, total_fee, version, line_direct, preventmileage, preventfee, repairmileage, repairfee, rn from
    (select m.* from (
    select
    br.*,
    sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.MILEAGE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) preventMileage,
    sum(case when br.MAINTENANCE_TYPE = 'prevent' then br.FEE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) preventFee,
    sum(case when br.MAINTENANCE_TYPE = 'repair' then br.MILEAGE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repairMileage,
    sum(case when br.MAINTENANCE_TYPE = 'repair' then br.FEE else 0 end)
    over ( partition by br.UNIT_ID order by br.UNIT_ID desc) repairFee,
    row_number() over (partition by br.UNIT_ID order by br.UNIT_ID) rn
    from MEMSDB.PAVEMENT_REPOSITORY_NEW br
    <where>
      <if test="secondCompanyOrg != null and secondCompanyOrg != ''">
        br.SECOND_ORG_CODE = #{secondCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="threeCompanyOrg != null and threeCompanyOrg != ''">
        and br.OPRT_ORG_CODE = #{threeCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="startYear != null">
        and br.YEAR >= #{startYear,jdbcType=INTEGER}
      </if>

      <if test="endYear != null">
        and br.YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
      </if>

      <if test="target != null">
        and br.target = #{target,jdbcType=INTEGER}
      </if>

      <if test="lineType != null and lineType != ''">
        and br.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
      </if>

      <if test="userOrgId != null and userOrgId != ''">
        and exists(
        select 1 from (select ORG_CODE
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{userOrgId,jdbcType=VARCHAR}
        connect by prior o.ID = o.PARENT_ID) a
        where a.ORG_CODE = br.OPRT_ORG_CODE)
      </if>
    </where>
    ) m where rn = 1 order by ORDER_NUMBER)
  </select>
</mapper>