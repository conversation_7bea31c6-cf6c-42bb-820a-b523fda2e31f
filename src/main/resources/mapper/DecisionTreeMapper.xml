<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.DecisionTreeMapper">
  <select id="getDecisionTree"
    resultType="com.hualu.highwaymaintenance.module.decision.entity.DecisionTree">
    select
      t.*,
      (select d.ATTRIBUTE_VALUE from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM = 'PMS_MAINTAIN_PLAN' and ATTRIBUTE_ACTIVE = 0 and d.ATTRIBUTE_CODE = t.plan1) plan1Value,
      (select d.ATTRIBUTE_VALUE from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM = 'PMS_MAINTAIN_PLAN' and ATTRIBUTE_ACTIVE = 0 and d.ATTRIBUTE_CODE = t.plan2) plan2Value,
      (select d.ATTRIBUTE_VALUE from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM = 'PMS_MAINTAIN_PLAN' and ATTRIBUTE_ACTIVE = 0 and d.ATTRIBUTE_CODE = t.plan3) plan1Value,
      (select d.ATTRIBUTE_VALUE from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM = 'PMS_MAINTAIN_PLAN' and ATTRIBUTE_ACTIVE = 0 and d.ATTRIBUTE_CODE = t.plan4) plan1Value
    from PMSDB.DECISION_TREE t
    where t.PROJECT_ID = #{projectId,jdbcType=VARCHAR} and t.CONSTRAINT_GROUP_ID = #{groupId,jdbcType=VARCHAR}
  </select>
</mapper>
