<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.oftenCheck.mapper.DmFinspRecordMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.oftenCheck.entity.DmFinspRecord">
            <id property="dssId" column="DSS_ID" jdbcType="VARCHAR"/>
            <result property="finspId" column="FINSP_ID" jdbcType="VARCHAR"/>
            <result property="lineDirect" column="LINE_DIRECT" jdbcType="VARCHAR"/>
            <result property="stake" column="STAKE" jdbcType="DECIMAL"/>
            <result property="structPartId" column="STRUCT_PART_ID" jdbcType="VARCHAR"/>
            <result property="structCompId" column="STRUCT_COMP_ID" jdbcType="VARCHAR"/>
            <result property="dssType" column="DSS_TYPE" jdbcType="VARCHAR"/>
            <result property="dssDegree" column="DSS_DEGREE" jdbcType="VARCHAR"/>
            <result property="mntnAdvice" column="MNTN_ADVICE" jdbcType="VARCHAR"/>
            <result property="lane" column="LANE" jdbcType="VARCHAR"/>
            <result property="dssPosition" column="DSS_POSITION" jdbcType="VARCHAR"/>
            <result property="dssDesc" column="DSS_DESC" jdbcType="VARCHAR"/>
            <result property="dssL" column="DSS_L" jdbcType="DECIMAL"/>
            <result property="dssLUnit" column="DSS_L_UNIT" jdbcType="VARCHAR"/>
            <result property="dssW" column="DSS_W" jdbcType="DECIMAL"/>
            <result property="dssWUnit" column="DSS_W_UNIT" jdbcType="VARCHAR"/>
            <result property="dssD" column="DSS_D" jdbcType="DECIMAL"/>
            <result property="dssDUnit" column="DSS_D_UNIT" jdbcType="VARCHAR"/>
            <result property="dssN" column="DSS_N" jdbcType="DECIMAL"/>
            <result property="dssNUnit" column="DSS_N_UNIT" jdbcType="VARCHAR"/>
            <result property="dssA" column="DSS_A" jdbcType="DECIMAL"/>
            <result property="dssAUnit" column="DSS_A_UNIT" jdbcType="VARCHAR"/>
            <result property="dssV" column="DSS_V" jdbcType="DECIMAL"/>
            <result property="dssVUnit" column="DSS_V_UNIT" jdbcType="VARCHAR"/>
            <result property="dssP" column="DSS_P" jdbcType="DECIMAL"/>
            <result property="dssG" column="DSS_G" jdbcType="DECIMAL"/>
            <result property="dssImpFlag" column="DSS_IMP_FLAG" jdbcType="DECIMAL"/>
            <result property="dssQuality" column="DSS_QUALITY" jdbcType="DECIMAL"/>
            <result property="hisDssId" column="HIS_DSS_ID" jdbcType="VARCHAR"/>
            <result property="dssCause" column="DSS_CAUSE" jdbcType="VARCHAR"/>
            <result property="x" column="X" jdbcType="DECIMAL"/>
            <result property="y" column="Y" jdbcType="DECIMAL"/>
            <result property="finspItemId" column="FINSP_ITEM_ID" jdbcType="VARCHAR"/>
            <result property="isphone" column="ISPHONE" jdbcType="DECIMAL"/>
            <result property="rampId" column="RAMP_ID" jdbcType="VARCHAR"/>
            <result property="structId" column="STRUCT_ID" jdbcType="VARCHAR"/>
            <result property="lDssId" column="L_DSS_ID" jdbcType="VARCHAR"/>
            <result property="tunnelMouth" column="TUNNEL_MOUTH" jdbcType="VARCHAR"/>
            <result property="startHigh" column="START_HIGH" jdbcType="DECIMAL"/>
            <result property="endHigh" column="END_HIGH" jdbcType="DECIMAL"/>
            <result property="startStakeNum" column="START_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="endStakeNum" column="END_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="stakeHigh" column="STAKE_HIGH" jdbcType="DECIMAL"/>
            <result property="liningStructure" column="LINING_STRUCTURE" jdbcType="VARCHAR"/>
            <result property="markingLinePosition" column="MARKING_LINE_POSITION" jdbcType="VARCHAR"/>
            <result property="foundDate" column="FOUND_DATE" jdbcType="TIMESTAMP"/>
            <result property="closeType" column="CLOSE_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DSS_ID,FINSP_ID,LINE_DIRECT,
        STAKE,STRUCT_PART_ID,STRUCT_COMP_ID,
        DSS_TYPE,DSS_DEGREE,MNTN_ADVICE,
        LANE,DSS_POSITION,DSS_DESC,
        DSS_L,DSS_L_UNIT,DSS_W,
        DSS_W_UNIT,DSS_D,DSS_D_UNIT,
        DSS_N,DSS_N_UNIT,DSS_A,
        DSS_A_UNIT,DSS_V,DSS_V_UNIT,
        DSS_P,DSS_G,DSS_IMP_FLAG,
        DSS_QUALITY,HIS_DSS_ID,DSS_CAUSE,
        X,Y,FINSP_ITEM_ID,
        ISPHONE,RAMP_ID,STRUCT_ID,
        L_DSS_ID,TUNNEL_MOUTH,START_HIGH,
        END_HIGH,START_STAKE_NUM,END_STAKE_NUM,
        STAKE_HIGH,LINING_STRUCTURE,MARKING_LINE_POSITION,
        FOUND_DATE,CLOSE_TYPE
    </sql>
</mapper>
