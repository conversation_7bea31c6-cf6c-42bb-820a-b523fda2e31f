<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.oftenCheck.mapper.WfworkitemMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.oftenCheck.entity.Wfworkitem">
            <id property="workitemid" column="WORKITEMID" jdbcType="DECIMAL"/>
            <result property="workitemname" column="WORKITEMNAME" jdbcType="VARCHAR"/>
            <result property="workitemtype" column="WORKITEMTYPE" jdbcType="VARCHAR"/>
            <result property="workitemdesc" column="WORKITEMDESC" jdbcType="VARCHAR"/>
            <result property="currentstate" column="CURRENTSTATE" jdbcType="DECIMAL"/>
            <result property="participant" column="PARTICIPANT" jdbcType="VARCHAR"/>
            <result property="partiname" column="PARTINAME" jdbcType="VARCHAR"/>
            <result property="priority" column="PRIORITY" jdbcType="DECIMAL"/>
            <result property="istimeout" column="ISTIMEOUT" jdbcType="CHAR"/>
            <result property="limitnum" column="LIMITNUM" jdbcType="DECIMAL"/>
            <result property="limitnumdesc" column="LIMITNUMDESC" jdbcType="VARCHAR"/>
            <result property="createtime" column="CREATETIME" jdbcType="TIMESTAMP"/>
            <result property="starttime" column="STARTTIME" jdbcType="TIMESTAMP"/>
            <result property="endtime" column="ENDTIME" jdbcType="TIMESTAMP"/>
            <result property="finaltime" column="FINALTIME" jdbcType="TIMESTAMP"/>
            <result property="remindtime" column="REMINDTIME" jdbcType="TIMESTAMP"/>
            <result property="actionurl" column="ACTIONURL" jdbcType="VARCHAR"/>
            <result property="processinstid" column="PROCESSINSTID" jdbcType="DECIMAL"/>
            <result property="activityinstid" column="ACTIVITYINSTID" jdbcType="DECIMAL"/>
            <result property="stateslist" column="STATESLIST" jdbcType="VARCHAR"/>
            <result property="timeoutnum" column="TIMEOUTNUM" jdbcType="DECIMAL"/>
            <result property="timeoutnumdesc" column="TIMEOUTNUMDESC" jdbcType="VARCHAR"/>
            <result property="processinstname" column="PROCESSINSTNAME" jdbcType="VARCHAR"/>
            <result property="activityinstname" column="ACTIVITYINSTNAME" jdbcType="VARCHAR"/>
            <result property="processdefid" column="PROCESSDEFID" jdbcType="DECIMAL"/>
            <result property="processdefname" column="PROCESSDEFNAME" jdbcType="VARCHAR"/>
            <result property="processchname" column="PROCESSCHNAME" jdbcType="VARCHAR"/>
            <result property="activitydefid" column="ACTIVITYDEFID" jdbcType="VARCHAR"/>
            <result property="assistant" column="ASSISTANT" jdbcType="VARCHAR"/>
            <result property="assistantname" column="ASSISTANTNAME" jdbcType="VARCHAR"/>
            <result property="bizstate" column="BIZSTATE" jdbcType="DECIMAL"/>
            <result property="allowagent" column="ALLOWAGENT" jdbcType="VARCHAR"/>
            <result property="rootprocinstid" column="ROOTPROCINSTID" jdbcType="DECIMAL"/>
            <result property="actionmask" column="ACTIONMASK" jdbcType="VARCHAR"/>
            <result property="urltype" column="URLTYPE" jdbcType="VARCHAR"/>
            <result property="dealresult" column="DEALRESULT" jdbcType="VARCHAR"/>
            <result property="dealopinion" column="DEALOPINION" jdbcType="VARCHAR"/>
            <result property="extend1" column="EXTEND1" jdbcType="VARCHAR"/>
            <result property="extend2" column="EXTEND2" jdbcType="VARCHAR"/>
            <result property="extend3" column="EXTEND3" jdbcType="VARCHAR"/>
            <result property="extend4" column="EXTEND4" jdbcType="VARCHAR"/>
            <result property="extend5" column="EXTEND5" jdbcType="VARCHAR"/>
            <result property="extend6" column="EXTEND6" jdbcType="VARCHAR"/>
            <result property="extend7" column="EXTEND7" jdbcType="DECIMAL"/>
            <result property="cataloguuid" column="CATALOGUUID" jdbcType="VARCHAR"/>
            <result property="catalogname" column="CATALOGNAME" jdbcType="VARCHAR"/>
            <result property="tenantId" column="TENANT_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        WORKITEMID,WORKITEMNAME,WORKITEMTYPE,
        WORKITEMDESC,CURRENTSTATE,PARTICIPANT,
        PARTINAME,PRIORITY,ISTIMEOUT,
        LIMITNUM,LIMITNUMDESC,CREATETIME,
        STARTTIME,ENDTIME,FINALTIME,
        REMINDTIME,ACTIONURL,PROCESSINSTID,
        ACTIVITYINSTID,STATESLIST,TIMEOUTNUM,
        TIMEOUTNUMDESC,PROCESSINSTNAME,ACTIVITYINSTNAME,
        PROCESSDEFID,PROCESSDEFNAME,PROCESSCHNAME,
        ACTIVITYDEFID,ASSISTANT,ASSISTANTNAME,
        BIZSTATE,ALLOWAGENT,ROOTPROCINSTID,
        ACTIONMASK,URLTYPE,DEALRESULT,
        DEALOPINION,EXTEND1,EXTEND2,
        EXTEND3,EXTEND4,EXTEND5,
        EXTEND6,EXTEND7,CATALOGUUID,
        CATALOGNAME,TENANT_ID
    </sql>
</mapper>
