<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.platform.mapper.BaseRouteLogicMapper">

  <select id="getRouteForPermission"
    resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    select * from GDGS.BASE_ROUTE_LOGIC l
    where exists(
      select 1 from GDGS.FW_RIGHT_DATA_PERMISSION p where p.OPRT_ORG_CODE = #{orgId,jdbcType=VARCHAR} and l.ROUTE_CODE = p.ROUTE_CODE
      )
  </select>

  <select id="getBridgeRoute"
    resultType="com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic">
    select l.*
    from DOCK.ORG_COMPARE c
           inner join GDGS.BASE_ROUTE_LOGIC l on c.YH_ORG_CODE = l.OPRT_ORG_CODE
    where c.ORG_CODE = #{yrOrgCode}
      and l.LINE_CODE = #{lineCode}
      and l.LINE_DIRECT = #{lineDirect}
      and (#{centerStake} - l.START_STAKE) * (#{centerStake} - l.END_STAKE) <![CDATA[<=]]> 0
      and ROWNUM = 1
  </select>
</mapper>