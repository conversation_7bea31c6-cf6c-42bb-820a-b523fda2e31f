<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.YearReportBridgeMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.YearReportBridge">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.YEAR_REPORT_BRIDGE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BRIDGE_CODE" jdbcType="VARCHAR" property="bridgeCode" />
    <result column="BRIDGE_NAME" jdbcType="VARCHAR" property="bridgeName" />
    <result column="CENTER_STAKE" jdbcType="DECIMAL" property="centerStake" />
    <result column="CROSS_FUTURE_TYPE" jdbcType="VARCHAR" property="crossFutureType" />
    <result column="CROSS_FUTURE_NAME" jdbcType="VARCHAR" property="crossFutureName" />
    <result column="CHARGE_NATURE" jdbcType="VARCHAR" property="chargeNature" />
    <result column="RAMP_CODE" jdbcType="VARCHAR" property="rampCode" />
    <result column="ROUTE_TYPE" jdbcType="VARCHAR" property="routeType" />
    <result column="CROSS_FUTURE_TYPE1" jdbcType="VARCHAR" property="crossFutureType1" />
    <result column="CROSS_FUTURE_NAME1" jdbcType="VARCHAR" property="crossFutureName1" />
    <result column="SRC_BRIDGE_CODE" jdbcType="VARCHAR" property="srcBridgeCode" />
    <result column="WHETHER_NARROW_WIDTH_ROAD" jdbcType="VARCHAR" property="whetherNarrowWidthRoad" />
    <result column="WHETHER_LONG_BIG_CAT" jdbcType="VARCHAR" property="whetherLongBigCat" />
    <result column="WHETHER_TRANS_PROVINCE" jdbcType="VARCHAR" property="whetherTransProvince" />
    <result column="WHETHER_TRANS_LINE" jdbcType="VARCHAR" property="whetherTransLine" />
    <result column="WHETHER_OFF_LINE" jdbcType="VARCHAR" property="whetherOffLine" />
    <result column="WHETHER_HIGHWAY_RAILWAY" jdbcType="VARCHAR" property="whetherHighwayRailway" />
    <result column="BRIDGE_IDENTIFY_CODE" jdbcType="VARCHAR" property="bridgeIdentifyCode" />
    <result column="INTER_FLOW_TYPE" jdbcType="VARCHAR" property="interFlowType" />
    <result column="INTER_FLOW_SHAPE" jdbcType="VARCHAR" property="interFlowShape" />
    <result column="INTER_FLOW_CROSS" jdbcType="VARCHAR" property="interFlowCross" />
    <result column="BRIDGE_CLASSIFY" jdbcType="VARCHAR" property="bridgeClassify" />
    <result column="BRIDGE_LENGTH" jdbcType="DECIMAL" property="bridgeLength" />
    <result column="SPAN_TOTAL_LENGTH" jdbcType="DECIMAL" property="spanTotalLength" />
    <result column="MAIN_SPAN" jdbcType="DECIMAL" property="mainSpan" />
    <result column="MAIN_HOLE" jdbcType="DECIMAL" property="mainHole" />
    <result column="SPAN_GROUP" jdbcType="VARCHAR" property="spanGroup" />
    <result column="BRIDGE_PROPERTY" jdbcType="VARCHAR" property="bridgeProperty" />
    <result column="DESIGN_LOAD" jdbcType="VARCHAR" property="designLoad" />
    <result column="MAIN_SUPER_STRUCTURE" jdbcType="VARCHAR" property="mainSuperStructure" />
    <result column="MAIN_MATERIAL" jdbcType="VARCHAR" property="mainMaterial" />
    <result column="BRIDGE_DECK_TYPE" jdbcType="VARCHAR" property="bridgeDeckType" />
    <result column="BRIDGE_TOTAL_WIDTH" jdbcType="DECIMAL" property="bridgeTotalWidth" />
    <result column="DECK_WIDTH" jdbcType="DECIMAL" property="deckWidth" />
    <result column="BRIDGE_DOWN_HEIGHT" jdbcType="DECIMAL" property="bridgeDownHeight" />
    <result column="SEISMIC_GRADE" jdbcType="VARCHAR" property="seismicGrade" />
    <result column="NAVIGATION_LEVEL" jdbcType="VARCHAR" property="navigationLevel" />
    <result column="ABUTMENT_TYPE" jdbcType="VARCHAR" property="abutmentType" />
    <result column="PIER_TYPE" jdbcType="VARCHAR" property="pierType" />
    <result column="PIER_PREVENTION" jdbcType="VARCHAR" property="pierPrevention" />
    <result column="EXPANSION_JOINT_TYPE" jdbcType="VARCHAR" property="expansionJointType" />
    <result column="SUPPORT_TYPE" jdbcType="VARCHAR" property="supportType" />
    <result column="CURVE_SLOPE" jdbcType="VARCHAR" property="curveSlope" />
    <result column="BRIDGE_HEIGHT" jdbcType="DECIMAL" property="bridgeHeight" />
    <result column="SIDEWALK_WIDTH" jdbcType="DECIMAL" property="sidewalkWidth" />
    <result column="FLOOD_CONTROL_STANDARD" jdbcType="DECIMAL" property="floodControlStandard" />
    <result column="BUILT_COMPANY" jdbcType="VARCHAR" property="builtCompany" />
    <result column="BUILT_DATE" jdbcType="TIMESTAMP" property="builtDate" />
    <result column="OPEN_DATE" jdbcType="TIMESTAMP" property="openDate" />
    <result column="REBUILD_DATE" jdbcType="TIMESTAMP" property="rebuildDate" />
    <result column="TOTAL_COST" jdbcType="DECIMAL" property="totalCost" />
    <result column="DESIGN_COMPANY" jdbcType="VARCHAR" property="designCompany" />
    <result column="CONSTRUCTION_COMPANY" jdbcType="VARCHAR" property="constructionCompany" />
    <result column="SUPERVISION_COMPANY" jdbcType="VARCHAR" property="supervisionCompany" />
    <result column="CONSTRUCTION_NATURE" jdbcType="VARCHAR" property="constructionNature" />
    <result column="EVAL_DATE" jdbcType="TIMESTAMP" property="evalDate" />
    <result column="EVAL_GRADE" jdbcType="VARCHAR" property="evalGrade" />
    <result column="EVAL_COMPANY" jdbcType="VARCHAR" property="evalCompany" />
    <result column="MAIN_DISEASE" jdbcType="VARCHAR" property="mainDisease" />
    <result column="DISEASE_DESC" jdbcType="VARCHAR" property="diseaseDesc" />
    <result column="CONTROL_MEASURES" jdbcType="VARCHAR" property="controlMeasures" />
    <result column="RECENT_PERIODICAL_DATE" jdbcType="TIMESTAMP" property="recentPeriodicalDate" />
    <result column="MANAGE_COMPANY_NATURE" jdbcType="VARCHAR" property="manageCompanyNature" />
    <result column="MANAGE_COMPANY_CODE" jdbcType="VARCHAR" property="manageCompanyCode" />
    <result column="REGULATOR_COMPANY" jdbcType="VARCHAR" property="regulatorCompany" />
    <result column="RENOVATION_CONSTRUCTION_UNIT" jdbcType="VARCHAR" property="renovationConstructionUnit" />
    <result column="WHETHER_SUBSIDY_PROJECT" jdbcType="VARCHAR" property="whetherSubsidyProject" />
    <result column="PROJECT_NATURE" jdbcType="VARCHAR" property="projectNature" />
    <result column="RENOVATION_POSITION" jdbcType="VARCHAR" property="renovationPosition" />
    <result column="RENOVATION_COMPLETE_DATE" jdbcType="TIMESTAMP" property="renovationCompleteDate" />
    <result column="WHETHER_DANGER_RENOVATION" jdbcType="VARCHAR" property="whetherDangerRenovation" />
    <result column="CHANGE_REASON" jdbcType="VARCHAR" property="changeReason" />
    <result column="FILL_COMPANY" jdbcType="VARCHAR" property="fillCompany" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="YH_BRIDGE_ID" jdbcType="VARCHAR" property="yhBridgeId" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
    <result column="WHETHER_ENABLE" jdbcType="DECIMAL" property="whetherEnable" />
    <result column="DISABLE_DATE" jdbcType="TIMESTAMP" property="disableDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, BRIDGE_CODE, BRIDGE_NAME, CENTER_STAKE, CROSS_FUTURE_TYPE, CROSS_FUTURE_NAME, 
    CHARGE_NATURE, RAMP_CODE, ROUTE_TYPE, CROSS_FUTURE_TYPE1, CROSS_FUTURE_NAME1, SRC_BRIDGE_CODE, 
    WHETHER_NARROW_WIDTH_ROAD, WHETHER_LONG_BIG_CAT, WHETHER_TRANS_PROVINCE, WHETHER_TRANS_LINE, 
    WHETHER_OFF_LINE, WHETHER_HIGHWAY_RAILWAY, BRIDGE_IDENTIFY_CODE, INTER_FLOW_TYPE, 
    INTER_FLOW_SHAPE, INTER_FLOW_CROSS, BRIDGE_CLASSIFY, BRIDGE_LENGTH, SPAN_TOTAL_LENGTH, 
    MAIN_SPAN, MAIN_HOLE, SPAN_GROUP, BRIDGE_PROPERTY, DESIGN_LOAD, MAIN_SUPER_STRUCTURE, 
    MAIN_MATERIAL, BRIDGE_DECK_TYPE, BRIDGE_TOTAL_WIDTH, DECK_WIDTH, BRIDGE_DOWN_HEIGHT, 
    SEISMIC_GRADE, NAVIGATION_LEVEL, ABUTMENT_TYPE, PIER_TYPE, PIER_PREVENTION, EXPANSION_JOINT_TYPE, 
    SUPPORT_TYPE, CURVE_SLOPE, BRIDGE_HEIGHT, SIDEWALK_WIDTH, FLOOD_CONTROL_STANDARD, 
    BUILT_COMPANY, BUILT_DATE, OPEN_DATE, REBUILD_DATE, TOTAL_COST, DESIGN_COMPANY, CONSTRUCTION_COMPANY, 
    SUPERVISION_COMPANY, CONSTRUCTION_NATURE, EVAL_DATE, EVAL_GRADE, EVAL_COMPANY, MAIN_DISEASE, 
    DISEASE_DESC, CONTROL_MEASURES, RECENT_PERIODICAL_DATE, MANAGE_COMPANY_NATURE, MANAGE_COMPANY_CODE, 
    REGULATOR_COMPANY, RENOVATION_CONSTRUCTION_UNIT, WHETHER_SUBSIDY_PROJECT, PROJECT_NATURE, 
    RENOVATION_POSITION, RENOVATION_COMPLETE_DATE, WHETHER_DANGER_RENOVATION, CHANGE_REASON, 
    FILL_COMPANY, REMARK, YH_BRIDGE_ID, AREA, CITY, REPORT_ID, WHETHER_ENABLE, DISABLE_DATE
  </sql>
  <select id="getBridgeData" resultType="com.hualu.highwaymaintenance.module.datareport.entity.YearReportBridge">
    WITH BRIDGE AS (
      select c.*,
        (select p.ORG_CODE
         from DOCK.ORG_COMPARE p
         where p.ORG_CODE = c.MANAGE_COMPANY_CODE
           and p.IS_DELETE = 0) YH_ORG_CODE
      from BCTCMSDB.YEAR_REPORT_BRIDGE c
      where exists(
        select 1
        from (select (select p.ORG_CODE
                      from DOCK.ORG_COMPARE p
                      where p.YH_ORG_CODE = l.OPRT_ORG_CODE
                        and p.IS_DELETE = 0) ORG_CODE
              from GDGS.BASE_ROUTE_LOGIC l
              where exists(
                select 1
                from (select *
                      from GDGS.FW_RIGHT_ORG o
                      where o.IS_DELETED = 0
                        and o.IS_ENABLE = 1
                      start with o.ID =
                                 (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = 'fkql')
                      connect by prior o.ID = o.PARENT_ID) m
                where m.ID = l.OPRT_ORG_CODE
              )) p
        where p.ORG_CODE = c.MANAGE_COMPANY_CODE
      )),
      BRIDGE2 AS (
        select b.*,
          (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o where o.IS_ENABLE = 1 and o.IS_DELETED = 0 and o.ORG_CODE = b.YH_ORG_CODE) ORG_NAME,
          (select op.ORG_NAME from GDGS.FW_RIGHT_ORG o
                                     inner join GDGS.FW_RIGHT_ORG op on o.PARENT_ID = op.ID
           where o.IS_ENABLE = 1 and o.IS_DELETED = 0
             and op.IS_ENABLE = 1 and op.IS_DELETED = 0
             and o.ORG_CODE = b.YH_ORG_CODE) SECOND_COMPANY
        from BRIDGE b)
    SELECT * FROM BRIDGE2 C
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.YH_ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>

      <if test="bridgeName != null and bridgeName != ''">
        and  c.BRIDGE_NAME like concat(concat('%',#{bridgeName}),'%')
      </if>
    </where>
  </select>
</mapper>