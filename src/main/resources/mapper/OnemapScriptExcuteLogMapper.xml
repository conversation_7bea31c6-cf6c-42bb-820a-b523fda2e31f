<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.scriptManagement.mapper.OnemapScriptExcuteLogMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog">
    <!--@mbg.generated-->
    <!--@Table GDGS.ONEMAP_SCRIPT_EXCUTE_LOG-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCRIPT_ID" jdbcType="VARCHAR" property="scriptId" />
    <result column="EXECUTION_TIME" jdbcType="TIMESTAMP" property="executionTime" />
    <result column="EXECUTION_STATUS" jdbcType="VARCHAR" property="executionStatus" />
    <result column="ERROR_MESSAGE" jdbcType="VARCHAR" property="errorMessage" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SCRIPT_ID, EXECUTION_TIME, EXECUTION_STATUS, ERROR_MESSAGE
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog">
    <!--@mbg.generated-->
    insert into GDGS.ONEMAP_SCRIPT_EXCUTE_LOG (ID, SCRIPT_ID, EXECUTION_TIME, 
      EXECUTION_STATUS, ERROR_MESSAGE)
    values (#{id,jdbcType=VARCHAR}, #{scriptId,jdbcType=VARCHAR}, #{executionTime,jdbcType=TIMESTAMP}, 
      #{executionStatus,jdbcType=VARCHAR}, #{errorMessage,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog">
    <!--@mbg.generated-->
    insert into GDGS.ONEMAP_SCRIPT_EXCUTE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="scriptId != null">
        SCRIPT_ID,
      </if>
      <if test="executionTime != null">
        EXECUTION_TIME,
      </if>
      <if test="executionStatus != null">
        EXECUTION_STATUS,
      </if>
      <if test="errorMessage != null">
        ERROR_MESSAGE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="scriptId != null">
        #{scriptId,jdbcType=VARCHAR},
      </if>
      <if test="executionTime != null">
        #{executionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="executionStatus != null">
        #{executionStatus,jdbcType=VARCHAR},
      </if>
      <if test="errorMessage != null">
        #{errorMessage,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getPage" resultType="com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog">
    SELECT  *  FROM GDGS.ONEMAP_SCRIPT_EXCUTE_LOG g
    where g.SCRIPT_ID=#{scriptId}
  </select>
</mapper>