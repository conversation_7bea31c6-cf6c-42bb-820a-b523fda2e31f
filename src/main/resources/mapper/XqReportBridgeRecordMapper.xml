<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqReportBridgeRecordMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqReportBridgeRecord">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_REPORT_BRIDGE_RECORD-->
    <id column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
    <result column="REPORT_DATE" jdbcType="TIMESTAMP" property="reportDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    REPORT_ID, REPORT_DATE
  </sql>
</mapper>