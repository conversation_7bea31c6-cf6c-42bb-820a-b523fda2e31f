<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqPeriodicDetectionMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqPeriodicDetection">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_PERIODIC_DETECTION-->
    <result column="BRIDGE_IDENTITY_CODE" jdbcType="VARCHAR" property="bridgeIdentityCode" />
    <result column="DETECTION_ID" jdbcType="VARCHAR" property="detectionId" />
    <result column="DETECTION_DATE" jdbcType="TIMESTAMP" property="detectionDate" />
    <result column="DETECTION_COMPANY_NAME" jdbcType="VARCHAR" property="detectionCompanyName" />
    <result column="LEVEL" jdbcType="DECIMAL" property="level" />
    <result column="SCORE" jdbcType="DECIMAL" property="score" />
    <result column="REVIEW_UNIT" jdbcType="VARCHAR" property="reviewUnit" />
    <result column="REVIEW_TIME" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="DETECTION_REPORT_FILE_PATH" jdbcType="VARCHAR" property="detectionReportFilePath" />
    <result column="BRIDGE_ID" jdbcType="VARCHAR" property="bridgeId" />
    <result column="PROJECT_ID" jdbcType="VARCHAR" property="projectId" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRIDGE_IDENTITY_CODE, DETECTION_ID, DETECTION_DATE, DETECTION_COMPANY_NAME, "LEVEL", 
    SCORE, REVIEW_UNIT, REVIEW_TIME, DETECTION_REPORT_FILE_PATH, BRIDGE_ID, PROJECT_ID, 
    SECOND_COMPANY, ORG_CODE, REPORT_ID
  </sql>
</mapper>