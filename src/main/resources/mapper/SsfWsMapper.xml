<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.scriptManagement.mapper.SsfWsMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.scriptManagement.SsfWs">
    <!--@mbg.generated-->
    <!--@Table SSF_WS-->
    <result column="BRIDGE_ID" jdbcType="VARCHAR" property="bridgeId" />
    <result column="SSF_TYPE" jdbcType="VARCHAR" property="ssfType" />
    <result column="SSF_LOCATION" jdbcType="VARCHAR" property="ssfLocation" />
    <result column="SSF_LENGTH" jdbcType="VARCHAR" property="ssfLength" />
    <result column="SSF_GAP" jdbcType="VARCHAR" property="ssfGap" />
    <result column="SSF_PRODUCER" jdbcType="VARCHAR" property="ssfProducer" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRIDGE_ID, SSF_TYPE, SSF_LOCATION, SSF_LENGTH, SSF_GAP, SSF_PRODUCER
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.scriptManagement.SsfWs">
    <!--@mbg.generated-->
    insert into SSF_WS (BRIDGE_ID, SSF_TYPE, SSF_LOCATION, 
      SSF_LENGTH, SSF_GAP, SSF_PRODUCER
      )
    values (#{bridgeId,jdbcType=VARCHAR}, #{ssfType,jdbcType=VARCHAR}, #{ssfLocation,jdbcType=VARCHAR}, 
      #{ssfLength,jdbcType=VARCHAR}, #{ssfGap,jdbcType=VARCHAR}, #{ssfProducer,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.scriptManagement.SsfWs">
    <!--@mbg.generated-->
    insert into SSF_WS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bridgeId != null">
        BRIDGE_ID,
      </if>
      <if test="ssfType != null">
        SSF_TYPE,
      </if>
      <if test="ssfLocation != null">
        SSF_LOCATION,
      </if>
      <if test="ssfLength != null">
        SSF_LENGTH,
      </if>
      <if test="ssfGap != null">
        SSF_GAP,
      </if>
      <if test="ssfProducer != null">
        SSF_PRODUCER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bridgeId != null">
        #{bridgeId,jdbcType=VARCHAR},
      </if>
      <if test="ssfType != null">
        #{ssfType,jdbcType=VARCHAR},
      </if>
      <if test="ssfLocation != null">
        #{ssfLocation,jdbcType=VARCHAR},
      </if>
      <if test="ssfLength != null">
        #{ssfLength,jdbcType=VARCHAR},
      </if>
      <if test="ssfGap != null">
        #{ssfGap,jdbcType=VARCHAR},
      </if>
      <if test="ssfProducer != null">
        #{ssfProducer,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>