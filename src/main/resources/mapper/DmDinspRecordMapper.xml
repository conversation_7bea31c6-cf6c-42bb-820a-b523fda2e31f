<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dailycheck.mapper.DmDinspRecordMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinspRecord">
            <id property="dssId" column="DSS_ID" jdbcType="VARCHAR"/>
            <result property="dinspId" column="DINSP_ID" jdbcType="VARCHAR"/>
            <result property="inspTime" column="INSP_TIME" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvl" column="INSP_TIME_INTVL" jdbcType="VARCHAR"/>
            <result property="issueType" column="ISSUE_TYPE" jdbcType="VARCHAR"/>
            <result property="lineDirect" column="LINE_DIRECT" jdbcType="VARCHAR"/>
            <result property="stake" column="STAKE" jdbcType="DECIMAL"/>
            <result property="dssType" column="DSS_TYPE" jdbcType="VARCHAR"/>
            <result property="dssDegree" column="DSS_DEGREE" jdbcType="VARCHAR"/>
            <result property="mntnAdvice" column="MNTN_ADVICE" jdbcType="VARCHAR"/>
            <result property="facilityCat" column="FACILITY_CAT" jdbcType="VARCHAR"/>
            <result property="structId" column="STRUCT_ID" jdbcType="VARCHAR"/>
            <result property="structPartId" column="STRUCT_PART_ID" jdbcType="VARCHAR"/>
            <result property="structCompId" column="STRUCT_COMP_ID" jdbcType="VARCHAR"/>
            <result property="lane" column="LANE" jdbcType="VARCHAR"/>
            <result property="dssPosition" column="DSS_POSITION" jdbcType="VARCHAR"/>
            <result property="dssDesc" column="DSS_DESC" jdbcType="VARCHAR"/>
            <result property="dssCause" column="DSS_CAUSE" jdbcType="VARCHAR"/>
            <result property="dssL" column="DSS_L" jdbcType="DECIMAL"/>
            <result property="dssLUnit" column="DSS_L_UNIT" jdbcType="VARCHAR"/>
            <result property="dssW" column="DSS_W" jdbcType="DECIMAL"/>
            <result property="dssWUnit" column="DSS_W_UNIT" jdbcType="VARCHAR"/>
            <result property="dssD" column="DSS_D" jdbcType="DECIMAL"/>
            <result property="dssDUnit" column="DSS_D_UNIT" jdbcType="VARCHAR"/>
            <result property="dssN" column="DSS_N" jdbcType="DECIMAL"/>
            <result property="dssNUnit" column="DSS_N_UNIT" jdbcType="VARCHAR"/>
            <result property="dssA" column="DSS_A" jdbcType="DECIMAL"/>
            <result property="dssAUnit" column="DSS_A_UNIT" jdbcType="VARCHAR"/>
            <result property="dssV" column="DSS_V" jdbcType="DECIMAL"/>
            <result property="dssVUnit" column="DSS_V_UNIT" jdbcType="VARCHAR"/>
            <result property="dssP" column="DSS_P" jdbcType="DECIMAL"/>
            <result property="dssG" column="DSS_G" jdbcType="DECIMAL"/>
            <result property="dssImpFlag" column="DSS_IMP_FLAG" jdbcType="DECIMAL"/>
            <result property="dssQuality" column="DSS_QUALITY" jdbcType="DECIMAL"/>
            <result property="hisDssId" column="HIS_DSS_ID" jdbcType="VARCHAR"/>
            <result property="x" column="X" jdbcType="DECIMAL"/>
            <result property="y" column="Y" jdbcType="DECIMAL"/>
            <result property="isphone" column="ISPHONE" jdbcType="DECIMAL"/>
            <result property="rampId" column="RAMP_ID" jdbcType="VARCHAR"/>
            <result property="tunnelMouth" column="TUNNEL_MOUTH" jdbcType="VARCHAR"/>
            <result property="startHigh" column="START_HIGH" jdbcType="DECIMAL"/>
            <result property="endHigh" column="END_HIGH" jdbcType="DECIMAL"/>
            <result property="startStakeNum" column="START_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="endStakeNum" column="END_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="stakeHigh" column="STAKE_HIGH" jdbcType="DECIMAL"/>
            <result property="finishStake" column="FINISH_STAKE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        DSS_ID,DINSP_ID,INSP_TIME,
        INSP_TIME_INTVL,ISSUE_TYPE,LINE_DIRECT,
        STAKE,DSS_TYPE,DSS_DEGREE,
        MNTN_ADVICE,FACILITY_CAT,STRUCT_ID,
        STRUCT_PART_ID,STRUCT_COMP_ID,LANE,
        DSS_POSITION,DSS_DESC,DSS_CAUSE,
        DSS_L,DSS_L_UNIT,DSS_W,
        DSS_W_UNIT,DSS_D,DSS_D_UNIT,
        DSS_N,DSS_N_UNIT,DSS_A,
        DSS_A_UNIT,DSS_V,DSS_V_UNIT,
        DSS_P,DSS_G,DSS_IMP_FLAG,
        DSS_QUALITY,HIS_DSS_ID,X,
        Y,ISPHONE,RAMP_ID,
        TUNNEL_MOUTH,START_HIGH,END_HIGH,
        START_STAKE_NUM,END_STAKE_NUM,STAKE_HIGH,
        FINISH_STAKE
    </sql>
    <select id="loadDmDinspRecordInfo"
            resultType="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinspRecord">
        select d.*,dt.DSS_TYPE_NAME,decode(d.ISSUE_TYPE,2,'病害','非病害') as ISSUE_TYPE_NAME,dc1.ATTRIBUTE_VALUE as FACILITY_CAT_NAME,
               dc2.ATTRIBUTE_VALUE as DSS_DEGREE_NAME,decode(d.DSS_QUALITY,'0','新病害','1','旧病害','2','修复后损坏','3','修复后良好') as DSS_QUALITY_NAME,
               decode(LINE_DIRECT,4,rl.LINE_SNAME,dc3.ATTRIBUTE_VALUE) as LANE_NAME,
               decode(d.FACILITY_CAT, 'QL', d.STRUCT_COMP_ID,'HD', d.STRUCT_COMP_ID, dc6.ATTRIBUTE_VALUE) as STRUCT_COMP_NAME,
               decode(LINE_DIRECT,4,rl.LINE_SNAME,dc3.ATTRIBUTE_VALUE) as RAMP_NAME,
               decode(d.FACILITY_CAT,'QL',br.BRDG_NAME,'HD',hd.CLVRT_NAME,'BP',slo.SLOPE_NAME,'SD',tun.TUNNEL_NAME,'FWQ',
                      a.SERVICE_NAME,'SFZ',t.TOLLGATE_NAME) as struct_name,
               decode(d.FACILITY_CAT,'QL',qlpa.PARTSTYPE_NAME,'HD',hdpa.PART_NAME,'SFZ',dc4.ATTRIBUTE_VALUE,'FWQ',dc5.ATTRIBUTE_VALUE) as STRUCT_PART_NAME,
               decode(LINE_DIRECT,'1','上行','2','下行','匝道') as LINE_DIRECT_NAME,nvl(cod.POST_NAME,d.DSS_POSITION) DSS_POSITION_NAME,
               decode(nvl(di.REPAIR_STATUS,0),0,'待修复',1,'修复中','已修复') REPAIR_STATUS
        from MEMSDB.DM_DINSP_RECORD d
                 inner join memsdb.DSS_TYPE_NEW dt on d.DSS_TYPE=dt.DSS_TYPE
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc1
                           on dc1.ATTRIBUTE_ITEM='FACILITY_CAT' and d.FACILITY_CAT=dc1.ATTRIBUTE_CODE and dc1.ATTRIBUTE_ACTIVE=0
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc2
                           on dc2.ATTRIBUTE_ITEM='DSS_DEGREE' and d.DSS_DEGREE=dc2.ATTRIBUTE_CODE and dc2.ATTRIBUTE_ACTIVE=0
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc3
                           on dc3.ATTRIBUTE_ITEM='LANE' and d.LANE=dc3.ATTRIBUTE_CODE and dc3.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'LM'
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc4
                           on dc4.ATTRIBUTE_ITEM='SFZ_QYHF' and d.STRUCT_PART_ID=dc4.ATTRIBUTE_CODE and dc4.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'SFZ'
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc5
                           on dc5.ATTRIBUTE_ITEM='FWQ_QYHF' and d.STRUCT_PART_ID=dc5.ATTRIBUTE_CODE and dc5.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'FWQ'
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc6
                           on dc6.ATTRIBUTE_ITEM='FJ_TYPE' and d.STRUCT_COMP_ID=dc6.ATTRIBUTE_CODE and dc6.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'FJGC'
                 left join BCTCMSDB.T_BRDG_BRDGRECOG br on d.STRUCT_ID = br.BRDGRECOG_ID and d.FACILITY_CAT = 'QL'
                 left join BCTCMSDB.T_BRDG_PARTSTYPE qlpa on d.STRUCT_PART_ID = qlpa.PARTSTYPE_ID and d.FACILITY_CAT = 'QL'
                 left join BCTCMSDB.T_CLVRT_CLVRTRECOG hd on d.STRUCT_ID = hd.CLVRTRECOG_ID and d.FACILITY_CAT = 'HD'
                 left join BCTCMSDB.T_CLVRT_PARTTYPE hdpa on d.STRUCT_PART_ID = hdpa.PART_CODE and d.FACILITY_CAT = 'HD'
                 left join HSMSDB.HSMS_SLOPE_INFO slo on d.STRUCT_ID = slo.SLOPE_ID and d.FACILITY_CAT = 'BP'
                 left join MTMSDB.MTMS_TUNNEL_BASIC tun on d.STRUCT_ID = tun.TUNNEL_ID and d.FACILITY_CAT = 'SD'
                 left join GDGS.BASE_SERVICE_AREA a on d.STRUCT_ID = a.SERVICE_ID and d.FACILITY_CAT = 'FWQ'
                 left join GDGS.BASE_TOLLGATE t on d.STRUCT_ID = t.TOLLGATE_ID and d.FACILITY_CAT = 'SFZ'
                 left join BCTCMSDB.T_BRDG_PARTPOST cod on d.DSS_POSITION = cod.PARTPOST_D and d.FACILITY_CAT = 'QL'
                 left join gdgs.BASE_RAMP_LINE rl on d.RAMP_ID = rl.LINE_ID and LINE_DIRECT = '4'
                 left join MEMSDB.DSS_INFO di on d.DSS_ID = di.DSS_ID
        where d.DINSP_ID=#{dinspId} order by INSP_TIME desc
    </select>

    <select id="loadDmDinspRecordInfoList"
            resultType="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinspRecord">
        select d.*,dt.DSS_TYPE_NAME,decode(d.ISSUE_TYPE,2,'病害','非病害') as ISSUE_TYPE_NAME,dc1.ATTRIBUTE_VALUE as FACILITY_CAT_NAME,
               dc2.ATTRIBUTE_VALUE as DSS_DEGREE_NAME,decode(d.DSS_QUALITY,'0','新病害','1','旧病害','2','修复后损坏','3','修复后良好') as DSS_QUALITY_NAME,
               decode(LINE_DIRECT,4,rl.LINE_SNAME,dc3.ATTRIBUTE_VALUE) as LANE_NAME,
               decode(LINE_DIRECT,4,rl.LINE_SNAME,dc3.ATTRIBUTE_VALUE) as RAMP_NAME,
               decode(d.FACILITY_CAT, 'QL', d.STRUCT_COMP_ID,'HD', d.STRUCT_COMP_ID, dc6.ATTRIBUTE_VALUE) as STRUCT_COMP_NAME,
               decode(d.FACILITY_CAT,'QL',br.BRDG_NAME,'HD',hd.CLVRT_NAME,'BP',slo.SLOPE_NAME,'SD',tun.TUNNEL_NAME,'FWQ',
                      a.SERVICE_NAME,'SFZ',t.TOLLGATE_NAME) as struct_name,
               decode(d.FACILITY_CAT,'QL',qlpa.PARTSTYPE_NAME,'HD',hdpa.PART_NAME,'SFZ',dc4.ATTRIBUTE_VALUE,'FWQ',dc5.ATTRIBUTE_VALUE) as STRUCT_PART_NAME,
               decode(LINE_DIRECT,'1','上行','2','下行','匝道') as LINE_DIRECT_NAME,nvl(cod.POST_NAME,d.DSS_POSITION) DSS_POSITION_NAME,
               decode(nvl(di.REPAIR_STATUS,0),0,'待修复',1,'修复中','已修复') REPAIR_STATUS
        from MEMSDB.DM_DINSP_RECORD d
                 inner join memsdb.DSS_TYPE_NEW dt on d.DSS_TYPE=dt.DSS_TYPE
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc1
                           on dc1.ATTRIBUTE_ITEM='FACILITY_CAT' and d.FACILITY_CAT=dc1.ATTRIBUTE_CODE and dc1.ATTRIBUTE_ACTIVE=0
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc2
                           on dc2.ATTRIBUTE_ITEM='DSS_DEGREE' and d.DSS_DEGREE=dc2.ATTRIBUTE_CODE and dc2.ATTRIBUTE_ACTIVE=0
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc3
                           on dc3.ATTRIBUTE_ITEM='LANE' and d.LANE=dc3.ATTRIBUTE_CODE and dc3.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'LM'
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc4
                           on dc4.ATTRIBUTE_ITEM='SFZ_QYHF' and d.STRUCT_PART_ID=dc4.ATTRIBUTE_CODE and dc4.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'SFZ'
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc5
                           on dc5.ATTRIBUTE_ITEM='FWQ_QYHF' and d.STRUCT_PART_ID=dc5.ATTRIBUTE_CODE and dc5.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'FWQ'
                 LEFT join GDGS.BASE_DATATHIRD_DIC dc6
                           on dc6.ATTRIBUTE_ITEM='FJ_TYPE' and d.STRUCT_COMP_ID=dc6.ATTRIBUTE_CODE and dc6.ATTRIBUTE_ACTIVE=0 and d.FACILITY_CAT = 'FJGC'
                 left join BCTCMSDB.T_BRDG_BRDGRECOG br on d.STRUCT_ID = br.BRDGRECOG_ID and d.FACILITY_CAT = 'QL'
                 left join BCTCMSDB.T_BRDG_PARTSTYPE qlpa on d.STRUCT_PART_ID = qlpa.PARTSTYPE_ID and d.FACILITY_CAT = 'QL'
                 left join BCTCMSDB.T_CLVRT_CLVRTRECOG hd on d.STRUCT_ID = hd.CLVRTRECOG_ID and d.FACILITY_CAT = 'HD'
                 left join BCTCMSDB.T_CLVRT_PARTTYPE hdpa on d.STRUCT_PART_ID = hdpa.PART_CODE and d.FACILITY_CAT = 'HD'
                 left join HSMSDB.HSMS_SLOPE_INFO slo on d.STRUCT_ID = slo.SLOPE_ID and d.FACILITY_CAT = 'BP'
                 left join MTMSDB.MTMS_TUNNEL_BASIC tun on d.STRUCT_ID = tun.TUNNEL_ID and d.FACILITY_CAT = 'SD'
                 left join GDGS.BASE_SERVICE_AREA a on d.STRUCT_ID = a.SERVICE_ID and d.FACILITY_CAT = 'FWQ'
                 left join GDGS.BASE_TOLLGATE t on d.STRUCT_ID = t.TOLLGATE_ID and d.FACILITY_CAT = 'SFZ'
                 left join BCTCMSDB.T_BRDG_PARTPOST cod on d.DSS_POSITION = cod.PARTPOST_D and d.FACILITY_CAT = 'QL'
                 left join gdgs.BASE_RAMP_LINE rl on d.RAMP_ID = rl.LINE_ID and LINE_DIRECT = '4'
                 left join MEMSDB.DSS_INFO di on d.DSS_ID = di.DSS_ID
        where d.DINSP_ID=#{dinspId} and d.facility_cat = #{facilityCat} order by INSP_TIME desc
    </select>
</mapper>
