<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dailycheck.mapper.DmDinspMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinsp">
            <id property="dinspId" column="DINSP_ID" jdbcType="VARCHAR"/>
            <result property="dinspCode" column="DINSP_CODE" jdbcType="VARCHAR"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="mntnOrgNm" column="MNTN_ORG_NM" jdbcType="VARCHAR"/>
            <result property="inspDate" column="INSP_DATE" jdbcType="TIMESTAMP"/>
            <result property="inspTimeIntvlM" column="INSP_TIME_INTVL_M" jdbcType="DECIMAL"/>
            <result property="inspScopeM" column="INSP_SCOPE_M" jdbcType="VARCHAR"/>
            <result property="weatherM" column="WEATHER_M" jdbcType="VARCHAR"/>
            <result property="inspDistanceM" column="INSP_DISTANCE_M" jdbcType="VARCHAR"/>
            <result property="inspPersonM" column="INSP_PERSON_M" jdbcType="VARCHAR"/>
            <result property="inspCarM" column="INSP_CAR_M" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlA" column="INSP_TIME_INTVL_A" jdbcType="DECIMAL"/>
            <result property="inspScopeA" column="INSP_SCOPE_A" jdbcType="VARCHAR"/>
            <result property="weatherA" column="WEATHER_A" jdbcType="VARCHAR"/>
            <result property="inspDistanceA" column="INSP_DISTANCE_A" jdbcType="VARCHAR"/>
            <result property="inspPersonA" column="INSP_PERSON_A" jdbcType="VARCHAR"/>
            <result property="inspCarA" column="INSP_CAR_A" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlN" column="INSP_TIME_INTVL_N" jdbcType="DECIMAL"/>
            <result property="inspScopeN" column="INSP_SCOPE_N" jdbcType="VARCHAR"/>
            <result property="weatherN" column="WEATHER_N" jdbcType="VARCHAR"/>
            <result property="inspDistanceN" column="INSP_DISTANCE_N" jdbcType="VARCHAR"/>
            <result property="inspPersonN" column="INSP_PERSON_N" jdbcType="VARCHAR"/>
            <result property="inspCarN" column="INSP_CAR_N" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="processinstid" column="PROCESSINSTID" jdbcType="DECIMAL"/>
            <result property="searchDept" column="SEARCH_DEPT" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlStartM" column="INSP_TIME_INTVL_START_M" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlEndM" column="INSP_TIME_INTVL_END_M" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlStartA" column="INSP_TIME_INTVL_START_A" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlEndA" column="INSP_TIME_INTVL_END_A" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlStartN" column="INSP_TIME_INTVL_START_N" jdbcType="VARCHAR"/>
            <result property="inspTimeIntvlEndN" column="INSP_TIME_INTVL_END_N" jdbcType="VARCHAR"/>
            <result property="dinVersion" column="DIN_VERSION" jdbcType="VARCHAR"/>
            <result property="patrolusername" column="PATROLUSERNAME" jdbcType="VARCHAR"/>
            <result property="dssNum" column="DSS_NUM" jdbcType="DECIMAL"/>
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
            <result property="structInfo" column="STRUCT_INFO" jdbcType="VARCHAR"/>
            <result property="principal" column="PRINCIPAL" jdbcType="VARCHAR"/>
            <result property="lineName" column="LINE_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DINSP_ID,DINSP_CODE,LINE_CODE,
        MNT_ORG_ID,MNTN_ORG_NM,INSP_DATE,
        INSP_TIME_INTVL_M,INSP_SCOPE_M,WEATHER_M,
        INSP_DISTANCE_M,INSP_PERSON_M,INSP_CAR_M,
        INSP_TIME_INTVL_A,INSP_SCOPE_A,WEATHER_A,
        INSP_DISTANCE_A,INSP_PERSON_A,INSP_CAR_A,
        INSP_TIME_INTVL_N,INSP_SCOPE_N,WEATHER_N,
        INSP_DISTANCE_N,INSP_PERSON_N,INSP_CAR_N,
        REMARK,STATUS,CREATE_USER_ID,
        CREATE_TIME,UPDATE_USER_ID,UPDATE_TIME,
        PROCESSINSTID,SEARCH_DEPT,INSP_TIME_INTVL_START_M,
        INSP_TIME_INTVL_END_M,INSP_TIME_INTVL_START_A,INSP_TIME_INTVL_END_A,
        INSP_TIME_INTVL_START_N,INSP_TIME_INTVL_END_N,DIN_VERSION,
        PATROLUSERNAME,DSS_NUM,TYPE,
        STRUCT_INFO,PRINCIPAL
    </sql>
    <insert id="insertResultRGD" statementType="CALLABLE">
        {call memsdb.insert_din_result_201801_rgd(#{dinspId})}
    </insert>

    <select id="insertResult" statementType="CALLABLE" >
    {call memsdb.insert_din_result_201801(#{dinspId})}
  </select>

  <update id="updateDssNum">
    UPDATE memsdb.dm_dinsp d SET d.DSS_NUM = ( select count(1) from memsdb.dm_dinsp_record b where b.dinsp_id = d.dinsp_id )
                      where d.dinsp_id = #{dinspId}
  </update>

  <select id="getPageDmDinsps" resultMap="BaseResultMap">
        select d.*,concat(concat(concat(l.LINE_ALLNAME,'(') ,l.line_code),')') as line_name from  memsdb.dm_dinsp d
        inner join gdgs.base_line l on d.line_code = l.line_id
        <if test="taskStatus == '2'">
            where 1=1 ${token}
        </if>
        <if test="taskStatus != '2'">
            ${token} where 1=1
        </if>
    </select>
    <select id="selectTopOneByUser" resultMap="BaseResultMap">
       select * from (select d.*,concat(concat(concat(l.LINE_ALLNAME,'('),l.LINE_CODE),')') as line_name from  MEMSDB.DM_DINSP d
       inner join gdgs.BASE_LINE l on d.LINE_CODE = l.LINE_ID
        where d.create_user_id=#{userCode}  order by d.create_time desc)
        where ROWNUM=1
    </select>
    <select id="getNextCode" resultType="string">
        select max(substr(DINSP_CODE,length(DINSP_CODE)-2))
				 from MEMSDB.DM_DINSP t where DINSP_CODE like concat(concat('',#{strCode}),'%')
                                          and t.MNT_ORG_ID= #{orgId} and type = '1'
    </select>

    <select id="queryRecords" resultType="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinspRecord">
        select a.* from MEMSDB.DM_DINSP_RECORD a
        inner join MEMSDB.DM_DINSP b on a.DINSP_ID = b.DINSP_ID
        where b.DINSP_ID != #{dinspId} and b.STATUS = 0
        and a.SOURCE in
        <foreach collection="dssSources" item="s" index="index" separator="," open="(" close=")">
            #{s}
        </foreach>
    </select>
</mapper>
