<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintainbase.mapper.SocialUnitMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.maintainbase.domain.SocialUnit">
    <!--@mbg.generated-->
    <!--@Table SOCIAL_UNIT-->
    <result column="SOCIAL_UNIT_ID" jdbcType="DECIMAL" property="socialUnitId" />
    <result column="SOCIAL_UNIT_NAME" jdbcType="VARCHAR" property="socialUnitName" />
    <result column="SOCIAL_UNIT_TYPE" jdbcType="DECIMAL" property="socialUnitType" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="SCALE_LEVEL" jdbcType="VARCHAR" property="scaleLevel" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="COMPANY_ID" jdbcType="VARCHAR" property="companyId" />
    <result column="LONGITUDE" jdbcType="FLOAT" property="longitude" />
    <result column="LATITUDE" jdbcType="FLOAT" property="latitude" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="WRITE_DATE" jdbcType="TIMESTAMP" property="writeDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SOCIAL_UNIT_ID, SOCIAL_UNIT_NAME, SOCIAL_UNIT_TYPE, PHONE, ADDRESS, SCALE_LEVEL,
    ADD_TIME, COMPANY_ID, LONGITUDE, LATITUDE, CREATE_TIME, WRITE_DATE
  </sql>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.SocialUnit">
    <!--@mbg.generated-->
    insert into onemap.SOCIAL_UNIT (SOCIAL_UNIT_ID, SOCIAL_UNIT_NAME, SOCIAL_UNIT_TYPE,
      PHONE, ADDRESS, SCALE_LEVEL,
      ADD_TIME, COMPANY_ID, LONGITUDE,
      LATITUDE, CREATE_TIME, WRITE_DATE,BASE_ID
      )
    values (#{socialUnitId,jdbcType=DECIMAL}, #{socialUnitName,jdbcType=VARCHAR}, #{socialUnitType,jdbcType=DECIMAL},
      #{phone,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{scaleLevel,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=VARCHAR}, #{longitude,jdbcType=FLOAT},
      #{latitude,jdbcType=FLOAT}, #{createTime,jdbcType=TIMESTAMP}, #{writeDate,jdbcType=TIMESTAMP},#{baseId,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.SocialUnit">
    <!--@mbg.generated-->
    insert into onemap.SOCIAL_UNIT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="socialUnitId != null">
        SOCIAL_UNIT_ID,
      </if>
      <if test="socialUnitName != null">
        SOCIAL_UNIT_NAME,
      </if>
      <if test="socialUnitType != null">
        SOCIAL_UNIT_TYPE,
      </if>
      <if test="phone != null">
        PHONE,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="scaleLevel != null">
        SCALE_LEVEL,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="companyId != null">
        COMPANY_ID,
      </if>
      <if test="longitude != null">
        LONGITUDE,
      </if>
      <if test="latitude != null">
        LATITUDE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="writeDate != null">
        WRITE_DATE,
      </if>
      <if test="baseId != null">
        BASE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="socialUnitId != null">
        #{socialUnitId,jdbcType=DECIMAL},
      </if>
      <if test="socialUnitName != null">
        #{socialUnitName,jdbcType=VARCHAR},
      </if>
      <if test="socialUnitType != null">
        #{socialUnitType,jdbcType=DECIMAL},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="scaleLevel != null">
        #{scaleLevel,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=FLOAT},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=FLOAT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="writeDate != null">
        #{writeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="baseId != null">
        #{baseId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getSocialUnitByMaintainBaseIdPage" resultType="com.hualu.highwaymaintenance.module.maintainbase.domain.SocialUnit">
    select * from ONEMAP.SOCIAL_UNIT t
    where t.BASE_ID=#{baseId}
  </select>
</mapper>