<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.RequireAnalysisSrcMapper">

<select id="page"
    resultType="com.hualu.highwaymaintenance.module.decision.entity.RequireAnalysisSrc">
    select c.*,
      nvl(PMSDB.getMultiMeasurePlanValue(replace(c.MAINTENANCE_PLAN, '、',',')), '日常养护') maintenancePlanName,
      nvl(PMSDB.getMultiMeasurePlanPrice(replace(c.MAINTENANCE_PLAN, '、',','), to_number(nvl(c.length, 0)), #{projectId}, 3.75), 0) totalConst,
      nvl(PMSDB.getMultiMeasurePlanPrice(SUBSTR(c.MAINTENANCE_PLAN, 1, INSTR(c.MAINTENANCE_PLAN, '、') - 1),to_number(nvl(c.length, 0)),
        #{projectId}, 3.75), 0) minTotalConst
    from PMSDB.REQUIRE_ANALYSIS_SRC c
    <where>
      <if test="projectId != null and projectId != ''">
        c.PROJECT_ID = #{projectId,jdbcType=VARCHAR}
      </if>

      <if test="lane != null and lane != ''">
        and c.lane = #{lane,jdbcType=VARCHAR}
      </if>

      <if test="year != null and year != ''">
        and c.YEAR = #{year,jdbcType=VARCHAR}
      </if>

      <if test="maintenanceTypeName != null and maintenanceTypeName != ''">
        and c.MAINTENANCE_NATURE_NAME = #{maintenanceTypeName,jdbcType=VARCHAR}
      </if>

      <if test="startStake != null and endStake != null">
        and c.START_STAKE between #{startStake,jdbcType=DOUBLE} and #{endStake,jdbcType=DOUBLE}
      </if>

      <if test="startStake != null and endStake != null">
        and c.END_STAKE between #{startStake,jdbcType=DOUBLE} and #{endStake,jdbcType=DOUBLE}
      </if>
    </where>
    order by c.lane, c.start_stake
  </select>
</mapper>