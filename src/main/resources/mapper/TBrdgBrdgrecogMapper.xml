<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dic.mapper.TBrdgBrdgrecogMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dic.domain.TBrdgBrdgrecog">
            <id property="brdgrecogId" column="BRDGRECOG_ID" jdbcType="VARCHAR"/>
            <result property="roadNum" column="ROAD_NUM" jdbcType="VARCHAR"/>
            <result property="roadName" column="ROAD_NAME" jdbcType="VARCHAR"/>
            <result property="routeLvl" column="ROUTE_LVL" jdbcType="DECIMAL"/>
            <result property="seqNo" column="SEQ_NO" jdbcType="DECIMAL"/>
            <result property="oldHighwayStake" column="OLD_HIGHWAY_STAKE" jdbcType="VARCHAR"/>
            <result property="funcType" column="FUNC_TYPE" jdbcType="DECIMAL"/>
            <result property="cnstrctStake" column="CNSTRCT_STAKE" jdbcType="VARCHAR"/>
            <result property="designStake" column="DESIGN_STAKE" jdbcType="VARCHAR"/>
            <result property="brdgNum" column="BRDG_NUM" jdbcType="DECIMAL"/>
            <result property="brdgNature" column="BRDG_NATURE" jdbcType="VARCHAR"/>
            <result property="longitude" column="LONGITUDE" jdbcType="DECIMAL"/>
            <result property="acrossFtrType" column="ACROSS_FTR_TYPE" jdbcType="VARCHAR"/>
            <result property="brdgSpanGroup" column="BRDG_SPAN_GROUP" jdbcType="VARCHAR"/>
            <result property="brdgStatus" column="BRDG_STATUS" jdbcType="VARCHAR"/>
            <result property="latitude" column="LATITUDE" jdbcType="DECIMAL"/>
            <result property="acrossFtrName" column="ACROSS_FTR_NAME" jdbcType="VARCHAR"/>
            <result property="passageName" column="PASSAGE_NAME" jdbcType="VARCHAR"/>
            <result property="passageNum" column="PASSAGE_NUM" jdbcType="VARCHAR"/>
            <result property="dsgnLoad" column="DSGN_LOAD" jdbcType="VARCHAR"/>
            <result property="trafficLoad" column="TRAFFIC_LOAD" jdbcType="VARCHAR"/>
            <result property="bendSlope" column="BEND_SLOPE" jdbcType="VARCHAR"/>
            <result property="brdgDeck" column="BRDG_DECK" jdbcType="VARCHAR"/>
            <result property="secRoute" column="SEC_ROUTE" jdbcType="VARCHAR"/>
            <result property="compTime" column="COMP_TIME" jdbcType="TIMESTAMP"/>
            <result property="dsgnOrg" column="DSGN_ORG" jdbcType="VARCHAR"/>
            <result property="cnstrctOrg" column="CNSTRCT_ORG" jdbcType="VARCHAR"/>
            <result property="superOrg" column="SUPER_ORG" jdbcType="VARCHAR"/>
            <result property="cbmsOrg" column="CBMS_ORG" jdbcType="VARCHAR"/>
            <result property="regulatoryOrg" column="REGULATORY_ORG" jdbcType="VARCHAR"/>
            <result property="brdgRating" column="BRDG_RATING" jdbcType="DECIMAL"/>
            <result property="apprTime" column="APPR_TIME" jdbcType="TIMESTAMP"/>
            <result property="ratingId" column="RATING_ID" jdbcType="VARCHAR"/>
            <result property="validFlag" column="VALID_FLAG" jdbcType="DECIMAL"/>
            <result property="rpIntrvlId" column="RP_INTRVL_ID" jdbcType="VARCHAR"/>
            <result property="brdgCode" column="BRDG_CODE" jdbcType="VARCHAR"/>
            <result property="brdgName" column="BRDG_NAME" jdbcType="VARCHAR"/>
            <result property="cntrStake" column="CNTR_STAKE" jdbcType="VARCHAR"/>
            <result property="cntrStakeNum" column="CNTR_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="cntrSenssionNum" column="CNTR_SENSSION_NUM" jdbcType="DECIMAL"/>
            <result property="oprtOrgCode" column="OPRT_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="createUserCode" column="CREATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserCode" column="UPDATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="lineId" column="LINE_ID" jdbcType="VARCHAR"/>
            <result property="place" column="PLACE" jdbcType="VARCHAR"/>
            <result property="brdgLineType" column="BRDG_LINE_TYPE" jdbcType="VARCHAR"/>
            <result property="frameNum" column="FRAME_NUM" jdbcType="VARCHAR"/>
            <result property="roadType" column="ROAD_TYPE" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="trafficCode" column="TRAFFIC_CODE" jdbcType="VARCHAR"/>
            <result property="boxroomType" column="BOXROOM_TYPE" jdbcType="VARCHAR"/>
            <result property="boxroomTypeValue" column="BOXROOM_TYPE_VALUE" jdbcType="VARCHAR"/>
            <result property="mainRampLineId" column="MAIN_RAMP_LINE_ID" jdbcType="VARCHAR"/>
            <result property="startOffset" column="START_OFFSET" jdbcType="DECIMAL"/>
            <result property="rampStake" column="RAMP_STAKE" jdbcType="VARCHAR"/>
            <result property="excelResult" column="EXCEL_RESULT" jdbcType="DECIMAL"/>
            <result property="startStake" column="START_STAKE" jdbcType="DECIMAL"/>
            <result property="endStake" column="END_STAKE" jdbcType="DECIMAL"/>
            <result property="lastPrjId" column="LAST_PRJ_ID" jdbcType="VARCHAR"/>
            <result property="inCatalogue" column="IN_CATALOGUE" jdbcType="DECIMAL"/>
            <result property="logicCntrStake" column="LOGIC_CNTR_STAKE" jdbcType="VARCHAR"/>
            <result property="logicCntrStakeNum" column="LOGIC_CNTR_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="oprtOrgName" column="OPRT_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="mainId" column="MAIN_ID" jdbcType="VARCHAR"/>
            <result property="mainName" column="MAIN_NAME" jdbcType="VARCHAR"/>
            <result property="jd" column="JD" jdbcType="DECIMAL"/>
            <result property="wd" column="WD" jdbcType="DECIMAL"/>
            <result property="ssfSet" column="SSF_SET" jdbcType="VARCHAR"/>
            <result property="newId" column="NEW_ID" jdbcType="VARCHAR"/>
            <result property="yhid" column="YHID" jdbcType="VARCHAR"/>
            <result property="grade2020" column="GRADE_2020" jdbcType="DECIMAL"/>
            <result property="grade2019" column="GRADE_2019" jdbcType="DECIMAL"/>
            <result property="grade2018" column="GRADE_2018" jdbcType="DECIMAL"/>
            <result property="grade2017" column="GRADE_2017" jdbcType="DECIMAL"/>
            <result property="grade2016" column="GRADE_2016" jdbcType="DECIMAL"/>
            <result property="brdgType" column="BRDG_TYPE" jdbcType="VARCHAR"/>
            <result property="prjCode" column="PRJ_CODE" jdbcType="VARCHAR"/>
            <result property="prjName" column="PRJ_NAME" jdbcType="VARCHAR"/>
            <result property="oldBrdgcode" column="OLD_BRDGCODE" jdbcType="VARCHAR"/>
            <result property="kuanlzq" column="KUANLZQ" jdbcType="DECIMAL"/>
            <result property="sfxzdm" column="SFXZDM" jdbcType="DECIMAL"/>
            <result property="f039" column="F039" jdbcType="DECIMAL"/>
            <result property="gjxzdm" column="GJXZDM" jdbcType="DECIMAL"/>
            <result property="f042" column="F042" jdbcType="DECIMAL"/>
            <result property="bzxm" column="BZXM" jdbcType="DECIMAL"/>
            <result property="gcxzdm" column="GCXZDM" jdbcType="DECIMAL"/>
            <result property="f099" column="F099" jdbcType="VARCHAR"/>
            <result property="f103" column="F103" jdbcType="DECIMAL"/>
            <result property="f107" column="F107" jdbcType="DECIMAL"/>
            <result property="f109" column="F109" jdbcType="DECIMAL"/>
            <result property="f105" column="F105" jdbcType="DECIMAL"/>
            <result property="hasThreePart" column="HAS_THREE_PART" jdbcType="DECIMAL"/>
            <result property="lastPrjDate" column="LAST_PRJ_DATE" jdbcType="TIMESTAMP"/>
            <result property="wsg" column="WSG" jdbcType="DECIMAL"/>
            <result property="transTime" column="TRANS_TIME" jdbcType="TIMESTAMP"/>
            <result property="transRemark" column="TRANS_REMARK" jdbcType="VARCHAR"/>
            <result property="transFile" column="TRANS_FILE" jdbcType="VARCHAR"/>
            <result property="lastDispCompDate" column="LAST_DISP_COMP_DATE" jdbcType="TIMESTAMP"/>
            <result property="lastDispId" column="LAST_DISP_ID" jdbcType="VARCHAR"/>
            <result property="routeCodeVersion" column="ROUTE_CODE_VERSION" jdbcType="VARCHAR"/>
            <result property="routeCode" column="ROUTE_CODE" jdbcType="VARCHAR"/>
            <result property="brdgCodeBak" column="BRDG_CODE_BAK" jdbcType="VARCHAR"/>
            <result property="physicsCntrStakeNumNew" column="PHYSICS_CNTR_STAKE_NUM_NEW" jdbcType="DECIMAL"/>
            <result property="widenBridge" column="WIDEN_BRIDGE" jdbcType="DECIMAL"/>
            <result property="radius" column="RADIUS" jdbcType="DECIMAL"/>
            <result property="hasHla" column="HAS_HLA" jdbcType="DECIMAL"/>
            <result property="hasScp" column="HAS_SCP" jdbcType="DECIMAL"/>
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="routeName" column="ROUTE_NAME" jdbcType="VARCHAR"/>
            <result property="hlaDesc" column="HLA_DESC" jdbcType="VARCHAR"/>
            <result property="jgjc" column="JGJC" jdbcType="DECIMAL"/>
            <result property="czjc" column="CZJC" jdbcType="DECIMAL"/>
            <result property="hzjc" column="HZJC" jdbcType="DECIMAL"/>
            <result property="underloadwarn" column="UNDERLOADWARN" jdbcType="DECIMAL"/>
            <result property="humiditywarn" column="HUMIDITYWARN" jdbcType="DECIMAL"/>
            <result property="strainwarn" column="STRAINWARN" jdbcType="DECIMAL"/>
            <result property="accelerationwarn" column="ACCELERATIONWARN" jdbcType="DECIMAL"/>
            <result property="routeCodeBak" column="ROUTE_CODE_BAK" jdbcType="VARCHAR"/>
            <result property="delTime" column="DEL_TIME" jdbcType="TIMESTAMP"/>
            <result property="delPerson" column="DEL_PERSON" jdbcType="VARCHAR"/>
            <result property="recoverTime" column="RECOVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="recoverPerson" column="RECOVER_PERSON" jdbcType="VARCHAR"/>
            <result property="xqCode" column="XQ_CODE" jdbcType="VARCHAR"/>
            <result property="brdgCode20210911" column="BRDG_CODE_20210911" jdbcType="VARCHAR"/>
            <result property="drivingDirection" column="DRIVING_DIRECTION" jdbcType="VARCHAR"/>
            <result property="brdgCode20210914" column="BRDG_CODE_20210914" jdbcType="VARCHAR"/>
            <result property="brdgCode20210915" column="BRDG_CODE_20210915" jdbcType="VARCHAR"/>
            <result property="bw" column="BW" jdbcType="DECIMAL"/>
            <result property="jcCode" column="JC_CODE" jdbcType="VARCHAR"/>
            <result property="reportRoadNum" column="REPORT_ROAD_NUM" jdbcType="VARCHAR"/>
            <result property="latitueBak" column="LATITUE_BAK" jdbcType="DECIMAL"/>
            <result property="longtitueBak" column="LONGTITUE_BAK" jdbcType="DECIMAL"/>
            <result property="transProvincial" column="TRANS_PROVINCIAL" jdbcType="DECIMAL"/>
            <result property="placeBak" column="PLACE_BAK" jdbcType="VARCHAR"/>
            <result property="oldYhid" column="OLD_YHID" jdbcType="VARCHAR"/>
            <result property="dataIntegrity" column="DATA_INTEGRITY" jdbcType="DECIMAL"/>
            <result property="neverDj" column="NEVER_DJ" jdbcType="DECIMAL"/>
            <result property="neverDj3" column="NEVER_DJ_3" jdbcType="DECIMAL"/>
            <result property="trafficCodeBak" column="TRAFFIC_CODE_BAK" jdbcType="VARCHAR"/>
            <result property="trafficCodeBak20220120" column="TRAFFIC_CODE_BAK_20220120" jdbcType="VARCHAR"/>
            <result property="trafficCodeBak20220221" column="TRAFFIC_CODE_BAK_20220221" jdbcType="VARCHAR"/>
            <result property="mortarrubble" column="MORTARRUBBLE" jdbcType="DECIMAL"/>
            <result property="reinforcementform" column="REINFORCEMENTFORM" jdbcType="VARCHAR"/>
            <result property="reinforcementformextra" column="REINFORCEMENTFORMEXTRA" jdbcType="VARCHAR"/>
            <result property="lghlMtrl" column="LGHL_MTRL" jdbcType="DECIMAL"/>
            <result property="maintainGrade" column="MAINTAIN_GRADE" jdbcType="DECIMAL"/>
            <result property="xiaoyu40" column="XIAOYU40" jdbcType="DECIMAL"/>
            <result property="xiaoyu100" column="XIAOYU100" jdbcType="DECIMAL"/>
            <result property="dayudengyu100" column="DAYUDENGYU100" jdbcType="DECIMAL"/>
            <result property="hasav" column="HASAV" jdbcType="VARCHAR"/>
            <result property="bse" column="BSE" jdbcType="VARCHAR"/>
            <result property="inspectCompany" column="INSPECT_COMPANY" jdbcType="VARCHAR"/>
            <result property="isMain" column="IS_MAIN" jdbcType="DECIMAL"/>
            <result property="oldRoadbm" column="OLD_ROADBM" jdbcType="VARCHAR"/>
            <result property="jwdgx" column="JWDGX" jdbcType="DECIMAL"/>
            <result property="jwdgxsj" column="JWDGXSJ" jdbcType="VARCHAR"/>
            <result property="jdGis" column="JD_GIS" jdbcType="DECIMAL"/>
            <result property="wdGis" column="WD_GIS" jdbcType="DECIMAL"/>
            <result property="jdStake" column="JD_STAKE" jdbcType="DECIMAL"/>
            <result property="wdStake" column="WD_STAKE" jdbcType="DECIMAL"/>
            <result property="disXt" column="DIS_XT" jdbcType="DECIMAL"/>
            <result property="disStake" column="DIS_STAKE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        BRDGRECOG_ID,ROAD_NUM,ROAD_NAME,
        ROUTE_LVL,SEQ_NO,OLD_HIGHWAY_STAKE,
        FUNC_TYPE,CNSTRCT_STAKE,DESIGN_STAKE,
        BRDG_NUM,BRDG_NATURE,LONGITUDE,
        ACROSS_FTR_TYPE,BRDG_SPAN_GROUP,BRDG_STATUS,
        LATITUDE,ACROSS_FTR_NAME,PASSAGE_NAME,
        PASSAGE_NUM,DSGN_LOAD,TRAFFIC_LOAD,
        BEND_SLOPE,BRDG_DECK,SEC_ROUTE,
        COMP_TIME,DSGN_ORG,CNSTRCT_ORG,
        SUPER_ORG,CBMS_ORG,REGULATORY_ORG,
        BRDG_RATING,APPR_TIME,RATING_ID,
        VALID_FLAG,RP_INTRVL_ID,BRDG_CODE,
        BRDG_NAME,CNTR_STAKE,CNTR_STAKE_NUM,
        CNTR_SENSSION_NUM,OPRT_ORG_CODE,CREATE_USER_CODE,
        CREATE_TIME,UPDATE_USER_CODE,UPDATE_TIME,
        LINE_ID,PLACE,BRDG_LINE_TYPE,
        FRAME_NUM,ROAD_TYPE,REMARK,
        TRAFFIC_CODE,BOXROOM_TYPE,BOXROOM_TYPE_VALUE,
        MAIN_RAMP_LINE_ID,START_OFFSET,RAMP_STAKE,
        EXCEL_RESULT,START_STAKE,END_STAKE,
        LAST_PRJ_ID,IN_CATALOGUE,LOGIC_CNTR_STAKE,
        LOGIC_CNTR_STAKE_NUM,OPRT_ORG_NAME,MAIN_ID,
        MAIN_NAME,JD,WD,
        SSF_SET,NEW_ID,YHID,
        GRADE_2020,GRADE_2019,GRADE_2018,
        GRADE_2017,GRADE_2016,BRDG_TYPE,
        PRJ_CODE,PRJ_NAME,OLD_BRDGCODE,
        KUANLZQ,SFXZDM,F039,
        GJXZDM,F042,BZXM,
        GCXZDM,F099,F103,
        F107,F109,F105,
        HAS_THREE_PART,LAST_PRJ_DATE,WSG,
        TRANS_TIME,TRANS_REMARK,TRANS_FILE,
        LAST_DISP_COMP_DATE,LAST_DISP_ID,ROUTE_CODE_VERSION,
        ROUTE_CODE,BRDG_CODE_BAK,PHYSICS_CNTR_STAKE_NUM_NEW,
        WIDEN_BRIDGE,RADIUS,HAS_HLA,
        HAS_SCP,ID,ROUTE_NAME,
        HLA_DESC,JGJC,CZJC,
        HZJC,UNDERLOADWARN,HUMIDITYWARN,
        STRAINWARN,ACCELERATIONWARN,ROUTE_CODE_BAK,
        DEL_TIME,DEL_PERSON,RECOVER_TIME,
        RECOVER_PERSON,XQ_CODE,BRDG_CODE_20210911,
        DRIVING_DIRECTION,BRDG_CODE_20210914,BRDG_CODE_20210915,
        BW,JC_CODE,REPORT_ROAD_NUM,
        LATITUE_BAK,LONGTITUE_BAK,TRANS_PROVINCIAL,
        PLACE_BAK,OLD_YHID,DATA_INTEGRITY,
        NEVER_DJ,NEVER_DJ_3,TRAFFIC_CODE_BAK,
        TRAFFIC_CODE_BAK_20220120,TRAFFIC_CODE_BAK_20220221,MORTARRUBBLE,
        REINFORCEMENTFORM,REINFORCEMENTFORMEXTRA,LGHL_MTRL,
        MAINTAIN_GRADE,XIAOYU40,XIAOYU100,
        DAYUDENGYU100,HASAV,BSE,
        INSPECT_COMPANY,IS_MAIN,OLD_ROADBM,
        JWDGX,JWDGXSJ,JD_GIS,
        WD_GIS,JD_STAKE,WD_STAKE,
        DIS_XT,DIS_STAKE
    </sql>
</mapper>
