<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.user.mapper.FwRightOrgMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.user.domain.FwRightOrg">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="orgName" column="ORG_NAME" jdbcType="VARCHAR"/>
            <result property="orgCode" column="ORG_CODE" jdbcType="VARCHAR"/>
            <result property="parentId" column="PARENT_ID" jdbcType="VARCHAR"/>
            <result property="orgLevel" column="ORG_LEVEL" jdbcType="VARCHAR"/>
            <result property="orgAddress" column="ORG_ADDRESS" jdbcType="VARCHAR"/>
            <result property="orgManager" column="ORG_MANAGER" jdbcType="VARCHAR"/>
            <result property="linkMan" column="LINK_MAN" jdbcType="VARCHAR"/>
            <result property="linkPhone" column="LINK_PHONE" jdbcType="VARCHAR"/>
            <result property="isDept" column="IS_DEPT" jdbcType="VARCHAR"/>
            <result property="zipCode" column="ZIP_CODE" jdbcType="VARCHAR"/>
            <result property="isEnable" column="IS_ENABLE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="VARCHAR"/>
            <result property="createUserCode" column="CREATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="createUserName" column="CREATE_USER_NAME" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserCode" column="UPDATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="UPDATE_USER_NAME" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="defaultSeqNo" column="DEFAULT_SEQ_NO" jdbcType="DECIMAL"/>
            <result property="orgFullname" column="ORG_FULLNAME" jdbcType="VARCHAR"/>
            <result property="orgReportId" column="ORG_REPORT_ID" jdbcType="DECIMAL"/>
            <result property="orgType" column="ORG_TYPE" jdbcType="VARCHAR"/>
            <result property="orgGrpFlag" column="ORG_GRP_FLAG" jdbcType="VARCHAR"/>
            <result property="orgNameFt" column="ORG_NAME_FT" jdbcType="VARCHAR"/>
            <result property="orgNameEn" column="ORG_NAME_EN" jdbcType="VARCHAR"/>
            <result property="orgNature" column="ORG_NATURE" jdbcType="VARCHAR"/>
            <result property="orgCategory" column="ORG_CATEGORY" jdbcType="VARCHAR"/>
            <result property="orgStatus" column="ORG_STATUS" jdbcType="VARCHAR"/>
            <result property="orgLegal" column="ORG_LEGAL" jdbcType="VARCHAR"/>
            <result property="orgCertiNo" column="ORG_CERTI_NO" jdbcType="VARCHAR"/>
            <result property="orgRegAddr" column="ORG_REG_ADDR" jdbcType="VARCHAR"/>
            <result property="orgIcaRegNo" column="ORG_ICA_REG_NO" jdbcType="VARCHAR"/>
            <result property="orgTaxRegNo" column="ORG_TAX_REG_NO" jdbcType="VARCHAR"/>
            <result property="fax" column="FAX" jdbcType="VARCHAR"/>
            <result property="cbmsOrgCode" column="CBMS_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="maxX" column="MAX_X" jdbcType="DECIMAL"/>
            <result property="maxY" column="MAX_Y" jdbcType="DECIMAL"/>
            <result property="minX" column="MIN_X" jdbcType="DECIMAL"/>
            <result property="minY" column="MIN_Y" jdbcType="DECIMAL"/>
            <result property="gisX" column="GIS_X" jdbcType="DECIMAL"/>
            <result property="gisY" column="GIS_Y" jdbcType="DECIMAL"/>
            <result property="isOprtorg" column="IS_OPRTORG" jdbcType="DECIMAL"/>
            <result property="isPrjorg" column="IS_PRJORG" jdbcType="DECIMAL"/>
            <result property="seqNo" column="SEQ_NO" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,ORG_NAME,ORG_CODE,
        PARENT_ID,ORG_LEVEL,ORG_ADDRESS,
        ORG_MANAGER,LINK_MAN,LINK_PHONE,
        IS_DEPT,ZIP_CODE,IS_ENABLE,
        IS_DELETED,CREATE_USER_CODE,CREATE_USER_NAME,
        CREATE_TIME,UPDATE_USER_CODE,UPDATE_USER_NAME,
        UPDATE_TIME,REMARK,DEFAULT_SEQ_NO,
        ORG_FULLNAME,ORG_REPORT_ID,ORG_TYPE,
        ORG_GRP_FLAG,ORG_NAME_FT,ORG_NAME_EN,
        ORG_NATURE,ORG_CATEGORY,ORG_STATUS,
        ORG_LEGAL,ORG_CERTI_NO,ORG_REG_ADDR,
        ORG_ICA_REG_NO,ORG_TAX_REG_NO,FAX,
        CBMS_ORG_CODE,MAX_X,MAX_Y,
        MIN_X,MIN_Y,GIS_X,
        GIS_Y,IS_OPRTORG,IS_PRJORG,
        SEQ_NO
    </sql>
    <select id="queryGrandParentSelf" resultType="com.hualu.highwaymaintenance.module.user.domain.FwRightOrg">
        ${sql}
    </select>
</mapper>
