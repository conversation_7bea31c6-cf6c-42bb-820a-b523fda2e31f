<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgPartStrctInfoMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.TBrdgPartStrctInfo">
    <!--@mbg.generated-->
    <!--@Table T_BRDG_PART_STRCT_INFO-->
    <id column="PARTS_ID" jdbcType="VARCHAR" property="partsId" />
    <result column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="PARTSTYPE_ID" jdbcType="VARCHAR" property="partstypeId" />
    <result column="PARTS_TYPE" jdbcType="DECIMAL" property="partsType" />
    <result column="BRDGTYPE_ID" jdbcType="VARCHAR" property="brdgtypeId" />
    <result column="COMPATTR_NUM" jdbcType="DECIMAL" property="compattrNum" />
    <result column="HOLE_NUM_PARTS" jdbcType="DECIMAL" property="holeNumParts" />
    <result column="BRDG_CODE" jdbcType="VARCHAR" property="brdgCode" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="GRID_ID" jdbcType="VARCHAR" property="gridId" />
    <result column="CHILD_PARTS_ID" jdbcType="VARCHAR" property="childPartsId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PARTS_ID, BRDGRECOG_ID, PARTSTYPE_ID, PARTS_TYPE, BRDGTYPE_ID, COMPATTR_NUM, HOLE_NUM_PARTS, 
    BRDG_CODE, VALID_FLAG, GRID_ID, CHILD_PARTS_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BRDG_PART_STRCT_INFO
    where PARTS_ID = #{partsId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from T_BRDG_PART_STRCT_INFO
    where PARTS_ID = #{partsId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgPartStrctInfo">
    <!--@mbg.generated-->
    insert into T_BRDG_PART_STRCT_INFO (PARTS_ID, BRDGRECOG_ID, PARTSTYPE_ID, 
      PARTS_TYPE, BRDGTYPE_ID, COMPATTR_NUM, 
      HOLE_NUM_PARTS, BRDG_CODE, VALID_FLAG, 
      GRID_ID, CHILD_PARTS_ID)
    values (#{partsId,jdbcType=VARCHAR}, #{brdgrecogId,jdbcType=VARCHAR}, #{partstypeId,jdbcType=VARCHAR}, 
      #{partsType,jdbcType=DECIMAL}, #{brdgtypeId,jdbcType=VARCHAR}, #{compattrNum,jdbcType=DECIMAL}, 
      #{holeNumParts,jdbcType=DECIMAL}, #{brdgCode,jdbcType=VARCHAR}, #{validFlag,jdbcType=DECIMAL}, 
      #{gridId,jdbcType=VARCHAR}, #{childPartsId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgPartStrctInfo">
    <!--@mbg.generated-->
    insert into T_BRDG_PART_STRCT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="partsId != null">
        PARTS_ID,
      </if>
      <if test="brdgrecogId != null">
        BRDGRECOG_ID,
      </if>
      <if test="partstypeId != null">
        PARTSTYPE_ID,
      </if>
      <if test="partsType != null">
        PARTS_TYPE,
      </if>
      <if test="brdgtypeId != null">
        BRDGTYPE_ID,
      </if>
      <if test="compattrNum != null">
        COMPATTR_NUM,
      </if>
      <if test="holeNumParts != null">
        HOLE_NUM_PARTS,
      </if>
      <if test="brdgCode != null">
        BRDG_CODE,
      </if>
      <if test="validFlag != null">
        VALID_FLAG,
      </if>
      <if test="gridId != null">
        GRID_ID,
      </if>
      <if test="childPartsId != null">
        CHILD_PARTS_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="partsId != null">
        #{partsId,jdbcType=VARCHAR},
      </if>
      <if test="brdgrecogId != null">
        #{brdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="partstypeId != null">
        #{partstypeId,jdbcType=VARCHAR},
      </if>
      <if test="partsType != null">
        #{partsType,jdbcType=DECIMAL},
      </if>
      <if test="brdgtypeId != null">
        #{brdgtypeId,jdbcType=VARCHAR},
      </if>
      <if test="compattrNum != null">
        #{compattrNum,jdbcType=DECIMAL},
      </if>
      <if test="holeNumParts != null">
        #{holeNumParts,jdbcType=DECIMAL},
      </if>
      <if test="brdgCode != null">
        #{brdgCode,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=DECIMAL},
      </if>
      <if test="gridId != null">
        #{gridId,jdbcType=VARCHAR},
      </if>
      <if test="childPartsId != null">
        #{childPartsId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgPartStrctInfo">
    <!--@mbg.generated-->
    update T_BRDG_PART_STRCT_INFO
    <set>
      <if test="brdgrecogId != null">
        BRDGRECOG_ID = #{brdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="partstypeId != null">
        PARTSTYPE_ID = #{partstypeId,jdbcType=VARCHAR},
      </if>
      <if test="partsType != null">
        PARTS_TYPE = #{partsType,jdbcType=DECIMAL},
      </if>
      <if test="brdgtypeId != null">
        BRDGTYPE_ID = #{brdgtypeId,jdbcType=VARCHAR},
      </if>
      <if test="compattrNum != null">
        COMPATTR_NUM = #{compattrNum,jdbcType=DECIMAL},
      </if>
      <if test="holeNumParts != null">
        HOLE_NUM_PARTS = #{holeNumParts,jdbcType=DECIMAL},
      </if>
      <if test="brdgCode != null">
        BRDG_CODE = #{brdgCode,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      </if>
      <if test="gridId != null">
        GRID_ID = #{gridId,jdbcType=VARCHAR},
      </if>
      <if test="childPartsId != null">
        CHILD_PARTS_ID = #{childPartsId,jdbcType=VARCHAR},
      </if>
    </set>
    where PARTS_ID = #{partsId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgPartStrctInfo">
    <!--@mbg.generated-->
    update T_BRDG_PART_STRCT_INFO
    set BRDGRECOG_ID = #{brdgrecogId,jdbcType=VARCHAR},
      PARTSTYPE_ID = #{partstypeId,jdbcType=VARCHAR},
      PARTS_TYPE = #{partsType,jdbcType=DECIMAL},
      BRDGTYPE_ID = #{brdgtypeId,jdbcType=VARCHAR},
      COMPATTR_NUM = #{compattrNum,jdbcType=DECIMAL},
      HOLE_NUM_PARTS = #{holeNumParts,jdbcType=DECIMAL},
      BRDG_CODE = #{brdgCode,jdbcType=VARCHAR},
      VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      GRID_ID = #{gridId,jdbcType=VARCHAR},
      CHILD_PARTS_ID = #{childPartsId,jdbcType=VARCHAR}
    where PARTS_ID = #{partsId,jdbcType=VARCHAR}
  </update>
</mapper>