<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgRecogmanageMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.TBrdgRecogmanage">
    <!--@mbg.generated-->
    <!--@Table T_BRDG_RECOGMANAGE-->
    <id column="RECOGMANAGE_ID" jdbcType="VARCHAR" property="recogmanageId" />
    <result column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="MAIN_BRDGRECOG_ID" jdbcType="VARCHAR" property="mainBrdgrecogId" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="ZZ_NUM" jdbcType="DECIMAL" property="zzNum" />
    <result column="MAIN_BRDG_TYPE_NAME" jdbcType="VARCHAR" property="mainBrdgTypeName" />
    <result column="MAX_SPAN_KIND" jdbcType="DECIMAL" property="maxSpanKind" />
    <result column="HL_NUM" jdbcType="DECIMAL" property="hlNum" />
    <result column="PS_NUM" jdbcType="DECIMAL" property="psNum" />
    <result column="SSF_NUM" jdbcType="DECIMAL" property="ssfNum" />
    <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="AVG_LEN" jdbcType="DECIMAL" property="avgLen" />
    <result column="COUN_BRDG" jdbcType="DECIMAL" property="counBrdg" />
    <result column="SUM_LEN" jdbcType="DECIMAL" property="sumLen" />
    <result column="MAX_SPAN" jdbcType="DECIMAL" property="maxSpan" />
    <result column="MAINTAIN_GRADE" jdbcType="DECIMAL" property="maintainGrade" />
    <result column="FRAME_NUM" jdbcType="DECIMAL" property="frameNum" />
    <result column="COMMUTE_DATE" jdbcType="DECIMAL" property="commuteDate" />
    <result column="HAS_SCP" jdbcType="DECIMAL" property="hasScp" />
    <result column="YHID" jdbcType="VARCHAR" property="yhid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RECOGMANAGE_ID, BRDGRECOG_ID, MAIN_BRDGRECOG_ID, VALID_FLAG, ZZ_NUM, MAIN_BRDG_TYPE_NAME, 
    MAX_SPAN_KIND, HL_NUM, PS_NUM, SSF_NUM, UPDATE_USER, UPDATE_DATE, AVG_LEN, COUN_BRDG, 
    SUM_LEN, MAX_SPAN, MAINTAIN_GRADE, FRAME_NUM, COMMUTE_DATE, HAS_SCP, YHID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BRDG_RECOGMANAGE
    where RECOGMANAGE_ID = #{recogmanageId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from T_BRDG_RECOGMANAGE
    where RECOGMANAGE_ID = #{recogmanageId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgRecogmanage">
    <!--@mbg.generated-->
    insert into T_BRDG_RECOGMANAGE (RECOGMANAGE_ID, BRDGRECOG_ID, MAIN_BRDGRECOG_ID, 
      VALID_FLAG, ZZ_NUM, MAIN_BRDG_TYPE_NAME, 
      MAX_SPAN_KIND, HL_NUM, PS_NUM, 
      SSF_NUM, UPDATE_USER, UPDATE_DATE, 
      AVG_LEN, COUN_BRDG, SUM_LEN, 
      MAX_SPAN, MAINTAIN_GRADE, FRAME_NUM, 
      COMMUTE_DATE, HAS_SCP, YHID
      )
    values (#{recogmanageId,jdbcType=VARCHAR}, #{brdgrecogId,jdbcType=VARCHAR}, #{mainBrdgrecogId,jdbcType=VARCHAR}, 
      #{validFlag,jdbcType=DECIMAL}, #{zzNum,jdbcType=DECIMAL}, #{mainBrdgTypeName,jdbcType=VARCHAR}, 
      #{maxSpanKind,jdbcType=DECIMAL}, #{hlNum,jdbcType=DECIMAL}, #{psNum,jdbcType=DECIMAL}, 
      #{ssfNum,jdbcType=DECIMAL}, #{updateUser,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{avgLen,jdbcType=DECIMAL}, #{counBrdg,jdbcType=DECIMAL}, #{sumLen,jdbcType=DECIMAL}, 
      #{maxSpan,jdbcType=DECIMAL}, #{maintainGrade,jdbcType=DECIMAL}, #{frameNum,jdbcType=DECIMAL}, 
      #{commuteDate,jdbcType=DECIMAL}, #{hasScp,jdbcType=DECIMAL}, #{yhid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgRecogmanage">
    <!--@mbg.generated-->
    insert into T_BRDG_RECOGMANAGE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recogmanageId != null">
        RECOGMANAGE_ID,
      </if>
      <if test="brdgrecogId != null">
        BRDGRECOG_ID,
      </if>
      <if test="mainBrdgrecogId != null">
        MAIN_BRDGRECOG_ID,
      </if>
      <if test="validFlag != null">
        VALID_FLAG,
      </if>
      <if test="zzNum != null">
        ZZ_NUM,
      </if>
      <if test="mainBrdgTypeName != null">
        MAIN_BRDG_TYPE_NAME,
      </if>
      <if test="maxSpanKind != null">
        MAX_SPAN_KIND,
      </if>
      <if test="hlNum != null">
        HL_NUM,
      </if>
      <if test="psNum != null">
        PS_NUM,
      </if>
      <if test="ssfNum != null">
        SSF_NUM,
      </if>
      <if test="updateUser != null">
        UPDATE_USER,
      </if>
      <if test="updateDate != null">
        UPDATE_DATE,
      </if>
      <if test="avgLen != null">
        AVG_LEN,
      </if>
      <if test="counBrdg != null">
        COUN_BRDG,
      </if>
      <if test="sumLen != null">
        SUM_LEN,
      </if>
      <if test="maxSpan != null">
        MAX_SPAN,
      </if>
      <if test="maintainGrade != null">
        MAINTAIN_GRADE,
      </if>
      <if test="frameNum != null">
        FRAME_NUM,
      </if>
      <if test="commuteDate != null">
        COMMUTE_DATE,
      </if>
      <if test="hasScp != null">
        HAS_SCP,
      </if>
      <if test="yhid != null">
        YHID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recogmanageId != null">
        #{recogmanageId,jdbcType=VARCHAR},
      </if>
      <if test="brdgrecogId != null">
        #{brdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="mainBrdgrecogId != null">
        #{mainBrdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=DECIMAL},
      </if>
      <if test="zzNum != null">
        #{zzNum,jdbcType=DECIMAL},
      </if>
      <if test="mainBrdgTypeName != null">
        #{mainBrdgTypeName,jdbcType=VARCHAR},
      </if>
      <if test="maxSpanKind != null">
        #{maxSpanKind,jdbcType=DECIMAL},
      </if>
      <if test="hlNum != null">
        #{hlNum,jdbcType=DECIMAL},
      </if>
      <if test="psNum != null">
        #{psNum,jdbcType=DECIMAL},
      </if>
      <if test="ssfNum != null">
        #{ssfNum,jdbcType=DECIMAL},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="avgLen != null">
        #{avgLen,jdbcType=DECIMAL},
      </if>
      <if test="counBrdg != null">
        #{counBrdg,jdbcType=DECIMAL},
      </if>
      <if test="sumLen != null">
        #{sumLen,jdbcType=DECIMAL},
      </if>
      <if test="maxSpan != null">
        #{maxSpan,jdbcType=DECIMAL},
      </if>
      <if test="maintainGrade != null">
        #{maintainGrade,jdbcType=DECIMAL},
      </if>
      <if test="frameNum != null">
        #{frameNum,jdbcType=DECIMAL},
      </if>
      <if test="commuteDate != null">
        #{commuteDate,jdbcType=DECIMAL},
      </if>
      <if test="hasScp != null">
        #{hasScp,jdbcType=DECIMAL},
      </if>
      <if test="yhid != null">
        #{yhid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgRecogmanage">
    <!--@mbg.generated-->
    update T_BRDG_RECOGMANAGE
    <set>
      <if test="brdgrecogId != null">
        BRDGRECOG_ID = #{brdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="mainBrdgrecogId != null">
        MAIN_BRDGRECOG_ID = #{mainBrdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      </if>
      <if test="zzNum != null">
        ZZ_NUM = #{zzNum,jdbcType=DECIMAL},
      </if>
      <if test="mainBrdgTypeName != null">
        MAIN_BRDG_TYPE_NAME = #{mainBrdgTypeName,jdbcType=VARCHAR},
      </if>
      <if test="maxSpanKind != null">
        MAX_SPAN_KIND = #{maxSpanKind,jdbcType=DECIMAL},
      </if>
      <if test="hlNum != null">
        HL_NUM = #{hlNum,jdbcType=DECIMAL},
      </if>
      <if test="psNum != null">
        PS_NUM = #{psNum,jdbcType=DECIMAL},
      </if>
      <if test="ssfNum != null">
        SSF_NUM = #{ssfNum,jdbcType=DECIMAL},
      </if>
      <if test="updateUser != null">
        UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="avgLen != null">
        AVG_LEN = #{avgLen,jdbcType=DECIMAL},
      </if>
      <if test="counBrdg != null">
        COUN_BRDG = #{counBrdg,jdbcType=DECIMAL},
      </if>
      <if test="sumLen != null">
        SUM_LEN = #{sumLen,jdbcType=DECIMAL},
      </if>
      <if test="maxSpan != null">
        MAX_SPAN = #{maxSpan,jdbcType=DECIMAL},
      </if>
      <if test="maintainGrade != null">
        MAINTAIN_GRADE = #{maintainGrade,jdbcType=DECIMAL},
      </if>
      <if test="frameNum != null">
        FRAME_NUM = #{frameNum,jdbcType=DECIMAL},
      </if>
      <if test="commuteDate != null">
        COMMUTE_DATE = #{commuteDate,jdbcType=DECIMAL},
      </if>
      <if test="hasScp != null">
        HAS_SCP = #{hasScp,jdbcType=DECIMAL},
      </if>
      <if test="yhid != null">
        YHID = #{yhid,jdbcType=VARCHAR},
      </if>
    </set>
    where RECOGMANAGE_ID = #{recogmanageId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgRecogmanage">
    <!--@mbg.generated-->
    update T_BRDG_RECOGMANAGE
    set BRDGRECOG_ID = #{brdgrecogId,jdbcType=VARCHAR},
      MAIN_BRDGRECOG_ID = #{mainBrdgrecogId,jdbcType=VARCHAR},
      VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      ZZ_NUM = #{zzNum,jdbcType=DECIMAL},
      MAIN_BRDG_TYPE_NAME = #{mainBrdgTypeName,jdbcType=VARCHAR},
      MAX_SPAN_KIND = #{maxSpanKind,jdbcType=DECIMAL},
      HL_NUM = #{hlNum,jdbcType=DECIMAL},
      PS_NUM = #{psNum,jdbcType=DECIMAL},
      SSF_NUM = #{ssfNum,jdbcType=DECIMAL},
      UPDATE_USER = #{updateUser,jdbcType=VARCHAR},
      UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      AVG_LEN = #{avgLen,jdbcType=DECIMAL},
      COUN_BRDG = #{counBrdg,jdbcType=DECIMAL},
      SUM_LEN = #{sumLen,jdbcType=DECIMAL},
      MAX_SPAN = #{maxSpan,jdbcType=DECIMAL},
      MAINTAIN_GRADE = #{maintainGrade,jdbcType=DECIMAL},
      FRAME_NUM = #{frameNum,jdbcType=DECIMAL},
      COMMUTE_DATE = #{commuteDate,jdbcType=DECIMAL},
      HAS_SCP = #{hasScp,jdbcType=DECIMAL},
      YHID = #{yhid,jdbcType=VARCHAR}
    where RECOGMANAGE_ID = #{recogmanageId,jdbcType=VARCHAR}
  </update>
</mapper>