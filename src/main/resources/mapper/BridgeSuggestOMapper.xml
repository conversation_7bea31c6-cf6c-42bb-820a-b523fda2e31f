<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.BridgeSuggestOMapper">

    <select id="getBridgeSuggestO"
            resultType="com.hualu.highwaymaintenance.module.national.entity.BridgeSuggestO">
        select *
        from MEMSDB.BRIDGE_SUGGEST_O
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="target != null">
                and TARGET = #{target,jdbcType=INTEGER}
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
            order by orders
        </where>
    </select>

    <select id="getBridgeSuggestOPage"
            resultType="com.hualu.highwaymaintenance.module.national.entity.BridgeSuggestO">
        select *
        from MEMSDB.BRIDGE_SUGGEST_O
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="target != null">
                and TARGET = #{target,jdbcType=INTEGER}
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
            order by orders
        </where>
    </select>
</mapper>
