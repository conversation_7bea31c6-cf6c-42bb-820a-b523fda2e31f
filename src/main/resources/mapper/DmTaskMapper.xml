<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.task.mapper.DmTaskMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.task.domain.DmTask">
            <id property="mtaskId" column="MTASK_ID" jdbcType="VARCHAR"/>
            <result property="mtaskCode" column="MTASK_CODE" jdbcType="VARCHAR"/>
            <result property="mtaskName" column="MTASK_NAME" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="mngDeptNm" column="MNG_DEPT_NM" jdbcType="VARCHAR"/>
            <result property="askedStartDate" column="ASKED_START_DATE" jdbcType="TIMESTAMP"/>
            <result property="askedComplDate" column="ASKED_COMPL_DATE" jdbcType="TIMESTAMP"/>
            <result property="mntnOrgName" column="MNTN_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="emergyDegree" column="EMERGY_DEGREE" jdbcType="VARCHAR"/>
            <result property="sendUser" column="SEND_USER" jdbcType="VARCHAR"/>
            <result property="sendTime" column="SEND_TIME" jdbcType="TIMESTAMP"/>
            <result property="receiveUser" column="RECEIVE_USER" jdbcType="VARCHAR"/>
            <result property="receiveTime" column="RECEIVE_TIME" jdbcType="TIMESTAMP"/>
            <result property="cancelOpinion" column="CANCEL_OPINION" jdbcType="VARCHAR"/>
            <result property="techReq" column="TECH_REQ" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="processinstid" column="PROCESSINSTID" jdbcType="DECIMAL"/>
            <result property="mtaskFile" column="MTASK_FILE" jdbcType="VARCHAR"/>
            <result property="contrId" column="CONTR_ID" jdbcType="VARCHAR"/>
            <result property="place" column="PLACE" jdbcType="VARCHAR"/>
            <result property="majorProject" column="MAJOR_PROJECT" jdbcType="VARCHAR"/>
            <result property="prjOrgName" column="PRJ_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="contrName" column="CONTR_NAME" jdbcType="VARCHAR"/>
            <result property="prjOrgCode" column="PRJ_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="totalCount" column="TOTAL_COUNT" jdbcType="DECIMAL"/>
            <result property="accptCount" column="ACCPT_COUNT" jdbcType="DECIMAL"/>
        <result property="partiName" column="PARTINAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        MTASK_ID,MTASK_CODE,MTASK_NAME,
        MNT_ORG_ID,MNG_DEPT_NM,ASKED_START_DATE,
        ASKED_COMPL_DATE,MNTN_ORG_NAME,EMERGY_DEGREE,
        SEND_USER,SEND_TIME,RECEIVE_USER,
        RECEIVE_TIME,CANCEL_OPINION,TECH_REQ,
        STATUS,CREATE_USER_ID,CREATE_TIME,
        UPDATE_USER_ID,UPDATE_TIME,PROCESSINSTID,
        MTASK_FILE,CONTR_ID,PLACE,
        MAJOR_PROJECT,PRJ_ORG_NAME,PRJ_ORG_CODE,
        TOTAL_COUNT,ACCPT_COUNT
    </sql>

    <select id="taskStatus" resultMap="BaseResultMap">
         select tend.*,work.PARTINAME,c.CONTR_NAME from  memsdb.dm_task tend
        inner join  MEMSDB.MPC_CONTRACT c
        on c.CONTR_ID=tend.CONTR_ID
         left join (select PROCESSINSTID,PARTINAME from (select ROW_NUMBER() OVER(PARTITION BY PROCESSINSTID ORDER BY CREATETIME DESC) rn 
         ,CURRENTSTATE,PROCESSINSTID,PARTINAME from bps.WFWORKITEM) where rn = 1 and CURRENTSTATE != '12') work on tend.PROCESSINSTID = work.PROCESSINSTID
        <if test="taskStatus == '2'">
             where 1=1 ${token}
        </if>
        <if test="taskStatus != '2'">
            ${token} where 1=1
        </if>
    </select>
    <select id="getNextCode" resultType="java.lang.Integer">
        select max(substr(MTASK_CODE,length(MTASK_CODE)-2))  from memsdb.DM_TASK t where MTASK_CODE  like concat(concat('',#{strCode}),'%')  and t.MNT_ORG_ID=#{token}
    </select>
    <select id="getUserLastDmtask" resultMap="BaseResultMap">
        select * from (select * from memsdb.dm_task d where d.CREATE_USER_ID = #{token}  order by d.create_time desc,d.mtask_code desc)
        where ROWNUM=1
    </select>
    <select id="chooseSender4Task" resultType="com.hualu.highwaymaintenance.module.user.domain.FwRightUser">

    </select>
    <select id="loadDmTaskDetailSummaryInfo"
            resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        select c.*,(select sum(mmp.UNIT_PRICE*de.MPITEM_ACCOUNT) from MEMSDB.DM_TASK dk inner join memsdb.DM_TASK_DETAIL de on dk.MTASK_ID=de.MTASK_ID
                                                                                        inner join MEMSDB.MPC_MPITEM t on de.MPITEM_ID=t.MPITEM_ID
                                                                                        left join MEMSDB.MPC_MPITEM_PRICE mmp on mmp.contr_id =dk.contr_id and   mmp.mp_item_id = t.MPITEM_ID
                    where de.DSS_ID=c.DSS_ID) as SUM_MONEY,dt.DSS_TYPE_NAME ,
               (select count(1) from   memsdb.DM_TASK_DETAIL de
                                           inner join MEMSDB.MPC_MPITEM t on de.MPITEM_ID=t.MPITEM_ID

                where de.DSS_ID=c.DSS_ID) as MEASURE_COUNT
        from MEMSDB.DSS_INFO c
                 inner join MEMSDB.DSS_TYPE_NEW dt on c.DSS_TYPE=dt.DSS_TYPE
                 inner join GDGS.V_BASE_LINE gl on decode(c.ramp_id,c.ramp_id,c.MAIN_ROAD_ID) = gl.line_id
        where c.DSS_ID in (
            select de.DSS_ID  from memsdb.DM_TASK_DETAIL de where de.MTASK_ID=#{taskId}
        )
    </select>
</mapper>
