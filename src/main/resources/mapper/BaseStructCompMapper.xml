<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dsstype.mapper.BaseStructCompMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dsstype.domain.BaseStructComp">
            <id property="structCompId" column="STRUCT_COMP_ID" jdbcType="VARCHAR"/>
            <result property="structCompCode" column="STRUCT_COMP_CODE" jdbcType="VARCHAR"/>
            <result property="structCompName" column="STRUCT_COMP_NAME" jdbcType="VARCHAR"/>
            <result property="pStructCompId" column="P_STRUCT_COMP_ID" jdbcType="VARCHAR"/>
            <result property="assetCompType" column="ASSET_COMP_TYPE" jdbcType="VARCHAR"/>
            <result property="partCode" column="PART_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        STRUCT_COMP_ID,STRUCT_COMP_CODE,STRUCT_COMP_NAME,
        P_STRUCT_COMP_ID,ASSET_COMP_TYPE,PART_CODE
    </sql>
</mapper>
