<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgChildPartsMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.TBrdgChildParts">
    <!--@mbg.generated-->
    <!--@Table T_BRDG_CHILD_PARTS-->
    <id column="CHILD_PARTS_ID" jdbcType="VARCHAR" property="childPartsId" />
    <result column="PARTS_ID" jdbcType="VARCHAR" property="partsId" />
    <result column="HOLE_NUM" jdbcType="VARCHAR" property="holeNum" />
    <result column="PARTSTYPE_ID" jdbcType="VARCHAR" property="partstypeId" />
    <result column="PARTS_CODE" jdbcType="DECIMAL" property="partsCode" />
    <result column="BRDG_SPAN" jdbcType="DECIMAL" property="brdgSpan" />
    <result column="PIER_OR_ABUTMENT" jdbcType="VARCHAR" property="pierOrAbutment" />
    <result column="SHAPE" jdbcType="VARCHAR" property="shape" />
    <result column="BASE_TYPE" jdbcType="VARCHAR" property="baseType" />
    <result column="SIMPLE_OR_CONTINUED" jdbcType="VARCHAR" property="simpleOrContinued" />
    <result column="ABUTMENT_NUM" jdbcType="VARCHAR" property="abutmentNum" />
    <result column="PIER" jdbcType="VARCHAR" property="pier" />
    <result column="SUPPORT_ROWS" jdbcType="DECIMAL" property="supportRows" />
    <result column="NUM_PER_ROW" jdbcType="DECIMAL" property="numPerRow" />
    <result column="SUPPORT_FORM" jdbcType="VARCHAR" property="supportForm" />
    <result column="TOWER_NUM" jdbcType="VARCHAR" property="towerNum" />
    <result column="MTRL_CODE" jdbcType="VARCHAR" property="mtrlCode" />
    <result column="COMP_CREATE_WAY" jdbcType="VARCHAR" property="compCreateWay" />
    <result column="COMPATTR_NUM" jdbcType="DECIMAL" property="compattrNum" />
    <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_CODE" jdbcType="VARCHAR" property="updateUserCode" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
    <result column="COMPATTR_ID" jdbcType="VARCHAR" property="compattrId" />
    <result column="BOX_ROOM" jdbcType="VARCHAR" property="boxRoom" />
    <result column="IS_WATER_PILE" jdbcType="DECIMAL" property="isWaterPile" />
    <result column="BRDG_ID" jdbcType="VARCHAR" property="brdgId" />
    <result column="parts_ID_BAK" jdbcType="VARCHAR" property="partsIdBak" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CHILD_PARTS_ID, PARTS_ID, HOLE_NUM, PARTSTYPE_ID, PARTS_CODE, BRDG_SPAN, PIER_OR_ABUTMENT, 
    SHAPE, BASE_TYPE, SIMPLE_OR_CONTINUED, ABUTMENT_NUM, PIER, SUPPORT_ROWS, NUM_PER_ROW, 
    SUPPORT_FORM, TOWER_NUM, MTRL_CODE, COMP_CREATE_WAY, COMPATTR_NUM, CREATE_USER_CODE, 
    CREATE_TIME, UPDATE_USER_CODE, UPDATE_TIME, REMARK, VALID_FLAG, COMPATTR_ID, BOX_ROOM, 
    IS_WATER_PILE, BRDG_ID, parts_ID_BAK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BRDG_CHILD_PARTS
    where CHILD_PARTS_ID = #{childPartsId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from T_BRDG_CHILD_PARTS
    where CHILD_PARTS_ID = #{childPartsId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgChildParts">
    <!--@mbg.generated-->
    insert into T_BRDG_CHILD_PARTS (CHILD_PARTS_ID, PARTS_ID, HOLE_NUM, 
      PARTSTYPE_ID, PARTS_CODE, BRDG_SPAN, 
      PIER_OR_ABUTMENT, SHAPE, BASE_TYPE, 
      SIMPLE_OR_CONTINUED, ABUTMENT_NUM, PIER, 
      SUPPORT_ROWS, NUM_PER_ROW, SUPPORT_FORM, 
      TOWER_NUM, MTRL_CODE, COMP_CREATE_WAY, 
      COMPATTR_NUM, CREATE_USER_CODE, CREATE_TIME, 
      UPDATE_USER_CODE, UPDATE_TIME, REMARK, 
      VALID_FLAG, COMPATTR_ID, BOX_ROOM, 
      IS_WATER_PILE, BRDG_ID, parts_ID_BAK
      )
    values (#{childPartsId,jdbcType=VARCHAR}, #{partsId,jdbcType=VARCHAR}, #{holeNum,jdbcType=VARCHAR}, 
      #{partstypeId,jdbcType=VARCHAR}, #{partsCode,jdbcType=DECIMAL}, #{brdgSpan,jdbcType=DECIMAL}, 
      #{pierOrAbutment,jdbcType=VARCHAR}, #{shape,jdbcType=VARCHAR}, #{baseType,jdbcType=VARCHAR}, 
      #{simpleOrContinued,jdbcType=VARCHAR}, #{abutmentNum,jdbcType=VARCHAR}, #{pier,jdbcType=VARCHAR}, 
      #{supportRows,jdbcType=DECIMAL}, #{numPerRow,jdbcType=DECIMAL}, #{supportForm,jdbcType=VARCHAR}, 
      #{towerNum,jdbcType=VARCHAR}, #{mtrlCode,jdbcType=VARCHAR}, #{compCreateWay,jdbcType=VARCHAR}, 
      #{compattrNum,jdbcType=DECIMAL}, #{createUserCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUserCode,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{validFlag,jdbcType=DECIMAL}, #{compattrId,jdbcType=VARCHAR}, #{boxRoom,jdbcType=VARCHAR}, 
      #{isWaterPile,jdbcType=DECIMAL}, #{brdgId,jdbcType=VARCHAR}, #{partsIdBak,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgChildParts">
    <!--@mbg.generated-->
    insert into T_BRDG_CHILD_PARTS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="childPartsId != null">
        CHILD_PARTS_ID,
      </if>
      <if test="partsId != null">
        PARTS_ID,
      </if>
      <if test="holeNum != null">
        HOLE_NUM,
      </if>
      <if test="partstypeId != null">
        PARTSTYPE_ID,
      </if>
      <if test="partsCode != null">
        PARTS_CODE,
      </if>
      <if test="brdgSpan != null">
        BRDG_SPAN,
      </if>
      <if test="pierOrAbutment != null">
        PIER_OR_ABUTMENT,
      </if>
      <if test="shape != null">
        SHAPE,
      </if>
      <if test="baseType != null">
        BASE_TYPE,
      </if>
      <if test="simpleOrContinued != null">
        SIMPLE_OR_CONTINUED,
      </if>
      <if test="abutmentNum != null">
        ABUTMENT_NUM,
      </if>
      <if test="pier != null">
        PIER,
      </if>
      <if test="supportRows != null">
        SUPPORT_ROWS,
      </if>
      <if test="numPerRow != null">
        NUM_PER_ROW,
      </if>
      <if test="supportForm != null">
        SUPPORT_FORM,
      </if>
      <if test="towerNum != null">
        TOWER_NUM,
      </if>
      <if test="mtrlCode != null">
        MTRL_CODE,
      </if>
      <if test="compCreateWay != null">
        COMP_CREATE_WAY,
      </if>
      <if test="compattrNum != null">
        COMPATTR_NUM,
      </if>
      <if test="createUserCode != null">
        CREATE_USER_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateUserCode != null">
        UPDATE_USER_CODE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="validFlag != null">
        VALID_FLAG,
      </if>
      <if test="compattrId != null">
        COMPATTR_ID,
      </if>
      <if test="boxRoom != null">
        BOX_ROOM,
      </if>
      <if test="isWaterPile != null">
        IS_WATER_PILE,
      </if>
      <if test="brdgId != null">
        BRDG_ID,
      </if>
      <if test="partsIdBak != null">
        parts_ID_BAK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="childPartsId != null">
        #{childPartsId,jdbcType=VARCHAR},
      </if>
      <if test="partsId != null">
        #{partsId,jdbcType=VARCHAR},
      </if>
      <if test="holeNum != null">
        #{holeNum,jdbcType=VARCHAR},
      </if>
      <if test="partstypeId != null">
        #{partstypeId,jdbcType=VARCHAR},
      </if>
      <if test="partsCode != null">
        #{partsCode,jdbcType=DECIMAL},
      </if>
      <if test="brdgSpan != null">
        #{brdgSpan,jdbcType=DECIMAL},
      </if>
      <if test="pierOrAbutment != null">
        #{pierOrAbutment,jdbcType=VARCHAR},
      </if>
      <if test="shape != null">
        #{shape,jdbcType=VARCHAR},
      </if>
      <if test="baseType != null">
        #{baseType,jdbcType=VARCHAR},
      </if>
      <if test="simpleOrContinued != null">
        #{simpleOrContinued,jdbcType=VARCHAR},
      </if>
      <if test="abutmentNum != null">
        #{abutmentNum,jdbcType=VARCHAR},
      </if>
      <if test="pier != null">
        #{pier,jdbcType=VARCHAR},
      </if>
      <if test="supportRows != null">
        #{supportRows,jdbcType=DECIMAL},
      </if>
      <if test="numPerRow != null">
        #{numPerRow,jdbcType=DECIMAL},
      </if>
      <if test="supportForm != null">
        #{supportForm,jdbcType=VARCHAR},
      </if>
      <if test="towerNum != null">
        #{towerNum,jdbcType=VARCHAR},
      </if>
      <if test="mtrlCode != null">
        #{mtrlCode,jdbcType=VARCHAR},
      </if>
      <if test="compCreateWay != null">
        #{compCreateWay,jdbcType=VARCHAR},
      </if>
      <if test="compattrNum != null">
        #{compattrNum,jdbcType=DECIMAL},
      </if>
      <if test="createUserCode != null">
        #{createUserCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserCode != null">
        #{updateUserCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=DECIMAL},
      </if>
      <if test="compattrId != null">
        #{compattrId,jdbcType=VARCHAR},
      </if>
      <if test="boxRoom != null">
        #{boxRoom,jdbcType=VARCHAR},
      </if>
      <if test="isWaterPile != null">
        #{isWaterPile,jdbcType=DECIMAL},
      </if>
      <if test="brdgId != null">
        #{brdgId,jdbcType=VARCHAR},
      </if>
      <if test="partsIdBak != null">
        #{partsIdBak,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgChildParts">
    <!--@mbg.generated-->
    update T_BRDG_CHILD_PARTS
    <set>
      <if test="partsId != null">
        PARTS_ID = #{partsId,jdbcType=VARCHAR},
      </if>
      <if test="holeNum != null">
        HOLE_NUM = #{holeNum,jdbcType=VARCHAR},
      </if>
      <if test="partstypeId != null">
        PARTSTYPE_ID = #{partstypeId,jdbcType=VARCHAR},
      </if>
      <if test="partsCode != null">
        PARTS_CODE = #{partsCode,jdbcType=DECIMAL},
      </if>
      <if test="brdgSpan != null">
        BRDG_SPAN = #{brdgSpan,jdbcType=DECIMAL},
      </if>
      <if test="pierOrAbutment != null">
        PIER_OR_ABUTMENT = #{pierOrAbutment,jdbcType=VARCHAR},
      </if>
      <if test="shape != null">
        SHAPE = #{shape,jdbcType=VARCHAR},
      </if>
      <if test="baseType != null">
        BASE_TYPE = #{baseType,jdbcType=VARCHAR},
      </if>
      <if test="simpleOrContinued != null">
        SIMPLE_OR_CONTINUED = #{simpleOrContinued,jdbcType=VARCHAR},
      </if>
      <if test="abutmentNum != null">
        ABUTMENT_NUM = #{abutmentNum,jdbcType=VARCHAR},
      </if>
      <if test="pier != null">
        PIER = #{pier,jdbcType=VARCHAR},
      </if>
      <if test="supportRows != null">
        SUPPORT_ROWS = #{supportRows,jdbcType=DECIMAL},
      </if>
      <if test="numPerRow != null">
        NUM_PER_ROW = #{numPerRow,jdbcType=DECIMAL},
      </if>
      <if test="supportForm != null">
        SUPPORT_FORM = #{supportForm,jdbcType=VARCHAR},
      </if>
      <if test="towerNum != null">
        TOWER_NUM = #{towerNum,jdbcType=VARCHAR},
      </if>
      <if test="mtrlCode != null">
        MTRL_CODE = #{mtrlCode,jdbcType=VARCHAR},
      </if>
      <if test="compCreateWay != null">
        COMP_CREATE_WAY = #{compCreateWay,jdbcType=VARCHAR},
      </if>
      <if test="compattrNum != null">
        COMPATTR_NUM = #{compattrNum,jdbcType=DECIMAL},
      </if>
      <if test="createUserCode != null">
        CREATE_USER_CODE = #{createUserCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserCode != null">
        UPDATE_USER_CODE = #{updateUserCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      </if>
      <if test="compattrId != null">
        COMPATTR_ID = #{compattrId,jdbcType=VARCHAR},
      </if>
      <if test="boxRoom != null">
        BOX_ROOM = #{boxRoom,jdbcType=VARCHAR},
      </if>
      <if test="isWaterPile != null">
        IS_WATER_PILE = #{isWaterPile,jdbcType=DECIMAL},
      </if>
      <if test="brdgId != null">
        BRDG_ID = #{brdgId,jdbcType=VARCHAR},
      </if>
      <if test="partsIdBak != null">
        parts_ID_BAK = #{partsIdBak,jdbcType=VARCHAR},
      </if>
    </set>
    where CHILD_PARTS_ID = #{childPartsId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgChildParts">
    <!--@mbg.generated-->
    update T_BRDG_CHILD_PARTS
    set PARTS_ID = #{partsId,jdbcType=VARCHAR},
      HOLE_NUM = #{holeNum,jdbcType=VARCHAR},
      PARTSTYPE_ID = #{partstypeId,jdbcType=VARCHAR},
      PARTS_CODE = #{partsCode,jdbcType=DECIMAL},
      BRDG_SPAN = #{brdgSpan,jdbcType=DECIMAL},
      PIER_OR_ABUTMENT = #{pierOrAbutment,jdbcType=VARCHAR},
      SHAPE = #{shape,jdbcType=VARCHAR},
      BASE_TYPE = #{baseType,jdbcType=VARCHAR},
      SIMPLE_OR_CONTINUED = #{simpleOrContinued,jdbcType=VARCHAR},
      ABUTMENT_NUM = #{abutmentNum,jdbcType=VARCHAR},
      PIER = #{pier,jdbcType=VARCHAR},
      SUPPORT_ROWS = #{supportRows,jdbcType=DECIMAL},
      NUM_PER_ROW = #{numPerRow,jdbcType=DECIMAL},
      SUPPORT_FORM = #{supportForm,jdbcType=VARCHAR},
      TOWER_NUM = #{towerNum,jdbcType=VARCHAR},
      MTRL_CODE = #{mtrlCode,jdbcType=VARCHAR},
      COMP_CREATE_WAY = #{compCreateWay,jdbcType=VARCHAR},
      COMPATTR_NUM = #{compattrNum,jdbcType=DECIMAL},
      CREATE_USER_CODE = #{createUserCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_USER_CODE = #{updateUserCode,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      COMPATTR_ID = #{compattrId,jdbcType=VARCHAR},
      BOX_ROOM = #{boxRoom,jdbcType=VARCHAR},
      IS_WATER_PILE = #{isWaterPile,jdbcType=DECIMAL},
      BRDG_ID = #{brdgId,jdbcType=VARCHAR},
      parts_ID_BAK = #{partsIdBak,jdbcType=VARCHAR}
    where CHILD_PARTS_ID = #{childPartsId,jdbcType=VARCHAR}
  </update>
</mapper>