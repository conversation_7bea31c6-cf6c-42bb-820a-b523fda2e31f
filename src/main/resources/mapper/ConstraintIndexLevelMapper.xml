<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.ConstraintIndexLevelMapper">
  <select id="listByGroupId"
    resultType="com.hualu.highwaymaintenance.module.decision.entity.ConstraintIndexLevel">
    select m.* from (
      select l.*,
        (select i.CONSTRAINT_INDEX_NAME
         from pmsdb.CONSTRAINT_INDEX i
         where i.CONSTRAINT_INDEX_ID = l.CONSTRAINT_INDEX_ID and ROWNUM = 1) constraintIndexName,
        (select i.DECISION_TREE_ID_ORDER from PMSDB.CONSTRAINT_GROUP_INDEX i where i.CONSTRAINT_INDEX_ID = l.CONSTRAINT_INDEX_ID and ROWNUM = 1) indexOrder
      from pmsdb.CONSTRAINT_INDEX_LEVEL l
      where l.CONSTRAINT_GROUP_ID = #{groupId,jdbcType=VARCHAR}
      order by l.LEVEL_ORDER) m order by m.indexOrder
  </select>
</mapper>
