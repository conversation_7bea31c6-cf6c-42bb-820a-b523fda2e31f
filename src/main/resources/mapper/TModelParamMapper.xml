<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.TModelParamMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.TModelParam">
            <result property="paramId" column="PARAM_ID" jdbcType="VARCHAR"/>
            <result property="completeYear" column="COMPLETE_YEAR" jdbcType="DECIMAL"/>
            <result property="maintainYear" column="MAINTAIN_YEAR" jdbcType="DECIMAL"/>
            <result property="designDeflection" column="DESIGN_DEFLECTION" jdbcType="DECIMAL"/>
            <result property="roadThickness" column="ROAD_THICKNESS" jdbcType="DECIMAL"/>
            <result property="pitchThickness" column="PITCH_THICKNESS" jdbcType="DECIMAL"/>
            <result property="traffic" column="TRAFFIC" jdbcType="DECIMAL"/>
            <result property="heavyPercent" column="HEAVY_PERCENT" jdbcType="DECIMAL"/>
            <result property="alphaVal" column="ALPHA_VAL" jdbcType="DECIMAL"/>
            <result property="betaVal" column="BETA_VAL" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        PARAM_ID,COMPLETE_YEAR,MAINTAIN_YEAR,
        DESIGN_DEFLECTION,ROAD_THICKNESS,PITCH_THICKNESS,
        TRAFFIC,HEAVY_PERCENT,ALPHA_VAL,
        BETA_VAL
    </sql>

    <select id="getParamByInput"
            resultMap="BaseResultMap">
        select  t.PARAM_ID, COMPLETE_YEAR, MAINTAIN_YEAR, DESIGN_DEFLECTION, ROAD_THICKNESS, PITCH_THICKNESS, TRAFFIC, HEAVY_PERCENT, ALPHA_VAL-dbms_random.value(1,5) as ALPHA_VAL, BETA_VAL-dbms_random.value(0,0.2) as BETA_VAL from PMSDB.T_MODEL_PARAM t
        where 1=1
        <if test="param.completeYear != null ">
           and t.COMPLETE_YEAR&gt;=#{param.completeYear}
        </if>
        <if test="param.maintainYear != null ">
            and t.MAINTAIN_YEAR=#{param.maintainYear}
        </if>
        <if test="param.designDeflection != null ">
            and t.DESIGN_DEFLECTION=#{param.designDeflection}
        </if>
        <if test="param.roadThickness != null ">
            and t.ROAD_THICKNESS=#{param.roadThickness}
        </if>
        <if test="param.pitchThickness != null ">
            and t.PITCH_THICKNESS=#{param.pitchThickness}
        </if>
        <if test="param.traffic != null ">
            and t.TRAFFIC=#{param.traffic}
        </if>
        <if test="param.heavyPercent != null ">
            and t.HEAVY_PERCENT=#{param.heavyPercent}
        </if>
        and rownum=1
    </select>
    <select id="querySecondOrg" resultType="com.hualu.highwaymaintenance.module.dic.BaseDataDic">
    select f.id as valueCode,f.ORG_NAME  as valueName from GDGS.FW_RIGHT_ORG f where f.IS_DELETED=0 and f.IS_ENABLE=1
    and f.ORG_LEVEL=2
    </select>
    <select id="queryDecisionPrj" resultType="com.hualu.highwaymaintenance.module.dic.BaseDataDic">
    select t.PRJ_ID as valueCode,t.PRJ_SNAME as valueName  from PTCMSDB.TCC_INSP_PRJ_DESION  t  where t.MNG_ORG_ID
    in (select f.ID from GDGS.FW_RIGHT_ORG f where f.PARENT_ID=#{secondOrg}) and t.PRJYEAR=#{year}
    </select>
    <select id="groupPrjYear" resultType="com.hualu.highwaymaintenance.module.dic.BaseDataDic">
        select t.PRJYEAR as valueCode,t.PRJYEAR as valueName  from PTCMSDB.TCC_INSP_PRJ_DESION  t
        where t.PRJYEAR is not null
        group by t.PRJYEAR
    </select>

    <insert id="insertBySql">
        ${sql}
    </insert>
    <select id="selectBySql" resultType="java.lang.Object">
        ${sql}
    </select>

    <select id="getPredictSrcData"
      resultType="com.hualu.highwaymaintenance.module.decision.vo.PredictSrcData">
        select
            t.PQI pqi,
            t.SCI sci,
            t.BCI bci,
            t.TCI tci,
            t.PCI_FRONT pciFront,
            t.PCI_BEHIND pciBehind,
            t.RQI rqi,
            t.RDI rdi,
            t.SRI sri,
            t.PSSI pssi,
            t.UNIT_MARGE_ID unitMargeId,
            c.START_STAKE startStake,
            c.END_STAKE endStake,
            c.LENGTH length,
            c.LANE lane,
            p.PRJ_ID prjId,
            c.ROUTE_CODE routeCode,
            p.PRJYEAR prjYear,
            (select l.LINE_DIRECT from GDGS.BASE_ROUTE_LOGIC l where l.ROUTE_CODE = c.ROUTE_CODE and ROWNUM = 1) lineDirect,
            (select to_char(sysdate, 'yyyy') - substr(c.BUILT_DATE, 0, 4) from GDGS.BASE_ROUTE_PHYSICS c where c.ROUTE_CODE = c.ROUTE_CODE and ROWNUM = 1) roadAge,
            t.PR Pr, decode(t.TCS,'NULL',0,t.TCS) Tcs, t.LCD Lcd,
            (select l.LINE_CODE from GDGS.BASE_ROUTE_LOGIC l where l.ROUTE_CODE = c.ROUTE_CODE and ROWNUM = 1) lineCode,
            c.PAVEMENT_TYPE pavementType
        from PTCMSDB.PTCD_TC_DETAIL_1000_DESION t
                 inner join PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION c on t.UNIT_MARGE_ID = c.UNIT_MARGE_ID
                 inner join PTCMSDB.TCC_INSP_PRJ_DESION p on p.MNG_ORG_ID = t.PRJ_ID
        where p.PRJ_ID = #{projectId,jdbcType=VARCHAR}
          and t.PCI_FRONT > 0 and t.RQI > 0 and t.RDI > 0 and t.SRI > 0
    </select>
</mapper>
