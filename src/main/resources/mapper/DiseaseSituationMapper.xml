<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.emergenceCheck.mapper.DiseaseSituationMapper">
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation">
    <!--@mbg.generated-->
    select *
    from HSMSDB.DISEASE_SITUATION
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    update HSMSDB.DISEASE_SITUATION set IS_DELETE = 1
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation">
    <!--@mbg.generated-->
    insert into HSMSDB.DISEASE_SITUATION (DATA_ID, DISEASE_SITUATION, TAKE_STEPS,
      TREATMENT_BEFORE, TREATMENT_IN, TREATMENT_AFTER,
      IS_DELETE, RECTIFICATION_LEDGER_ID, DISEASE_EXTRA_DESC,PROJECT_PROGRESS_DESC,UPDATE_TIME
      )
    values (#{dataId,jdbcType=VARCHAR}, #{diseaseSituation,jdbcType=VARCHAR}, #{takeSteps,jdbcType=VARCHAR},
      #{treatmentBefore,jdbcType=VARCHAR}, #{treatmentIn,jdbcType=VARCHAR}, #{treatmentAfter,jdbcType=VARCHAR},
      #{isDelete,jdbcType=VARCHAR}, #{rectificationLedgerId,jdbcType=VARCHAR}, #{diseaseExtraDesc,jdbcType=VARCHAR}, #{projectProgressDesc,jdbcType=VARCHAR}
    , #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation">
    <!--@mbg.generated-->
    insert into HSMSDB.DISEASE_SITUATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        DATA_ID,
      </if>
      <if test="diseaseSituation != null">
        DISEASE_SITUATION,
      </if>
      <if test="takeSteps != null">
        TAKE_STEPS,
      </if>
      <if test="treatmentBefore != null">
        TREATMENT_BEFORE,
      </if>
      <if test="treatmentIn != null">
        TREATMENT_IN,
      </if>
      <if test="treatmentAfter != null">
        TREATMENT_AFTER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="rectificationLedgerId != null">
        RECTIFICATION_LEDGER_ID,
      </if>
      <if test="diseaseExtraDesc != null">
        DISEASE_EXTRA_DESC,
      </if>
      <if test="projectProgressDesc != null">
        PROJECT_PROGRESS_DESC,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        #{dataId,jdbcType=VARCHAR},
      </if>
      <if test="diseaseSituation != null">
        #{diseaseSituation,jdbcType=VARCHAR},
      </if>
      <if test="takeSteps != null">
        #{takeSteps,jdbcType=VARCHAR},
      </if>
      <if test="treatmentBefore != null">
        #{treatmentBefore,jdbcType=VARCHAR},
      </if>
      <if test="treatmentIn != null">
        #{treatmentIn,jdbcType=VARCHAR},
      </if>
      <if test="treatmentAfter != null">
        #{treatmentAfter,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="rectificationLedgerId != null">
        #{rectificationLedgerId,jdbcType=VARCHAR},
      </if>
      <if test="diseaseExtraDesc != null">
        #{diseaseExtraDesc,jdbcType=VARCHAR},
      </if>
      <if test="projectProgressDesc != null">
        #{projectProgressDesc,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation">
    <!--@mbg.generated-->
    update HSMSDB.DISEASE_SITUATION
    <set>
      <if test="diseaseSituation != null">
        DISEASE_SITUATION = #{diseaseSituation,jdbcType=VARCHAR},
      </if>
      <if test="takeSteps != null">
        TAKE_STEPS = #{takeSteps,jdbcType=VARCHAR},
      </if>
      <if test="treatmentBeforeMain != null">
        TREATMENT_BEFORE_MAIN = #{treatmentBeforeMain,jdbcType=VARCHAR},
      </if>
      <if test="treatmentBefore != null">
        TREATMENT_BEFORE = #{treatmentBefore,jdbcType=VARCHAR},
      </if>
      <if test="treatmentIn != null">
        TREATMENT_IN = #{treatmentIn,jdbcType=VARCHAR},
      </if>
      <if test="treatmentAfter != null">
        TREATMENT_AFTER = #{treatmentAfter,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=VARCHAR},
      </if>
      <if test="rectificationLedgerId != null">
        RECTIFICATION_LEDGER_ID = #{rectificationLedgerId,jdbcType=VARCHAR},
      </if>
      <if test="diseaseExtraDesc != null">
        DISEASE_EXTRA_DESC = #{diseaseExtraDesc,jdbcType=VARCHAR},
      </if>
      <if test="projectProgressDesc != null">
        PROJECT_PROGRESS_DESC = #{projectProgressDesc,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation">
    <!--@mbg.generated-->
    update HSMSDB.DISEASE_SITUATION
    set DISEASE_SITUATION = #{diseaseSituation,jdbcType=VARCHAR},
      TAKE_STEPS = #{takeSteps,jdbcType=VARCHAR},
      TREATMENT_BEFORE = #{treatmentBefore,jdbcType=VARCHAR},
      TREATMENT_IN = #{treatmentIn,jdbcType=VARCHAR},
      TREATMENT_AFTER = #{treatmentAfter,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=VARCHAR},
      RECTIFICATION_LEDGER_ID = #{rectificationLedgerId,jdbcType=VARCHAR},
      DISEASE_EXTRA_DESC = #{diseaseExtraDesc,jdbcType=VARCHAR},PROJECT_PROGRESS_DESC = #{projectProgressDesc,jdbcType=VARCHAR}
    ,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </update>
</mapper>