<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBaseFilelinkMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.TBaseFilelink">
            <id property="filelinkId" column="FILELINK_ID" jdbcType="VARCHAR"/>
            <result property="bussiId" column="BUSSI_ID" jdbcType="VARCHAR"/>
            <result property="bussiType" column="BUSSI_TYPE" jdbcType="DECIMAL"/>
            <result property="photoType" column="PHOTO_TYPE" jdbcType="DECIMAL"/>
            <result property="fileId" column="FILE_ID" jdbcType="VARCHAR"/>
            <result property="createUserCode" column="CREATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="validFlag" column="VALID_FLAG" jdbcType="DECIMAL"/>
            <result property="directoryType" column="DIRECTORY_TYPE" jdbcType="DECIMAL"/>
            <result property="newId" column="NEW_ID" jdbcType="VARCHAR"/>
            <result property="prjId" column="PRJ_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FILELINK_ID,BUSSI_ID,BUSSI_TYPE,
        PHOTO_TYPE,FILE_ID,CREATE_USER_CODE,
        CREATE_TIME,REMARK,VALID_FLAG,
        DIRECTORY_TYPE,NEW_ID,PRJ_ID,
        DIRECTORY_ID
    </sql>
</mapper>
