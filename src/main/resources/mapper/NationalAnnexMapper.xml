<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.NationalAnnexMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.national.entity.NationalAnnex">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="nationalId" column="NATIONAL_ID" jdbcType="VARCHAR"/>
            <result property="nationalSource" column="NATIONAL_SOURCE" jdbcType="VARCHAR"/>
            <result property="annexPath" column="ANNEX_PATH" jdbcType="VARCHAR"/>
            <result property="uploadTime" column="UPLOAD_TIME" jdbcType="TIMESTAMP"/>
            <result property="annexName" column="ANNEX_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NATIONAL_ID,NATIONAL_SOURCE,
        ANNEX_PATH,UPLOAD_TIME,ANNEX_NAME
    </sql>
</mapper>
