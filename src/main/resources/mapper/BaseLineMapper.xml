<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.baseline.mapper.BaseLineMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.baseline.domain.BaseLine">
            <id property="lineId" column="LINE_ID" jdbcType="VARCHAR"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="lineAllname" column="LINE_ALLNAME" jdbcType="VARCHAR"/>
            <result property="lineSname" column="LINE_SNAME" jdbcType="VARCHAR"/>
            <result property="lineDesc" column="LINE_DESC" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="lineCounterCode" column="LINE_COUNTER_CODE" jdbcType="VARCHAR"/>
            <result property="isEnable" column="IS_ENABLE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="VARCHAR"/>
            <result property="stakeType" column="STAKE_TYPE" jdbcType="VARCHAR"/>
            <result property="lineLength" column="LINE_LENGTH" jdbcType="DECIMAL"/>
            <result property="isNewGgw" column="IS_NEW_GGW" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        LINE_ID,LINE_CODE,LINE_ALLNAME,
        LINE_SNAME,LINE_DESC,REMARK,
        CREATE_USER_ID,CREATE_TIME,UPDATE_USER_ID,
        UPDATE_TIME,LINE_COUNTER_CODE,IS_ENABLE,
        IS_DELETED,STAKE_TYPE,LINE_LENGTH,
        IS_NEW_GGW
    </sql>
    <select id="loadGrantedLines" resultMap="BaseResultMap">
        select distinct l.* from GDGS.V_BASE_LINE l inner join gdgs.fw_right_data_permission a on l.LINE_ID = a.line_id   and (a.oprt_org_code in (select u.org_id from gdgs.fw_right_user u where u.user_code =  #{userCode})
                 or a.prj_org_code in (select u.org_id from gdgs.fw_right_user u where u.user_code = #{userCode}))
    </select>
    <select id="selectBySql" resultType="java.util.Map">
        ${sql}
    </select>

    <select id="loadRamps" resultType="com.hualu.highwaymaintenance.module.baseline.domain.BaseRamp">
      select distinct rampline.*,
                      rampline.line_id                                                                 as ramplineid,
                      frdp.line_id                                                                 as mainlogiclineid,
                      decode(rampline.remark, null, '', rampline.remark || '-') || rampline.line_sname as lineAllName
      from gdgs.base_route_intrvl_logic bril
         , gdgs.fw_right_data_permission frdp
         , gdgs.base_ramp_line rampline
         , gdgs.base_ramp_intrvl_logic ramplogic
      where ramplogic.line_id = rampline.line_id
        and ramplogic.rp_intrvl_id = bril.rp_intrvl_id
        and frdp.route_code = bril.route_code
        and frdp.OPRT_ORG_CODE in (select ORG_CODE
                                   from GDGS.FW_RIGHT_ORG o
                                   where o.IS_ENABLE = 1
      start with o.ID = #{orgId}
      connect by prior ID = PARENT_ID)
             and frdp.line_id = #{lineId}
      order by rampline.remark || '-' || rampline.line_sname
    </select>

    <select id="getUserLine" resultType="com.hualu.highwaymaintenance.module.baseline.domain.BaseLine">
        select * from GDGS.BASE_LINE f  where exists(select 1 from GDGS.FW_RIGHT_DATA_PERMISSION t inner join GDGS.FW_RIGHT_USER u
                                                                                                              on u.ORG_ID=t.OPRT_ORG_CODE or u.ORG_ID=t.PRJ_ORG_CODE where  u.USER_CODE=#{userCode}
                                                                                                                                                                       and t.LINE_ID=f.LINE_ID)
    </select>
</mapper>
