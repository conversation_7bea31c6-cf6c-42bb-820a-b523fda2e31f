<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.inspect.mapper.LineInfoMapper">
    <select id="queryLineInfo" resultType="com.hualu.highwaymaintenance.module.inspect.domain.LineInfo">
        with ORG AS ( select * from GDGS.FW_RIGHT_ORG o
                      where o.IS_ENABLE = 1 start with o.ID =
            (select ORG_ID from gdgs.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
        connect by prior ID = PARENT_ID)
        select distinct l.LINE_ID,LINE_CODE,l.LINE_ALLNAME as LINE_NAME,l.LINE_SNAME
        from ORG o
                 inner join gdgs.FW_RIGHT_DATA_PERMISSION p on o.ID = p.OPRT_ORG_CODE
                 inner join gdgs.BASE_LINE l on p.LINE_ID = l.LINE_ID
        where  l.IS_DELETED = 0 and o.IS_DELETED = 0
          and l.IS_ENABLE = 1 and o.IS_ENABLE = 1
          and IS_NEW_GGW = 1
    </select>
</mapper>
