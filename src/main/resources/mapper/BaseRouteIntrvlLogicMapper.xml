<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintainbase.mapper.BaseRouteIntrvlLogicMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRouteIntrvlLogic">
    <!--@mbg.generated-->
    <!--@Table GDGS.BASE_ROUTE_INTRVL_LOGIC-->
    <id column="RL_INTRVL_ID" jdbcType="VARCHAR" property="rlIntrvlId" />
    <result column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
    <result column="RP_INTRVL_ID" jdbcType="VARCHAR" property="rpIntrvlId" />
    <result column="RL_INTRVL_CODE" jdbcType="VARCHAR" property="rlIntrvlCode" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="STAKE_TYPE" jdbcType="VARCHAR" property="stakeType" />
    <result column="START_STAKE" jdbcType="VARCHAR" property="startStake" />
    <result column="END_STAKE" jdbcType="VARCHAR" property="endStake" />
    <result column="START_STAKE_NUM" jdbcType="DECIMAL" property="startStakeNum" />
    <result column="END_STAKE_NUM" jdbcType="DECIMAL" property="endStakeNum" />
    <result column="START_SENSSION_NUM" jdbcType="DECIMAL" property="startSenssionNum" />
    <result column="END_SENSSION_NUM" jdbcType="DECIMAL" property="endSenssionNum" />
    <result column="START_PLACE" jdbcType="VARCHAR" property="startPlace" />
    <result column="END_PLACE" jdbcType="VARCHAR" property="endPlace" />
    <result column="START_DSTRCT_CODE" jdbcType="VARCHAR" property="startDstrctCode" />
    <result column="END_DSTRCT_CODE" jdbcType="VARCHAR" property="endDstrctCode" />
    <result column="WAYS_DSTRCT_CODE" jdbcType="VARCHAR" property="waysDstrctCode" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_USER_ID" jdbcType="VARCHAR" property="createUserId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_ID" jdbcType="VARCHAR" property="updateUserId" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="BUILT_DATE" jdbcType="DECIMAL" property="builtDate" />
    <result column="COMMUT_DATE" jdbcType="DECIMAL" property="commutDate" />
    <result column="IS_ENABLE" jdbcType="VARCHAR" property="isEnable" />
    <result column="IS_DELETED" jdbcType="VARCHAR" property="isDeleted" />
    <result column="GIS_ID" jdbcType="VARCHAR" property="gisId" />
    <result column="GIS_XY" jdbcType="VARCHAR" property="gisXy" />
    <result column="RP_START_STAKE_NUM" jdbcType="DECIMAL" property="rpStartStakeNum" />
    <result column="RP_END_STAKE_NUM" jdbcType="DECIMAL" property="rpEndStakeNum" />
    <result column="IS_SHOW" jdbcType="DECIMAL" property="isShow" />
    <result column="PRJORGCODE" jdbcType="VARCHAR" property="prjorgcode" />
    <result column="OPRTORGCODE" jdbcType="VARCHAR" property="oprtorgcode" />
    <result column="TR_LOGIC_ID" jdbcType="VARCHAR" property="trLogicId" />
    <result column="LINENO" jdbcType="DECIMAL" property="lineno" />
    <result column="DESIGN_SPEED" jdbcType="VARCHAR" property="designSpeed" />
    <result column="IS_CF" jdbcType="VARCHAR" property="isCf" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
    <result column="ROUTE_VERSION" jdbcType="VARCHAR" property="routeVersion" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RL_INTRVL_ID, LINE_ID, RP_INTRVL_ID, RL_INTRVL_CODE, LINE_CODE, STAKE_TYPE, START_STAKE, 
    END_STAKE, START_STAKE_NUM, END_STAKE_NUM, START_SENSSION_NUM, END_SENSSION_NUM, 
    START_PLACE, END_PLACE, START_DSTRCT_CODE, END_DSTRCT_CODE, WAYS_DSTRCT_CODE, LINE_DIRECT, 
    REMARK, CREATE_USER_ID, CREATE_TIME, UPDATE_USER_ID, UPDATE_TIME, BUILT_DATE, COMMUT_DATE, 
    IS_ENABLE, IS_DELETED, GIS_ID, GIS_XY, RP_START_STAKE_NUM, RP_END_STAKE_NUM, IS_SHOW, 
    PRJORGCODE, OPRTORGCODE, TR_LOGIC_ID, "LINENO", DESIGN_SPEED, IS_CF, ROUTE_CODE, 
    ROUTE_VERSION
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from GDGS.BASE_ROUTE_INTRVL_LOGIC
    where RL_INTRVL_ID = #{rlIntrvlId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from GDGS.BASE_ROUTE_INTRVL_LOGIC
    where RL_INTRVL_ID = #{rlIntrvlId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRouteIntrvlLogic">
    <!--@mbg.generated-->
    insert into GDGS.BASE_ROUTE_INTRVL_LOGIC (RL_INTRVL_ID, LINE_ID, RP_INTRVL_ID, 
      RL_INTRVL_CODE, LINE_CODE, STAKE_TYPE, 
      START_STAKE, END_STAKE, START_STAKE_NUM, 
      END_STAKE_NUM, START_SENSSION_NUM, END_SENSSION_NUM, 
      START_PLACE, END_PLACE, START_DSTRCT_CODE, 
      END_DSTRCT_CODE, WAYS_DSTRCT_CODE, LINE_DIRECT, 
      REMARK, CREATE_USER_ID, CREATE_TIME, 
      UPDATE_USER_ID, UPDATE_TIME, BUILT_DATE, 
      COMMUT_DATE, IS_ENABLE, IS_DELETED, 
      GIS_ID, GIS_XY, RP_START_STAKE_NUM, 
      RP_END_STAKE_NUM, IS_SHOW, PRJORGCODE, 
      OPRTORGCODE, TR_LOGIC_ID, "LINENO", 
      DESIGN_SPEED, IS_CF, ROUTE_CODE, 
      ROUTE_VERSION)
    values (#{rlIntrvlId,jdbcType=VARCHAR}, #{lineId,jdbcType=VARCHAR}, #{rpIntrvlId,jdbcType=VARCHAR}, 
      #{rlIntrvlCode,jdbcType=VARCHAR}, #{lineCode,jdbcType=VARCHAR}, #{stakeType,jdbcType=VARCHAR}, 
      #{startStake,jdbcType=VARCHAR}, #{endStake,jdbcType=VARCHAR}, #{startStakeNum,jdbcType=DECIMAL}, 
      #{endStakeNum,jdbcType=DECIMAL}, #{startSenssionNum,jdbcType=DECIMAL}, #{endSenssionNum,jdbcType=DECIMAL}, 
      #{startPlace,jdbcType=VARCHAR}, #{endPlace,jdbcType=VARCHAR}, #{startDstrctCode,jdbcType=VARCHAR}, 
      #{endDstrctCode,jdbcType=VARCHAR}, #{waysDstrctCode,jdbcType=VARCHAR}, #{lineDirect,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{createUserId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateUserId,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{builtDate,jdbcType=DECIMAL}, 
      #{commutDate,jdbcType=DECIMAL}, #{isEnable,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR}, 
      #{gisId,jdbcType=VARCHAR}, #{gisXy,jdbcType=VARCHAR}, #{rpStartStakeNum,jdbcType=DECIMAL}, 
      #{rpEndStakeNum,jdbcType=DECIMAL}, #{isShow,jdbcType=DECIMAL}, #{prjorgcode,jdbcType=VARCHAR}, 
      #{oprtorgcode,jdbcType=VARCHAR}, #{trLogicId,jdbcType=VARCHAR}, #{lineno,jdbcType=DECIMAL}, 
      #{designSpeed,jdbcType=VARCHAR}, #{isCf,jdbcType=VARCHAR}, #{routeCode,jdbcType=VARCHAR}, 
      #{routeVersion,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRouteIntrvlLogic">
    <!--@mbg.generated-->
    insert into GDGS.BASE_ROUTE_INTRVL_LOGIC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rlIntrvlId != null">
        RL_INTRVL_ID,
      </if>
      <if test="lineId != null">
        LINE_ID,
      </if>
      <if test="rpIntrvlId != null">
        RP_INTRVL_ID,
      </if>
      <if test="rlIntrvlCode != null">
        RL_INTRVL_CODE,
      </if>
      <if test="lineCode != null">
        LINE_CODE,
      </if>
      <if test="stakeType != null">
        STAKE_TYPE,
      </if>
      <if test="startStake != null">
        START_STAKE,
      </if>
      <if test="endStake != null">
        END_STAKE,
      </if>
      <if test="startStakeNum != null">
        START_STAKE_NUM,
      </if>
      <if test="endStakeNum != null">
        END_STAKE_NUM,
      </if>
      <if test="startSenssionNum != null">
        START_SENSSION_NUM,
      </if>
      <if test="endSenssionNum != null">
        END_SENSSION_NUM,
      </if>
      <if test="startPlace != null">
        START_PLACE,
      </if>
      <if test="endPlace != null">
        END_PLACE,
      </if>
      <if test="startDstrctCode != null">
        START_DSTRCT_CODE,
      </if>
      <if test="endDstrctCode != null">
        END_DSTRCT_CODE,
      </if>
      <if test="waysDstrctCode != null">
        WAYS_DSTRCT_CODE,
      </if>
      <if test="lineDirect != null">
        LINE_DIRECT,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="builtDate != null">
        BUILT_DATE,
      </if>
      <if test="commutDate != null">
        COMMUT_DATE,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="gisId != null">
        GIS_ID,
      </if>
      <if test="gisXy != null">
        GIS_XY,
      </if>
      <if test="rpStartStakeNum != null">
        RP_START_STAKE_NUM,
      </if>
      <if test="rpEndStakeNum != null">
        RP_END_STAKE_NUM,
      </if>
      <if test="isShow != null">
        IS_SHOW,
      </if>
      <if test="prjorgcode != null">
        PRJORGCODE,
      </if>
      <if test="oprtorgcode != null">
        OPRTORGCODE,
      </if>
      <if test="trLogicId != null">
        TR_LOGIC_ID,
      </if>
      <if test="lineno != null">
        "LINENO",
      </if>
      <if test="designSpeed != null">
        DESIGN_SPEED,
      </if>
      <if test="isCf != null">
        IS_CF,
      </if>
      <if test="routeCode != null">
        ROUTE_CODE,
      </if>
      <if test="routeVersion != null">
        ROUTE_VERSION,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rlIntrvlId != null">
        #{rlIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="lineId != null">
        #{lineId,jdbcType=VARCHAR},
      </if>
      <if test="rpIntrvlId != null">
        #{rpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="rlIntrvlCode != null">
        #{rlIntrvlCode,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="stakeType != null">
        #{stakeType,jdbcType=VARCHAR},
      </if>
      <if test="startStake != null">
        #{startStake,jdbcType=VARCHAR},
      </if>
      <if test="endStake != null">
        #{endStake,jdbcType=VARCHAR},
      </if>
      <if test="startStakeNum != null">
        #{startStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="endStakeNum != null">
        #{endStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="startSenssionNum != null">
        #{startSenssionNum,jdbcType=DECIMAL},
      </if>
      <if test="endSenssionNum != null">
        #{endSenssionNum,jdbcType=DECIMAL},
      </if>
      <if test="startPlace != null">
        #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="endPlace != null">
        #{endPlace,jdbcType=VARCHAR},
      </if>
      <if test="startDstrctCode != null">
        #{startDstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="endDstrctCode != null">
        #{endDstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="waysDstrctCode != null">
        #{waysDstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="lineDirect != null">
        #{lineDirect,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="builtDate != null">
        #{builtDate,jdbcType=DECIMAL},
      </if>
      <if test="commutDate != null">
        #{commutDate,jdbcType=DECIMAL},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="gisId != null">
        #{gisId,jdbcType=VARCHAR},
      </if>
      <if test="gisXy != null">
        #{gisXy,jdbcType=VARCHAR},
      </if>
      <if test="rpStartStakeNum != null">
        #{rpStartStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="rpEndStakeNum != null">
        #{rpEndStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="isShow != null">
        #{isShow,jdbcType=DECIMAL},
      </if>
      <if test="prjorgcode != null">
        #{prjorgcode,jdbcType=VARCHAR},
      </if>
      <if test="oprtorgcode != null">
        #{oprtorgcode,jdbcType=VARCHAR},
      </if>
      <if test="trLogicId != null">
        #{trLogicId,jdbcType=VARCHAR},
      </if>
      <if test="lineno != null">
        #{lineno,jdbcType=DECIMAL},
      </if>
      <if test="designSpeed != null">
        #{designSpeed,jdbcType=VARCHAR},
      </if>
      <if test="isCf != null">
        #{isCf,jdbcType=VARCHAR},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="routeVersion != null">
        #{routeVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRouteIntrvlLogic">
    <!--@mbg.generated-->
    update GDGS.BASE_ROUTE_INTRVL_LOGIC
    <set>
      <if test="lineId != null">
        LINE_ID = #{lineId,jdbcType=VARCHAR},
      </if>
      <if test="rpIntrvlId != null">
        RP_INTRVL_ID = #{rpIntrvlId,jdbcType=VARCHAR},
      </if>
      <if test="rlIntrvlCode != null">
        RL_INTRVL_CODE = #{rlIntrvlCode,jdbcType=VARCHAR},
      </if>
      <if test="lineCode != null">
        LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      </if>
      <if test="stakeType != null">
        STAKE_TYPE = #{stakeType,jdbcType=VARCHAR},
      </if>
      <if test="startStake != null">
        START_STAKE = #{startStake,jdbcType=VARCHAR},
      </if>
      <if test="endStake != null">
        END_STAKE = #{endStake,jdbcType=VARCHAR},
      </if>
      <if test="startStakeNum != null">
        START_STAKE_NUM = #{startStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="endStakeNum != null">
        END_STAKE_NUM = #{endStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="startSenssionNum != null">
        START_SENSSION_NUM = #{startSenssionNum,jdbcType=DECIMAL},
      </if>
      <if test="endSenssionNum != null">
        END_SENSSION_NUM = #{endSenssionNum,jdbcType=DECIMAL},
      </if>
      <if test="startPlace != null">
        START_PLACE = #{startPlace,jdbcType=VARCHAR},
      </if>
      <if test="endPlace != null">
        END_PLACE = #{endPlace,jdbcType=VARCHAR},
      </if>
      <if test="startDstrctCode != null">
        START_DSTRCT_CODE = #{startDstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="endDstrctCode != null">
        END_DSTRCT_CODE = #{endDstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="waysDstrctCode != null">
        WAYS_DSTRCT_CODE = #{waysDstrctCode,jdbcType=VARCHAR},
      </if>
      <if test="lineDirect != null">
        LINE_DIRECT = #{lineDirect,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createUserId != null">
        CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="builtDate != null">
        BUILT_DATE = #{builtDate,jdbcType=DECIMAL},
      </if>
      <if test="commutDate != null">
        COMMUT_DATE = #{commutDate,jdbcType=DECIMAL},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="gisId != null">
        GIS_ID = #{gisId,jdbcType=VARCHAR},
      </if>
      <if test="gisXy != null">
        GIS_XY = #{gisXy,jdbcType=VARCHAR},
      </if>
      <if test="rpStartStakeNum != null">
        RP_START_STAKE_NUM = #{rpStartStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="rpEndStakeNum != null">
        RP_END_STAKE_NUM = #{rpEndStakeNum,jdbcType=DECIMAL},
      </if>
      <if test="isShow != null">
        IS_SHOW = #{isShow,jdbcType=DECIMAL},
      </if>
      <if test="prjorgcode != null">
        PRJORGCODE = #{prjorgcode,jdbcType=VARCHAR},
      </if>
      <if test="oprtorgcode != null">
        OPRTORGCODE = #{oprtorgcode,jdbcType=VARCHAR},
      </if>
      <if test="trLogicId != null">
        TR_LOGIC_ID = #{trLogicId,jdbcType=VARCHAR},
      </if>
      <if test="lineno != null">
        "LINENO" = #{lineno,jdbcType=DECIMAL},
      </if>
      <if test="designSpeed != null">
        DESIGN_SPEED = #{designSpeed,jdbcType=VARCHAR},
      </if>
      <if test="isCf != null">
        IS_CF = #{isCf,jdbcType=VARCHAR},
      </if>
      <if test="routeCode != null">
        ROUTE_CODE = #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="routeVersion != null">
        ROUTE_VERSION = #{routeVersion,jdbcType=VARCHAR},
      </if>
    </set>
    where RL_INTRVL_ID = #{rlIntrvlId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRouteIntrvlLogic">
    <!--@mbg.generated-->
    update GDGS.BASE_ROUTE_INTRVL_LOGIC
    set LINE_ID = #{lineId,jdbcType=VARCHAR},
      RP_INTRVL_ID = #{rpIntrvlId,jdbcType=VARCHAR},
      RL_INTRVL_CODE = #{rlIntrvlCode,jdbcType=VARCHAR},
      LINE_CODE = #{lineCode,jdbcType=VARCHAR},
      STAKE_TYPE = #{stakeType,jdbcType=VARCHAR},
      START_STAKE = #{startStake,jdbcType=VARCHAR},
      END_STAKE = #{endStake,jdbcType=VARCHAR},
      START_STAKE_NUM = #{startStakeNum,jdbcType=DECIMAL},
      END_STAKE_NUM = #{endStakeNum,jdbcType=DECIMAL},
      START_SENSSION_NUM = #{startSenssionNum,jdbcType=DECIMAL},
      END_SENSSION_NUM = #{endSenssionNum,jdbcType=DECIMAL},
      START_PLACE = #{startPlace,jdbcType=VARCHAR},
      END_PLACE = #{endPlace,jdbcType=VARCHAR},
      START_DSTRCT_CODE = #{startDstrctCode,jdbcType=VARCHAR},
      END_DSTRCT_CODE = #{endDstrctCode,jdbcType=VARCHAR},
      WAYS_DSTRCT_CODE = #{waysDstrctCode,jdbcType=VARCHAR},
      LINE_DIRECT = #{lineDirect,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_USER_ID = #{updateUserId,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      BUILT_DATE = #{builtDate,jdbcType=DECIMAL},
      COMMUT_DATE = #{commutDate,jdbcType=DECIMAL},
      IS_ENABLE = #{isEnable,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=VARCHAR},
      GIS_ID = #{gisId,jdbcType=VARCHAR},
      GIS_XY = #{gisXy,jdbcType=VARCHAR},
      RP_START_STAKE_NUM = #{rpStartStakeNum,jdbcType=DECIMAL},
      RP_END_STAKE_NUM = #{rpEndStakeNum,jdbcType=DECIMAL},
      IS_SHOW = #{isShow,jdbcType=DECIMAL},
      PRJORGCODE = #{prjorgcode,jdbcType=VARCHAR},
      OPRTORGCODE = #{oprtorgcode,jdbcType=VARCHAR},
      TR_LOGIC_ID = #{trLogicId,jdbcType=VARCHAR},
      "LINENO" = #{lineno,jdbcType=DECIMAL},
      DESIGN_SPEED = #{designSpeed,jdbcType=VARCHAR},
      IS_CF = #{isCf,jdbcType=VARCHAR},
      ROUTE_CODE = #{routeCode,jdbcType=VARCHAR},
      ROUTE_VERSION = #{routeVersion,jdbcType=VARCHAR}
    where RL_INTRVL_ID = #{rlIntrvlId,jdbcType=VARCHAR}
  </update>

  <insert id="synchRight">
    insert into GDGS.FW_RIGHT_DATA f (f.ID,f.USER_CODE,f.RP_INTRVL_ID)
    select sys_guid(),u.USER_CODE,l.RP_INTRVL_ID from gdgs.base_ramp_intrvl_logic l inner join GDGS.BASE_RAMP_LINE r on l.LINE_ID=r.LINE_ID
                                                                                    inner join GDGS.BASE_ROUTE_LOGIC b on b.ROUTE_CODE = r.S_ROUTE_CODE
                                                                                    inner join gdgs.FW_RIGHT_USER u on b.OPRT_ORG_CODE = u.ORG_ID and u.IS_DELETED=0 and u.IS_ENABLE=1
    where not exists(select 1 from gdgs.fw_right_data t
                     where t.RP_INTRVL_ID = l.RP_INTRVL_ID) and r.S_ROUTE_CODE is not null
    </insert>

  <delete id="deleteRight">
    delete GDGS.FW_RIGHT_DATA f where f.RP_INTRVL_ID=#{rpIntrvlId}
  </delete>

  <select id="getLinePage" resultType="com.hualu.highwaymaintenance.module.maintainbase.domain.BaseRouteIntrvlLogic">
    select r.*,t.LINE_ALLNAME from GDGS.BASE_RAMP_INTRVL_LOGIC logic
                                     inner join  GDGS.BASE_RAMP_LINE t
                                                 on t.LINE_ID=logic.LINE_ID
                                     inner join GDGS.BASE_route_INTRVL_LOGIC r
                                                on r.RP_INTRVL_ID=logic.RP_INTRVL_ID
    where exists(select 1 from GDGS.BASE_ROUTE_LOGIC x
                                 inner join GDGS.FW_RIGHT_USER u
                                            on x.OPRT_ORG_CODE=u.ORG_ID
                 where x.ROUTE_CODE=t.E_ROUTE_CODE
                   and u.USER_CODE=#{userCode})
      and logic.IS_ENABLE=1 and logic.IS_DELETED=0 and r.LINE_DIRECT=4
    <if test="lineCode != null and lineCode != ''">and r.LINE_CODE=#{lineCode}</if>
    </select>
</mapper>