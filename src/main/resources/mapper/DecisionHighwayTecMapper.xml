<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.DecisionHighwayTecMapper">
    <select id="loadDecisionHighwayTecDatas" resultType="com.hualu.highwaymaintenance.module.decision.domain.HighwayTecBean">
            with OO AS ( select * from GDGS.FW_RIGHT_ORG o
            where o.IS_ENABLE = 1 and o.IS_DELETED = 0
            start with o.ID = #{orgCode}
            connect by prior ID = PARENT_ID),
            ROUTE_INFO AS (
            select distinct p.SECOND_ORG_CODE,l.OPRT_ORG_CODE,l.ROUTE_NAME from gdgs.BASE_ROUTE_LOGIC l
            inner join OO o on l.OPRT_ORG_CODE = o.ORG_CODE
            inner join gdgs.PRO_ROUTE p on p.OPRT_ORG_CODE = l.OPRT_ORG_CODE
            where l.IS_ENABLE = 1 and p.IS_UNIT = 1
            ),TEC as (SELECT l.ROUTE_NAME,
                   ROUND(AVG(nvl(D.MQI,0)), 2) AS MQI, ROUND(AVG(nvl(D.PQI,0)), 2) PQI,
                   ROUND(AVG(nvl(D.BCI,0)), 2) AS BCI,
                   ROUND(AVG(nvl(D.SCI,0)), 2) AS SCI,
                   ROUND(AVG(nvl(D.TCI,0)), 2) AS TCI,
                   round(sum(case when MQI &gt;= 90 then LENGTH else 0 end) * 100/sum(LENGTH),2) as MQI_GOOD,
                   round(sum(case when PQI &gt;= 90 then LENGTH else 0 end) * 100/sum(LENGTH),2) as PQI_GOOD
        FROM PTCMSDB.PTCD_TC_DETAIL_PROVIDE_1000 d
        inner join gdgs.BASE_ROUTE_LOGIC l on d.ROUTE_CODE = l.ROUTE_CODE
        inner join GDGS.PRO_ROUTE p on l.OPRT_ORG_CODE = p.OPRT_ORG_CODE
        where d.YEAR = #{year}
        group by l.ROUTE_NAME)
        select a.ROUTE_NAME as org_name, MQI, PQI, BCI, SCI, TCI, MQI_GOOD, PQI_GOOD from ROUTE_INFO a
        left join TEC b on a.ROUTE_NAME = b.ROUTE_NAME
        order by a.SECOND_ORG_CODE,a.OPRT_ORG_CODE
    </select>

    <select id="loadDecisionHighwayTecTotal" resultType="com.hualu.highwaymaintenance.module.decision.domain.HighwayTecBean">
        select ROUND(AVG(nvl(d.MQI,0)), 2) AS MQI, ROUND(AVG(nvl(d.PQI,0)), 2) PQI,
               round(sum(case when MQI &gt;= 90 then LENGTH else 0 end) * 100/sum(LENGTH),2) as MQI_GOOD,
               round(sum(case when PQI &gt;= 90 then LENGTH else 0 end) * 100/sum(LENGTH),2) as PQI_GOOD
        from PTCMSDB.PTCD_TC_DETAIL_PROVIDE_1000 d
                 inner join gdgs.BASE_ROUTE r on r.ROUTE_CODE = d.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON r.PRJ_ORG_CODE=ORG.ID
        WHERE d.YEAR = #{year} and round(months_between(to_date(2022 ||'-12','yyyy-MM'),
        case when length(replace(OPEN_DATE,'-')) = 7 then to_date(substr(OPEN_DATE,0,6),'yyyy-fmMM')
        else to_date(substr(OPEN_DATE,0,7),'yyyy-MM') end)/12) >= 5 and org.parent_id = #{orgCode}
    </select>
</mapper>
