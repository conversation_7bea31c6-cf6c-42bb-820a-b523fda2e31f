<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.TForecastResultMapper">
  <select id="getPage"
    resultType="com.hualu.highwaymaintenance.module.decision.entity.TForecastResult">
    select
    replace(replace('K'||to_char(start_stake, '999999.999'),' ',''), '.', '+') startStakeStr,
    replace(replace('K'||to_char(end_stake, '999999.999'),' ',''), '.', '+') endStakeStr,
    result_id, forecast_unit_id, model_param_id, start_stake, end_stake, length, lane,
    round(custom_alpha, 3) custom_alpha, round(custom_beta, 3) custom_beta, prj_id, forecast_type,
    ppi, line_id, route_code, year1, year2, year3, year4, year5, year6, year7, year8, year9, year10,
    year11, year12, year13, year14, year15, current_year, score, predict_year_count, LINE_DIRECT
    from PMSDB.T_FORECAST_RESULT t
    where 1=1
    <if test="prjId != null and prjId != ''">
      and t.PRJ_ID = #{prjId}
    </if>
    <if test="prjId == null || prjId == ''">
      and 1=2
    </if>

    <if test="direct != null and direct != ''">
      <if test="lane != null and lane != ''">
        and t.LANE= #{lane}
      </if>
      <if test="lane == null || lane == ''">
        <if test="direct == '1'">
          and t.LANE in ('A','C','E')
        </if>
        <if test="direct == '2'">
          and t.LANE in ('B','D','F')
        </if>
      </if>
    </if>

    <if test="forecastType != null and forecastType != ''">
      and t. FORECAST_TYPE= #{forecastType}
    </if>

    <if test="start != null and start != ''">
      and (t.start_stake-#{start})*(t.end_stake-#{end})&lt;=0
    </if>
    order by start_stake
  </select>
</mapper>


