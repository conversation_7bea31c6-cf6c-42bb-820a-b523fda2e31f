<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.platform.mapper.AppTrackMapper">
    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.platform.entity.AppTrack">
        <!--@mbg.generated-->
        <!--@Table GDGS.APP_TRACK-->
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="TRACK_FILE_ID" jdbcType="VARCHAR" property="trackFileId" />
        <result column="TRACK_FILE_PATH" jdbcType="VARCHAR" property="trackFilePath" />
        <result column="UPLOAD_TYPE" jdbcType="VARCHAR" property="uploadType" />
        <result column="TRACK_LENGTH" jdbcType="DECIMAL" property="trackLength" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, TRACK_FILE_ID, TRACK_FILE_PATH, UPLOAD_TYPE, TRACK_LENGTH
    </sql>
</mapper>