<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.TunnelRepositoryNewMapper">

  <select id="getTunnelRepository" resultType="com.hualu.highwaymaintenance.module.national.entity.TunnelRepositoryNew">
    select id,ROWNUM orders, oprt_org_code, oprt_org_name, line_code, line_name, stake, second_org_code, second_org_name, remark, year, import_date, import_version_id, order_number, unit_id, prevent_tunnel_count, repair_tunnel_count, prevent_mileage, repair_mileage, prevent_civil_const, repair_civil_const, civil_total_const, ele_total_const, total_const, version
    from (select *
    from MEMSDB.TUNNEL_REPOSITORY_NEW br
    <where>
      <if test="secondCompanyOrg != null and secondCompanyOrg != ''">
        br.SECOND_ORG_CODE = #{secondCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="threeCompanyOrg != null and threeCompanyOrg != ''">
        and br.OPRT_ORG_CODE = #{threeCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="startYear != null">
        and br.YEAR >= #{startYear,jdbcType=INTEGER}
      </if>

      <if test="endYear != null">
        and br.YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
      </if>

      <if test="userOrgId != null and userOrgId != ''">
        and exists(
        select 1 from (select ORG_CODE
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{userOrgId,jdbcType=VARCHAR}
        connect by prior o.ID = o.PARENT_ID) a
        where a.ORG_CODE = br.OPRT_ORG_CODE)
      </if>
      <if test="version == 'package'">
        and br.version is null
      </if>
    
      <if test="target != null">
        and br.target = #{target,jdbcType=INTEGER}
      </if>
      <if test="lineType != null and lineType != ''">
        and br.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
      </if>
    </where>
    order by ORDER_NUMBER)
  </select>

  <select id="getTunnelRepositoryPage" resultType="com.hualu.highwaymaintenance.module.national.entity.TunnelRepositoryNew">
    select id,ROWNUM orders, oprt_org_code, oprt_org_name, line_code, line_name, stake, second_org_code, second_org_name, remark, year, import_date, import_version_id, order_number, unit_id, prevent_tunnel_count, repair_tunnel_count, prevent_mileage, repair_mileage, prevent_civil_const, repair_civil_const, civil_total_const, ele_total_const, total_const, version
    from (select *
    from MEMSDB.TUNNEL_REPOSITORY_NEW br
    <where>
      <if test="secondCompanyOrg != null and secondCompanyOrg != ''">
        br.SECOND_ORG_CODE = #{secondCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="threeCompanyOrg != null and threeCompanyOrg != ''">
        and br.OPRT_ORG_CODE = #{threeCompanyOrg,jdbcType=VARCHAR}
      </if>

      <if test="startYear != null">
        and br.YEAR >= #{startYear,jdbcType=INTEGER}
      </if>

      <if test="endYear != null">
        and br.YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
      </if>

      <if test="target != null">
        and br.target = #{target,jdbcType=INTEGER}
      </if>

      <if test="lineType != null and lineType != ''">
        and br.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
      </if>

      <if test="userOrgId != null and userOrgId != ''">
        and exists(
        select 1 from (select ORG_CODE
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{userOrgId,jdbcType=VARCHAR}
        connect by prior o.ID = o.PARENT_ID) a
        where a.ORG_CODE = br.OPRT_ORG_CODE)
      </if>
    </where>
    order by ORDER_NUMBER)
  </select>
</mapper>