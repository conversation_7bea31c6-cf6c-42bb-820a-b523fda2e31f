<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.decision.mapper.TdServiceMapper">
    <select id="findDssTypeGroupList" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        SELECT LINE.LINE_CODE as lineCode,
           DECODE(L.LINE_DIRECT, '1', '上行', '2', '下行') as lineDirect,
           O.LANE,
           PTCMSDB.FUNC_ZHZ(O.STAKE) as rlStake,
           T.DSS_TYPE_NAME as dssTypeName,
           Decode(O.dss_degree,'01','轻','02','中','03','重') AS dssDegree,
           O<PERSON><PERSON>S_L as dssL,
           <PERSON><PERSON><PERSON>S_<PERSON> as dssA,
           <PERSON><PERSON><PERSON>S_CAUSE as dssC<PERSON>e,
           <PERSON><PERSON>S_N AS dssN,
           o<PERSON><PERSON>S_TYPE as dssType,
           DECODE(O.MEANS,'0','人工检测','1','自动检测') as means
        FROM MEMSDB.DSS_INFO_DESION O
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON O.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.PTCD_HPAS_SD_PAVEMENT T ON O.DSS_TYPE = T.DSS_TYPE
            AND T.DSS_DEGREE = decode(O.dss_degree, null, '01', '重', '03', '中', '02', '轻', '01', O.dss_degree)
                 JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE = LINE.LINE_CODE
        WHERE ORG.ID = '${orgId}'
          AND O.DSS_TYPE LIKE '%${type}%'
          AND LINE.LINE_ID = '${lineId}'
          AND O.YEAR = '${year}'
          AND T.MEANS=0
          ORDER BY lineCode,LANE,O.STAKE
    </select>
    <select id="findRqiGroupList" resultType="com.hualu.highwaymaintenance.decision.entity.FlatnessData">
        select d.left_iri_m  as leftIriM,
           d.right_iri_m as rightIriM,
           d.left_iri as leftIri,
           d.RIGHT_IRI as rightIri,
           L.LINE_CODE AS lineCode,
           ORG.ID      as prjId,
           d.lane        as lane,
           PTCMSDB.FUNC_ZHZ(D.START_STAKE) as rlStartStake,
           PTCMSDB.FUNC_ZHZ(D.END_STAKE)   as rlEndStake,
           D.ROUTECODE   as routeCode,
           D.YEAR as year
    from PTCMSDB.PTCD_FLATNESS_DATA_DESION d
             join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
             JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
             JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
    where D.YEAR = '${year}'
      AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
       ORDER BY lineCode,lane,D.START_STAKE
    </select>
    <select id="findRdiGroupList" resultType="com.hualu.highwaymaintenance.decision.entity.RuttingData">
        select d.LEFT_RD_M  as leftRdM,
               d.RIGHT_RD_M as rightRdM,
               d.LEFT_RD as leftRd,
               d.RIGHT_RD as rightRd,
               ORG.ID      as prjId,
               L.LINE_CODE AS lineCode,
               DECODE(d.LANE_DIRECTION,'1','上行','2', '下行') as laneDirection,
               d.lane        as lane,
               PTCMSDB.FUNC_ZHZ(D.START_STAKE) as rlStartStake,
               PTCMSDB.FUNC_ZHZ(D.END_STAKE)   as rlEndStake,
               D.ROUTECODE   as routeCode,
               d.YEAR as year
        from PTCMSDB.PTCD_RUTTING_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where d.YEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
          ORDER BY lineCode,lane,D.START_STAKE
    </select>

    <insert id="saveHistoryRdiDataList">
        INSERT INTO PTCMSDB.PTCD_RUTTING_DATA_DESION (RUTTING_ID,LEFT_RD_M,RIGHT_RD_M,LEFT_RD,RIGHT_RD,PRJ_ID,LANE_DIRECTION,lane,START_STAKE,END_STAKE,ROUTECODE,year)
        SELECT SYS_GUID(),A.* FROM (select DISTINCT d.LEFT_RD_M  as leftRdM,
               d.RIGHT_RD_M as rightRdM,
               d.LEFT_RD as leftRd,
               d.RIGHT_RD as rightRd,
               ORG.ID      as prjId,
               d.LANE_DIRECTION as laneDirection,
               d.lane        as lane,
               D.START_STAKE as startStake,
               D.END_STAKE   as endStake,
               D.ROUTECODE   as routeCode,
               p.PRJYEAR as year
        from PTCMSDB.PTCD_RUTTING_DATA d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON D.PRJ_ID = P.PRJ_ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where P.PRJYEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}'))A
    </insert>

    <select id="findSriGroupList" resultType="com.hualu.highwaymaintenance.decision.entity.AntiSlipData">
        select d.SFC_S  as sfcS,
               d.SFC_M as sfcM,
               d.TEST_SPEED as testSpeed,
               d.PAVE_TEMP as paveTemp,
               ORG.ID      as prjId,
               DECODE(d.LANE_DIRECTION,'1','上行','2', '下行') as laneDirection,
               L.LINE_CODE AS lineCode,
               d.lane        as lane,
               PTCMSDB.FUNC_ZHZ(D.START_STAKE) as rlStartStake,
               PTCMSDB.FUNC_ZHZ(D.END_STAKE)   as rlEndStake,
               D.ROUTECODE   as routeCode,
               d.YEAR,
               d.REMARK as remark
        from PTCMSDB.PTCD_ANTI_SLIP_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where d.YEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
          ORDER BY lineCode,lane,D.START_STAKE
    </select>

    <insert id="saveHistorySriDataList">
        INSERT INTO PTCMSDB.PTCD_ANTI_SLIP_DATA_DESION (ANTI_SLIP_ID,SFC_S,SFC_M,TEST_SPEED,PAVE_TEMP,PRJ_ID,LANE_DIRECTION,lane,START_STAKE,END_STAKE,ROUTECODE,year)
        SELECT SYS_GUID(),A.* FROM (select DISTINCT d.SFC_S  as sfcS,
               d.SFC_M as sfcM,
               d.TEST_SPEED as testSpeed,
               d.PAVE_TEMP as paveTemp,
               ORG.ID      as prjId,
               d.LANE_DIRECTION as laneDirection,
               d.lane        as lane,
               D.START_STAKE as startStake,
               D.END_STAKE   as endStake,
               D.ROUTECODE   as routeCode,
               p.PRJYEAR as year
        from PTCMSDB.PTCD_ANTI_SLIP_DATA d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON D.PRJ_ID = P.PRJ_ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where P.PRJYEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}'))A
    </insert>

    <select id="findPbiGroupList" resultType="com.hualu.highwaymaintenance.decision.entity.PtcdCarData">
        select d.PB  as pb,
               d.PB_AI as pbAi,
               d.PBI_TYPE as pbiType,
               d.PBI_COUNT as pbiCount,
               d.PB_LENGTH as length,
               d.PBI_HIGHT as pbiHight,
                d.PB_HIGHT_LEFT as pbHightLeft,
               d.PB_HIGHT_RIGHT as pbHightRight,
               L.LINE_CODE AS lineCode,
               ORG.ID      as prjId,
               DECODE(d.LANE_DIRECTION,'1','上行','2', '下行') as laneDirection,
               d.lane        as lane,
               PTCMSDB.FUNC_ZHZ(D.START_STAKE) as rlStartStake,
               PTCMSDB.FUNC_ZHZ(D.END_STAKE)   as rlEndStake,
               D.ROUTECODE   as routeCode,
               d.YEAR,
               d.REMARK as remark
        from PTCMSDB.PTCD_CAR_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where d.YEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
          ORDER BY lineCode,lane,D.START_STAKE
    </select>

    <insert id="saveHistoryPbiDataList">
        INSERT INTO PTCMSDB.PTCD_CAR_DATA_DESION (PBI_ID,PB,PB_AI,PBI_TYPE,PBI_COUNT,PB_LENGTH,PB_HIGHT_LEFT,PB_HIGHT_RIGHT,PBI_HIGHT,PRJ_ID,LANE_DIRECTION,lane,START_STAKE,END_STAKE,ROUTECODE,year)
        SELECT SYS_GUID(),A.* FROM (select DISTINCT d.PB  as pb,
               d.PB_AI as pbAi,
               d.PBI_TYPE as pbiType,
               d.PBI_COUNT as pbiCount,
               d.PB_LENGTH as length,
               d.PB_HIGHT_LEFT,
               d.PB_HIGHT_RIGHT,
               d.PBI_HIGHT as PBI_HIGHT,
               ORG.ID      as prjId,
               d.LANE_DIRECTION as laneDirection,
               d.lane        as lane,
               D.START_STAKE as startStake,
               D.END_STAKE   as endStake,
               D.ROUTECODE   as routeCode,
               p.PRJYEAR as year
        from PTCMSDB.PTCD_CAR_DATA d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON D.PRJ_ID = P.PRJ_ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where P.PRJYEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}'))A
    </insert>

    <select id="findPssiGroupList" resultType="com.hualu.highwaymaintenance.decision.entity.DeflectionData">
        select d.PAVE_TEMP  as paveTemp,
               d.BACKMAN_S as backmanS,
               d.BACKMAN_M as backmanM,
               d.TEST_METHOD as testMethod,
               ORG.ID      as prjId,
               L.LINE_CODE AS lineCode,
               DECODE(d.LANE_DIRECTION,'1','上行','2', '下行') as laneDirection,
               d.lane        as lane,
               PTCMSDB.FUNC_ZHZ(D.STAKE) as rlStake,
               D.ROUTECODE   as routeCode,
               d.YEAR
        from PTCMSDB.PTCD_DEFLECTION_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where d.YEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
          ORDER BY lineCode,lane,D.STAKE
    </select>

    <insert id="saveHistoryPssiDataList">
        INSERT INTO PTCMSDB.PTCD_DEFLECTION_DATA_DESION (DEFLECTION_ID,PAVE_TEMP,BACKMAN_S,BACKMAN_M,TEST_METHOD,PRJ_ID,LANE_DIRECTION,lane,STAKE,ROUTECODE,year)
        SELECT SYS_GUID(),A.* FROM (select DISTINCT d.PAVE_TEMP  as paveTemp,
               d.BACKMAN_S as backmanS,
               d.BACKMAN_M as backmanM,
               d.TEST_METHOD as testMethod,
               ORG.ID      as prjId,
               d.LANE_DIRECTION as laneDirection,
               d.lane        as lane,
               D.STAKE as startStake,
               D.ROUTECODE   as routeCode,
               p.PRJYEAR as year
        from PTCMSDB.PTCD_DEFLECTION_DATA d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON D.PRJ_ID = P.PRJ_ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where P.PRJYEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}'))A
    </insert>

    <select id="findBciGroupList" resultType="com.hualu.highwaymaintenance.decision.entity.BctCheckData">
        select d.TC_CODE  as tcCode,
               ORG.ID      as prjId,
               DECODE(L.LINE_DIRECT,'1','上行','2', '下行') AS lineDirect,
               PTCMSDB.FUNC_ZHZ(D.STAKE) as rlStake,
               D.ROUTECODE   as routeCode,
               d.YEAR as year,
               d.lane,
               L.LINE_CODE AS lineCode
        from PTCMSDB.PTCD_BCT_CHECK_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where d.YEAR = '${year}'
        AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
        ORDER BY lineCode,lane,D.STAKE
    </select>

    <insert id="saveHistoryBciDataList">
        INSERT INTO PTCMSDB.PTCD_BCT_CHECK_DATA_DESION (BRG_CLVT_TNNL_ID,TC_CODE,PRJ_ID,STAKE,ROUTECODE,year)
           select SYS_GUID(),d.TC_CODE  as tcCode,
               ORG.ID      as prjId,
               D.STAKE as stake,
               D.ROUTECODE   as routeCode,
               p.PRJYEAR as year
        from PTCMSDB.PTCD_BCT_CHECK_DATA d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON D.PRJ_ID = P.PRJ_ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where P.PRJYEAR = '${year}'
          AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
    </insert>

    <insert id="saveHistoryRqiDataList">
        INSERT INTO PTCMSDB.PTCD_FLATNESS_DATA_DESION (FLATNESS_ID,left_iri_m,right_iri_m,LEFT_IRI,RIGHT_IRI,PRJ_ID,lane,START_STAKE,END_STAKE,ROUTECODE,year)
        SELECT SYS_GUID(),A.* FROM (select DISTINCT d.left_iri_m  as leftIriM,
           d.right_iri_m as rightIriM,
           d.LEFT_IRI as leftIri,
           d.RIGHT_IRI as rightIri,
           ORG.ID      as prjId,
           d.lane        as lane,
           D.START_STAKE as startStake,
           D.END_STAKE   as endStake,
           D.ROUTECODE   as routeCode,
           p.PRJYEAR as year
    from PTCMSDB.PTCD_FLATNESS_DATA d
             join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
             JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
             JOIN PTCMSDB.TCC_INSP_PRJ P ON D.PRJ_ID = P.PRJ_ID
             JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
    where P.PRJYEAR = '${year}'
      AND ORG.ID = '${orgId}' AND (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')) A
    </insert>
    <select id="findPavementHistoryDssType" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        SELECT DISTINCT O.REL_TASK_CODE as relTaskCode,LINE.LINE_CODE as lineCode,
           DECODE(L.LINE_DIRECT, '1', '上行', '2', '下行') as lineDirect,
           O.lane,
           O.STAKE,
           O.FACILITY_CAT AS facilityCat,
           T.DSS_TYPE AS dssType,
           T.DSS_TYPE_NAME as dssTypeName,
           O.dss_degree AS dssDegree,
           O.DSS_L as dssL,
           O.DSS_A as dssA,
           O.DSS_A_UNIT as dssAUnit,
           O.DSS_L_UNIT as dssLUnit,
           O.DSS_CAUSE as dssCause,
           O.DSS_N AS dssN,
           o.DSS_DEGREE as dssDegree,
           o.DSS_TYPE as dssType,
           o.FACILITY_CAT as facilityCat,
           O.routecode AS routecode,
           O.MEANS as means,
           ORG.ID as orgId,
           O.MAIN_ROAD_ID AS mainRoadId,
           O.LANE_DIRECTION AS laneDirection,
           O.REL_TASK_CODE AS relTaskCode,
           O.DSS_SOURCE AS dssSource
        FROM MEMSDB.DSS_INFO O
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON O.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN PTCMSDB.PTCD_HPAS_SD_PAVEMENT T ON O.DSS_TYPE = T.DSS_TYPE
            AND T.DSS_DEGREE = decode(O.dss_degree, null, '01', '重', '03', '中', '02', '轻', '01', O.dss_degree)
                 JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE = LINE.LINE_CODE
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON O.REL_TASK_CODE=P.PRJ_ID
        WHERE ORG.ID = '${orgId}'
          AND O.DSS_TYPE LIKE '%${type}%'
          AND LINE.LINE_ID = '${lineId}'
          AND P.PRJYEAR='${year}' AND T.MEANS=0
    </select>
    <insert id="insertTestFlatnessData" parameterType="list">
        INSERT INTO PTCMSDB.PTCD_FLATNESS_DATA (
        FLATNESS_ID,PRJ_ID,RP_INTRVL_ID,START_STAKE,END_STAKE,LANE,
        PAVEMENT_TYPE,LEFT_IRI,RIGHT_IRI,LEFT_IRI_M,RIGHT_IRI_M,REMARK,LINE_ID,
        LANE_DIRECTION,YEAR,UNIT_MARGE_ID,routecode,routeversion
        )
        SELECT A.* FROM (
        <foreach item="item" collection="list" separator="UNION ALL">
            SELECT
            #{item.flatnessId,jdbcType=VARCHAR},
            #{item.prjId,jdbcType=VARCHAR},
            #{item.rpIntrvlId,jdbcType=VARCHAR},
            #{item.startStake,jdbcType=NUMERIC},
            #{item.endStake,jdbcType=NUMERIC},
            #{item.lane,jdbcType=VARCHAR},
            #{item.pavementType,jdbcType=VARCHAR},
            #{item.leftIri,jdbcType=NUMERIC},
            #{item.rightIri,jdbcType=NUMERIC},
            #{item.leftIriM,jdbcType=NUMERIC},
            #{item.rightIriM,jdbcType=NUMERIC},
            #{item.remark,jdbcType=VARCHAR},
            #{item.lineId,jdbcType=VARCHAR},
            #{item.laneDirection,jdbcType=VARCHAR},
            #{item.year,jdbcType=NUMERIC},
            #{item.unitMargeId,jdbcType=VARCHAR},
            #{item.routeCode,jdbcType=VARCHAR},
            #{item.routeversion,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
        )
    </insert>
    <insert id="insertTestBctCheckData" parameterType="com.hualu.highwaymaintenance.decision.entity.BctCheckData">
		INSERT INTO PTCMSDB.PTCD_BCT_CHECK_DATA (BRG_CLVT_TNNL_ID,PRJ_ID,RP_INTRVL_ID,STAKE,FACILITY_CAT,
			STRUCT_ID,TC_CODE,STRUCT_ALL_NAME,LINE_ID,YEAR,UNIT_MARGE_ID,ROUTECODE,routeversion) values (
 			#{item.brgClvtTnnlId,jdbcType=VARCHAR},#{item.prjId,jdbcType=VARCHAR},
			#{item.rpIntrvlId,jdbcType=VARCHAR},#{item.stake,jdbcType=NUMERIC},
			#{item.facilityCat,jdbcType=VARCHAR},#{item.structId,jdbcType=VARCHAR},
			#{item.tcCode,jdbcType=VARCHAR},#{item.structAllName,jdbcType=VARCHAR},
			#{item.lineId,jdbcType=VARCHAR},#{item.year,jdbcType=NUMERIC},
			#{item.unitMargeId,jdbcType=VARCHAR},#{item.routeCode,jdbcType=VARCHAR}
			,#{item.routeversion,jdbcType=VARCHAR}
 		)
    </insert>
    <insert id="insertTestDeflectionData" parameterType="com.hualu.highwaymaintenance.decision.entity.DeflectionData">
		INSERT INTO PTCMSDB.PTCD_DEFLECTION_DATA_DESION (DEFLECTION_ID,PRJ_ID,RP_INTRVL_ID,STAKE,LANE,
			PAVEMENT_TYPE,PAVE_TEMP,BACKMAN_S,BACKMAN_M,TEST_METHOD,REMARK,LINE_ID,LANE_DIRECTION
			,YEAR,UNIT_MARGE_ID,routecode,routeversion) values (
 			#{item.deflectionId,jdbcType=VARCHAR},#{item.prjId,jdbcType=VARCHAR},
			#{item.rpIntrvlId,jdbcType=VARCHAR},#{item.stake,jdbcType=NUMERIC},
			#{item.lane,jdbcType=VARCHAR},#{item.pavementType,jdbcType=VARCHAR},
			#{item.paveTemp,jdbcType=NUMERIC},#{item.backmanS,jdbcType=NUMERIC},
			#{item.backmanM,jdbcType=NUMERIC},#{item.testMethod,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR},#{item.lineId,jdbcType=VARCHAR},
			#{item.laneDirection,jdbcType=VARCHAR},#{item.year,jdbcType=NUMERIC},
			#{item.unitMargeId,jdbcType=VARCHAR},#{item.routeCode,jdbcType=VARCHAR}
			,#{item.routeversion,jdbcType=VARCHAR}
  		)
    </insert>
</mapper>
