<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.MaintainPlanTotalMapper">
    <select id="queryMaintainPlanTotals" resultType="com.hualu.highwaymaintenance.module.decision.domain.MaintainPlanTotal">
        with ORG AS ( select * from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1 start with o.ID = #{orgCode}
        connect by prior ID = PARENT_ID),
        ROUTE_INFO AS (select distinct pro.PROJECT_NAME,o.ORG_NAME,pro.PRJ_ORG_CODE,o.ORG_CODE from ORG o
        inner join ORG op on o.PARENT_ID = op.ID
        inner join GDGS.PRO_ROUTE pro on pro.OPRT_ORG_CODE = o.ID
        inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.OPRT_ORG_CODE = o.ID
        where pro.IS_UNIT = 1)
        select ROWNUM as seq,o.PROJECT_NAME,aa.PRJ_ORG_CODE,repair_mileage, maintain_mileage, total_mileage, repair_fee, maintain_fee, total_fee from
        ROUTE_INFO o left join
        (select  PRJ_ORG_NAME,r.PRJ_ORG_CODE,
        sum(case when NATURE = 'O' or NATURE = 'R' then LENGTH end)/1000 as repair_mileage,
        sum(case when NATURE = 'Y' then LENGTH end)/1000 as maintain_mileage,
        sum(length)/1000 as total_mileage,
        sum(case when NATURE = 'O' or NATURE = 'R' then PRICE * a.LENGTH * 3.75 end)/10000 as repair_fee,
        sum(case when NATURE = 'Y' then PRICE * a.LENGTH * 3.75 end)/10000 as maintain_fee
        ,sum(PRICE * a.LENGTH * 3.75)/10000 as total_fee
        from PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION a
        inner join PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC ON a.UNIT_MARGE_ID=TC.UNIT_MARGE_ID
        inner join gdgs.BASE_ROUTE r on r.ROUTE_CODE = a.ROUTE_CODE
        inner join (select * from
        (select * from
        (select ROW_NUMBER ( ) OVER ( PARTITION BY ab.MEASURE_ID,LANE_TYPE,ROUTE_CODE,
        START_STAKE,END_STAKE,LINE_DIRECTION ORDER BY ab.year DESC ) rn,ab.year,m.MEASURE_NAME,m.PRICE,
        LANE_TYPE,ROUTE_CODE,START_STAKE,END_STAKE,LINE_DIRECTION,NATURE
        from PTCMSDB.PBD_PAVEMENT_MAINTAIN ab
        inner join PTCMSDB.PBD_PAVEMENT_MEASURE m on m.MEASURE_ID = ab.MEASURE_ID
        ) where rn = 1)) b on a.ROUTE_CODE = b.ROUTE_CODE and b.YEAR &lt;= a.YEAR
        and a.LINE_DIRECT = b.LINE_DIRECTION and ((a.START_STAKE &gt;= b.START_STAKE and a.END_STAKE &lt;= b.END_STAKE and LINE_DIRECT = 1)
        or (a.START_STAKE &lt;= b.START_STAKE and a.END_STAKE &gt;= b.END_STAKE and LINE_DIRECT = 2))
        where  a.YEAR = #{year}
        group by r.PRJ_ORG_CODE,PRJ_ORG_NAME
        order by PRJ_ORG_CODE,PRJ_ORG_NAME) aa on o.PRJ_ORG_CODE = aa.PRJ_ORG_CODE
    </select>
</mapper>
