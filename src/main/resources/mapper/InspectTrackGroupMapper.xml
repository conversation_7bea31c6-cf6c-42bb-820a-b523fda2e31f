<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dailycheck.mapper.InspectTrackGroupMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dailycheck.entity.InspectTrackGroup">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="inspectId" column="INSPECT_ID" jdbcType="VARCHAR"/>
            <result property="startTime" column="START_TIME" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="END_TIME" jdbcType="TIMESTAMP"/>
            <result property="length" column="LENGTH" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,INSPECT_ID,START_TIME,
        END_TIME,LENGTH
    </sql>
</mapper>
