<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.MaintainMeasureIndexMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.MaintainMeasureIndex">
    <!--@mbg.generated-->
    <!--@Table PMSDB.MAINTAIN_MEASURE_INDEX-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MAINTAIN_MEASURE_ID" jdbcType="VARCHAR" property="maintainMeasureId" />
    <result column="INDEX_TYPE" jdbcType="VARCHAR" property="indexType" />
    <result column="ALPHA" jdbcType="DECIMAL" property="alpha" />
    <result column="BETA" jdbcType="DECIMAL" property="beta" />
    <result column="PPI" jdbcType="DECIMAL" property="ppi" />
    <result column="AADT" jdbcType="DECIMAL" property="aadt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MAINTAIN_MEASURE_ID, INDEX_TYPE, ALPHA, BETA, PPI, AADT
  </sql>
</mapper>