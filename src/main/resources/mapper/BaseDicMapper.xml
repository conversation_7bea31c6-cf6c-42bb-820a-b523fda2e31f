<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.inspect.mapper.BaseDicMapper">
    <select id="queryBaseDicList" resultType="com.hualu.highwaymaintenance.module.inspect.domain.BaseDic">
        select ATTRIBUTE_VALUE as attr_value,ATTRIBUTE_CODE as attr_code from gdgs.BASE_DATATHIRD_DIC
        where ATTRIBUTE_ITEM = #{code}
          and ATTRIBUTE_ACTIVE = 0
        order by ATTRIBUTE_CODE
    </select>
</mapper>
