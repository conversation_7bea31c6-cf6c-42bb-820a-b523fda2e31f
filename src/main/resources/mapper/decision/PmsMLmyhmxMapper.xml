<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsMLmyhmxMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsMLmyhmx">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_M_LMYHMX-->
    <result column="LMJG_ID" jdbcType="VARCHAR" property="lmjgId" />
    <result column="LM_MX_ID" jdbcType="VARCHAR" property="lmMxId" />
    <result column="CODE" jdbcType="VARCHAR" property="code" />
    <result column="NM" jdbcType="VARCHAR" property="nm" />
    <result column="THK" jdbcType="FLOAT" property="thk" />
    <result column="LMLX" jdbcType="VARCHAR" property="lmlx" />
    <result column="DESIGN_LD" jdbcType="DECIMAL" property="designLd" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="PRICE" jdbcType="FLOAT" property="price" />
    <result column="XNYCQXID" jdbcType="VARCHAR" property="xnycqxid" />
    <result column="XNYCQX" jdbcType="VARCHAR" property="xnycqx" />
    <result column="IS_LMJG" jdbcType="DECIMAL" property="isLmjg" />
    <result column="TECHINDEX" jdbcType="VARCHAR" property="techindex" />
    <result column="PLAN_TYPE" jdbcType="VARCHAR" property="planType" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LMJG_ID, LM_MX_ID, CODE, NM, THK, LMLX, DESIGN_LD, "TYPE", PRICE, XNYCQXID, XNYCQX, 
    IS_LMJG, TECHINDEX, PLAN_TYPE, REMARK
  </sql>
</mapper>