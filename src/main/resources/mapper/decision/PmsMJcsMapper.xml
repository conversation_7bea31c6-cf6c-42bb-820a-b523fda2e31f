<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsMJcsMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsMJcs">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_M_JCS-->
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="LM_MX_ID" jdbcType="VARCHAR" property="lmMxId" />
    <result column="LMLXID" jdbcType="VARCHAR" property="lmlxid" />
    <result column="FA1" jdbcType="VARCHAR" property="fa1" />
    <result column="FA2" jdbcType="VARCHAR" property="fa2" />
    <result column="FA3" jdbcType="VARCHAR" property="fa3" />
    <result column="FA4" jdbcType="VARCHAR" property="fa4" />
    <result column="YS_GROUP_NAME" jdbcType="VARCHAR" property="ysGroupName" />
    <result column="YS_CHILD_NAME" jdbcType="VARCHAR" property="ysChildName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, LM_MX_ID, LMLXID, FA1, FA2, FA3, FA4, YS_GROUP_NAME, YS_CHILD_NAME
  </sql>
</mapper>