<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsRT3xuqiufenxisetMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsRT3xuqiufenxiset">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_R_T3XUQIUFENXISET-->
    <id column="LM_PRJ_ID" jdbcType="VARCHAR" property="lmPrjId" />
    <result column="DQND" jdbcType="DECIMAL" property="dqnd" />
    <result column="FXNX" jdbcType="DECIMAL" property="fxnx" />
    <result column="JTLYCMX" jdbcType="DECIMAL" property="jtlycmx" />
    <result column="ZYGLPCIBZ" jdbcType="FLOAT" property="zyglpcibz" />
    <result column="ZYGLRQIBZ" jdbcType="FLOAT" property="zyglrqibz" />
    <result column="ZYGLPSSIBZ" jdbcType="FLOAT" property="zyglpssibz" />
    <result column="ZYGLRDIBZ" jdbcType="FLOAT" property="zyglrdibz" />
    <result column="ZYGLSRIBZ" jdbcType="FLOAT" property="zyglsribz" />
    <result column="YBGLPCIBZ" jdbcType="FLOAT" property="ybglpcibz" />
    <result column="YBGLRQIBZ" jdbcType="FLOAT" property="ybglrqibz" />
    <result column="YBGLPSSIBZ" jdbcType="FLOAT" property="ybglpssibz" />
    <result column="ZYGLPCISP" jdbcType="FLOAT" property="zyglpcisp" />
    <result column="ZYGLRQISP" jdbcType="FLOAT" property="zyglrqisp" />
    <result column="ZYGLPSSISP" jdbcType="FLOAT" property="zyglpssisp" />
    <result column="YBGLPCISP" jdbcType="FLOAT" property="ybglpcisp" />
    <result column="YBGLRQISP" jdbcType="FLOAT" property="ybglrqisp" />
    <result column="YBGLPSSISP" jdbcType="FLOAT" property="ybglpssisp" />
    <result column="STATE" jdbcType="DECIMAL" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LM_PRJ_ID, DQND, FXNX, JTLYCMX, ZYGLPCIBZ, ZYGLRQIBZ, ZYGLPSSIBZ, ZYGLRDIBZ, ZYGLSRIBZ, 
    YBGLPCIBZ, YBGLRQIBZ, YBGLPSSIBZ, ZYGLPCISP, ZYGLRQISP, ZYGLPSSISP, YBGLPCISP, YBGLRQISP, 
    YBGLPSSISP, "STATE"
  </sql>
</mapper>