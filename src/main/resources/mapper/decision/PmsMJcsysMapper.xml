<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsMJcsysMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsMJcsys">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_M_JCSYS-->
    <result column="LM_MX_ID" jdbcType="VARCHAR" property="lmMxId" />
    <result column="LMLXID" jdbcType="VARCHAR" property="lmlxid" />
    <result column="XH" jdbcType="DECIMAL" property="xh" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="ZYGL1" jdbcType="FLOAT" property="zygl1" />
    <result column="ZYGL2" jdbcType="FLOAT" property="zygl2" />
    <result column="YBGL1" jdbcType="FLOAT" property="ybgl1" />
    <result column="YBGL2" jdbcType="FLOAT" property="ybgl2" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="IS_DYNAMIC" jdbcType="DECIMAL" property="isDynamic" />
    <result column="DYNAMIC_ZYGL" jdbcType="VARCHAR" property="dynamicZygl" />
    <result column="DYNAMIC_YBGL" jdbcType="VARCHAR" property="dynamicYbgl" />
    <result column="DYNAMIC_ZYGL_NAME" jdbcType="VARCHAR" property="dynamicZyglName" />
    <result column="DYNAMIC_YBGL_NAME" jdbcType="VARCHAR" property="dynamicYbglName" />
    <result column="CHILD_NAME" jdbcType="VARCHAR" property="childName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LM_MX_ID, LMLXID, XH, XM, ZYGL1, ZYGL2, YBGL1, YBGL2, "NAME", IS_DYNAMIC, DYNAMIC_ZYGL, 
    DYNAMIC_YBGL, DYNAMIC_ZYGL_NAME, DYNAMIC_YBGL_NAME, CHILD_NAME
  </sql>
</mapper>