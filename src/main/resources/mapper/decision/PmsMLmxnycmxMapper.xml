<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsMLmxnycmxMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsMLmxnycmx">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_M_LMXNYCMX-->
    <result column="LMJG_ID" jdbcType="VARCHAR" property="lmjgId" />
    <result column="LM_MX_ID" jdbcType="VARCHAR" property="lmMxId" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="AADTJBID" jdbcType="DECIMAL" property="aadtjbid" />
    <result column="AADTJB" jdbcType="VARCHAR" property="aadtjb" />
    <result column="CS1" jdbcType="FLOAT" property="cs1" />
    <result column="CS2" jdbcType="FLOAT" property="cs2" />
    <result column="CS3" jdbcType="FLOAT" property="cs3" />
    <result column="CS4" jdbcType="FLOAT" property="cs4" />
    <result column="CS5" jdbcType="FLOAT" property="cs5" />
    <result column="CS6" jdbcType="FLOAT" property="cs6" />
    <result column="CS7" jdbcType="FLOAT" property="cs7" />
    <result column="CS8" jdbcType="FLOAT" property="cs8" />
    <result column="CS9" jdbcType="FLOAT" property="cs9" />
    <result column="CS10" jdbcType="FLOAT" property="cs10" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LMJG_ID, LM_MX_ID, XM, AADTJBID, AADTJB, CS1, CS2, CS3, CS4, CS5, CS6, CS7, CS8, 
    CS9, CS10
  </sql>
</mapper>