<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsRDetailMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsRDetail">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_R_DETAIL-->
    <id column="TC_DETAIL_ID" jdbcType="VARCHAR" property="tcDetailId" />
    <id column="LM_PRJ_ID" jdbcType="VARCHAR" property="lmPrjId" />
    <result column="STRUCT_INTRVL_UID" jdbcType="VARCHAR" property="structIntrvlUid" />
    <result column="PRJ_ID" jdbcType="VARCHAR" property="prjId" />
    <result column="RP_INTRVL_ID" jdbcType="VARCHAR" property="rpIntrvlId" />
    <result column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
    <result column="LINE_NAME" jdbcType="VARCHAR" property="lineName" />
    <result column="WAYS_DSTRCT_CODE" jdbcType="VARCHAR" property="waysDstrctCode" />
    <result column="DSTRCT_NAME" jdbcType="VARCHAR" property="dstrctName" />
    <result column="OPRT_ORG_CODE" jdbcType="VARCHAR" property="oprtOrgCode" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="RP_INTRVL_CODE" jdbcType="VARCHAR" property="rpIntrvlCode" />
    <result column="START_STAKE" jdbcType="DECIMAL" property="startStake" />
    <result column="END_STAKE" jdbcType="DECIMAL" property="endStake" />
    <result column="LENGTH" jdbcType="DECIMAL" property="length" />
    <result column="YEAR" jdbcType="DECIMAL" property="year" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="LANE" jdbcType="VARCHAR" property="lane" />
    <result column="LANEXS" jdbcType="DECIMAL" property="lanexs" />
    <result column="TECH_GRADE" jdbcType="VARCHAR" property="techGrade" />
    <result column="TECH_GRADE_NAME" jdbcType="VARCHAR" property="techGradeName" />
    <result column="PAVEMENT_TYPE" jdbcType="VARCHAR" property="pavementType" />
    <result column="PAVEMENT_TYPE_NAME" jdbcType="VARCHAR" property="pavementTypeName" />
    <result column="PAVEMENT_STRUCT_ID" jdbcType="VARCHAR" property="pavementStructId" />
    <result column="PAVEMENTCODE" jdbcType="VARCHAR" property="pavementcode" />
    <result column="PAVEMENTNM" jdbcType="VARCHAR" property="pavementnm" />
    <result column="PAVEMENTTHK" jdbcType="DECIMAL" property="pavementthk" />
    <result column="DESIGN_LD" jdbcType="DECIMAL" property="designLd" />
    <result column="EFFECT_WIDTH" jdbcType="DECIMAL" property="effectWidth" />
    <result column="LANENUM" jdbcType="DECIMAL" property="lanenum" />
    <result column="CAL_WIDTH" jdbcType="DECIMAL" property="calWidth" />
    <result column="TRUCKSL" jdbcType="DECIMAL" property="trucksl" />
    <result column="TRUCKSM" jdbcType="DECIMAL" property="trucksm" />
    <result column="TRUCKSH" jdbcType="DECIMAL" property="trucksh" />
    <result column="PASSENGERVC" jdbcType="DECIMAL" property="passengervc" />
    <result column="PASSENGERVM" jdbcType="DECIMAL" property="passengervm" />
    <result column="PASSENGERVB" jdbcType="DECIMAL" property="passengervb" />
    <result column="TRAILER" jdbcType="DECIMAL" property="trailer" />
    <result column="AADT" jdbcType="DECIMAL" property="aadt" />
    <result column="JTZZ01" jdbcType="DECIMAL" property="jtzz01" />
    <result column="JTZZ02" jdbcType="DECIMAL" property="jtzz02" />
    <result column="PQI" jdbcType="DECIMAL" property="pqi" />
    <result column="SCI" jdbcType="DECIMAL" property="sci" />
    <result column="BCI" jdbcType="DECIMAL" property="bci" />
    <result column="TCI" jdbcType="DECIMAL" property="tci" />
    <result column="PCI" jdbcType="DECIMAL" property="pci" />
    <result column="RQI" jdbcType="DECIMAL" property="rqi" />
    <result column="RDI" jdbcType="DECIMAL" property="rdi" />
    <result column="SRI" jdbcType="DECIMAL" property="sri" />
    <result column="PSSI" jdbcType="DECIMAL" property="pssi" />
    <result column="MQI" jdbcType="DECIMAL" property="mqi" />
    <result column="VALI" jdbcType="VARCHAR" property="vali" />
    <result column="VALICAUSE" jdbcType="VARCHAR" property="valicause" />
    <result column="WEARING_COURSE" jdbcType="VARCHAR" property="wearingCourse" />
    <result column="MAINTAIN_HISTORY" jdbcType="VARCHAR" property="maintainHistory" />
    <result column="PAVEMENT_STRUCT" jdbcType="VARCHAR" property="pavementStruct" />
    <result column="ROAD_AGE" jdbcType="DECIMAL" property="roadAge" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TC_DETAIL_ID, LM_PRJ_ID, STRUCT_INTRVL_UID, PRJ_ID, RP_INTRVL_ID, LINE_ID, LINE_NAME, 
    WAYS_DSTRCT_CODE, DSTRCT_NAME, OPRT_ORG_CODE, ORG_NAME, RP_INTRVL_CODE, START_STAKE, 
    END_STAKE, "LENGTH", "YEAR", LINE_DIRECT, LANE, LANEXS, TECH_GRADE, TECH_GRADE_NAME, 
    PAVEMENT_TYPE, PAVEMENT_TYPE_NAME, PAVEMENT_STRUCT_ID, PAVEMENTCODE, PAVEMENTNM, 
    PAVEMENTTHK, DESIGN_LD, EFFECT_WIDTH, LANENUM, CAL_WIDTH, TRUCKSL, TRUCKSM, TRUCKSH, 
    PASSENGERVC, PASSENGERVM, PASSENGERVB, TRAILER, AADT, JTZZ01, JTZZ02, PQI, SCI, BCI, 
    TCI, PCI, RQI, RDI, SRI, PSSI, MQI, VALI, VALICAUSE, WEARING_COURSE, MAINTAIN_HISTORY, 
    PAVEMENT_STRUCT, ROAD_AGE
  </sql>
</mapper>