<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsRT3xuqiufenxiMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsRT3xuqiufenxi">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_R_T3XUQIUFENXI-->
    <id column="TC_DETAIL_ID" jdbcType="VARCHAR" property="tcDetailId" />
    <id column="LM_PRJ_ID" jdbcType="VARCHAR" property="lmPrjId" />
    <id column="LM_YEAR" jdbcType="DECIMAL" property="lmYear" />
    <result column="STRUCT_INTRVL_UID" jdbcType="VARCHAR" property="structIntrvlUid" />
    <result column="PRJ_ID" jdbcType="VARCHAR" property="prjId" />
    <result column="LINE_ID" jdbcType="VARCHAR" property="lineId" />
    <result column="RP_INTRVL_ID" jdbcType="VARCHAR" property="rpIntrvlId" />
    <result column="START_STAKE" jdbcType="DECIMAL" property="startStake" />
    <result column="END_STAKE" jdbcType="DECIMAL" property="endStake" />
    <result column="LENGTH" jdbcType="DECIMAL" property="length" />
    <result column="YEAR" jdbcType="DECIMAL" property="year" />
    <result column="LANE" jdbcType="VARCHAR" property="lane" />
    <result column="LANEXS" jdbcType="DECIMAL" property="lanexs" />
    <result column="TECH_GRADE" jdbcType="VARCHAR" property="techGrade" />
    <result column="PAVEMENT_TYPE" jdbcType="VARCHAR" property="pavementType" />
    <result column="PAVEMENT_STRUCT_ID" jdbcType="VARCHAR" property="pavementStructId" />
    <result column="PAVEMENTCODE" jdbcType="VARCHAR" property="pavementcode" />
    <result column="EFFECT_WIDTH" jdbcType="DECIMAL" property="effectWidth" />
    <result column="LANENUM" jdbcType="DECIMAL" property="lanenum" />
    <result column="CAL_WIDTH" jdbcType="DECIMAL" property="calWidth" />
    <result column="AADT" jdbcType="DECIMAL" property="aadt" />
    <result column="JTZZ01" jdbcType="DECIMAL" property="jtzz01" />
    <result column="JTZZ02" jdbcType="DECIMAL" property="jtzz02" />
    <result column="PN" jdbcType="DECIMAL" property="pn" />
    <result column="PQI" jdbcType="DECIMAL" property="pqi" />
    <result column="PCI" jdbcType="DECIMAL" property="pci" />
    <result column="RQI" jdbcType="DECIMAL" property="rqi" />
    <result column="RDI" jdbcType="DECIMAL" property="rdi" />
    <result column="SRI" jdbcType="DECIMAL" property="sri" />
    <result column="PSSI" jdbcType="DECIMAL" property="pssi" />
    <result column="PQI2" jdbcType="DECIMAL" property="pqi2" />
    <result column="PCI2" jdbcType="DECIMAL" property="pci2" />
    <result column="RQI2" jdbcType="DECIMAL" property="rqi2" />
    <result column="RDI2" jdbcType="DECIMAL" property="rdi2" />
    <result column="SRI2" jdbcType="DECIMAL" property="sri2" />
    <result column="PSSI2" jdbcType="DECIMAL" property="pssi2" />
    <result column="PQINEW" jdbcType="DECIMAL" property="pqinew" />
    <result column="PCINEW" jdbcType="DECIMAL" property="pcinew" />
    <result column="RQINEW" jdbcType="DECIMAL" property="rqinew" />
    <result column="RDINEW" jdbcType="DECIMAL" property="rdinew" />
    <result column="SRINEW" jdbcType="DECIMAL" property="srinew" />
    <result column="PSSINEW" jdbcType="DECIMAL" property="pssinew" />
    <result column="FACODE" jdbcType="VARCHAR" property="facode" />
    <result column="FANAME" jdbcType="VARCHAR" property="faname" />
    <result column="FATYPE" jdbcType="VARCHAR" property="fatype" />
    <result column="PRICES" jdbcType="DECIMAL" property="prices" />
    <result column="LINE_NAME" jdbcType="VARCHAR" property="lineName" />
    <result column="WAYS_DSTRCT_CODE" jdbcType="VARCHAR" property="waysDstrctCode" />
    <result column="DSTRCT_NAME" jdbcType="VARCHAR" property="dstrctName" />
    <result column="OPRT_ORG_CODE" jdbcType="VARCHAR" property="oprtOrgCode" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="RP_INTRVL_CODE" jdbcType="VARCHAR" property="rpIntrvlCode" />
    <result column="TECH_GRADE_NAME" jdbcType="VARCHAR" property="techGradeName" />
    <result column="PAVEMENT_TYPE_NAME" jdbcType="VARCHAR" property="pavementTypeName" />
    <result column="PAVEMENTNM" jdbcType="VARCHAR" property="pavementnm" />
    <result column="PAVEMENTTHK" jdbcType="DECIMAL" property="pavementthk" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="ROAD_AGE" jdbcType="DECIMAL" property="roadAge" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TC_DETAIL_ID, LM_PRJ_ID, LM_YEAR, STRUCT_INTRVL_UID, PRJ_ID, LINE_ID, RP_INTRVL_ID, 
    START_STAKE, END_STAKE, "LENGTH", "YEAR", LANE, LANEXS, TECH_GRADE, PAVEMENT_TYPE, 
    PAVEMENT_STRUCT_ID, PAVEMENTCODE, EFFECT_WIDTH, LANENUM, CAL_WIDTH, AADT, JTZZ01, 
    JTZZ02, PN, PQI, PCI, RQI, RDI, SRI, PSSI, PQI2, PCI2, RQI2, RDI2, SRI2, PSSI2, PQINEW, 
    PCINEW, RQINEW, RDINEW, SRINEW, PSSINEW, FACODE, FANAME, FATYPE, PRICES, LINE_NAME, 
    WAYS_DSTRCT_CODE, DSTRCT_NAME, OPRT_ORG_CODE, ORG_NAME, RP_INTRVL_CODE, TECH_GRADE_NAME, 
    PAVEMENT_TYPE_NAME, PAVEMENTNM, PAVEMENTTHK, LINE_DIRECT, ROAD_AGE
  </sql>
</mapper>