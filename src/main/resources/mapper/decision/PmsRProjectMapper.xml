<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsRProjectMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsRProject">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_R_PROJECT-->
    <id column="LM_PRJ_ID" jdbcType="VARCHAR" property="lmPrjId" />
    <result column="LM_PRJ_FID" jdbcType="VARCHAR" property="lmPrjFid" />
    <result column="LM_MX_ID" jdbcType="VARCHAR" property="lmMxId" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TNUM" jdbcType="DECIMAL" property="tnum" />
    <result column="ACTIVENUM" jdbcType="DECIMAL" property="activenum" />
    <result column="STATUS" jdbcType="VARCHAR" property="status" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="THIRD_COMPANY" jdbcType="VARCHAR" property="thirdCompany" />
    <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LM_PRJ_ID, LM_PRJ_FID, LM_MX_ID, USERID, "NAME", TNUM, ACTIVENUM, "STATUS", REMARK, 
    CREATE_TIME, SECOND_COMPANY, THIRD_COMPANY, ROUTE_NAME
  </sql>
</mapper>