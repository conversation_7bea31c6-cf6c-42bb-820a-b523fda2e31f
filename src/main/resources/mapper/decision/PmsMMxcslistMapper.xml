<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.PmsMMxcslistMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.PmsMMxcslist">
    <!--@mbg.generated-->
    <!--@Table PMSDB.PMS_M_MXCSLIST-->
    <id column="LM_MX_ID" jdbcType="VARCHAR" property="lmMxId" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="LM_MX_ID_MR" jdbcType="VARCHAR" property="lmMxIdMr" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="XH" jdbcType="DECIMAL" property="xh" />
    <result column="XZ" jdbcType="DECIMAL" property="xz" />
    <result column="UPDATE_TIME" jdbcType="VARCHAR" property="updateTime" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    LM_MX_ID, USERID, LM_MX_ID_MR, "NAME", REMARK, XH, XZ, UPDATE_TIME, CREATE_TIME
  </sql>
</mapper>