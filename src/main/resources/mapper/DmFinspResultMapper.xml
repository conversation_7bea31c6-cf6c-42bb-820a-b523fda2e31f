<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.oftenCheck.mapper.DmFinspResultMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.oftenCheck.entity.DmFinspResult">
            <id property="finspResId" column="FINSP_RES_ID" jdbcType="VARCHAR"/>
            <result property="finspId" column="FINSP_ID" jdbcType="VARCHAR"/>
            <result property="finspItemId" column="FINSP_ITEM_ID" jdbcType="VARCHAR"/>
            <result property="issueDesc" column="ISSUE_DESC" jdbcType="VARCHAR"/>
            <result property="inspResult" column="INSP_RESULT" jdbcType="VARCHAR"/>
            <result property="mntnAdvice" column="MNTN_ADVICE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FINSP_RES_ID,FINSP_ID,FINSP_ITEM_ID,
        ISSUE_DESC,INSP_RESULT,MNTN_ADVICE,
        REMARK
    </sql>
</mapper>
