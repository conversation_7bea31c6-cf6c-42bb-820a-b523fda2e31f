<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBrdgTechForecastPrjMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBrdgTechForecastPrj">
    <!--@mbg.generated-->
    <!--@Table PMSDB.T_BRDG_TECH_FORECAST_PRJ-->
    <id column="PRJ_ID" jdbcType="VARCHAR" property="prjId" />
    <result column="PRJ_NAME" jdbcType="VARCHAR" property="prjName" />
    <result column="MODEL_ID" jdbcType="VARCHAR" property="modelId" />
    <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
    <result column="BRDG_TYPE_ID" jdbcType="VARCHAR" property="brdgTypeId" />
    <result column="CHECK_ROUND" jdbcType="DECIMAL" property="checkRound" />
    <result column="REDUCE_STAGE" jdbcType="DECIMAL" property="reduceStage" />
    <result column="YEARS" jdbcType="DECIMAL" property="years" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="CREATE_TIME" jdbcType="VARCHAR" property="createTime" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="CAT" jdbcType="VARCHAR" property="cat" />
    <result column="PRJ_YEAR" jdbcType="DECIMAL" property="prjYear" />
    <result column="SEC_ORG" jdbcType="VARCHAR" property="secOrg" />
    <result column="JCXMID" jdbcType="VARCHAR" property="jcxmid" />
    <result column="HAS_REPORT" jdbcType="DECIMAL" property="hasReport" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PRJ_ID, PRJ_NAME, MODEL_ID, ORG_ID, BRDG_TYPE_ID, CHECK_ROUND, REDUCE_STAGE, "YEARS", 
    REMARK, USERID, CREATE_TIME, "TYPE", CAT, PRJ_YEAR, SEC_ORG, JCXMID, HAS_REPORT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from PMSDB.T_BRDG_TECH_FORECAST_PRJ
    where PRJ_ID = #{prjId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from PMSDB.T_BRDG_TECH_FORECAST_PRJ
    where PRJ_ID = #{prjId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBrdgTechForecastPrj">
    <!--@mbg.generated-->
    insert into PMSDB.T_BRDG_TECH_FORECAST_PRJ (PRJ_ID, PRJ_NAME, MODEL_ID, 
      ORG_ID, BRDG_TYPE_ID, CHECK_ROUND, 
      REDUCE_STAGE, "YEARS", REMARK, 
      USERID, CREATE_TIME, "TYPE", 
      CAT, PRJ_YEAR, SEC_ORG, 
      JCXMID, HAS_REPORT)
    values (#{prjId,jdbcType=VARCHAR}, #{prjName,jdbcType=VARCHAR}, #{modelId,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=VARCHAR}, #{brdgTypeId,jdbcType=VARCHAR}, #{checkRound,jdbcType=DECIMAL}, 
      #{reduceStage,jdbcType=DECIMAL}, #{years,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, 
      #{userid,jdbcType=VARCHAR}, #{createTime,jdbcType=VARCHAR}, #{type,jdbcType=DECIMAL}, 
      #{cat,jdbcType=VARCHAR}, #{prjYear,jdbcType=DECIMAL}, #{secOrg,jdbcType=VARCHAR}, 
      #{jcxmid,jdbcType=VARCHAR}, #{hasReport,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBrdgTechForecastPrj">
    <!--@mbg.generated-->
    insert into PMSDB.T_BRDG_TECH_FORECAST_PRJ
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="prjId != null">
        PRJ_ID,
      </if>
      <if test="prjName != null">
        PRJ_NAME,
      </if>
      <if test="modelId != null">
        MODEL_ID,
      </if>
      <if test="orgId != null">
        ORG_ID,
      </if>
      <if test="brdgTypeId != null">
        BRDG_TYPE_ID,
      </if>
      <if test="checkRound != null">
        CHECK_ROUND,
      </if>
      <if test="reduceStage != null">
        REDUCE_STAGE,
      </if>
      <if test="years != null">
        "YEARS",
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="userid != null">
        USERID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="type != null">
        "TYPE",
      </if>
      <if test="cat != null">
        CAT,
      </if>
      <if test="prjYear != null">
        PRJ_YEAR,
      </if>
      <if test="secOrg != null">
        SEC_ORG,
      </if>
      <if test="jcxmid != null">
        JCXMID,
      </if>
      <if test="hasReport != null">
        HAS_REPORT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="prjId != null">
        #{prjId,jdbcType=VARCHAR},
      </if>
      <if test="prjName != null">
        #{prjName,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="brdgTypeId != null">
        #{brdgTypeId,jdbcType=VARCHAR},
      </if>
      <if test="checkRound != null">
        #{checkRound,jdbcType=DECIMAL},
      </if>
      <if test="reduceStage != null">
        #{reduceStage,jdbcType=DECIMAL},
      </if>
      <if test="years != null">
        #{years,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=DECIMAL},
      </if>
      <if test="cat != null">
        #{cat,jdbcType=VARCHAR},
      </if>
      <if test="prjYear != null">
        #{prjYear,jdbcType=DECIMAL},
      </if>
      <if test="secOrg != null">
        #{secOrg,jdbcType=VARCHAR},
      </if>
      <if test="jcxmid != null">
        #{jcxmid,jdbcType=VARCHAR},
      </if>
      <if test="hasReport != null">
        #{hasReport,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBrdgTechForecastPrj">
    <!--@mbg.generated-->
    update PMSDB.T_BRDG_TECH_FORECAST_PRJ
    <set>
      <if test="prjName != null">
        PRJ_NAME = #{prjName,jdbcType=VARCHAR},
      </if>
      <if test="modelId != null">
        MODEL_ID = #{modelId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        ORG_ID = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="brdgTypeId != null">
        BRDG_TYPE_ID = #{brdgTypeId,jdbcType=VARCHAR},
      </if>
      <if test="checkRound != null">
        CHECK_ROUND = #{checkRound,jdbcType=DECIMAL},
      </if>
      <if test="reduceStage != null">
        REDUCE_STAGE = #{reduceStage,jdbcType=DECIMAL},
      </if>
      <if test="years != null">
        "YEARS" = #{years,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        USERID = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        "TYPE" = #{type,jdbcType=DECIMAL},
      </if>
      <if test="cat != null">
        CAT = #{cat,jdbcType=VARCHAR},
      </if>
      <if test="prjYear != null">
        PRJ_YEAR = #{prjYear,jdbcType=DECIMAL},
      </if>
      <if test="secOrg != null">
        SEC_ORG = #{secOrg,jdbcType=VARCHAR},
      </if>
      <if test="jcxmid != null">
        JCXMID = #{jcxmid,jdbcType=VARCHAR},
      </if>
      <if test="hasReport != null">
        HAS_REPORT = #{hasReport,jdbcType=DECIMAL},
      </if>
    </set>
    where PRJ_ID = #{prjId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBrdgTechForecastPrj">
    <!--@mbg.generated-->
    update PMSDB.T_BRDG_TECH_FORECAST_PRJ
    set PRJ_NAME = #{prjName,jdbcType=VARCHAR},
      MODEL_ID = #{modelId,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=VARCHAR},
      BRDG_TYPE_ID = #{brdgTypeId,jdbcType=VARCHAR},
      CHECK_ROUND = #{checkRound,jdbcType=DECIMAL},
      REDUCE_STAGE = #{reduceStage,jdbcType=DECIMAL},
      "YEARS" = #{years,jdbcType=DECIMAL},
      REMARK = #{remark,jdbcType=VARCHAR},
      USERID = #{userid,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=VARCHAR},
      "TYPE" = #{type,jdbcType=DECIMAL},
      CAT = #{cat,jdbcType=VARCHAR},
      PRJ_YEAR = #{prjYear,jdbcType=DECIMAL},
      SEC_ORG = #{secOrg,jdbcType=VARCHAR},
      JCXMID = #{jcxmid,jdbcType=VARCHAR},
      HAS_REPORT = #{hasReport,jdbcType=DECIMAL}
    where PRJ_ID = #{prjId,jdbcType=VARCHAR}
  </update>

  <select id="getBrdgSum" resultType="int">
    select count(1) from BCTCMSDB.T_BRDG_BRDGRECOG b
    where
      exists(select 1 from GDGS.BASE_ROUTE_LOGIC r where r.ROUTE_CODE=b.ROUTE_CODE
                                                     and r.OPRT_ORG_CODE=(select t.ORG_ID from PMSDB.T_BRDG_TECH_FORECAST_PRJ t where t.PRJ_ID=#{prjId})
                                                     and r.IS_ENABLE=1 ) and
      exists(select 1 from T_BRDG_RECOGMANAGE c where c.MAIN_BRDGRECOG_ID=b.BRDGRECOG_ID
                                                  and c.VALID_FLAG=1)
      and b.VALID_FLAG=1
    </select>

  <select id="listBridgeMoneyList" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.BrdgMoneyVo">
    select b.customized, b.money_2025 as money, b.BRDGRECOG_ID as BRDG_ID,b.ROAD_NUM as LINE_CODE,b.LOGIC_CNTR_STAKE as CNTR_STAKE,decode(b.BRDG_RATING,1,'1',2,'2',3,3,'未评定') as grade,b.BRDG_NAME,th.BRDG_LEN as LEN,(select decode(count(1),0,'否','是') from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=#{prjId} and t.STRUCT_ID=b.BRDGRECOG_ID and t.CAT='QL') as HAS_SUGGEST,
    (select t.maintain_type from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=#{prjId} and t.STRUCT_ID=b.BRDGRECOG_ID and t.CAT='QL' and rownum=1) as MAINTAIN_TYPE,
    (select count(1) from BCTCMSDB.T_THREE_COMPATTR t where t.BRDG_ID=b.BRDGRECOG_ID) as THREE_PART,decode(b.MAINTAIN_GRADE,1,'一级','二级') as MAINTAIN_GRADE,
    (select t.PRJYEAR from BCTCMSDB.EVAL_BRIDGE_NEW_F f
    inner join PTCMSDB.TCC_INSP_PRJ t on f.PRJ_ID=t.PRJ_ID
    where f.BRDGRECOG_ID=b.BRDGRECOG_ID) as PRJ_YEAR,
    (select WM_CONCAT(distinct p.BRDG_TYPE_NAME) from T_BRDG_TOPTYPE t
    inner join T_BRDG_BRDGTYPE bt
    on bt.BRDGTYPE_ID=t.BRDG_TYPE
    inner join T_BRDG_BRDGTYPE p
    on bt.P_BRDGTYPE_ID=p.BRDGTYPE_ID
    where t.BRDGRECOG_ID=b.BRDGRECOG_ID
    and t.VALID_FLAG=1) as BRDG_TYPE
    from BCTCMSDB.T_BRDG_BRDGRECOG b
    inner join BCTCMSDB.T_BRDG_TECHINDEX th on b.BRDGRECOG_ID = th.BRDGRECOG_ID and th.VALID_FLAG=1
    where
    exists(select 1 from GDGS.BASE_ROUTE_LOGIC r where r.ROUTE_CODE=b.ROUTE_CODE
    and r.OPRT_ORG_CODE=(select t.ORG_ID from PMSDB.T_BRDG_TECH_FORECAST_PRJ t where t.PRJ_ID=#{prjId})
    and r.IS_ENABLE=1 ) and
    exists(select 1 from T_BRDG_RECOGMANAGE c where c.MAIN_BRDGRECOG_ID=b.BRDGRECOG_ID
    and c.VALID_FLAG=1)
    and b.VALID_FLAG=1
    order by b.ROAD_NUM,b.LOGIC_CNTR_STAKE_NUM
  </select>

  <select id="countMoney" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.ItemMoneyVo">
    select * from (select sum(t.AMOUNT*d.PRICE) as MONEY_PREVENT from
    BCTCMSDB.T_BRIDGE_DECISION_DSS_STS t
    inner join BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM d
    on d.MODEL_ID=#{modelId}
    and d.DMAGE_ID=t.DIC_ID
    inner join PMSDB.PMS_MAINTENANCE_MEASURES p on d.MEASURE_ID=p.ID
    where t.BRIDGE_ID=#{brdgId} and p.MEASURE='预防养护')
    join
    (select sum(t.AMOUNT*d.PRICE)  as MONEY_REPAIR from
    BCTCMSDB.T_BRIDGE_DECISION_DSS_STS t
    inner join BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM d
    on d.MODEL_ID=#{modelId}
    and d.DMAGE_ID=t.DIC_ID
    inner join PMSDB.PMS_MAINTENANCE_MEASURES p on d.MEASURE_ID=p.ID
    where t.BRIDGE_ID=#{brdgId} and p.MEASURE='修复养护') on 1=1
  </select>

  <select id="queryBridgeWeight" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.BrdgWeight">
    select b.MAINTAIN_GRADE as MAINTAIN_LVL,(select WM_CONCAT(p.BRDG_TYPE_NAME) from BCTCMSDB.T_BRDG_TOPTYPE t inner join BCTCMSDB.T_BRDG_BRDGTYPE d
                                                                                                                                                 on t.BRDG_TYPE=d.BRDGTYPE_ID inner join BCTCMSDB.T_BRDG_BRDGTYPE p on d.P_BRDGTYPE_ID=p.BRDGTYPE_ID
                                                                    where t.BRDGRECOG_ID=b.BRDGRECOG_ID and t.VALID_FLAG=1 and d.VALID_FLAG=1 and p.VALID_FLAG=1) as BRDG_TYPE,(
             select p.PRJYEAR from BCTCMSDB.EVAL_BRIDGE_NEW_F t
                                     inner join PTCMSDB.TCC_INSP_PRJ p on t.PRJ_ID=p.PRJ_ID
             where t.BRDGRECOG_ID=b.BRDGRECOG_ID
           ) as INSPECT_YEAR,(
    select t.SCORE from BCTCMSDB.EVAL_BRIDGE_NEW_F t
    where t.BRDGRECOG_ID=b.BRDGRECOG_ID
    ) as  SCORE,b.ADDT from BCTCMSDB.T_BRDG_BRDGRECOG b where b.BRDGRECOG_ID=#{brdgId}
  </select>

  <select id="getTunnelSum" resultType="int">
    select count(1) from MTMSDB.MTMS_TUNNEL_BASIC b
    where
    exists(select 1 from GDGS.BASE_ROUTE_LOGIC r where r.ROUTE_CODE=b.ROUTE_CODE
    and r.OPRT_ORG_CODE=(select t.ORG_ID from PMSDB.T_BRDG_TECH_FORECAST_PRJ t where t.PRJ_ID=#{prjId})
    and r.IS_ENABLE=1 )
    and b.IS_ENABLE=1 and b.IS_DELETED=0
  </select>

  <select id="listTunnelMoneyList" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.BrdgMoneyVo">
    select b.TUNNEL_ID                                                           as BRDG_ID,
    b.LINE_CODE                                                               as LINE_CODE,
    b.LOGIC_CNTR_STAKE                                                       as CNTR_STAKE,
    b.TUNNEL_RANGE                                                            as grade,
    b.TUNNEL_NAME as BRDG_NAME,
    b.TUNNEL_LENGTH                                                              as LEN,
    (select decode(count(1), 0, '否', '是')
    from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t
    where t.PRJ_ID =#{prjId} and t.STRUCT_ID=b.TUNNEL_ID and t.CAT='SD') as HAS_SUGGEST,
    (select t.maintain_type from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=#{prjId} and t.STRUCT_ID=b.TUNNEL_ID and t.CAT='SD' and rownum=1) as MAINTAIN_TYPE
    from MTMSDB.MTMS_TUNNEL_BASIC b
    where exists(select 1
    from GDGS.BASE_ROUTE_LOGIC r
    where r.ROUTE_CODE = b.ROUTE_CODE
    and r.OPRT_ORG_CODE = (select t.ORG_ID from PMSDB.T_BRDG_TECH_FORECAST_PRJ t where t.PRJ_ID =#{prjId})
    and r.IS_ENABLE = 1)
    and b.IS_ENABLE = 1 and b.IS_DELETED = 0
    order by b.LINE_CODE, b.LOGIC_CNTR_STAKE
  </select>

  <select id="queryTunnelWeight" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.TunnelWeight">
    select t.TUNNEL_CLASSIFIED as TUNNEL_TYPE,t.TUNNEL_MAINTAIN_GRADE as MAINTAIN_LVL,(select decode(x.RANGE_SCORE,null,95,x.RANGE_SCORE) from MTMSDB.MTMS_SFEVAL x where x.TUNNEL_ID=t.TUNNEL_ID and ROWNUM=1) as SCORE,
           (select count(1) from MTMSDB.mtms_dssinfo_project d where d.STRUCT_ID=t.TUNNEL_ID
                                                                 and d.tunnel_mouth='70C46495CA0E01E0E05380012EC801E0'
           ) as CQ,
           (select count(1) from MTMSDB.mtms_dssinfo_project d where d.STRUCT_ID=t.TUNNEL_ID
                                                                 and d.STRUCT_PART_ID in('70C46495CA0C01E0E05380012EC801E0','70C46495CA0D01E0E05380012EC801E0','219f165d911c0b9fea33b518f84d1c48')) as DK,t.addt
    from MTMSDB.MTMS_TUNNEL_BASIC t
    where t.TUNNEL_ID=#{tunnelId}
  </select>

  <select id="countTunnelMoney" resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.ItemMoneyVo">
    select * from (select sum(t.AMOUNT*d.PRICE) as MONEY_PREVENT from
    BCTCMSDB.T_TUNNEL_DECISION_DSS_STS t
    inner join BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM d
    on d.MODEL_ID=#{modelId}
    and d.DMAGE_ID=t.DIC_ID
    inner join PMSDB.PMS_MAINTENANCE_MEASURES p on d.MEASURE_ID=p.ID
    where t.TUNNEL_ID=#{brdgId} and p.MEASURE='预防养护')
    join
    (select sum(t.AMOUNT*d.PRICE)  as MONEY_REPAIR from
    BCTCMSDB.T_TUNNEL_DECISION_DSS_STS t
    inner join BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM d
    on d.MODEL_ID=#{modelId}
    and d.DMAGE_ID=t.DIC_ID
    inner join PMSDB.PMS_MAINTENANCE_MEASURES p on d.MEASURE_ID=p.ID
    where t.TUNNEL_ID=#{brdgId} and p.MEASURE='修复养护') on 1=1
  </select>

  <select id="tBrdgTechForecastPrjMapper"
          resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.MaintainSummaryVo">
    select prj.PRJ_ID,f.ORG_NAME,b.ROUTE_CODE,b.LINE_CODE,l.LINE_SNAME as LINE_NAME,case when b.END_STAKE&lt;b.START_STAKE then 'K'||b.END_STAKE||'-K'||b.START_STAKE else 'K'||b.START_STAKE||'-K'||b.END_STAKE end  as STAKE,p.ORG_NAME as SER_ORG_NAME,
    (select count(1) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=prj.PRJ_ID and t.CAT='QL' and t.MAINTAIN_TYPE=#{prjYear}
    and exists(select 1 from BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM x inner join PMSDB.PMS_MAINTENANCE_MEASURES me
    on x.MEASURE_ID=me.ID and me.MEASURE='预防养护'
    where x.MODEL_ID=t.MAINTAIN_MODEL_ID)
    and t.STRUCT_ID in (select br.BRDGRECOG_ID from BCTCMSDB.T_BRDG_BRDGRECOG br where br.ROUTE_CODE=b.ROUTE_CODE)
    ) as PREVENT_NO,
    (select count(1) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=prj.PRJ_ID and t.CAT='QL' and t.MAINTAIN_TYPE=#{prjYear}
    and exists(select 1 from BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM x inner join PMSDB.PMS_MAINTENANCE_MEASURES me
    on x.MEASURE_ID=me.ID and me.MEASURE='修复养护'
    where x.MODEL_ID=t.MAINTAIN_MODEL_ID)
    and t.STRUCT_ID in (select br.BRDGRECOG_ID from BCTCMSDB.T_BRDG_BRDGRECOG br where br.ROUTE_CODE=b.ROUTE_CODE)
    ) as REPAIR_NO,(select sum(bt.BRDG_LEN) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t inner join BCTCMSDB.T_BRDG_TECHINDEX bt on t.STRUCT_ID=bt.BRDGRECOG_ID where t.PRJ_ID=prj.PRJ_ID and t.CAT='QL' and t.MAINTAIN_TYPE=#{prjYear}
    and exists(select 1 from BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM x inner join PMSDB.PMS_MAINTENANCE_MEASURES me
    on x.MEASURE_ID=me.ID and me.MEASURE='预防养护'
    where x.MODEL_ID=t.MAINTAIN_MODEL_ID)
    and t.STRUCT_ID in (select br.BRDGRECOG_ID from BCTCMSDB.T_BRDG_BRDGRECOG br where br.ROUTE_CODE=b.ROUTE_CODE))as PREVENT_LEN,
    (select sum(bt.BRDG_LEN) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t inner join BCTCMSDB.T_BRDG_TECHINDEX bt on t.STRUCT_ID=bt.BRDGRECOG_ID where t.PRJ_ID=prj.PRJ_ID and t.CAT='QL' and t.MAINTAIN_TYPE=#{prjYear}
    and exists(select 1 from BCTCMSDB.T_BRIDGE_MAINTAIN_MODEL_PARAM x inner join PMSDB.PMS_MAINTENANCE_MEASURES me
    on x.MEASURE_ID=me.ID and me.MEASURE='修复养护'
    where x.MODEL_ID=t.MAINTAIN_MODEL_ID)  and t.STRUCT_ID in (select br.BRDGRECOG_ID from BCTCMSDB.T_BRDG_BRDGRECOG br where br.ROUTE_CODE=b.ROUTE_CODE))  as REPAIR_LEN
    from GDGS.BASE_ROUTE_LOGIC b
    inner join GDGS.FW_RIGHT_ORG f on b.OPRT_ORG_CODE=f.ID
    inner join GDGS.BASE_LINE l on l.LINE_ID=b.LINE_ID
    inner join GDGS.FW_RIGHT_ORG p on p.ID=f.PARENT_ID
    inner join PMSDB.T_BRDG_TECH_FORECAST_PRJ prj on prj.ORG_ID=f.ID
    where  b.ROUTE_CODE in
    (select distinct br.ROUTE_CODE from PMSDB.T_BRDG_TECH_FORECAST_PRJ f
    inner join BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t on t.PRJ_ID=f.PRJ_ID and t.CAT='QL' and t.MAINTAIN_TYPE=#{prjYear}
    inner join BCTCMSDB.T_BRDG_BRDGRECOG br on t.STRUCT_ID=br.BRDGRECOG_ID
    where  f.PRJ_YEAR=#{curentYear} and f.CAT='QL' and f.TYPE=2)
    and prj.PRJ_YEAR=#{curentYear} and prj.CAT='QL' and prj.TYPE=2
    order by f.id
  </select>

  <select id="listTunnelMaintainSummary"
          resultType="com.hualu.highwaymaintenance.module.bridgeDecision.vo.MaintainSummaryVo">
    select prj.PRJ_ID,b.ROUTE_CODE,f.ORG_NAME,b.LINE_CODE,l.LINE_SNAME as LINE_NAME,case when b.END_STAKE&lt;b.START_STAKE then 'K'||b.END_STAKE||'-K'||b.START_STAKE else 'K'||b.START_STAKE||'-K'||b.END_STAKE end  as STAKE,p.ORG_NAME as SER_ORG_NAME,
    (select count(1) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=prj.PRJ_ID and t.CAT='SD'  and t.MAINTAIN_TYPE=#{prjYear}
    and t.MAINTAIN_TYPE='预防养护'
    and t.STRUCT_ID in (select br.TUNNEL_ID from MTMSDB.MTMS_TUNNEL_BASIC br where br.ROUTE_CODE=b.ROUTE_CODE)
    ) as PREVENT_NO,
    (select count(1) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t where t.PRJ_ID=prj.PRJ_ID and t.CAT='SD'  and t.MAINTAIN_TYPE=#{prjYear}
    and t.MAINTAIN_TYPE='修复养护'
    and t.STRUCT_ID in (select br.TUNNEL_ID from MTMSDB.MTMS_TUNNEL_BASIC br where br.ROUTE_CODE=b.ROUTE_CODE)
    ) as REPAIR_NO,(select sum(bt.TUNNEL_LENGTH) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t inner join MTMSDB.MTMS_TUNNEL_BASIC bt on t.STRUCT_ID=bt.TUNNEL_ID where t.PRJ_ID=prj.PRJ_ID and t.CAT='SD'  and t.MAINTAIN_TYPE=#{prjYear}
    and t.MAINTAIN_TYPE='预防养护'
    and t.STRUCT_ID in (select br.TUNNEL_ID from MTMSDB.MTMS_TUNNEL_BASIC br where br.ROUTE_CODE=b.ROUTE_CODE))as PREVENT_LEN,
    (select sum(bt.TUNNEL_LENGTH) from BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t inner join MTMSDB.MTMS_TUNNEL_BASIC bt on t.STRUCT_ID=bt.TUNNEL_ID where t.PRJ_ID=prj.PRJ_ID and t.CAT='SD'  and t.MAINTAIN_TYPE=#{prjYear}
    and t.MAINTAIN_TYPE='修复养护'  and t.STRUCT_ID in (select br.TUNNEL_ID from MTMSDB.MTMS_TUNNEL_BASIC br where br.ROUTE_CODE=b.ROUTE_CODE))  as REPAIR_LEN
    from GDGS.BASE_ROUTE_LOGIC b
    inner join GDGS.FW_RIGHT_ORG f on b.OPRT_ORG_CODE=f.ID
    inner join GDGS.BASE_LINE l on l.LINE_ID=b.LINE_ID
    inner join GDGS.FW_RIGHT_ORG p on p.ID=f.PARENT_ID
    inner join PMSDB.T_BRDG_TECH_FORECAST_PRJ prj on prj.ORG_ID=f.ID
    where  b.ROUTE_CODE in
    (select distinct br.ROUTE_CODE from PMSDB.T_BRDG_TECH_FORECAST_PRJ f
    inner join BCTCMSDB.T_STRUCT_MAINTAIN_SUGGEST t on t.PRJ_ID=f.PRJ_ID and t.CAT='SD'  and t.MAINTAIN_TYPE=#{prjYear} and t.MAINTAIN_TYPE in ('修复养护','预防养护')
    inner join MTMSDB.MTMS_TUNNEL_BASIC br on t.STRUCT_ID=br.TUNNEL_ID
    where  f.PRJ_YEAR=#{curentYear} and f.CAT='SD' and f.TYPE=2)
    and prj.PRJ_YEAR=#{curentYear} and prj.CAT='SD' and prj.TYPE=2
    order by f.id
  </select>

  <select id="getBrdgLenthByBrdgId" resultType="double">
    select sum(bt.BRDG_LEN) from BCTCMSDB.T_BRDG_TECHINDEX bt where bt.BRDGRECOG_ID=#{brdgId} and bt.VALID_FLAG=1
  </select>

  <select id="getTunnelLenthByTunnelId" resultType="double">
    select sum(bt.TUNNEL_LENGTH) from MTMSDB.MTMS_TUNNEL_BASIC bt where bt.TUNNEL_ID=#{structId} and bt.IS_DELETED=0 and bt.IS_ENABLE=1
  </select>

  <select id="getBrdgRouteCode" resultType="java.lang.String">
    select bt.ROUTE_CODE from BCTCMSDB.T_BRDG_BRDGRECOG bt where bt.BRDGRECOG_ID=#{structId}
  </select>

  <select id="getTunnelRouteCode" resultType="java.lang.String">
    select b.ROUTE_CODE from MTMSDB.MTMS_TUNNEL_BASIC b where b.TUNNEL_ID=#{structId}
  </select>

  <select id="selectSummary" resultType="java.util.Map">
    select t.ROUTE_CODE,t.YEAR,sum(t.PREVENT_NO+t.REPAIR_NO) as NO from SUGGEST_SUMMARY_TEMP t
    where not exists(select 1 from T_STRUCT_MAINTAIN_SUGGEST s
      inner join BCTCMSDB.T_BRDG_BRDGRECOG b on s.STRUCT_ID=b.BRDGRECOG_ID and s.CAT='QL'
      where b.ROUTE_CODE=t.ROUTE_CODE  and s.MAINTAIN_TYPE=t.YEAR                 )
    group by t.ROUTE_CODE,t.YEAR
  </select>

  <select id="getPrjByRouteCode" resultType="java.lang.String">
    select f.PRJ_ID from PMSDB.T_BRDG_TECH_FORECAST_PRJ f where f.PRJ_YEAR=2023
                                                            and f.CAT='QL' and f.TYPE=2
                                                            and (f.ORG_ID=(select b.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC b where b.ROUTE_CODE=#{routeCode})
        or f.ORG_ID=(select b.PRJ_ORG_CODE from GDGS.BASE_ROUTE_LOGIC b where b.ROUTE_CODE=#{routeCode}))
                                                            and rownum=1
  </select>

  <select id="getBrdgListByRouteCode" resultType="java.lang.String">
    select br.BRDGRECOG_ID from BCTCMSDB.T_BRDG_BRDGRECOG br
    where br.ROUTE_CODE=#{routeCode}
      and br.VALID_FLAG=1
      and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.MAIN_BRDGRECOG_ID=br.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID=m.BRDGRECOG_ID and m.VALID_FLAG=1)
  </select>

  <select id="findSuggestTByRouteCode"
            resultType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgeSuggestT">
    select *
    from MEMSDB.BRIDGE_SUGGEST_T t
    where t.ORG_CODE=(select b.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC b where b.ROUTE_CODE=#{brdgRouteCode})
      and t.LINE_CODE=(select b.LINE_CODE from GDGS.BASE_ROUTE_LOGIC b where b.ROUTE_CODE=#{brdgRouteCode})
      and t.YEAR=#{year} and t.TARGET=2024 and rownum=1
    </select>

  <select id="findSuggestTTunnelByRouteCode"
          resultType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TunnelSuggestT">
    select *
    from MEMSDB.TUNNEL_SUGGEST_T t
    where t.ORG_CODE=(select b.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC b where b.ROUTE_CODE=#{brdgRouteCode})
    and t.LINE_CODE=(select b.LINE_CODE from GDGS.BASE_ROUTE_LOGIC b where b.ROUTE_CODE=#{brdgRouteCode})
    and t.YEAR=#{year} and t.TARGET=2024 and rownum=1
  </select>
</mapper>