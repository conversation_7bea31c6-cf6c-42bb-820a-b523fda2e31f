<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.task.mapper.DmNoticeMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.task.domain.DmNotice">
            <id property="noticeId" column="NOTICE_ID" jdbcType="VARCHAR"/>
            <result property="noticeCode" column="NOTICE_CODE" jdbcType="VARCHAR"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="mntnOrgNm" column="MNTN_ORG_NM" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="noticeDate" column="NOTICE_DATE" jdbcType="TIMESTAMP"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="mtaskId" column="MTASK_ID" jdbcType="VARCHAR"/>
            <result property="mtaskCode" column="MTASK_CODE" jdbcType="VARCHAR"/>
            <result property="sendUser" column="SEND_USER" jdbcType="VARCHAR"/>
            <result property="sendTime" column="SEND_TIME" jdbcType="TIMESTAMP"/>
            <result property="confirmUser" column="CONFIRM_USER" jdbcType="VARCHAR"/>
            <result property="confirmTime" column="CONFIRM_TIME" jdbcType="TIMESTAMP"/>
            <result property="auditOpinion" column="AUDIT_OPINION" jdbcType="VARCHAR"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="processinstid" column="PROCESSINSTID" jdbcType="DECIMAL"/>
            <result property="contrId" column="CONTR_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        NOTICE_ID,NOTICE_CODE,LINE_CODE,
        MNTN_ORG_NM,MNT_ORG_ID,NOTICE_DATE,
        REMARK,MTASK_ID,MTASK_CODE,
        SEND_USER,SEND_TIME,CONFIRM_USER,
        CONFIRM_TIME,AUDIT_OPINION,CREATE_USER_ID,
        CREATE_TIME,UPDATE_USER_ID,UPDATE_TIME,
        STATUS,PROCESSINSTID,CONTR_ID
    </sql>
</mapper>
