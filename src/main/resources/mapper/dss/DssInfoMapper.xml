<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dss.mapper.DssInfoMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dss.entity.DssInfo">
            <id property="dssId" column="DSS_ID" jdbcType="VARCHAR"/>
            <result property="dssCode" column="DSS_CODE" jdbcType="VARCHAR"/>
            <result property="rpIntrvlId" column="RP_INTRVL_ID" jdbcType="VARCHAR"/>
            <result property="stake" column="STAKE" jdbcType="DECIMAL"/>
            <result property="facilityCat" column="FACILITY_CAT" jdbcType="VARCHAR"/>
            <result property="dssType" column="DSS_TYPE" jdbcType="VARCHAR"/>
            <result property="dssDegree" column="DSS_DEGREE" jdbcType="VARCHAR"/>
            <result property="mntnAdvice" column="MNTN_ADVICE" jdbcType="VARCHAR"/>
            <result property="structId" column="STRUCT_ID" jdbcType="VARCHAR"/>
            <result property="structType" column="STRUCT_TYPE" jdbcType="VARCHAR"/>
            <result property="structPartId" column="STRUCT_PART_ID" jdbcType="VARCHAR"/>
            <result property="structCompId" column="STRUCT_COMP_ID" jdbcType="VARCHAR"/>
            <result property="lane" column="LANE" jdbcType="VARCHAR"/>
            <result property="dssPosition" column="DSS_POSITION" jdbcType="VARCHAR"/>
            <result property="dssCause" column="DSS_CAUSE" jdbcType="VARCHAR"/>
            <result property="dssDesc" column="DSS_DESC" jdbcType="VARCHAR"/>
            <result property="dssL" column="DSS_L" jdbcType="DECIMAL"/>
            <result property="dssLUnit" column="DSS_L_UNIT" jdbcType="VARCHAR"/>
            <result property="dssW" column="DSS_W" jdbcType="DECIMAL"/>
            <result property="dssWUnit" column="DSS_W_UNIT" jdbcType="VARCHAR"/>
            <result property="dssD" column="DSS_D" jdbcType="DECIMAL"/>
            <result property="dssDUnit" column="DSS_D_UNIT" jdbcType="VARCHAR"/>
            <result property="dssN" column="DSS_N" jdbcType="DECIMAL"/>
            <result property="dssNUnit" column="DSS_N_UNIT" jdbcType="VARCHAR"/>
            <result property="dssA" column="DSS_A" jdbcType="DECIMAL"/>
            <result property="dssAUnit" column="DSS_A_UNIT" jdbcType="VARCHAR"/>
            <result property="dssV" column="DSS_V" jdbcType="DECIMAL"/>
            <result property="dssVUnit" column="DSS_V_UNIT" jdbcType="VARCHAR"/>
            <result property="dssP" column="DSS_P" jdbcType="DECIMAL"/>
            <result property="dssG" column="DSS_G" jdbcType="DECIMAL"/>
            <result property="dssImpFlag" column="DSS_IMP_FLAG" jdbcType="DECIMAL"/>
            <result property="dssQuality" column="DSS_QUALITY" jdbcType="DECIMAL"/>
            <result property="hisDssId" column="HIS_DSS_ID" jdbcType="VARCHAR"/>
            <result property="dssSource" column="DSS_SOURCE" jdbcType="DECIMAL"/>
            <result property="relTaskCode" column="REL_TASK_CODE" jdbcType="VARCHAR"/>
            <result property="foundDate" column="FOUND_DATE" jdbcType="TIMESTAMP"/>
            <result property="repairStatus" column="REPAIR_STATUS" jdbcType="DECIMAL"/>
            <result property="repairDate" column="REPAIR_DATE" jdbcType="TIMESTAMP"/>
            <result property="dealStatus" column="DEAL_STATUS" jdbcType="DECIMAL"/>
            <result property="dealMeasure" column="DEAL_MEASURE" jdbcType="DECIMAL"/>
            <result property="dealOpinion" column="DEAL_OPINION" jdbcType="VARCHAR"/>
            <result property="dealPrsn" column="DEAL_PRSN" jdbcType="VARCHAR"/>
            <result property="dealDate" column="DEAL_DATE" jdbcType="TIMESTAMP"/>
            <result property="dealBillId" column="DEAL_BILL_ID" jdbcType="VARCHAR"/>
            <result property="x" column="X" jdbcType="DECIMAL"/>
            <result property="y" column="Y" jdbcType="DECIMAL"/>
            <result property="mtaskId" column="MTASK_ID" jdbcType="VARCHAR"/>
            <result property="mtaskAccptId" column="MTASK_ACCPT_ID" jdbcType="VARCHAR"/>
            <result property="mainRoadId" column="MAIN_ROAD_ID" jdbcType="VARCHAR"/>
            <result property="rampId" column="RAMP_ID" jdbcType="VARCHAR"/>
            <result property="isign" column="ISIGN" jdbcType="VARCHAR"/>
            <result property="findDssUserName" column="FIND_DSS_USER_NAME" jdbcType="VARCHAR"/>
            <result property="tunnelMouth" column="TUNNEL_MOUTH" jdbcType="VARCHAR"/>
            <result property="startHigh" column="START_HIGH" jdbcType="DECIMAL"/>
            <result property="endHigh" column="END_HIGH" jdbcType="DECIMAL"/>
            <result property="startStakeNum" column="START_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="endStakeNum" column="END_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="stakeHigh" column="STAKE_HIGH" jdbcType="DECIMAL"/>
            <result property="liningStructure" column="LINING_STRUCTURE" jdbcType="VARCHAR"/>
            <result property="markingLinePosition" column="MARKING_LINE_POSITION" jdbcType="VARCHAR"/>
            <result property="means" column="MEANS" jdbcType="VARCHAR"/>
            <result property="finishStake" column="FINISH_STAKE" jdbcType="DECIMAL"/>
            <result property="laneDirection" column="LANE_DIRECTION" jdbcType="VARCHAR"/>
            <result property="year" column="YEAR" jdbcType="DECIMAL"/>
            <result property="unitMargeId" column="UNIT_MARGE_ID" jdbcType="VARCHAR"/>
            <result property="structDesc" column="STRUCT_DESC" jdbcType="VARCHAR"/>
            <result property="xGd" column="X_GD" jdbcType="DECIMAL"/>
            <result property="yGd" column="Y_GD" jdbcType="DECIMAL"/>
            <result property="dir" column="DIR" jdbcType="DECIMAL"/>
            <result property="routecode" column="ROUTECODE" jdbcType="VARCHAR"/>
            <result property="routeversion" column="ROUTEVERSION" jdbcType="VARCHAR"/>
            <result property="rlStakeNew" column="RL_STAKE_NEW" jdbcType="DECIMAL"/>
            <result property="linedirect" column="LINEDIRECT" jdbcType="VARCHAR"/>
            <result property="rlFinishStake" column="RL_FINISH_STAKE" jdbcType="DECIMAL"/>
            <result property="crackpoor" column="CRACKPOOR" jdbcType="DECIMAL"/>
            <result property="opertOrgId" column="OPERT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
            <result property="datatype" column="DATATYPE" jdbcType="VARCHAR"/>
            <result property="dssTypeCat" column="DSS_TYPE_CAT" jdbcType="DECIMAL"/>
            <result property="closeType" column="CLOSE_TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DSS_ID,DSS_CODE,RP_INTRVL_ID,
        STAKE,FACILITY_CAT,DSS_TYPE,
        DSS_DEGREE,MNTN_ADVICE,STRUCT_ID,
        STRUCT_TYPE,STRUCT_PART_ID,STRUCT_COMP_ID,
        LANE,DSS_POSITION,DSS_CAUSE,
        DSS_DESC,DSS_L,DSS_L_UNIT,
        DSS_W,DSS_W_UNIT,DSS_D,
        DSS_D_UNIT,DSS_N,DSS_N_UNIT,
        DSS_A,DSS_A_UNIT,DSS_V,
        DSS_V_UNIT,DSS_P,DSS_G,
        DSS_IMP_FLAG,DSS_QUALITY,HIS_DSS_ID,
        DSS_SOURCE,REL_TASK_CODE,FOUND_DATE,
        REPAIR_STATUS,REPAIR_DATE,DEAL_STATUS,
        DEAL_MEASURE,DEAL_OPINION,DEAL_PRSN,
        DEAL_DATE,DEAL_BILL_ID,X,
        Y,MTASK_ID,MTASK_ACCPT_ID,
        MAIN_ROAD_ID,RAMP_ID,ISIGN,
        FIND_DSS_USER_NAME,TUNNEL_MOUTH,START_HIGH,
        END_HIGH,START_STAKE_NUM,END_STAKE_NUM,
        STAKE_HIGH,LINING_STRUCTURE,MARKING_LINE_POSITION,
        MEANS,FINISH_STAKE,LANE_DIRECTION,
        YEAR,UNIT_MARGE_ID,STRUCT_DESC,
        X_GD,Y_GD,DIR,
        ROUTECODE,ROUTEVERSION,RL_STAKE_NEW,
        LINEDIRECT,RL_FINISH_STAKE,CRACKPOOR,
        OPERT_ORG_ID,ORG_ID,DATATYPE,
        DSS_TYPE_CAT,CLOSE_TYPE
    </sql>
    <select id="queryCalculateInfo" resultType="com.hualu.highwaymaintenance.module.dss.entity.DssInfo">
        select FACILITY_CAT,REPAIR_STATUS,count(1) total from MEMSDB.DSS_INFO a
        where to_char(found_date,'yyyy') = to_char(sysdate,'yyyy')
        and DSS_SOURCE in (0,1,2,4,5,6,7,8)
        and FACILITY_CAT in ('LM','QL','SD','BP','HD')
        and a.ROUTECODE in
        <foreach collection="routes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        group by FACILITY_CAT, REPAIR_STATUS
    </select>
</mapper>
