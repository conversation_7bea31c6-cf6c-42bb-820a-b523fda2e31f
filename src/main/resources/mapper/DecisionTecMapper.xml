<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.DecisionTecMapper">
    <select id="queryDecisionTecs" resultType="com.hualu.highwaymaintenance.module.decision.domain.DecisionTec">
        select line_direct, traffic, toll_station, max(history) history, start_stake, max(rqi) rqi, max(rdi) rdi, max(sri) sri,
        max(main_pci_front) main_pci_front,max(pci_front) pci_front, max(main_pci_behind) main_pci_behind,max(unit_length) as unit_length,
        max(pci_behind) pci_behind, max(struct_name) struct_name,max(struct) struct,max(MEASURENAME) as MEASURENAME from
        (SELECT td.TOLL_STATION,round(a.START_STAKE/1000,3) as start_stake,a.RQI,a.RDI,a.SRI,
        decode(a.LANE,'A',a.PCI,'B',a.PCI,'') MAIN_PCI_FRONT,
        decode(a.LANE,'C',a.PCI,'D',a.PCI,'E',a.PCI,'F',a.PCI,'') PCI_FRONT,
        case when
        (c.id is not null and (c.MAINTENANCE_PLAN is not null or c.MAINTENANCE_PLAN != 0))
        or (c.id is null and (a.MAINTENANCE_PLAN is not null or a.MAINTENANCE_PLAN != 0))
        then decode(a.LANE,'A',100,'B',100,'')
        else decode(a.LANE,'A',a.PCI,'B',a.PCI,'') end MAIN_PCI_BEHIND,
        case when
        (c.id is not null and (c.MAINTENANCE_PLAN is not null or c.MAINTENANCE_PLAN != 0))
        or (c.id is null and (a.MAINTENANCE_PLAN is not null or a.MAINTENANCE_PLAN != 0))
        then decode(a.LANE,'C',a.PCI,'D',a.PCI,'E',a.PCI,'F',a.PCI,'')
        else decode(a.LANE,'C',a.PCI,'D',a.PCI,'E',a.PCI,'F',a.PCI,'') end PCI_BEHIND,
        td.LINE_DIRECT,round(td.TYPE_TOTAL/(365)) as traffic,a.BRIDGE_TUNNEL as struct_name,
        (case when nvl(c.BRIDGE_TUNNEL,a.BRIDGE_TUNNEL) like '%桥%' then '桥梁' when nvl(c.BRIDGE_TUNNEL,a.BRIDGE_TUNNEL) like '%隧%' then '隧道' else '' end) as struct,
        nvl(c.MAINTENANCE_HIS,a.MAINTENANCE_HIS) as history,PMSDB.GETMULTIMEASUREPLANVALUE(replace(a.MAINTENANCE_PLAN,'、',',')) as MEASURENAME,
        nvl(c.DISEASE_BEHAVIOR,a.DISEASE_BEHAVIOR) as problem,nvl(c.LENGTH,a.LENGTH) as unit_length
        FROM PMSDB.REQUIRE_ANALYSIS_SRC a
        left join PMSDB.REQUIRE_ANALYSIS_HANDLE c on a.UNIT_CODE = c.UNIT_CODE and a.YEAR = c.year
        inner join PTCMSDB.TCC_INSP_PRJ_DESION b on a.PROJECT_ID = b.PRJ_ID
        inner join (select a.* from MEMSDB.TRAFFIC_DECISION a
        inner join
        (select PRJ_ORG_CODE,max(year) year from MEMSDB.TRAFFIC_DECISION
        group by PRJ_ORG_CODE) b on a.PRJ_ORG_CODE = b.PRJ_ORG_CODE and a.YEAR = b.year) td on td.PRJ_ORG_CODE = b.MNG_ORG_ID
        where a.PROJECT_ID = 'F520759DF2173348E05386001DAC0655'
        and (a.START_STAKE - td.START_STAKE_NUM * 1000) * (a.START_STAKE - td.END_STAKE_NUM * 1000) &lt;= 0
        and (a.END_STAKE - td.START_STAKE_NUM * 1000) * (a.END_STAKE - td.END_STAKE_NUM * 1000) &lt;= 0)
        group by line_direct, traffic, toll_station,start_stake
        order by LINE_DIRECT,TOLL_STATION,START_STAKE
    </select>
</mapper>
