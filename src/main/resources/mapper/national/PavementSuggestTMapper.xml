<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.PavementSuggestTMapper">

  <select id="requirementAnalysisToSuggestion" resultType="com.hualu.highwaymaintenance.module.national.entity.PavementSuggestT">
    WITH BASE AS (select d.*,
                    case when d.FATYPE = 'Y' then 'Y' else 'X' end F_TYPE
                  from PMSDB.PMS_R_T4XUQIUFENXI d
                  where d.LM_YEAR = to_char(sysdate, 'yyyy')
                    and exists(
                    select 1 from PMSDB.PMS_R_PROJECT p where p.LM_PRJ_ID = d.LM_PRJ_ID
                      and substr(p.CREATE_TIME, 0, 4) = to_number(to_char(sysdate, 'yyyy')) - 1
                  )
                    and d.<PERSON>TY<PERSON> in ('Y', 'R', 'O')),
      BASE1 AS(
        select
          b.*,
          ROW_NUMBER() over (PARTITION BY LM_PRJ_ID, LM_YEAR ORDER BY LM_YEAR) rn,
          ROUND(sum(case when b.F_TYPE = 'Y' then b.LENGTH else 0 end) over (partition by LM_PRJ_ID,b.LM_YEAR order by b.LM_YEAR) / 1000, 3) YF_MILEAGE,
          ROUND(sum(case when b.F_TYPE = 'X' then b.LENGTH else 0 end) over (partition by LM_PRJ_ID,b.LM_YEAR order by b.LM_YEAR) / 1000, 3)  XF_MILEAGE,
          ROUND(sum(case when b.F_TYPE = 'Y' then b.PRICES else 0 end) over (partition by LM_PRJ_ID,b.LM_YEAR order by b.LM_YEAR) / 10000, 4) YF_CONST,
          ROUND(sum(case when b.F_TYPE = 'X' then b.PRICES else 0 end) over (partition by LM_PRJ_ID,b.LM_YEAR order by b.LM_YEAR) / 10000, 4) XF_CONST
        from BASE b),
      BASE2 AS (
        select b.LM_YEAR, b.YF_MILEAGE, b.YF_CONST, b.XF_CONST, b.XF_MILEAGE,b.LINE_NAME, b.LINE_DIRECT,b.LINE_ID,
          (select l.LINE_CODE from GDGS.BASE_LINE l where l.LINE_ID = b.LINE_ID and l.IS_ENABLE = 1 and l.IS_DELETED = 0 and ROWNUM = 1) s_line_code,
          (select p.THIRD_COMPANY from PMSDB.PMS_R_PROJECT p
           where p.LM_PRJ_ID = b.LM_PRJ_ID) ORG_CODE, LM_PRJ_ID, decode(b.LINE_DIRECT, '1', '上行', '下行') s_line_direct
        from BASE1 b where b.rn = 1),
      BASE3 AS (
        select
          sys_guid() id,
          to_number(nvl((select s.ORDERS from MEMSDB.PAVEMENT_SUGGEST s where s.ORG_CODE = b.ORG_CODE and ROWNUM = 1), 1000)) orders1,
          (select o.ORG_FULLNAME from GDGS.FW_RIGHT_ORG o where o.ORG_CODE = b.ORG_CODE) ORG_NAME,
          B.ORG_CODE,
          B.s_line_code line_code,
          b.LINE_NAME,
          (select l.START_STAKE from GDGS.BASE_ROUTE_LOGIC l where l.OPRT_ORG_CODE = b.ORG_CODE and l.LINE_CODE = b.s_line_code and ROWNUM = 1) start_stake,
          (select l.END_STAKE from GDGS.BASE_ROUTE_LOGIC l where l.OPRT_ORG_CODE = b.ORG_CODE and l.LINE_CODE = b.s_line_code and ROWNUM = 1) end_stake,
          (select s.STAKE from MEMSDB.PAVEMENT_SUGGEST s where s.ORG_CODE = b.ORG_CODE and ROWNUM = 1) STAKE1,
          b.s_line_direct line_direct,
          1 line_no,
          b.YF_MILEAGE PREVENT_QUANTITY,
          b.XF_MILEAGE REPAIR_QUANTITY,
          b.YF_CONST PREVENT_FEE,
          b.XF_CONST REPAIR_FEE,
          b.XF_CONST + b.YF_CONST TOTAL_FEE,
          (select o.PARENT_ID from GDGS.FW_RIGHT_ORG o
           where o.ID = b.ORG_CODE and o.IS_DELETED = 0 and o.IS_ENABLE = 1 and ROWNUM = 1) SECOND_ORG_CODE,
          (select op.ORG_NAME from GDGS.FW_RIGHT_ORG o
                                     inner join GDGS.FW_RIGHT_ORG op on o.PARENT_ID = op.ID
           where o.ID = b.ORG_CODE
             and o.IS_DELETED = 0 and o.IS_ENABLE = 1
             and op.IS_DELETED = 0 and op.IS_ENABLE = 1
             and ROWNUM = 1) SECOND_ORG_NAME,
          '' remark,
          sysdate import_time,
          b.LM_YEAR YEAR
        from BASE2 b)
    SELECT
      row_number() over (order by orders1) orders,
      c.*, nvl(
      c.STAKE1,
      trim('K' || replace(to_char(to_number(least(c.start_stake, c.end_stake)), 'fm999990.000'), '.', '+')) || '-' ||
      trim('K' || replace(to_char(to_number(greatest(c.start_stake, c.end_stake)), 'fm999990.000'), '.', '+'))
      ) stake FROM BASE3 c order by c.orders1
  </select>
</mapper>