<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.ExternalMatMeasureMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.national.entity.ExternalMatMeasure">
    <!--@mbg.generated-->
    <!--@Table MEMSDB.EXTERNAL_MAT_MEASURE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ORG_NAME" jdbcType="VARCHAR" property="orgName" />
    <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName" />
    <result column="LINE_CODE" jdbcType="VARCHAR" property="lineCode" />
    <result column="LINE_DIRECT" jdbcType="VARCHAR" property="lineDirect" />
    <result column="START_STAKE" jdbcType="VARCHAR" property="startStake" />
    <result column="END_STAKE" jdbcType="VARCHAR" property="endStake" />
    <result column="LANE_TYPE" jdbcType="VARCHAR" property="laneType" />
    <result column="MAINTENANCE_MEASURE" jdbcType="VARCHAR" property="maintenanceMeasure" />
    <result column="HANDLE_YEAR" jdbcType="DECIMAL" property="handleYear" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ORG_NAME, ROUTE_NAME, LINE_CODE, LINE_DIRECT, START_STAKE, END_STAKE, LANE_TYPE, 
    MAINTENANCE_MEASURE, HANDLE_YEAR
  </sql>

  <select id="getOrgList" resultType="java.lang.String">
    select d.ORG_NAME from MEMSDB.EXTERNAL_MAT_MEASURE d group by d.ORG_NAME order by ORG_NAME
  </select>
</mapper>