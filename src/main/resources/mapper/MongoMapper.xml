<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.file.mapper.MongoMapper">
  <select id="getMongoCode" resultType="java.lang.String">
    select (case when c.ORG_ID = 'N000053' then '2' else '1' end) || c.DCCODE dccode
    from GDGS.MG_DCCODE_ORG c
    where c.ORG_ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode} and
    ROWNUM = 1) and ROWNUM = 1
  </select>
</mapper>