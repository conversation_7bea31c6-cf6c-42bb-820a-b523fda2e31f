<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgBrdgrecordMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.TBrdgBrdgrecord">
    <!--@mbg.generated-->
    <!--@Table T_BRDG_BRDGRECORD-->
    <id column="BRDGRECORD_ID" jdbcType="VARCHAR" property="brdgrecordId" />
    <result column="BRDGRECOG_ID" jdbcType="VARCHAR" property="brdgrecogId" />
    <result column="DESIGN_PAPER" jdbcType="DECIMAL" property="designPaper" />
    <result column="DESIGN_FILE" jdbcType="DECIMAL" property="designFile" />
    <result column="CNSTRCT_FILE" jdbcType="DECIMAL" property="cnstrctFile" />
    <result column="CNSTRCT_PAPER" jdbcType="DECIMAL" property="cnstrctPaper" />
    <result column="ACCEPT_FILE" jdbcType="DECIMAL" property="acceptFile" />
    <result column="DSTRC_FILE" jdbcType="DECIMAL" property="dstrcFile" />
    <result column="REG_CHCK_REP" jdbcType="DECIMAL" property="regChckRep" />
    <result column="SPECIAL_REPORT" jdbcType="DECIMAL" property="specialReport" />
    <result column="MAINTAIN_DATUM" jdbcType="DECIMAL" property="maintainDatum" />
    <result column="FILE_NO" jdbcType="VARCHAR" property="fileNo" />
    <result column="FILE_PLACE" jdbcType="VARCHAR" property="filePlace" />
    <result column="RECORD_TIME" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_USER_CODE" jdbcType="VARCHAR" property="updateUserCode" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VALID_FLAG" jdbcType="DECIMAL" property="validFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRDGRECORD_ID, BRDGRECOG_ID, DESIGN_PAPER, DESIGN_FILE, CNSTRCT_FILE, CNSTRCT_PAPER, 
    ACCEPT_FILE, DSTRC_FILE, REG_CHCK_REP, SPECIAL_REPORT, MAINTAIN_DATUM, FILE_NO, FILE_PLACE, 
    RECORD_TIME, CREATE_USER_CODE, CREATE_TIME, UPDATE_USER_CODE, UPDATE_TIME, REMARK, 
    VALID_FLAG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BRDG_BRDGRECORD
    where BRDGRECORD_ID = #{brdgrecordId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from T_BRDG_BRDGRECORD
    where BRDGRECORD_ID = #{brdgrecordId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgBrdgrecord">
    <!--@mbg.generated-->
    insert into T_BRDG_BRDGRECORD (BRDGRECORD_ID, BRDGRECOG_ID, DESIGN_PAPER, 
      DESIGN_FILE, CNSTRCT_FILE, CNSTRCT_PAPER, 
      ACCEPT_FILE, DSTRC_FILE, REG_CHCK_REP, 
      SPECIAL_REPORT, MAINTAIN_DATUM, FILE_NO, 
      FILE_PLACE, RECORD_TIME, CREATE_USER_CODE, 
      CREATE_TIME, UPDATE_USER_CODE, UPDATE_TIME, 
      REMARK, VALID_FLAG)
    values (#{brdgrecordId,jdbcType=VARCHAR}, #{brdgrecogId,jdbcType=VARCHAR}, #{designPaper,jdbcType=DECIMAL}, 
      #{designFile,jdbcType=DECIMAL}, #{cnstrctFile,jdbcType=DECIMAL}, #{cnstrctPaper,jdbcType=DECIMAL}, 
      #{acceptFile,jdbcType=DECIMAL}, #{dstrcFile,jdbcType=DECIMAL}, #{regChckRep,jdbcType=DECIMAL}, 
      #{specialReport,jdbcType=DECIMAL}, #{maintainDatum,jdbcType=DECIMAL}, #{fileNo,jdbcType=VARCHAR}, 
      #{filePlace,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP}, #{createUserCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateUserCode,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{validFlag,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgBrdgrecord">
    <!--@mbg.generated-->
    insert into T_BRDG_BRDGRECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="brdgrecordId != null">
        BRDGRECORD_ID,
      </if>
      <if test="brdgrecogId != null">
        BRDGRECOG_ID,
      </if>
      <if test="designPaper != null">
        DESIGN_PAPER,
      </if>
      <if test="designFile != null">
        DESIGN_FILE,
      </if>
      <if test="cnstrctFile != null">
        CNSTRCT_FILE,
      </if>
      <if test="cnstrctPaper != null">
        CNSTRCT_PAPER,
      </if>
      <if test="acceptFile != null">
        ACCEPT_FILE,
      </if>
      <if test="dstrcFile != null">
        DSTRC_FILE,
      </if>
      <if test="regChckRep != null">
        REG_CHCK_REP,
      </if>
      <if test="specialReport != null">
        SPECIAL_REPORT,
      </if>
      <if test="maintainDatum != null">
        MAINTAIN_DATUM,
      </if>
      <if test="fileNo != null">
        FILE_NO,
      </if>
      <if test="filePlace != null">
        FILE_PLACE,
      </if>
      <if test="recordTime != null">
        RECORD_TIME,
      </if>
      <if test="createUserCode != null">
        CREATE_USER_CODE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateUserCode != null">
        UPDATE_USER_CODE,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="validFlag != null">
        VALID_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="brdgrecordId != null">
        #{brdgrecordId,jdbcType=VARCHAR},
      </if>
      <if test="brdgrecogId != null">
        #{brdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="designPaper != null">
        #{designPaper,jdbcType=DECIMAL},
      </if>
      <if test="designFile != null">
        #{designFile,jdbcType=DECIMAL},
      </if>
      <if test="cnstrctFile != null">
        #{cnstrctFile,jdbcType=DECIMAL},
      </if>
      <if test="cnstrctPaper != null">
        #{cnstrctPaper,jdbcType=DECIMAL},
      </if>
      <if test="acceptFile != null">
        #{acceptFile,jdbcType=DECIMAL},
      </if>
      <if test="dstrcFile != null">
        #{dstrcFile,jdbcType=DECIMAL},
      </if>
      <if test="regChckRep != null">
        #{regChckRep,jdbcType=DECIMAL},
      </if>
      <if test="specialReport != null">
        #{specialReport,jdbcType=DECIMAL},
      </if>
      <if test="maintainDatum != null">
        #{maintainDatum,jdbcType=DECIMAL},
      </if>
      <if test="fileNo != null">
        #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="filePlace != null">
        #{filePlace,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserCode != null">
        #{createUserCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserCode != null">
        #{updateUserCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        #{validFlag,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgBrdgrecord">
    <!--@mbg.generated-->
    update T_BRDG_BRDGRECORD
    <set>
      <if test="brdgrecogId != null">
        BRDGRECOG_ID = #{brdgrecogId,jdbcType=VARCHAR},
      </if>
      <if test="designPaper != null">
        DESIGN_PAPER = #{designPaper,jdbcType=DECIMAL},
      </if>
      <if test="designFile != null">
        DESIGN_FILE = #{designFile,jdbcType=DECIMAL},
      </if>
      <if test="cnstrctFile != null">
        CNSTRCT_FILE = #{cnstrctFile,jdbcType=DECIMAL},
      </if>
      <if test="cnstrctPaper != null">
        CNSTRCT_PAPER = #{cnstrctPaper,jdbcType=DECIMAL},
      </if>
      <if test="acceptFile != null">
        ACCEPT_FILE = #{acceptFile,jdbcType=DECIMAL},
      </if>
      <if test="dstrcFile != null">
        DSTRC_FILE = #{dstrcFile,jdbcType=DECIMAL},
      </if>
      <if test="regChckRep != null">
        REG_CHCK_REP = #{regChckRep,jdbcType=DECIMAL},
      </if>
      <if test="specialReport != null">
        SPECIAL_REPORT = #{specialReport,jdbcType=DECIMAL},
      </if>
      <if test="maintainDatum != null">
        MAINTAIN_DATUM = #{maintainDatum,jdbcType=DECIMAL},
      </if>
      <if test="fileNo != null">
        FILE_NO = #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="filePlace != null">
        FILE_PLACE = #{filePlace,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null">
        RECORD_TIME = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserCode != null">
        CREATE_USER_CODE = #{createUserCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserCode != null">
        UPDATE_USER_CODE = #{updateUserCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="validFlag != null">
        VALID_FLAG = #{validFlag,jdbcType=DECIMAL},
      </if>
    </set>
    where BRDGRECORD_ID = #{brdgrecordId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridge.TBrdgBrdgrecord">
    <!--@mbg.generated-->
    update T_BRDG_BRDGRECORD
    set BRDGRECOG_ID = #{brdgrecogId,jdbcType=VARCHAR},
      DESIGN_PAPER = #{designPaper,jdbcType=DECIMAL},
      DESIGN_FILE = #{designFile,jdbcType=DECIMAL},
      CNSTRCT_FILE = #{cnstrctFile,jdbcType=DECIMAL},
      CNSTRCT_PAPER = #{cnstrctPaper,jdbcType=DECIMAL},
      ACCEPT_FILE = #{acceptFile,jdbcType=DECIMAL},
      DSTRC_FILE = #{dstrcFile,jdbcType=DECIMAL},
      REG_CHCK_REP = #{regChckRep,jdbcType=DECIMAL},
      SPECIAL_REPORT = #{specialReport,jdbcType=DECIMAL},
      MAINTAIN_DATUM = #{maintainDatum,jdbcType=DECIMAL},
      FILE_NO = #{fileNo,jdbcType=VARCHAR},
      FILE_PLACE = #{filePlace,jdbcType=VARCHAR},
      RECORD_TIME = #{recordTime,jdbcType=TIMESTAMP},
      CREATE_USER_CODE = #{createUserCode,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_USER_CODE = #{updateUserCode,jdbcType=VARCHAR},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      VALID_FLAG = #{validFlag,jdbcType=DECIMAL}
    where BRDGRECORD_ID = #{brdgrecordId,jdbcType=VARCHAR}
  </update>
</mapper>