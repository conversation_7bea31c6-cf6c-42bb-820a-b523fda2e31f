<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.DecisionTrafficMapper">

    <select id="selectDecisionTraffic" resultType="com.hualu.highwaymaintenance.module.decision.domain.DecisionTrafficBean">
        with ORG AS ( select * from GDGS.FW_RIGHT_ORG o
                      where o.IS_ENABLE = 1 start with o.ID = #{orgCode}
        connect by prior ID = PARENT_ID),
            ROUTE_INFO AS (select distinct pro.PROJECT_NAME,o.ORG_NAME,pro.PRJ_ORG_CODE,o.ORG_CODE from ORG o
            inner join ORG op on o.PARENT_ID = op.ID
            inner join GDGS.PRO_ROUTE pro on pro.OPRT_ORG_CODE = o.ID
            inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.OPRT_ORG_CODE = o.ID
        where pro.IS_UNIT = 1)
        select distinct a.PROJECT_NAME as PRJ_ORG_NAME,a.ORG_CODE, YEAR, TYPE1, TYPE2, TYPE3, TYPE4, TYPE5, TYPE6, TYPE_OTHER, TYPE_TOTAL
            from ROUTE_INFO a left join
            (select PRJ_ORG_NAME,PRJ_ORG_CODE,OPRT_ORG_NAME,OPRT_ORG_CODE,YEAR,
            round(avg(nvl(TYPE1,0))) as TYPE1,round(avg(nvl(TYPE2,0))) as TYPE2,round(avg(nvl(TYPE3,0))) as TYPE3,round(avg(nvl(TYPE4,0))) as TYPE4,
            round(avg(nvl(TYPE5,0))) as TYPE5,round(avg(nvl(TYPE6,0))) as TYPE6,round(avg(nvl(TYPE_OTHER,0))) as TYPE_OTHER,
            round(avg(nvl(TYPE_TOTAL,0))) as TYPE_TOTAL from MEMSDB.TRAFFIC_DECISION
            where YEAR = #{year}
            group by PRJ_ORG_NAME,PRJ_ORG_CODE,OPRT_ORG_NAME,OPRT_ORG_CODE,YEAR) b on a.PRJ_ORG_CODE = b.PRJ_ORG_CODE
            and a.ORG_CODE = b.OPRT_ORG_CODE and a.PROJECT_NAME = b.PRJ_ORG_NAME
        order by a.ORG_CODE
    </select>

</mapper>
