<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintenance.mapper.SpecialProjectMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.maintenance.entity.SpecialProject">
    <!--@mbg.generated-->
    <!--@Table MEMSDB.SPECIAL_PROJECT-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PROJECT_CODE" jdbcType="VARCHAR" property="projectCode" />
    <result column="PROJECT_NAME" jdbcType="VARCHAR" property="projectName" />
    <result column="PROJECT_TYPE" jdbcType="VARCHAR" property="projectType" />
    <result column="FACILITY_CAT" jdbcType="VARCHAR" property="facilityCat" />
    <result column="IS_PLAN" jdbcType="VARCHAR" property="isPlan" />
    <result column="PLAN_YEAR" jdbcType="VARCHAR" property="planYear" />
    <result column="EFFECT_METHOD" jdbcType="VARCHAR" property="effectMethod" />
    <result column="BUDGET_INVEST" jdbcType="DECIMAL" property="budgetInvest" />
    <result column="PROCESS" jdbcType="VARCHAR" property="process" />
    <result column="DESIGN_TIME" jdbcType="TIMESTAMP" property="designTime" />
    <result column="INVITE_BID_TIME" jdbcType="TIMESTAMP" property="inviteBidTime" />
    <result column="START_TIME" jdbcType="TIMESTAMP" property="startTime" />
    <result column="FINISH_TIME" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="ACCEPT_TIME" jdbcType="TIMESTAMP" property="acceptTime" />
    <result column="FINISH_INVEST" jdbcType="DECIMAL" property="finishInvest" />
    <result column="FINISH_INVEST_RATE" jdbcType="DECIMAL" property="finishInvestRate" />
    <result column="IS_FINISH" jdbcType="VARCHAR" property="isFinish" />
    <result column="OPRT_ORG_CODE" jdbcType="VARCHAR" property="oprtOrgCode" />
    <result column="SECOND_ORG_CODE" jdbcType="VARCHAR" property="secondOrgCode" />
    <result column="OPRT_ORG_NAME" jdbcType="VARCHAR" property="oprtOrgName" />
    <result column="SECOND_ORG_NAME" jdbcType="VARCHAR" property="secondOrgName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_USER_CODE" jdbcType="VARCHAR" property="createUserCode" />
    <result column="CREATE_ORG_CODE" jdbcType="VARCHAR" property="createOrgCode" />
    <result column="IS_DELETE" jdbcType="DECIMAL" property="isDelete" />
    <result column="UPDATE_USER_CODE" jdbcType="VARCHAR" property="updateUserCode" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="DEVELOPMENT_UNIT" jdbcType="VARCHAR" property="developmentUnit" />
    <result column="DESIGN_UNIT" jdbcType="VARCHAR" property="designUnit" />
    <result column="CONSTRUCTION_UNIT" jdbcType="VARCHAR" property="constructionUnit" />
    <result column="SUPERVISION_UNIT" jdbcType="VARCHAR" property="supervisionUnit" />
    <result column="OP" jdbcType="DECIMAL" property="op" />
    <result column="MEASURE" jdbcType="VARCHAR" property="measure" />
    <result column="BUILD_TYPE" jdbcType="VARCHAR" property="buildType" />
    <result column="BUILD_REASON" jdbcType="VARCHAR" property="buildReason" />
    <result column="SCOPE_OF_WORK" jdbcType="VARCHAR" property="scopeOfWork" />
    <result column="SOURCE_OF_INCOME" jdbcType="VARCHAR" property="sourceOfIncome" />
    <result column="QUALITY_ASSESSMMENT" jdbcType="VARCHAR" property="qualityAssessmment" />
    <result column="DESIGN_METHOD" jdbcType="VARCHAR" property="designMethod" />
    <result column="DESIGN_PAPERS_AUDIT_UNIT" jdbcType="VARCHAR" property="designPapersAuditUnit" />
    <result column="DESIGN_PAPERS_AUDIT_TIME" jdbcType="VARCHAR" property="designPapersAuditTime" />
    <result column="CONSTRUCTION_DETERMINE_METHOD" jdbcType="VARCHAR" property="constructionDetermineMethod" />
    <result column="HANDOVER_ACCEPTANCE_UNIT" jdbcType="VARCHAR" property="handoverAcceptanceUnit" />
    <result column="HANDOVER_ACCEPTANCE_TIME" jdbcType="VARCHAR" property="handoverAcceptanceTime" />
    <result column="ACCEPTANCE_METHOD" jdbcType="VARCHAR" property="acceptanceMethod" />
    <result column="COMPLETION_UNIT" jdbcType="VARCHAR" property="completionUnit" />
    <result column="COMPLETION_TIME" jdbcType="VARCHAR" property="completionTime" />
    <result column="PRJQUALITY_SUPERVISION_UNIT" jdbcType="VARCHAR" property="prjqualitySupervisionUnit" />
    <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PROJECT_CODE, PROJECT_NAME, PROJECT_TYPE, FACILITY_CAT, IS_PLAN, PLAN_YEAR, EFFECT_METHOD, 
    BUDGET_INVEST, "PROCESS", DESIGN_TIME, INVITE_BID_TIME, START_TIME, FINISH_TIME, 
    ACCEPT_TIME, FINISH_INVEST, FINISH_INVEST_RATE, IS_FINISH, OPRT_ORG_CODE, SECOND_ORG_CODE, 
    OPRT_ORG_NAME, SECOND_ORG_NAME, CREATE_TIME, CREATE_USER_CODE, CREATE_ORG_CODE, IS_DELETE, 
    UPDATE_USER_CODE, UPDATE_TIME, DEVELOPMENT_UNIT, DESIGN_UNIT, CONSTRUCTION_UNIT, 
    SUPERVISION_UNIT, OP, MEASURE, BUILD_TYPE, BUILD_REASON, SCOPE_OF_WORK, SOURCE_OF_INCOME, 
    QUALITY_ASSESSMMENT, DESIGN_METHOD, DESIGN_PAPERS_AUDIT_UNIT, DESIGN_PAPERS_AUDIT_TIME, 
    CONSTRUCTION_DETERMINE_METHOD, HANDOVER_ACCEPTANCE_UNIT, HANDOVER_ACCEPTANCE_TIME, 
    ACCEPTANCE_METHOD, COMPLETION_UNIT, COMPLETION_TIME, PRJQUALITY_SUPERVISION_UNIT, 
    ROUTE_NAME
  </sql>

  <select id="listSpecialPrjByOrgAndYear"
            resultType="com.hualu.highwaymaintenance.module.maintenance.vo.SpecialProjectVo">
    select
      ROWNUM as orders,
      decode(sp.FACILITY_CAT, 'LM', '路面工程', 'QL', '桥梁、涵洞工程', 'SD', '隧道工程', 'BP', '路基工程', 'HD', '桥梁、涵洞工程') as cat_name,
      sp.PROJECT_NAME as prj_name,
      sp.budget_Invest as annual_Budget,
      decode(sp.DESIGN_TIME,null,'否','是') as design_Completed,
      decode(sp.EFFECT_METHOD,'3','招标','非招标') as purchase_Type,
      sp.FINISH_INVEST as contract_Amount,
      decode(sp.START_TIME,null,'否','是') as is_Start_Work,
      decode(sp.FINISH_TIME,null,'否','是') as is_Finish_Work,
      '是' as is_Pay,
      decode(sp.FINISH_INVEST,null,'-',round(sp.FINISH_INVEST,3))||''  as pay_Amount,
      sp.IS_FINISH as is_Settlement,
      sp.FINISH_INVEST_RATE as complete_Rate
    from memsdb.special_project sp
    where sp.PROJECT_TYPE = 3
      and sp.PLAN_YEAR = #{year}
      and sp.OPRT_ORG_CODE
      in (select f.ID
          from GDGS.FW_RIGHT_ORG f
          where f.ID = #{orgId}
             or exists(select 1
                       from GDGS.FW_RIGHT_ORG p
                       where p.PARENT_ID = #{orgId} and p.ID = f.ID and p.IS_ENABLE = 1))
      and sp.IS_DELETE = 0
    order by sp.START_TIME desc
    </select>

  <select id="lisYearPlanProcessByOrgAndYear"
          resultType="com.hualu.highwaymaintenance.module.maintenance.vo.YearPlanProcess">

  </select>

  <select id="getYearPlanIdByOrgAndYear" resultType="java.lang.String">
    select  t.YEAR_PLAN_ID from MEMSDB.YEAR_PLAN t where t.MNTN_ORG_ID=#{orgId} and t.PLAN_YEAR=#{year} and t.SYS_TYPE='TJ' and ROWNUM=1
  </select>

  <select id="loadItemMenu" resultType="com.hualu.highwaymaintenance.module.maintenance.vo.PlanItem">
    select s.*,level from (select p.* from memsdb.PLAN_ITEM p,memsdb.YEAR_PLAN y where y.sys_id=p.sys_id
                                                                     and y.year_plan_id=#{yearPlanId})s start with s.p_plan_item_id is  null
    CONNECT BY PRIOR s.plan_item_id= s.p_plan_item_id and item_type='LB'  order by   TO_NUMBER(REGEXP_REPLACE(s.plan_item_code, '[^0-9]', '')),translate(s.plan_item_code,'零一二三四五六七八九十','012345678910')
  </select>

  <select id="getPrjByPlanId" resultType="com.hualu.highwaymaintenance.module.maintenance.vo.YearPlanPrj">
    select distinct y.YEAR_PLAN_PRJ_ID,y.PRJ_CODE,y.YEAR_PLAN_ID,y.PRJ_NAME,y.P_PLAN_ITEM_ID,y.MONEY/10000 as MONEY,y.ENABLED,
                    y.REMARK,y.TOP_CODE,y.MONEY_A/10000 as MONEY_A,y.DELETED,y.MONEY_B/10000 as MONEY_B,y.ZCX_MONEY/10000 as ZCX_MONEY ,
                    y.PROJECT_MONEY/10000 as PROJECT_MONEY,y.IMPL_TYPE,(select  to_char(wm_concat(f.file_id)) from memsdb.PLAN_PRJ_FILE f where y.YEAR_PLAN_PRJ_ID = f.YEAR_PLAN_PRJ_ID and  f.is_delete = '0') as file_Ids,
                    (SELECT MAX(P.UPDATE_TIME)  FROM  MEMSDB.PRJ_UPDATE_LOGS P WHERE P.PLAN_PRJ_ID=Y.YEAR_PLAN_PRJ_ID) AS LAST_UPDATE
    from  memsdb.YEAR_PLAN_PRJ y
    where y.deleted=0 and  y.YEAR_PLAN_ID=#{yearPlanId}   order by  TO_NUMBER(REGEXP_REPLACE(y.PRJ_CODE, '[^0-9]', ''))
  </select>
</mapper>