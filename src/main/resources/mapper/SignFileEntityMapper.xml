<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.sign.mapper.SignFileEntityMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.sign.entity.SignFileEntity">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="baseFileEntityId" column="BASE_FILE_ENTITY_ID" jdbcType="VARCHAR"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="fileType" column="FILE_TYPE" jdbcType="VARCHAR"/>
            <result property="source" column="SOURCE" jdbcType="VARCHAR"/>
            <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
            <result property="time" column="TIME" jdbcType="TIMESTAMP"/>
            <result property="orderId" column="ORDER_ID" jdbcType="VARCHAR"/>
            <result property="userName" column="USER_NAME" jdbcType="VARCHAR"/>
            <result property="orgCode" column="ORG_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BASE_FILE_ENTITY_ID,FILE_NAME,
        FILE_TYPE,SOURCE,USER_CODE,
        TIME,ORDER_ID,USER_NAME,
        ORG_CODE
    </sql>
    <update id="backSign">
        update MEMSDB.SIGN_FILE_ENTITY set TEMP_PATH = 'back' where ORDER_ID = #{orderId} and SOURCE = #{orderType}
    </update>
    <select id="queryFinishItem" resultType="com.hualu.highwaymaintenance.module.sign.entity.SignFileEntity">
        <if test="orderType == 'XCD'">
            select a.DINSP_ID as order_id,a.DINSP_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.INSP_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID asc) rn
            from MEMSDB.DM_DINSP a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            where a.DINSP_ID = #{orderId}
            <if test="status != null">
                and b.CURRENTSTATE = #{status}
            </if>
        </if>
        <if test="orderType == 'JCJC'">
            select a.FINSP_ID as order_id,a.FINSP_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.INSP_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID asc) rn
            from MEMSDB.DM_FINSP a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            where a.FINSP_ID = #{orderId}
            <if test="status != null">
                and b.CURRENTSTATE = #{status}
            </if>
        </if>
        <if test="orderType == 'TZS'">
            select a.NOTICE_ID as order_id,a.NOTICE_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.NOTICE_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID asc) rn
            from MEMSDB.DM_NOTICE a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            where a.NOTICE_ID = #{orderId}
            <if test="status != null">
                and b.CURRENTSTATE = #{status}
            </if>
        </if>
        <if test="orderType == 'RWD'">
            select a.MTASK_ID as order_id,a.MTASK_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.ASKED_START_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID asc) rn
            from MEMSDB.DM_TASK a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            where a.MTASK_ID = #{orderId}
            <if test="status != null">
                and b.CURRENTSTATE = #{status}
            </if>
        </if>
        <if test="orderType == 'YSD'">
            select a.MTASK_ACCPT_ID as order_id,a.MTASK_ACCPT_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.APPLY_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID asc) rn,
            (select sum(x.ACCEPT_AMOUNT) from MEMSDB.DM_TASK_ACCPT_DETAIL x where a.MTASK_ACCPT_ID = x.MTASK_ACCPT_ID) as remark
            from MEMSDB.DM_TASK_ACCPT a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            where a.MTASK_ACCPT_ID = #{orderId}
            <if test="status != null">
                and b.CURRENTSTATE = #{status}
            </if>
        </if>
    </select>
    <select id="queryNewItem" resultType="com.hualu.highwaymaintenance.module.sign.entity.SignFileEntity">
        <if test="orderType == 'XCD'">
            select a.DINSP_ID as order_id,a.DINSP_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.INSP_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT or a.CREATE_USER_ID = c.ID then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn
            from MEMSDB.DM_DINSP a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on b.PARTICIPANT = c.USER_CODE
            where a.DINSP_ID = #{orderId}
        </if>
        <if test="orderType == 'XCD_NEW'">
            select a.DINSP_ID as order_id,a.DINSP_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.INSP_DATE,'yyyy-MM-dd') as order_time,
            case when c.USER_CODE = b.PARTICIPANT then 1 else 0 end as is_first,
            c.USER_CODE as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn
            from MEMSDB.NM_DINSP a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on a.CREATE_USER_ID = c.ID
            where a.DINSP_ID = #{orderId}
        </if>
        <if test="orderType == 'JCJC'">
            select a.FINSP_ID as order_id,a.FINSP_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.INSP_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT or a.CREATE_USER_ID = c.ID then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn
            from MEMSDB.DM_FINSP a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on b.PARTICIPANT = c.USER_CODE
            where a.FINSP_ID = #{orderId}
        </if>
        <if test="orderType == 'JCJC_NEW'">
            select a.FINSP_ID as order_id,a.FINSP_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.INSP_DATE,'yyyy-MM-dd') as order_time,
            case when c.USER_CODE = b.PARTICIPANT then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn
            from MEMSDB.NM_FINSP a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on a.CREATE_USER_ID = c.ID
            where a.FINSP_ID = #{orderId}
        </if>
        <if test="orderType == 'TZS'">
            select a.NOTICE_ID as order_id,a.NOTICE_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.NOTICE_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT or a.CREATE_USER_ID = c.ID then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn
            from MEMSDB.DM_NOTICE a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on b.PARTICIPANT = c.USER_CODE
            where a.NOTICE_ID = #{orderId}
        </if>
        <if test="orderType == 'RWD'">
            select a.MTASK_ID as order_id,a.MTASK_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.ASKED_START_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT or a.CREATE_USER_ID = c.ID then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn
            from MEMSDB.DM_TASK a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on b.PARTICIPANT = c.USER_CODE
            where a.MTASK_ID = #{orderId}
        </if>
        <if test="orderType == 'YSD'">
            select a.MTASK_ACCPT_ID as order_id,a.MTASK_ACCPT_CODE as order_code,a.MNT_ORG_ID as org_code,
            a.PROCESSINSTID,a.STATUS,to_char(a.APPLY_DATE,'yyyy-MM-dd') as order_time,
            case when a.CREATE_USER_ID = b.PARTICIPANT or a.CREATE_USER_ID = c.ID then 1 else 0 end as is_first,
            b.PARTICIPANT as user_code,b.PARTINAME as user_name,
            b.WORKITEMID,b.ACTIVITYINSTNAME as operation,b.ENDTIME as time,
            row_number() over (order by WORKITEMID desc) rn,
            (select sum(x.ACCEPT_AMOUNT) from MEMSDB.DM_TASK_ACCPT_DETAIL x where a.MTASK_ACCPT_ID = x.MTASK_ACCPT_ID) as remark
            from MEMSDB.DM_TASK_ACCPT a
            inner join bps.WFWORKITEM b  on a.PROCESSINSTID = b.PROCESSINSTID
            inner join gdgs.FW_RIGHT_USER c on b.PARTICIPANT = c.USER_CODE
            where a.MTASK_ACCPT_ID = #{orderId}
        </if>
    </select>
    <select id="queryPage" resultType="com.hualu.highwaymaintenance.module.sign.entity.SignFileEntity">
        with orgtable as (
        select *
        from GDGS.FW_RIGHT_ORG o
        where o.IS_ENABLE = 1
        and o.IS_DELETED = 0
        start with o.ID = #{orgId}
        connect by prior o.ID = o.PARENT_ID
        )
        select type, operation, order_code, id, base_file_entity_id mg_file_id, file_name, file_type, source, user_code, time_str, order_id, org_code,
        (select WM_CONCAT(distinct tt.USER_NAME) from MEMSDB.SIGN_FILE_ENTITY tt where tt.ORDER_ID = k.ORDER_ID group by tt.ORDER_ID) user_name
        from (
        select t.type,t.OPERATION,t.ORDER_CODE, t.ID, t.BASE_FILE_ENTITY_ID, t.FILE_NAME, t.FILE_TYPE,
        decode(t.SOURCE,'XCD_NEW','XCD','JCJC_NEW','JCJC',t.SOURCE) as SOURCE,
        t.USER_CODE, to_char(t.time,'yyyy-MM-dd') as time_str, t.ORDER_ID, t.USER_NAME, o.ORG_NAME as ORG_CODE ,orders,
        ROW_NUMBER() OVER (PARTITION BY t.org_code, t.source, t.order_code ORDER BY orders DESC) AS rn
        from MEMSDB.SIGN_FILE_ENTITY t
        left join MEMSDB.NM_DINSP a1 on t.ORDER_ID = a1.DINSP_ID and a1.DEL_FLAG = 0 and a1.STATUS = 3
        left join MEMSDB.NM_FINSP a2 on t.ORDER_ID = a2.FINSP_ID and a2.DEL_FLAG = 0 and a2.STATUS = 3
        left join MEMSDB.DM_NOTICE a3 on t.ORDER_ID = a3.NOTICE_ID and a3.STATUS = 2
        left join MEMSDB.DM_TASK a4 on t.ORDER_ID = a4.MTASK_ID and a4.STATUS in (2,3,4,5,7)
        left join MEMSDB.DM_TASK_ACCPT a5 on t.ORDER_ID = a5.MTASK_ACCPT_ID and a5.STATUS = 3
        inner join orgtable o on t.ORG_CODE =  o.ID
        where 1=1
        <if test="fileName != null and fileName != ''">
            and   t.FILE_NAME like'%${fileName}%'
        </if>
        <if test="fileDate != null and fileDate != ''">
            and   t.FILE_NAME like'%${fileDate}%'
        </if>
        <if test="nodeVal != null and nodeVal != ''">
            and   t.FILE_NAME like'%${nodeVal}%'
        </if>
        <if test="cat != null and cat != ''">
            and   t.FILE_NAME like'%${cat}%'
        </if>
        ) k where rn = 1
    </select>
</mapper>