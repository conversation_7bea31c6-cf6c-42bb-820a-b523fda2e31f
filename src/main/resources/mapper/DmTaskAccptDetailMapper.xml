<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.accpt.mapper.DmTaskAccptDetailMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.accpt.domain.DmTaskAccptDetail">
            <id property="mtaskAccptDtlId" column="MTASK_ACCPT_DTL_ID" jdbcType="VARCHAR"/>
            <result property="businessType" column="BUSINESS_TYPE" jdbcType="DECIMAL"/>
            <result property="mtaskAccptId" column="MTASK_ACCPT_ID" jdbcType="VARCHAR"/>
            <result property="mtaskDtlId" column="MTASK_DTL_ID" jdbcType="VARCHAR"/>
            <result property="dssId" column="DSS_ID" jdbcType="VARCHAR"/>
            <result property="rpIntrvlId" column="RP_INTRVL_ID" jdbcType="VARCHAR"/>
            <result property="stake" column="STAKE" jdbcType="DECIMAL"/>
            <result property="lane" column="LANE" jdbcType="VARCHAR"/>
            <result property="mpitemId" column="MPITEM_ID" jdbcType="VARCHAR"/>
            <result property="measureUnit" column="MEASURE_UNIT" jdbcType="VARCHAR"/>
            <result property="isLump" column="IS_LUMP" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="applyAmount" column="APPLY_AMOUNT" jdbcType="DECIMAL"/>
            <result property="acceptAmount" column="ACCEPT_AMOUNT" jdbcType="DECIMAL"/>
            <result property="repairDate" column="REPAIR_DATE" jdbcType="TIMESTAMP"/>
            <result property="acceptStatus" column="ACCEPT_STATUS" jdbcType="DECIMAL"/>
            <result property="mpitemNumBak" column="MPITEM_NUM_BAK" jdbcType="DECIMAL"/>
            <result property="mainRoadId" column="MAIN_ROAD_ID" jdbcType="VARCHAR"/>
            <result property="isTaskRecord" column="IS_TASK_RECORD" jdbcType="DECIMAL"/>
            <result property="rampId" column="RAMP_ID" jdbcType="VARCHAR"/>
            <result property="contrId" column="CONTR_ID" jdbcType="VARCHAR"/>
            <result property="mtaskAccptDtlCode" column="MTASK_ACCPT_DTL_CODE" jdbcType="VARCHAR"/>
            <result property="mtaskId" column="MTASK_ID" jdbcType="VARCHAR"/>
            <result property="lineDirect" column="LINE_DIRECT" jdbcType="VARCHAR"/>
            <result property="logicStakeNum" column="LOGIC_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="rlStake" column="RL_STAKE" jdbcType="VARCHAR"/>
            <result property="mpitemNum" column="MPITEM_NUM" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        MTASK_ACCPT_DTL_ID,BUSINESS_TYPE,MTASK_ACCPT_ID,
        MTASK_DTL_ID,DSS_ID,RP_INTRVL_ID,
        STAKE,LANE,MPITEM_ID,
        MEASURE_UNIT,IS_LUMP,REMARK,
        APPLY_AMOUNT,ACCEPT_AMOUNT,REPAIR_DATE,
        ACCEPT_STATUS,MPITEM_NUM_BAK,MAIN_ROAD_ID,
        IS_TASK_RECORD,RAMP_ID,CONTR_ID,
        MTASK_ACCPT_DTL_CODE,MTASK_ID,LINE_DIRECT,
        LOGIC_STAKE_NUM,RL_STAKE,MPITEM_NUM
    </sql>
</mapper>
