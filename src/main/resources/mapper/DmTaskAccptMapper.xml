<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.accpt.mapper.DmTaskAccptMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.accpt.domain.DmTaskAccpt">
            <id property="mtaskAccptId" column="MTASK_ACCPT_ID" jdbcType="VARCHAR"/>
            <result property="mtaskAccptCode" column="MTASK_ACCPT_CODE" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="mntnOrgName" column="MNTN_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="mntnDeptNm" column="MNTN_DEPT_NM" jdbcType="VARCHAR"/>
            <result property="businessType" column="BUSINESS_TYPE" jdbcType="DECIMAL"/>
            <result property="applyUser" column="APPLY_USER" jdbcType="VARCHAR"/>
            <result property="applyDate" column="APPLY_DATE" jdbcType="TIMESTAMP"/>
            <result property="applyComment" column="APPLY_COMMENT" jdbcType="VARCHAR"/>
            <result property="acceptUser" column="ACCEPT_USER" jdbcType="VARCHAR"/>
            <result property="acceptDate" column="ACCEPT_DATE" jdbcType="TIMESTAMP"/>
            <result property="acceptComment" column="ACCEPT_COMMENT" jdbcType="VARCHAR"/>
            <result property="isInTime" column="IS_IN_TIME" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="processinstid" column="PROCESSINSTID" jdbcType="DECIMAL"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="mtaskId" column="MTASK_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        MTASK_ACCPT_ID,MTASK_ACCPT_CODE,MNT_ORG_ID,
        MNTN_ORG_NAME,MNTN_DEPT_NM,BUSINESS_TYPE,
        APPLY_USER,APPLY_DATE,APPLY_COMMENT,
        ACCEPT_USER,ACCEPT_DATE,ACCEPT_COMMENT,
        IS_IN_TIME,REMARK,PROCESSINSTID,
        STATUS,CREATE_USER_ID,CREATE_TIME,
        UPDATE_USER_ID,UPDATE_TIME,MTASK_ID
    </sql>
    <select id="queryLastDmTaskAccpt"
            resultType="com.hualu.highwaymaintenance.module.accpt.domain.DmTaskAccpt">
        select *
        from MEMSDB.DM_TASK_ACCPT where CREATE_TIME = (select max(CREATE_TIME) from MEMSDB.DM_TASK_ACCPT where MNT_ORG_ID = #{orgId})
                                    and MNT_ORG_ID = #{orgId}
    </select>

    <select id="getNextCode" resultType="java.lang.String">
        select max(substr(MTASK_ACCPT_CODE,length(MTASK_ACCPT_CODE)-2))
        from MEMSDB.DM_TASK_ACCPT t where MTASK_ACCPT_CODE like concat(concat('',#{strCode}),'%')
                                      and t.MNT_ORG_ID= #{orgId}
    </select>
</mapper>
