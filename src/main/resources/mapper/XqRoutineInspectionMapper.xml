<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqRoutineInspectionMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqRoutineInspection">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_ROUTINE_INSPECTION-->
    <id column="ROUTINE_INSPECTION_ID" jdbcType="VARCHAR" property="routineInspectionId" />
    <result column="BRIDGE_IDENTITY_CODE" jdbcType="VARCHAR" property="bridgeIdentityCode" />
    <result column="INSPECTION_DATE" jdbcType="VARCHAR" property="inspectionDate" />
    <result column="RESPONSIBLE_PERSON" jdbcType="VARCHAR" property="responsiblePerson" />
    <result column="RECORDER" jdbcType="VARCHAR" property="recorder" />
    <result column="LNG_GCJ02" jdbcType="DECIMAL" property="lngGcj02" />
    <result column="LAT_GCJ02" jdbcType="DECIMAL" property="latGcj02" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ROUTINE_INSPECTION_ID, BRIDGE_IDENTITY_CODE, INSPECTION_DATE, RESPONSIBLE_PERSON, 
    RECORDER, LNG_GCJ02, LAT_GCJ02, SECOND_COMPANY, ORG_CODE, REPORT_ID
  </sql>
</mapper>