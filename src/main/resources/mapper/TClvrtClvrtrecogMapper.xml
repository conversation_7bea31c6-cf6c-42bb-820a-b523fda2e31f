<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dic.mapper.TClvrtClvrtrecogMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dic.domain.TClvrtClvrtrecog">
            <result property="clvrtrecogId" column="CLVRTRECOG_ID" jdbcType="VARCHAR"/>
            <result property="techGrade" column="TECH_GRADE" jdbcType="VARCHAR"/>
            <result property="judgeDate" column="JUDGE_DATE" jdbcType="TIMESTAMP"/>
            <result property="validFlag" column="VALID_FLAG" jdbcType="DECIMAL"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="lineName" column="LINE_NAME" jdbcType="VARCHAR"/>
            <result property="routeLvl" column="ROUTE_LVL" jdbcType="VARCHAR"/>
            <result property="place" column="PLACE" jdbcType="VARCHAR"/>
            <result property="seqNo" column="SEQ_NO" jdbcType="VARCHAR"/>
            <result property="cnstrctStake" column="CNSTRCT_STAKE" jdbcType="VARCHAR"/>
            <result property="dsgnStake" column="DSGN_STAKE" jdbcType="VARCHAR"/>
            <result property="oldHighwayStake" column="OLD_HIGHWAY_STAKE" jdbcType="VARCHAR"/>
            <result property="funcType" column="FUNC_TYPE" jdbcType="VARCHAR"/>
            <result property="longitude" column="LONGITUDE" jdbcType="DECIMAL"/>
            <result property="dsgnLoad" column="DSGN_LOAD" jdbcType="VARCHAR"/>
            <result property="lineHeterotropicQuadrature" column="LINE_HETEROTROPIC_QUADRATURE" jdbcType="VARCHAR"/>
            <result property="latitude" column="LATITUDE" jdbcType="DECIMAL"/>
            <result property="waterInletPosition" column="WATER_INLET_POSITION" jdbcType="VARCHAR"/>
            <result property="stage" column="STAGE" jdbcType="DECIMAL"/>
            <result property="completedYears" column="COMPLETED_YEARS" jdbcType="VARCHAR"/>
            <result property="dsgnOrg" column="DSGN_ORG" jdbcType="VARCHAR"/>
            <result property="cnstrctOrg" column="CNSTRCT_ORG" jdbcType="VARCHAR"/>
            <result property="superOrg" column="SUPER_ORG" jdbcType="VARCHAR"/>
            <result property="cbmsOrg" column="CBMS_ORG" jdbcType="VARCHAR"/>
            <result property="regulatoryOrg" column="REGULATORY_ORG" jdbcType="VARCHAR"/>
            <result property="clvrtStatus" column="CLVRT_STATUS" jdbcType="DECIMAL"/>
            <result property="clvrtName" column="CLVRT_NAME" jdbcType="VARCHAR"/>
            <result property="rpIntrvlId" column="RP_INTRVL_ID" jdbcType="VARCHAR"/>
            <result property="clvrtCode" column="CLVRT_CODE" jdbcType="VARCHAR"/>
            <result property="clvrtLineType" column="CLVRT_LINE_TYPE" jdbcType="VARCHAR"/>
            <result property="cntrStake" column="CNTR_STAKE" jdbcType="VARCHAR"/>
            <result property="cntrStakeNum" column="CNTR_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="cntrSenssionNum" column="CNTR_SENSSION_NUM" jdbcType="DECIMAL"/>
            <result property="oprtOrgCode" column="OPRT_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="createUserCode" column="CREATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserCode" column="UPDATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="dataStatus" column="DATA_STATUS" jdbcType="DECIMAL"/>
            <result property="structForm" column="STRUCT_FORM" jdbcType="VARCHAR"/>
            <result property="checkTime" column="CHECK_TIME" jdbcType="TIMESTAMP"/>
            <result property="lineId" column="LINE_ID" jdbcType="VARCHAR"/>
            <result property="mainRampLineId" column="MAIN_RAMP_LINE_ID" jdbcType="VARCHAR"/>
            <result property="startOffset" column="START_OFFSET" jdbcType="DECIMAL"/>
            <result property="rampStake" column="RAMP_STAKE" jdbcType="VARCHAR"/>
            <result property="angle" column="ANGLE" jdbcType="DECIMAL"/>
            <result property="logicCntrStake" column="LOGIC_CNTR_STAKE" jdbcType="VARCHAR"/>
            <result property="logicCntrStakeNum" column="LOGIC_CNTR_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="jd" column="JD" jdbcType="DECIMAL"/>
            <result property="wd" column="WD" jdbcType="DECIMAL"/>
            <result property="oldClvrtCode" column="OLD_CLVRT_CODE" jdbcType="VARCHAR"/>
            <result property="isp" column="ISP" jdbcType="DECIMAL"/>
            <result property="prjCode" column="PRJ_CODE" jdbcType="VARCHAR"/>
            <result property="prjName" column="PRJ_NAME" jdbcType="VARCHAR"/>
            <result property="routeCodeVersion" column="ROUTE_CODE_VERSION" jdbcType="VARCHAR"/>
            <result property="routeCode" column="ROUTE_CODE" jdbcType="VARCHAR"/>
            <result property="clvrtCodeBak" column="CLVRT_CODE_BAK" jdbcType="VARCHAR"/>
            <result property="brdgRouteCodeBak" column="BRDG_ROUTE_CODE_BAK" jdbcType="VARCHAR"/>
            <result property="delTime" column="DEL_TIME" jdbcType="TIMESTAMP"/>
            <result property="delPerson" column="DEL_PERSON" jdbcType="VARCHAR"/>
            <result property="recoverTime" column="RECOVER_TIME" jdbcType="TIMESTAMP"/>
            <result property="recoverPerson" column="RECOVER_PERSON" jdbcType="VARCHAR"/>
            <result property="mortarrubble" column="MORTARRUBBLE" jdbcType="DECIMAL"/>
            <result property="reinforcementform" column="REINFORCEMENTFORM" jdbcType="VARCHAR"/>
            <result property="reinforcementformextra" column="REINFORCEMENTFORMEXTRA" jdbcType="VARCHAR"/>
            <result property="placeBak" column="PLACE_BAK" jdbcType="VARCHAR"/>
            <result property="hasav" column="HASAV" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CLVRTRECOG_ID,TECH_GRADE,JUDGE_DATE,
        VALID_FLAG,LINE_CODE,LINE_NAME,
        ROUTE_LVL,PLACE,SEQ_NO,
        CNSTRCT_STAKE,DSGN_STAKE,OLD_HIGHWAY_STAKE,
        FUNC_TYPE,LONGITUDE,DSGN_LOAD,
        LINE_HETEROTROPIC_QUADRATURE,LATITUDE,WATER_INLET_POSITION,
        STAGE,COMPLETED_YEARS,DSGN_ORG,
        CNSTRCT_ORG,SUPER_ORG,CBMS_ORG,
        REGULATORY_ORG,CLVRT_STATUS,CLVRT_NAME,
        RP_INTRVL_ID,CLVRT_CODE,CLVRT_LINE_TYPE,
        CNTR_STAKE,CNTR_STAKE_NUM,CNTR_SENSSION_NUM,
        OPRT_ORG_CODE,REMARK,CREATE_USER_CODE,
        CREATE_TIME,UPDATE_USER_CODE,UPDATE_TIME,
        DATA_STATUS,STRUCT_FORM,CHECK_TIME,
        LINE_ID,MAIN_RAMP_LINE_ID,START_OFFSET,
        RAMP_STAKE,ANGLE,LOGIC_CNTR_STAKE,
        LOGIC_CNTR_STAKE_NUM,JD,WD,
        OLD_CLVRT_CODE,ISP,PRJ_CODE,
        PRJ_NAME,ROUTE_CODE_VERSION,ROUTE_CODE,
        CLVRT_CODE_BAK,BRDG_ROUTE_CODE_BAK,DEL_TIME,
        DEL_PERSON,RECOVER_TIME,RECOVER_PERSON,
        MORTARRUBBLE,REINFORCEMENTFORM,REINFORCEMENTFORMEXTRA,
        PLACE_BAK,HASAV
    </sql>
    <select id="queryTclvrtType" resultType="com.hualu.highwaymaintenance.module.oftenCheck.vo.TclvrtType">
        select ct.CLVRT_TYPE_NAME as name,s.CLVRTRECOG_ID as id from bctcmsdb.t_clvrt_consshape s
        left join  bctcmsdb.t_clvrt_clvrttype ct on ct.clvrt_type_code=s.struct_form
        where CLVRTRECOG_ID in
        <foreach collection="ids" index="index" open="(" close=")" item="id" separator=",">
            <if test="(index % 999) == 998"> NULL) OR CLVRTRECOG_ID IN(</if>#{id}
        </foreach>
    </select>
</mapper>
