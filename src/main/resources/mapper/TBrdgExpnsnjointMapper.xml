<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.TBrdgExpnsnjointMapper">

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, SSF_TYPE, SSF_DIRECT, SSF_LOCATION, SSF_LENGTH, SSF_LENGTH_BOTH_SIDE, BRDG_ID, 
    VALID_FLAG, CREATE_USER, CREATE_DATE, UPDATE_USER, UPDATE_DATE, DEL_USER, DEL_DATE
  </sql>
    <update id="updateSSfById">
update BCTCMSDB.T_BRDG_EXPNSNJOINT t set t.SSF_LENGTH_BOTH_SIDE=#{ssfLengthBothSide} where  t.ID=#{id}
    </update>
    <select id="getSSFPageAll" resultType="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgExpnsnjoint">
        select  t.BRDG_ID,t.id,t.SSF_LENGTH_BOTH_SIDE,t.ssf_location from BCTCMSDB.T_BRDG_EXPNSNJOINT t
            where t.SSF_LENGTH_BOTH_SIDE like '%$%'
       order by t.BRDG_ID
    </select>


  <select id="getSSFPage" resultType="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgExpnsnjoint">
select  t.*,decode(br.brdg_line_type,'L','左幅','R','右幅','Z','匝道','K','跨线','X','线外','其他')||br.frame_num as line_type from BCTCMSDB.T_BRDG_EXPNSNJOINT t inner join BCTCMSDB.T_BRDG_BRDGRECOG br
on t.BRDG_ID=br.BRDGRECOG_ID
where  br.brdgrecog_id in (select m.brdgrecog_id from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.main_BRDGRECOG_ID=#{userCode} and m.VALID_FLAG=1)
        <if test="ssfType != null and ssfType!=''">
          and t.SSF_TYPE=#{ssfType}
        </if> and t.valid_flag=1 order by t.BRDG_ID,t.ssf_type,t.SSF_LOCATION
  </select>
  <select id="getSSFListById" resultType="com.hualu.highwaymaintenance.module.bridge.entity.TBrdgExpnsnjoint">
select  t.* from BCTCMSDB.T_BRDG_EXPNSNJOINT t
where t.BRDG_ID=#{bridgeId} and t.valid_flag=1
  </select>
    <select id="getSSFInspectHis" resultType="com.hualu.highwaymaintenance.module.bridge.entity.SsfInspectHis">
    select ROWNUM||'' as no,t.SSF_TYPE,t.SSF_LOCATION,d.SSF_GAP,d.SSF_TEMP,di.FILE_ID as DSS_PIC,to_char(d.FOUND_DATE,'yyyy-MM-dd')||'' as INSPECT_TIME,dt.DSS_TYPE_NAME as DSS_TYPE from BCTCMSDB.T_BRDG_EXPNSNJOINT t inner join BCTCMSDB.DSS_INFO d
    on t.BRDG_ID=d.STRUCT_ID and d.FACILITY_CAT='QL'
    inner join MEMSDB.DSS_TYPE_NEW dt on d.DSS_TYPE=dt.DSS_TYPE
    left join MEMSDB.DSS_IMAGE di on d.DSS_ID=di.DSS_ID
    where t.ID=#{ssfId} and d.STRUCT_PART_ID='68' and t.SSF_LOCATION=replace(replace(d.struct_comp_id,'L',''),'R','')
  </select>


  <select id="getSSFMaintainHis" resultType="com.hualu.highwaymaintenance.module.bridge.entity.SsfMaintainHis">
select * from SSF_MAINTAIN_HIS t where t.SSF_ID=#{ssfId}
  </select>


  <select id="getSSfStatistic" resultType="com.hualu.highwaymaintenance.module.bridge.entity.SsfTj">
with orgIds as (select u.ID
                from GDGS.FW_RIGHT_ORG u
                where u.ID = #{orgId}
                union
                select c.ID
                from GDGS.FW_RIGHT_ORG c
                where c.PARENT_ID = #{orgId}
                  and c.IS_DELETED = 0
                  and c.IS_ENABLE = 1
                union
                select p.ID
                from GDGS.FW_RIGHT_ORG p
                where p.PARENT_ID in (select c.ID
                    from GDGS.FW_RIGHT_ORG c
                    where c.PARENT_ID = #{orgId}
                  and c.IS_DELETED = 0
                  and c.IS_ENABLE = 1)
                  and p.IS_ENABLE = 1
                  and p.IS_DELETED = 0)
select po.id as ORG_ID,po.ORG_NAME,fo.id as CHILD_ORG_ID,fo.org_name as CHILD_ORG_NAME,(select d.ATTRIBUTE_VALUE from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM='EXPNSN_JOINT_TYPE' and d.ATTRIBUTE_CODE =  t.SSF_TYPE and rownum=1) as SSF_TYPE,
       count( t.SSF_LOCATION)                       as SSF_NUM,
       sum(t.HAS_DSS) as SSF_DSS_NUM,
       count(distinct m.MAIN_BRDGRECOG_ID) as BRDG_ID,br.road_num as LINE_CODE
from BCTCMSDB.T_BRDG_EXPNSNJOINT t
         inner join BCTCMSDB.T_BRDG_BRDGRECOG br on t.BRDG_ID = br.BRDGRECOG_ID
    inner join BCTCMSDB.T_BRDG_RECOGMANAGE m on br.BRDGRECOG_ID = m.BRDGRECOG_ID and m.VALID_FLAG=1 and m.MAIN_BRDGRECOG_ID is not null
         inner join GDGS.BASE_ROUTE_LOGIC l on l.ROUTE_CODE=br.ROUTE_CODE
         inner join GDGS.FW_RIGHT_ORG fo on fo.ID=l.OPRT_ORG_CODE
         inner join GDGS.FW_RIGHT_ORG po on po.ID = fo.PARENT_ID
where br.ROUTE_CODE in
      (select f.ROUTE_CODE FROM GDGS.FW_RIGHT_DATA_PERMISSION F WHERE F.OPRT_ORG_CODE in (select * from orgIds))
  and t.valid_flag = 1 and t.SSF_TYPE is not null
group by po.id,po.ORG_NAME,fo.id,fo.org_name,br.road_num,t.SSF_TYPE
order by po.id,po.ORG_NAME,fo.id,fo.org_name,br.road_num,t.SSF_TYPE
  </select>

  <select id="getSSfStatisticByOprt" resultType="com.hualu.highwaymaintenance.module.bridge.entity.SsfOprtTj">
with orgIds as (select u.ORG_ID
                from GDGS.FW_RIGHT_USER u
                where u.USER_CODE = #{userCode}
                union
                select c.ID
                from GDGS.FW_RIGHT_ORG c
                where c.PARENT_ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
                  and c.IS_DELETED = 0
                  and c.IS_ENABLE = 1
                union
                select p.ID
                from GDGS.FW_RIGHT_ORG p
                where p.PARENT_ID in (select c.ID
                                      from GDGS.FW_RIGHT_ORG c
                                      where c.PARENT_ID =
                                            (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
                                        and c.IS_DELETED = 0
                                        and c.IS_ENABLE = 1)
                  and p.IS_ENABLE = 1
                  and p.IS_DELETED = 0)
select (select d.ATTRIBUTE_VALUE
        from GDGS.BASE_DATATHIRD_DIC d
        where d.ATTRIBUTE_ITEM = 'EXPNSN_JOINT_TYPE'
          and d.ATTRIBUTE_CODE = t.SSF_TYPE
          and d.ATTRIBUTE_ACTIVE = 0 and ROWNUM=1) as SSF_TYPE,
       count(1)                       as SSF_NUM, br.ROAD_NUM as LINE_CODE,fo.ORG_NAME as OPRT_NAME,l.ROUTE_NAME,ROWNUM as NO
from BCTCMSDB.T_BRDG_EXPNSNJOINT t
         inner join BCTCMSDB.T_BRDG_BRDGRECOG br on t.BRDG_ID = br.BRDGRECOG_ID
inner join GDGS.BASE_ROUTE_LOGIC l on l.ROUTE_CODE=br.ROUTE_CODE
inner join GDGS.FW_RIGHT_ORG fo on fo.ID=l.OPRT_ORG_CODE
where br.ROUTE_CODE in
      (select f.ROUTE_CODE FROM GDGS.FW_RIGHT_DATA_PERMISSION F WHERE F.OPRT_ORG_CODE in (select * from orgIds))
  and t.valid_flag = 1 and t.SSF_TYPE is not null
group by br.ROAD_NUM,fo.ORG_NAME,l.ROUTE_NAME, t.SSF_TYPE
order by br.ROAD_NUM,fo.ORG_NAME,l.ROUTE_NAME, t.SSF_TYPE,SSF_NUM
  </select>

    <select id="getSsfBridgeGroup" resultType="com.hualu.highwaymaintenance.module.bridge.entity.SsfBridgeGroup">

select  m.MAIN_BRDGRECOG_ID as BRDG_ID,mb.ROAD_NUM,mb.BRDG_NAME as BRIDGE_NAME,l.ROUTE_NAME,fo.ORG_NAME as OPRT_NAME,mb.LOGIC_CNTR_STAKE as CNTR_STAKE,decode(br.BRDG_LINE_TYPE,'L','左幅','R','右幅','Z','匝道','X','线外','K','跨线','其他')||br.frame_num as LINE_TYPE
                       ,(select d.ATTRIBUTE_VALUE from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM='EXPNSN_JOINT_TYPE' and d.ATTRIBUTE_CODE =  t.SSF_TYPE and rownum=1) as SSF_TYPE,WM_CONCAT(t.SSF_LOCATION) as SSF_LOCATIONS,decode(t.SSF_DIRECT,'1','横向','纵向') as SSF_DIRECTION,
       count(1) as SSF_NUM,br.ROAD_NAME as LINE_NAME
from BCTCMSDB.T_BRDG_EXPNSNJOINT t
         inner join BCTCMSDB.T_BRDG_BRDGRECOG br on t.BRDG_ID = br.BRDGRECOG_ID
         inner join BCTCMSDB.T_BRDG_RECOGMANAGE m on br.BRDGRECOG_ID = m.BRDGRECOG_ID and m.VALID_FLAG=1 and m.MAIN_BRDGRECOG_ID is not null
         inner join GDGS.BASE_ROUTE_LOGIC l on l.ROUTE_CODE=br.ROUTE_CODE
         inner join GDGS.FW_RIGHT_ORG fo on fo.ID=l.OPRT_ORG_CODE
         inner join BCTCMSDB.T_BRDG_BRDGRECOG mb on mb.BRDGRECOG_ID = m.MAIN_BRDGRECOG_ID
where br.ROUTE_CODE in
      (select f.ROUTE_CODE FROM GDGS.FW_RIGHT_DATA_PERMISSION F WHERE F.OPRT_ORG_CODE =#{orgId})
        <if test="ssfCode != null and ssfCode!=''"> and t.ssf_type=#{ssfCode}</if>
  and br.VALID_FLAG=1
  and t.valid_flag = 1 and t.SSF_TYPE is not null
   group by m.MAIN_BRDGRECOG_ID,mb.ROAD_NUM,mb.BRDG_NAME,l.ROUTE_NAME,fo.ORG_NAME,mb.LOGIC_CNTR_STAKE,br.BRDG_LINE_TYPE,br.ROAD_NAME
,t.SSF_TYPE,t.SSF_DIRECT,br.frame_num
order by mb.ROAD_NUM,m.MAIN_BRDGRECOG_ID,br.BRDG_LINE_TYPE,br.frame_num,t.SSF_TYPE
    </select>

    <select id="getSSfDssStatistic" resultType="com.hualu.highwaymaintenance.module.bridge.entity.SsfDssTj">
with orgIds as (select u.ID
                from GDGS.FW_RIGHT_ORG u
                where u.ID = #{orgId}
                union
                select c.ID
                from GDGS.FW_RIGHT_ORG c
                where c.PARENT_ID = #{orgId}
                  and c.IS_DELETED = 0
                  and c.IS_ENABLE = 1
                union
                select p.ID
                from GDGS.FW_RIGHT_ORG p
                where p.PARENT_ID in (select c.ID
                    from GDGS.FW_RIGHT_ORG c
                    where c.PARENT_ID = #{orgId}
                  and c.IS_DELETED = 0
                  and c.IS_ENABLE = 1)
                  and p.IS_ENABLE = 1
                  and p.IS_DELETED = 0)
select po.id as ORG_ID,po.ORG_NAME,fo.id as CHILD_ORG_ID,fo.org_name as CHILD_ORG_NAME,br.road_num as line_code,
       count( t.SSF_LOCATION)                       as SSF_NUM,
       sum(decode(dt.DSS_TYPE,'QLQM681300',1,0)) as yxgdl,
       sum(decode(dt.DSS_TYPE,'QLQM680300',1,0)) as xgps,
       sum(decode(dt.DSS_TYPE,'QLQM680900',1,0)) as yxgjct,
       sum(decode(dt.DSS_TYPE,'QLXB68-3546',1,0)) as lssd
from BCTCMSDB.T_BRDG_EXPNSNJOINT t
         inner join BCTCMSDB.T_BRDG_BRDGRECOG br on t.BRDG_ID = br.BRDGRECOG_ID
         inner join BCTCMSDB.T_BRDG_RECOGMANAGE m on br.BRDGRECOG_ID = m.BRDGRECOG_ID and m.VALID_FLAG=1 and m.MAIN_BRDGRECOG_ID is not null
         inner join GDGS.BASE_ROUTE_LOGIC l on l.ROUTE_CODE=br.ROUTE_CODE
         inner join GDGS.FW_RIGHT_ORG fo on fo.ID=l.OPRT_ORG_CODE
         inner join GDGS.FW_RIGHT_ORG po on po.ID = fo.PARENT_ID
        left join MEMSDB.DSS_INFO d on br.LAST_PRJ_ID = d.REL_TASK_CODE and  br.BRDGRECOG_ID = d.STRUCT_ID and d.STRUCT_PART_ID='68' and d.FACILITY_CAT='QL'
    and t.BRDG_ID = d.STRUCT_ID and t.SSF_LOCATION = replace(replace(d.STRUCT_COMP_ID,'L',''),'R','')
inner join MEMSDB.DSS_TYPE_NEW dt on d.DSS_TYPE = dt.DSS_TYPE
where br.ROUTE_CODE in
      (select f.ROUTE_CODE FROM GDGS.FW_RIGHT_DATA_PERMISSION F WHERE F.OPRT_ORG_CODE in (select * from orgIds))

  and t.valid_flag = 1 and t.SSF_TYPE is not null
group by po.id,po.ORG_NAME,fo.id,fo.org_name,br.road_num
order by po.id,po.ORG_NAME,fo.id,fo.org_name,br.road_num
    </select>
    <update id="uploadSsfPic">
    update BCTCMSDB.T_BRDG_EXPNSNJOINT t set t.IMAGE_ID = #{fileId} where t.ID=#{ssfId}
    </update>
    <update id="deleteSsfPic">
    update BCTCMSDB.T_BRDG_EXPNSNJOINT t set t.IMAGE_ID = null where t.ID=#{ssfId}
    </update>
    <select id="getSummaryInfoByOrg" resultType="java.lang.String">
select (select  f.ORG_NAME||'伸缩缝统计信息:' from GDGS.FW_RIGHT_ORG f where f.ID=#{orgId})||b.su from (
select WM_CONCAT(a.ssf_type||':'||a.ssf_num||'条')||';总计：'||sum(a.ssf_num)||'条' as su from (
select dc.ATTRIBUTE_VALUE as ssf_type,count(1) as ssf_num
from BCTCMSDB.T_BRDG_EXPNSNJOINT t
         inner join BCTCMSDB.T_BRDG_BRDGRECOG br on t.BRDG_ID = br.BRDGRECOG_ID
         inner join BCTCMSDB.T_BRDG_RECOGMANAGE m on br.BRDGRECOG_ID = m.BRDGRECOG_ID and m.VALID_FLAG=1 and m.MAIN_BRDGRECOG_ID is not null
         inner join GDGS.BASE_ROUTE_LOGIC l on l.ROUTE_CODE=br.ROUTE_CODE
         inner join GDGS.FW_RIGHT_ORG fo on fo.ID=l.OPRT_ORG_CODE
inner join GDGS.BASE_DATATHIRD_DIC dc
on dc.ATTRIBUTE_ITEM='EXPNSN_JOINT_TYPE'
and dc.ATTRIBUTE_CODE=t.SSF_TYPE
where fo.ID=#{orgId}
group by dc.ATTRIBUTE_VALUE
    order by ssf_num desc) a) b
    </select>
    <select id="getBrdgNameById" resultType="java.lang.String">
        select brdg_name from bctcmsdb.t_brdg_brdgrecog br where br.brdgrecog_id=#{brdgId}
    </select>
    <select id="getBrdgFullNameById" resultType="java.lang.String">
        select br.brdg_name||'('||br.brdg_line_type||br.frame_num||')' from bctcmsdb.t_brdg_brdgrecog br where br.brdgrecog_id=#{brdgId}
    </select>

    <select id="getBrdgListById" resultType="com.hualu.highwaymaintenance.module.dic.domain.TBrdgBrdgrecog">
     select * from bctcmsdb.t_brdg_brdgrecog br where br.brdgrecog_id
                                                     in (select m.brdgrecog_id from BCTCMSDB.T_BRDG_RECOGMANAGE m where m.MAIN_BRDGRECOG_ID=#{brdgId} and m.VALID_FLAG=1)
     and br.VALID_FLAG=1
    </select>
    <insert id="saveCompattrByEx">
        insert into BCTCMSDB.T_BRDG_COMPATTR t
        select c.id,(
            select cp.CHILD_PARTS_ID from BCTCMSDB.T_BRDG_PART_STRCT_INFO p
                                              inner join BCTCMSDB.T_BRDG_CHILD_PARTS cp on p.PARTS_ID = cp.PARTS_ID
            where p.BRDGRECOG_ID=c.BRDG_ID and p.PARTSTYPE_ID='68' and p.VALID_FLAG=1 and cp.VALID_FLAG=1 and rownum=1),
               c.BRDG_ID,null,68,null,3,'68',(select decode(b.BRDG_LINE_TYPE,'R','R','L') from T_BRDG_BRDGRECOG b where b.BRDGRECOG_ID=c.BRDG_ID)||c.SSF_LOCATION,null,c.CREATE_USER,c.CREATE_DATE,
               c.UPDATE_USER,1,c.CREATE_DATE,null,null,null,null
               from BCTCMSDB.T_BRDG_EXPNSNJOINT c where c.ID=#{id}
    </insert>
    <update id="delCompById">
update BCTCMSDB.T_BRDG_COMPATTR t set t.VALID_FLAG=0
where t.COMPATTR_ID=#{id}
    </update>
    <update id="updateCompById">
update BCTCMSDB.T_BRDG_COMPATTR t set t.COMPATTR_CODE=substr(t.COMPATTR_CODE,0,1)||#{ssfLocation}
where t.COMPATTR_ID=#{id}
    </update>
    <update id="updateCompNum">
update BCTCMSDB.T_BRDG_CHILD_PARTS cp
set cp.COMPATTR_NUM=
(select count(1) from T_BRDG_COMPATTR c where c.BRDGRECOG_ID=#{brdgId}
    and c.PARTSTYPE_ID='68' and c.VALID_FLAG=1)
where cp.CHILD_PARTS_ID=
      (select c.CHILD_PARTS_ID from T_BRDG_COMPATTR c where c.BRDGRECOG_ID=#{brdgId}
                                       and c.PARTSTYPE_ID='68' and c.VALID_FLAG=1 and rownum=1)
    </update>
    <update id="uploadHisPic">
        update BCTCMSDB.SSF_MAINTAIN_HIS t set t.PIC_AFTER=#{fileId} where t.ID=#{hisId}
    </update>

    <update id="delHisPic">
        update BCTCMSDB.SSF_MAINTAIN_HIS t set t.PIC_AFTER=null where t.ID=#{hisId}
    </update>

    <select id="getPartsByBrdgId" resultType="java.lang.String">
        select c.PARTS_ID from bctcmsdb.T_BRDG_PART_STRCT_INFO c
        where c.BRDGRECOG_ID =#{brdgId}
        and c.PARTSTYPE_ID='68' and c.VALID_FLAG=1
    </select>

    <insert id="addParts">
        insert into BCTCMSDB.T_BRDG_PART_STRCT_INFO
        select #{partId},br.BRDGRECOG_ID,#{partCode},3,null,null,null,null,1,'parts13_0',null from BCTCMSDB.T_BRDG_BRDGRECOG br
        where br.BRDGRECOG_ID = #{brdgId}
    </insert>

    <insert id="addCpParts">
    insert into BCTCMSDB.T_BRDG_CHILD_PARTS(PARTS_ID, CHILD_PARTS_ID, PARTSTYPE_ID, PARTS_CODE,  VALID_FLAG)
values(#{partId},sys_guid(),#{partCode},#{partCode},1)
    </insert>

    <select id="getClvrtById" resultType="java.util.Map">
select f.ORG_FULLNAME,t.CLVRT_NAME,t.LINE_NAME,t.CLVRT_CODE,t.LOGIC_CNTR_STAKE,x.ROADBED_WIDTH,x.ROAD_WIDTH,p.CLVRT_TYPE_NAME,di.ATTRIBUTE_VALUE as TECH_GRADE,x.APERTURE||'' as APERTURE,dx.ATTRIBUTE_VALUE as DSGN_LOAD
,substr(x.APERTURE,0,1) as STAGE,x.CLVRT_HEIGHT,dc.ATTRIBUTE_VALUE as PARTS_MTRL,x.CLVRT_BODY_LEN,t.COMPLETED_YEARS,decode(t.ANGLE,null,0,t.ANGLE) as ANGLE,(Select to_char(WM_CONCAT(p.CLVRT_TYPE_NAME||':'||sc.START_STAGES||'-'||sc.END_STAGES)) from  T_CLVRT_CONSSHAPE sc

left join T_CLVRT_CLVRTTYPE p
                                                                                                     on p.CLVRT_TYPE_CODE=sc.STRUCT_FORM     where t.CLVRTRECOG_ID=sc.CLVRTRECOG_ID and sc.VALID_FLAG=1) as STRUCT_FORM,
       x.FILL_HEIGHT,decode(t.REMARK,null,'-',t.REMARK) as REMARK,(select max(ff.MG_FILE_ID) from T_BASE_FILELINK k
       inner join GDGS.BASE_FILE_ENTITY ff on k.FILE_ID=ff.FILE_ENTITY_ID and ff.MG_IS_EXISTS = 1
           where k.BUSSI_ID=t.CLVRTRECOG_ID and k.BUSSI_TYPE=2 and (k.PHOTO_TYPE='2' or k.PHOTO_TYPE is not null) and k.VALID_FLAG=1) as JSK,
       (select min(ff.MG_FILE_ID) from T_BASE_FILELINK k
                                     inner join GDGS.BASE_FILE_ENTITY ff on k.FILE_ID=ff.FILE_ENTITY_ID and ff.MG_IS_EXISTS = 1
        where k.BUSSI_ID=t.CLVRTRECOG_ID and k.BUSSI_TYPE=2 and (k.PHOTO_TYPE='4' or k.PHOTO_TYPE is not null) and k.VALID_FLAG=1 ) as CSK,
        (select (select bdd.ATTRIBUTE_VALUE from  GDGS.BASE_DATATHIRD_DIC bdd  where bdd.ATTRIBUTE_ITEM='CUL_WATER_SHAPE' and bdd.ATTRIBUTE_CODE=t.PARTS_TYPE and bdd.ATTRIBUTE_ACTIVE=0 and ROWNUM=1) from T_CLVRT_PARTSCONS t where t.CLVRTRECOG_ID=t.CLVRTRECOG_ID and t.PARTS_CODE='1' and t.VALID_FLAG=1 and ROWNUM=1)
        as RKYS,
        (select (select bdd.ATTRIBUTE_VALUE from  GDGS.BASE_DATATHIRD_DIC bdd  where bdd.ATTRIBUTE_ITEM='CUL_WATER_SHAPE' and bdd.ATTRIBUTE_CODE=t.PARTS_TYPE and bdd.ATTRIBUTE_ACTIVE=0 and ROWNUM=1) from T_CLVRT_PARTSCONS t where t.CLVRTRECOG_ID=t.CLVRTRECOG_ID and t.PARTS_CODE='2' and t.VALID_FLAG=1 and ROWNUM=1)
        as CKYS
       from T_CLVRT_CLVRTRECOG t
inner join GDGS.FW_RIGHT_ORG f
on t.OPRT_ORG_CODE=f.ID
inner join T_CLVRT_TECHINDEX x on t.CLVRTRECOG_ID=x.CLVRTRECOG_ID
left join T_CLVRT_CONSSHAPE s
on t.CLVRTRECOG_ID=s.CLVRTRECOG_ID and s.VALID_FLAG=1
left join T_CLVRT_CLVRTTYPE p
on p.CLVRT_TYPE_CODE=s.STRUCT_FORM
left join T_CLVRT_PARTSCONS ts on ts.CLVRTRECOG_ID=t.CLVRTRECOG_ID and ts.PARTS_CODE=1
left join GDGS.BASE_DATATHIRD_DIC di on di.ATTRIBUTE_ITEM='CUL_GRADE' and di.ATTRIBUTE_CODE=t.TECH_GRADE
left join GDGS.BASE_DATATHIRD_DIC dx on dx.ATTRIBUTE_ITEM='DSGN_LOAD' and dx.ATTRIBUTE_CODE=t.DSGN_LOAD
left join GDGS.BASE_DATATHIRD_DIC dc on dc.ATTRIBUTE_ITEM='CUL_MATERIAL' and dc.ATTRIBUTE_CODE=ts.PARTS_MTRL
where t.CLVRTRECOG_ID=#{id} and ROWNUM=1
    </select>
    <select id="getXYBySql" resultType="java.util.Map">
        select * from (
        select point_X,point_Y,point_M,(select GDGS.func_rlStake_by_rpStake(#{px},gp.start_stake_num,gp.end_stake_num,#{rpStartNum},#{rpEndNum}) from GDGS.gp_road_node gq where gq.gis_id=gp.gis_id and rownum &lt;2 ) as PX from GDGS.gp_road_node gp where gis_id = #{rpid} and point_M &gt; (select func_rlStake_by_rpStake(#{px},gp.start_stake_num,gp.end_stake_num,#{rpStartNum},#{rpEndNum}) from GDGS.gp_road_node gq where gq.gis_id=gp.gis_id and rownum &lt;2 )
        order by point_M asc) where rownum &lt;2
        union all
        select * from (
        select point_X,point_Y,point_M,(select GDGS.func_rlStake_by_rpStake(#{px},gp.start_stake_num,gp.end_stake_num,#{rpStartNum},#{rpEndNum}) from GDGS.gp_road_node gq where gq.gis_id=gp.gis_id and rownum &lt;2 ) as PX from GDGS.gp_road_node gp where gis_id = #{rpid} and point_M &lt; (select func_rlStake_by_rpStake(#{px},gp.start_stake_num,gp.end_stake_num,#{rpStartNum},#{rpEndNum}) from GDGS.gp_road_node gq where gq.gis_id=gp.gis_id and rownum &lt;2 )
        order by point_M desc) where rownum &lt;2
    </select>
    <select id="getPhysicsStakeByLogicStake" resultType="java.util.Map">
        select distinct p.rp_intrvl_id,p.px,px_,rp_start_num,rp_end_num from table(gdgs.func_physics_by_logic(#{lineId},1,#{l},#{s},0)) p
    </select>
    <select id="getAllBrdg" resultType="java.util.Map">
        select t.RL_STAKE_NEW as S,t.LINE_ID as R,t.LINE_DIRECT as L,t.DSS_ID as BID from BCTCMSDB.TS_DSS_INFO t where t.X is null
    </select>
    <select id="getAll" resultType="java.util.Map">
        select decode(b.stake,null,0,b.stake)||'' as S,b.LINE_ID as R,decode(LINE_DIRECT,null,1,4,1,3,1,LINE_DIRECT) as L,b.dss_id as BID from  TS_DSS_INFO b where b.X is null
    </select>
    <select id="getBrdgById" resultType="java.util.Map">
select f.ORG_FULLNAME,t.BRDG_NAME,t.TRAFFIC_CODE,t.LOGIC_CNTR_STAKE,t.ROAD_NUM,t.ROAD_NAME,di.ATTRIBUTE_VALUE as ROUTE_LVL,x.BRDG_LEN,x.SPAN_LEN
,x.MAX_SPAN,t.BRDG_SPAN_GROUP,x.DECK_TOTAL_WIDTH,s.DECK_WIDTH,dxi.ATTRIBUTE_VALUE as BRDG_SPAN_KIND,to_char(t.COMP_TIME,'yyyy')||'' as SYNX,
       (select b.BRDG_TYPE_NAME from T_BRDG_TOPTYPE a inner join T_BRDG_BRDGTYPE b
           on a.BRDG_TYPE=b.BRDGTYPE_ID and a.VALID_FLAG=1 and b.VALID_FLAG=1
           where a.BRDGRECOG_ID=t.BRDGRECOG_ID and ROWNUM=1) as BRDG_TYPE_NAME,dc.ATTRIBUTE_VALUE as DSGN_LOAD,dc1.ATTRIBUTE_VALUE as SEISMIC_LVL,dc2.ATTRIBUTE_VALUE
as ACROSS_FTR_TYPE,t.TRAFFIC_LOAD,dc3.ATTRIBUTE_VALUE as PIER_FC_TYPE,p.ORG_NAME,t.DSGN_ORG,t.CNSTRCT_ORG,t.SUPER_ORG,to_char(t.COMP_TIME,'yyyy') as XJND,to_char(t.COMP_TIME,'yyyy-MM-dd') as COMP_TIME,
       dc4.ATTRIBUTE_VALUE as BRDG_TC_GRADE,
       (select max(ff.MG_FILE_ID) from T_BASE_FILELINK k
                                           inner join GDGS.BASE_FILE_ENTITY ff on k.FILE_ID=ff.FILE_ENTITY_ID and ff.MG_IS_EXISTS = 1
        where k.BUSSI_ID=t.BRDGRECOG_ID and k.BUSSI_TYPE=1 and (k.PHOTO_TYPE='1' or k.PHOTO_TYPE is not null) and k.VALID_FLAG=1) as SBJG,
       (select min(ff.MG_FILE_ID) from T_BASE_FILELINK k
                                           inner join GDGS.BASE_FILE_ENTITY ff on k.FILE_ID=ff.FILE_ENTITY_ID and ff.MG_IS_EXISTS = 1
        where k.BUSSI_ID=t.BRDGRECOG_ID and k.BUSSI_TYPE=1 and (k.PHOTO_TYPE='2' or k.PHOTO_TYPE is not null) and k.VALID_FLAG=1 ) as XBJG
from T_BRDG_BRDGRECOG t
                  inner join GDGS.FW_RIGHT_ORG f
                             on t.OPRT_ORG_CODE=f.ID            left join GDGS.FW_RIGHT_ORG p
                             on t.PRJ_CODE=p.ID
                  left join GDGS.BASE_DATATHIRD_DIC di on di.ATTRIBUTE_ITEM='route_lvl' and di.ATTRIBUTE_CODE=t.ROUTE_LVL

                  inner join T_BRDG_TECHINDEX x on t.BRDGRECOG_ID=x.BRDGRECOG_ID and x.VALID_FLAG=1
                  left join GDGS.BASE_DATATHIRD_DIC dxi on dxi.ATTRIBUTE_ITEM='BRDG_SPAN_KIND' and dxi.ATTRIBUTE_CODE=x.BRDG_SPAN_KIND
                  left join GDGS.BASE_DATATHIRD_DIC dc on dc.ATTRIBUTE_ITEM='DSGN_LOAD' and dc.ATTRIBUTE_CODE=t.DSGN_LOAD
                  left join GDGS.BASE_DATATHIRD_DIC dc1 on dc1.ATTRIBUTE_ITEM='SEISMIC_LVL' and dc1.ATTRIBUTE_CODE=x.SEISMICLVL
                  left join GDGS.BASE_DATATHIRD_DIC dc2 on dc2.ATTRIBUTE_ITEM='ACROSS_FTR_TYPE' and dc2.ATTRIBUTE_CODE=t.ACROSS_FTR_TYPE
                  left join GDGS.BASE_DATATHIRD_DIC dc3 on dc3.ATTRIBUTE_ITEM='PIER_FC_TYPE' and dc3.ATTRIBUTE_CODE=t.F103
                  left join GDGS.BASE_DATATHIRD_DIC dc4 on dc4.ATTRIBUTE_ITEM='BRDG_TC_GRADE' and dc4.ATTRIBUTE_CODE=t.BRDG_RATING
                  inner join T_BRDG_SUPPLEFIELD s on t.BRDGRECOG_ID=s.BRDGRECOG_ID
where t.BRDGRECOG_ID=#{id} and ROWNUM=1
    </select>
    <update id="saveOrUpdateBySql">
        ${sql}
    </update>

  <select id="getAllDssInfo" resultType="java.util.Map">
      select d.DSS_ID as DID,d.X ,d.Y from temp_dss_info d
    </select>
</mapper>