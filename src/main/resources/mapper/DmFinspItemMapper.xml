<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.oftenCheck.mapper.DmFinspItemMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.oftenCheck.entity.DmFinspItem">
            <id property="finspItemId" column="FINSP_ITEM_ID" jdbcType="VARCHAR"/>
            <result property="facilityCat" column="FACILITY_CAT" jdbcType="VARCHAR"/>
            <result property="finspItemCode" column="FINSP_ITEM_CODE" jdbcType="VARCHAR"/>
            <result property="inspCom" column="INSP_COM" jdbcType="VARCHAR"/>
            <result property="inspCont" column="INSP_CONT" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="finVersion" column="FIN_VERSION" jdbcType="VARCHAR"/>
            <result property="isDelete" column="IS_DELETE" jdbcType="VARCHAR"/>
            <result property="finDesc" column="FIN_DESC" jdbcType="VARCHAR"/>
            <result property="finRemark" column="FIN_REMARK" jdbcType="VARCHAR"/>
            <result property="partCode" column="PART_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        FINSP_ITEM_ID,FACILITY_CAT,FINSP_ITEM_CODE,
        INSP_COM,INSP_CONT,MNT_ORG_ID,
        FIN_VERSION,IS_DELETE,FIN_DESC,
        FIN_REMARK,PART_CODE
    </sql>
</mapper>
