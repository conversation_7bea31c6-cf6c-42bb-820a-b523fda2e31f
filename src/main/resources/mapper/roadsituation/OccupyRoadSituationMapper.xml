<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.hualu.highwaymaintenance.module.roadsituation.mapper.OccupyRoadSituationMapper">
  <select id="statisticByDay"
    resultType="com.hualu.highwaymaintenance.module.roadsituation.vo.OccupyRoadCountVo">
    with ORGS AS (select o.*
                  from gdgs.FW_RIGHT_ORG o
                  where o.IS_ENABLE = 1
                    and o.IS_DELETED = 0
                  start with o.ID = #{orgId}
                  connect by prior o.ID = o.PARENT_ID)
    select to_char(END_TIME, 'yyyy-mm-dd') x,
           count(*)                  y,
          sum(case when END_TIME > sysdate then 1 else 0 end) iy,
          sum(case when END_TIME &lt;= sysdate then 1 else 0 end) fy
    from MEMSDB.OCCUPY_ROAD_SITUATION d
    where d.VALID = 1
      and exists(select 1
                 from ORGS c
                 where c.id = d.ORG_ID)
      and END_TIME &lt; ADD_MONTHS(TRUNC(SYSDATE - INTERVAL '1' DAY, 'MM'), 1)
      AND END_TIME >= TRUNC(SYSDATE, 'MM') group by to_char(END_TIME, 'yyyy-mm-dd')
    order by to_char(END_TIME, 'yyyy-mm-dd')
  </select>

  <select id="statisticByOrgName"
    resultType="com.hualu.highwaymaintenance.module.roadsituation.vo.OccupyRoadCountVo">
    with ORGS AS (
    select o.* from gdgs.FW_RIGHT_ORG o
    where o.IS_ENABLE = 1 and o.IS_DELETED = 0
    start with o.ID = #{orgId}
    connect by prior o.ID = o.PARENT_ID
    )
    select
    (select oo.ORG_NAME from gdgs.FW_RIGHT_ORG oo where oo.ID = d.ORG_ID and oo.IS_ENABLE = 1 and
    oo.IS_DELETED = 0 and ROWNUM = 1) x,
    count(*) y,
    sum(case when END_TIME > sysdate then 1 else 0 end) iy,
    sum(case when END_TIME &lt;= sysdate then 1 else 0 end) fy
    from MEMSDB.OCCUPY_ROAD_SITUATION d where d.VALID = 1
    and exists(
    select 1 from ORGS c where c.id = d.ORG_ID
    ) and END_TIME &lt; ADD_MONTHS(TRUNC(SYSDATE - INTERVAL '1' DAY, 'MM'), 1)
    AND END_TIME >= TRUNC(SYSDATE, 'MM') group by d.ORG_ID
  </select>
</mapper>