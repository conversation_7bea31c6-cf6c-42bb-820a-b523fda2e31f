<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.CulvertStsTypeMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.CulvertStsType">
    <!--@mbg.generated-->
    <!--@Table CULVERT_STS_TYPE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PARTNAME" jdbcType="VARCHAR" property="partname" />
    <result column="STRUCT_COMP_NAME" jdbcType="VARCHAR" property="structCompName" />
    <result column="PART_CODE" jdbcType="DECIMAL" property="partCode" />
    <result column="DSS_TYPE_NAME" jdbcType="VARCHAR" property="dssTypeName" />
    <result column="DSS_TYPE" jdbcType="VARCHAR" property="dssType" />
    <result column="STS_PART_NAME" jdbcType="VARCHAR" property="stsPartName" />
    <result column="STS_DSS_TYPE_NAME" jdbcType="VARCHAR" property="stsDssTypeName" />
    <result column="OBJ_FIELD" jdbcType="VARCHAR" property="objField" />
    <result column="STS_COLUMN" jdbcType="VARCHAR" property="stsColumn" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PARTNAME, STRUCT_COMP_NAME, PART_CODE, DSS_TYPE_NAME, DSS_TYPE, STS_PART_NAME, 
    STS_DSS_TYPE_NAME, OBJ_FIELD, STS_COLUMN
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CULVERT_STS_TYPE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CULVERT_STS_TYPE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridge.CulvertStsType">
    <!--@mbg.generated-->
    insert into CULVERT_STS_TYPE (ID, PARTNAME, STRUCT_COMP_NAME, 
      PART_CODE, DSS_TYPE_NAME, DSS_TYPE, 
      STS_PART_NAME, STS_DSS_TYPE_NAME, OBJ_FIELD, 
      STS_COLUMN)
    values (#{id,jdbcType=VARCHAR}, #{partname,jdbcType=VARCHAR}, #{structCompName,jdbcType=VARCHAR}, 
      #{partCode,jdbcType=DECIMAL}, #{dssTypeName,jdbcType=VARCHAR}, #{dssType,jdbcType=VARCHAR}, 
      #{stsPartName,jdbcType=VARCHAR}, #{stsDssTypeName,jdbcType=VARCHAR}, #{objField,jdbcType=VARCHAR}, 
      #{stsColumn,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridge.CulvertStsType">
    <!--@mbg.generated-->
    insert into CULVERT_STS_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="partname != null">
        PARTNAME,
      </if>
      <if test="structCompName != null">
        STRUCT_COMP_NAME,
      </if>
      <if test="partCode != null">
        PART_CODE,
      </if>
      <if test="dssTypeName != null">
        DSS_TYPE_NAME,
      </if>
      <if test="dssType != null">
        DSS_TYPE,
      </if>
      <if test="stsPartName != null">
        STS_PART_NAME,
      </if>
      <if test="stsDssTypeName != null">
        STS_DSS_TYPE_NAME,
      </if>
      <if test="objField != null">
        OBJ_FIELD,
      </if>
      <if test="stsColumn != null">
        STS_COLUMN,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="partname != null">
        #{partname,jdbcType=VARCHAR},
      </if>
      <if test="structCompName != null">
        #{structCompName,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        #{partCode,jdbcType=DECIMAL},
      </if>
      <if test="dssTypeName != null">
        #{dssTypeName,jdbcType=VARCHAR},
      </if>
      <if test="dssType != null">
        #{dssType,jdbcType=VARCHAR},
      </if>
      <if test="stsPartName != null">
        #{stsPartName,jdbcType=VARCHAR},
      </if>
      <if test="stsDssTypeName != null">
        #{stsDssTypeName,jdbcType=VARCHAR},
      </if>
      <if test="objField != null">
        #{objField,jdbcType=VARCHAR},
      </if>
      <if test="stsColumn != null">
        #{stsColumn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridge.CulvertStsType">
    <!--@mbg.generated-->
    update CULVERT_STS_TYPE
    <set>
      <if test="partname != null">
        PARTNAME = #{partname,jdbcType=VARCHAR},
      </if>
      <if test="structCompName != null">
        STRUCT_COMP_NAME = #{structCompName,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        PART_CODE = #{partCode,jdbcType=DECIMAL},
      </if>
      <if test="dssTypeName != null">
        DSS_TYPE_NAME = #{dssTypeName,jdbcType=VARCHAR},
      </if>
      <if test="dssType != null">
        DSS_TYPE = #{dssType,jdbcType=VARCHAR},
      </if>
      <if test="stsPartName != null">
        STS_PART_NAME = #{stsPartName,jdbcType=VARCHAR},
      </if>
      <if test="stsDssTypeName != null">
        STS_DSS_TYPE_NAME = #{stsDssTypeName,jdbcType=VARCHAR},
      </if>
      <if test="objField != null">
        OBJ_FIELD = #{objField,jdbcType=VARCHAR},
      </if>
      <if test="stsColumn != null">
        STS_COLUMN = #{stsColumn,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridge.CulvertStsType">
    <!--@mbg.generated-->
    update CULVERT_STS_TYPE
    set PARTNAME = #{partname,jdbcType=VARCHAR},
      STRUCT_COMP_NAME = #{structCompName,jdbcType=VARCHAR},
      PART_CODE = #{partCode,jdbcType=DECIMAL},
      DSS_TYPE_NAME = #{dssTypeName,jdbcType=VARCHAR},
      DSS_TYPE = #{dssType,jdbcType=VARCHAR},
      STS_PART_NAME = #{stsPartName,jdbcType=VARCHAR},
      STS_DSS_TYPE_NAME = #{stsDssTypeName,jdbcType=VARCHAR},
      OBJ_FIELD = #{objField,jdbcType=VARCHAR},
      STS_COLUMN = #{stsColumn,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>