<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.MaintainMeasureMapper">

  <select id="getMaintainNature" resultType="com.hualu.highwaymaintenance.module.decision.vo.DicVo">
    select d.ATTRIBUTE_CODE id,
      d.ATTRIBUTE_VALUE value,
       d.ATTRIBUTE_ITEM itemType
    from GDGS.BASE_DATATHIRD_DIC d where d.ATTRIBUTE_ITEM = 'PMS_MAINTAIN_PLAN' and ATTRIBUTE_ACTIVE = 0
  </select>

  <select id="getMaintainMeasure"
    resultType="com.hualu.highwaymaintenance.module.decision.entity.MaintainMeasure">
    select m.*,
      decode(m.ROAD_TYPE, 'SN', '水泥路面', 'LQ', '沥青路面', '') roadTypeValue,
      (select d.ATTRIBUTE_VALUE
       from GDGS.BASE_DATATHIRD_DIC d
       where d.ATTRIBUTE_ITEM = 'PMS_MAINTAIN_PLAN'
         and d.ATTRIBUTE_CODE = m.MAINTAIN_NATURE
         and ROWNUM = 1) maintainNatureValue
    from PMSDB.MAINTAIN_MEASURE m
    where m.PROJECT_ID = #{projectId,jdbcType=VARCHAR}
  </select>
</mapper>