<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqReportBridgeMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqReportBridge">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_REPORT_BRIDGE-->
    <result column="BRIDGE_ID" jdbcType="VARCHAR" property="bridgeId" />
    <result column="BRIDGE_IDENTITY_CODE" jdbcType="VARCHAR" property="bridgeIdentityCode" />
    <result column="BRIDGE_NAME" jdbcType="VARCHAR" property="bridgeName" />
    <result column="BRIDGE_CODE" jdbcType="VARCHAR" property="bridgeCode" />
    <result column="COMPANY_NAME" jdbcType="VARCHAR" property="companyName" />
    <result column="MONITOR_UNIT_NAME" jdbcType="VARCHAR" property="monitorUnitName" />
    <result column="BRIDGE_ENGINEER" jdbcType="VARCHAR" property="bridgeEngineer" />
    <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="ROUTE_CODE" jdbcType="VARCHAR" property="routeCode" />
    <result column="ROUTE_NAME" jdbcType="VARCHAR" property="routeName" />
    <result column="ROUTE_TECH_GRADE" jdbcType="VARCHAR" property="routeTechGrade" />
    <result column="CENTER_STAKE" jdbcType="DECIMAL" property="centerStake" />
    <result column="TRAFFIC_DIRECTION" jdbcType="VARCHAR" property="trafficDirection" />
    <result column="SPAN_CLASSIFY" jdbcType="VARCHAR" property="spanClassify" />
    <result column="UNDERPASS_CHANNEL_NAME" jdbcType="VARCHAR" property="underpassChannelName" />
    <result column="UNDERPASS_CHANNEL_NUMBER" jdbcType="DECIMAL" property="underpassChannelNumber" />
    <result column="DESIGN_LOAD" jdbcType="VARCHAR" property="designLoad" />
    <result column="TRAFFIC_LOAD" jdbcType="VARCHAR" property="trafficLoad" />
    <result column="BRIDGE_SLOPE" jdbcType="DECIMAL" property="bridgeSlope" />
    <result column="PLANE_CURVE_RADIUS" jdbcType="DECIMAL" property="planeCurveRadius" />
    <result column="BUILD_DATE" jdbcType="TIMESTAMP" property="buildDate" />
    <result column="BUILD_UNIT" jdbcType="VARCHAR" property="buildUnit" />
    <result column="DESIGN_UNIT" jdbcType="VARCHAR" property="designUnit" />
    <result column="CONSTRUCTION_UNIT" jdbcType="VARCHAR" property="constructionUnit" />
    <result column="SUPERVISION_UNIT" jdbcType="VARCHAR" property="supervisionUnit" />
    <result column="NAVIGATION_GRADE" jdbcType="VARCHAR" property="navigationGrade" />
    <result column="PIER_COLLISION_AVOIDANCE" jdbcType="VARCHAR" property="pierCollisionAvoidance" />
    <result column="WHETHER_INTERCHANGE" jdbcType="VARCHAR" property="whetherInterchange" />
    <result column="WHETHER_NARROW_BRIDGE" jdbcType="VARCHAR" property="whetherNarrowBridge" />
    <result column="WHETHER_IN_LONG_SPAN_CATALOG" jdbcType="VARCHAR" property="whetherInLongSpanCatalog" />
    <result column="WHETHER_HEALTH_MONITOR_SYSTEM" jdbcType="VARCHAR" property="whetherHealthMonitorSystem" />
    <result column="WHETHER_SINGLE_COLUMN_PIER" jdbcType="VARCHAR" property="whetherSingleColumnPier" />
    <result column="CHARGE_PROPERTY" jdbcType="VARCHAR" property="chargeProperty" />
    <result column="SEISMIC_GRADE" jdbcType="VARCHAR" property="seismicGrade" />
    <result column="LNG_GCJ02" jdbcType="DECIMAL" property="lngGcj02" />
    <result column="LAT_GCJ02" jdbcType="DECIMAL" property="latGcj02" />
    <result column="TOTAL_LENGTH" jdbcType="DECIMAL" property="totalLength" />
    <result column="DECK_TOTAL_WIDTH" jdbcType="DECIMAL" property="deckTotalWidth" />
    <result column="LANE_WIDTH" jdbcType="DECIMAL" property="laneWidth" />
    <result column="DECK_CENTER_HEIGHT" jdbcType="DECIMAL" property="deckCenterHeight" />
    <result column="CLEAR_HEIGHT_ON_BRIDGE" jdbcType="DECIMAL" property="clearHeightOnBridge" />
    <result column="TOTAL_CHANNEL_WIDTH" jdbcType="DECIMAL" property="totalChannelWidth" />
    <result column="CHANNEL_CURVE_RADIUS" jdbcType="DECIMAL" property="channelCurveRadius" />
    <result column="SIDEWALK_WIDTH" jdbcType="DECIMAL" property="sidewalkWidth" />
    <result column="MEDIUM_BARRIER_GRADE" jdbcType="VARCHAR" property="mediumBarrierGrade" />
    <result column="GRADE_OF_SIDE_RAIL" jdbcType="VARCHAR" property="gradeOfSideRail" />
    <result column="GUARDRAIL_HEIGHT" jdbcType="DECIMAL" property="guardrailHeight" />
    <result column="CENTRAL_SEPARATOR_WIDTH" jdbcType="DECIMAL" property="centralSeparatorWidth" />
    <result column="DESIGN_FLOOD_LEVEL" jdbcType="DECIMAL" property="designFloodLevel" />
    <result column="DESIGN_FLOOD_FREQUENCY" jdbcType="VARCHAR" property="designFloodFrequency" />
    <result column="HISTORICAL_FLOOD_LEVEL" jdbcType="DECIMAL" property="historicalFloodLevel" />
    <result column="EARTHQUAKE_ACCELERATION" jdbcType="VARCHAR" property="earthquakeAcceleration" />
    <result column="SPAN_GROUPS" jdbcType="VARCHAR" property="spanGroups" />
    <result column="MAIN_SUPERSTRUCTURE_TYPE" jdbcType="VARCHAR" property="mainSuperstructureType" />
    <result column="PIER_TYPES" jdbcType="VARCHAR" property="pierTypes" />
    <result column="PIER_MATERIALS" jdbcType="VARCHAR" property="pierMaterials" />
    <result column="ABUTMENT_TYPES" jdbcType="VARCHAR" property="abutmentTypes" />
    <result column="ABUTMENT_MATERIALS" jdbcType="VARCHAR" property="abutmentMaterials" />
    <result column="BASE_TYPES" jdbcType="VARCHAR" property="baseTypes" />
    <result column="BASE_MATERIALS" jdbcType="VARCHAR" property="baseMaterials" />
    <result column="BEARING_TYPES" jdbcType="VARCHAR" property="bearingTypes" />
    <result column="EXPANSION_JOINT_TYPES" jdbcType="VARCHAR" property="expansionJointTypes" />
    <result column="DECK_PAVEMENT_TYPES" jdbcType="VARCHAR" property="deckPavementTypes" />
    <result column="FRONT_PHOTO_PATH" jdbcType="VARCHAR" property="frontPhotoPath" />
    <result column="FACADE_PHOTO_PATH" jdbcType="VARCHAR" property="facadePhotoPath" />
    <result column="TYPICAL_PHOTO_PATH" jdbcType="VARCHAR" property="typicalPhotoPath" />
    <result column="DESIGN_PAPERS_PATH" jdbcType="VARCHAR" property="designPapersPath" />
    <result column="DESIGN_FILES_PATH" jdbcType="VARCHAR" property="designFilesPath" />
    <result column="FINISH_PAPERS_PATH" jdbcType="VARCHAR" property="finishPapersPath" />
    <result column="CONSTRUCT_FILES_PATH" jdbcType="VARCHAR" property="constructFilesPath" />
    <result column="ACCEPTANCE_FILES_PATH" jdbcType="VARCHAR" property="acceptanceFilesPath" />
    <result column="ADMINISTRATION_FILES_PATH" jdbcType="VARCHAR" property="administrationFilesPath" />
    <result column="MAINTENANCE_CHECK_LEVEL" jdbcType="VARCHAR" property="maintenanceCheckLevel" />
    <result column="ROUTE_TYPE" jdbcType="VARCHAR" property="routeType" />
    <result column="WHETHER_CROSS_PROVINCE" jdbcType="VARCHAR" property="whetherCrossProvince" />
    <result column="ACROSS_TYPE" jdbcType="VARCHAR" property="acrossType" />
    <result column="ACROSS_NAME" jdbcType="VARCHAR" property="acrossName" />
    <result column="MAIN_MATERIAL" jdbcType="VARCHAR" property="mainMaterial" />
    <result column="DECK_WIDTH" jdbcType="DECIMAL" property="deckWidth" />
    <result column="TRAFFIC_MEASURE" jdbcType="VARCHAR" property="trafficMeasure" />
    <result column="MAX_SPAN" jdbcType="DECIMAL" property="maxSpan" />
    <result column="SPAN_TOTAL_LENGTH" jdbcType="DECIMAL" property="spanTotalLength" />
    <result column="ROUTE_CLASSIFY" jdbcType="VARCHAR" property="routeClassify" />
    <result column="PROPERTY" jdbcType="VARCHAR" property="property" />
    <result column="BUILD_YEAR" jdbcType="DECIMAL" property="buildYear" />
    <result column="COMPANY_PROPERTY" jdbcType="VARCHAR" property="companyProperty" />
    <result column="FUNCTION_TYPE" jdbcType="VARCHAR" property="functionType" />
    <result column="MAIN_BRIDGE_ID" jdbcType="VARCHAR" property="mainBridgeId" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BRIDGE_ID, BRIDGE_IDENTITY_CODE, BRIDGE_NAME, BRIDGE_CODE, COMPANY_NAME, MONITOR_UNIT_NAME, 
    BRIDGE_ENGINEER, AREA_CODE, ROUTE_CODE, ROUTE_NAME, ROUTE_TECH_GRADE, CENTER_STAKE, 
    TRAFFIC_DIRECTION, SPAN_CLASSIFY, UNDERPASS_CHANNEL_NAME, UNDERPASS_CHANNEL_NUMBER, 
    DESIGN_LOAD, TRAFFIC_LOAD, BRIDGE_SLOPE, PLANE_CURVE_RADIUS, BUILD_DATE, BUILD_UNIT, 
    DESIGN_UNIT, CONSTRUCTION_UNIT, SUPERVISION_UNIT, NAVIGATION_GRADE, PIER_COLLISION_AVOIDANCE, 
    WHETHER_INTERCHANGE, WHETHER_NARROW_BRIDGE, WHETHER_IN_LONG_SPAN_CATALOG, WHETHER_HEALTH_MONITOR_SYSTEM, 
    WHETHER_SINGLE_COLUMN_PIER, CHARGE_PROPERTY, SEISMIC_GRADE, LNG_GCJ02, LAT_GCJ02, 
    TOTAL_LENGTH, DECK_TOTAL_WIDTH, LANE_WIDTH, DECK_CENTER_HEIGHT, CLEAR_HEIGHT_ON_BRIDGE, 
    TOTAL_CHANNEL_WIDTH, CHANNEL_CURVE_RADIUS, SIDEWALK_WIDTH, MEDIUM_BARRIER_GRADE, 
    GRADE_OF_SIDE_RAIL, GUARDRAIL_HEIGHT, CENTRAL_SEPARATOR_WIDTH, DESIGN_FLOOD_LEVEL, 
    DESIGN_FLOOD_FREQUENCY, HISTORICAL_FLOOD_LEVEL, EARTHQUAKE_ACCELERATION, SPAN_GROUPS, 
    MAIN_SUPERSTRUCTURE_TYPE, PIER_TYPES, PIER_MATERIALS, ABUTMENT_TYPES, ABUTMENT_MATERIALS, 
    BASE_TYPES, BASE_MATERIALS, BEARING_TYPES, EXPANSION_JOINT_TYPES, DECK_PAVEMENT_TYPES, 
    FRONT_PHOTO_PATH, FACADE_PHOTO_PATH, TYPICAL_PHOTO_PATH, DESIGN_PAPERS_PATH, DESIGN_FILES_PATH, 
    FINISH_PAPERS_PATH, CONSTRUCT_FILES_PATH, ACCEPTANCE_FILES_PATH, ADMINISTRATION_FILES_PATH, 
    MAINTENANCE_CHECK_LEVEL, ROUTE_TYPE, WHETHER_CROSS_PROVINCE, ACROSS_TYPE, ACROSS_NAME, 
    MAIN_MATERIAL, DECK_WIDTH, TRAFFIC_MEASURE, MAX_SPAN, SPAN_TOTAL_LENGTH, ROUTE_CLASSIFY, 
    PROPERTY, BUILD_YEAR, COMPANY_PROPERTY, FUNCTION_TYPE, MAIN_BRIDGE_ID, SECOND_COMPANY, 
    ORG_CODE
  </sql>

  <select id="getBridgeData" resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqReportBridge">
    select c.*,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_REPORT_BRIDGE c
      <where>
        <if test="reportId != null and reportId != ''">
          c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
        </if>

        <if test="orgCode != null and orgCode != ''">
          AND c.ORG_CODE = #{orgCode}
        </if>

        <if test="secondCompany != null and secondCompany != ''">
          AND c.SECOND_COMPANY = #{secondCompany}
        </if>

        <if test="bridgeName != null and bridgeName != ''">
          and  c.BRIDGE_NAME like concat(concat('%',#{bridgeName}),'%')
        </if>
      </where>
    and exists(
      select 1 from (
      select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
      where exists(
        select 1 from (
        select * from GDGS.FW_RIGHT_ORG o
        where o.IS_DELETED = 0 and o.IS_ENABLE = 1
        start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
        connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
      )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>

  <select id="getBridgeInitInspect"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqInitialInspection">
    select c.*,
    (select br.BRDG_NAME from  T_BRDG_BRDGRECOG br
    where br.BRDGRECOG_ID = c.BRIDGE_IDENTITY_CODE and br.VALID_FLAG = 1 and ROWNUM = 1) bridgeName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_INITIAL_INSPECTION c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>

  <select id="getBridgeDailyInspect"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqDailyInspection">
    select c.*,
    (select br.BRDG_NAME from  T_BRDG_BRDGRECOG br
    where br.BRDGRECOG_ID = c.BRIDGE_IDENTITY_CODE and br.VALID_FLAG = 1 and ROWNUM = 1) bridgeName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_DAILY_INSPECTION c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>

  <select id="getBridgeOftenInspect"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqRoutineInspection">
    select c.*,
    (select br.BRDG_NAME from  T_BRDG_BRDGRECOG br
    where br.BRDGRECOG_ID = c.BRIDGE_IDENTITY_CODE and br.VALID_FLAG = 1 and ROWNUM = 1) bridgeName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_ROUTINE_INSPECTION c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>

  <select id="getBridgePeriodInspect"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqPeriodicDetection">
    select c.*,
    (select b.BRIDGE_NAME from BCTCMSDB.BRIDGE_IDENTIFIED_CODE b inner join T_BRDG_BRDGRECOG br on b.SRC_BRIDGE_ID = br.BRDGRECOG_ID
    where b.XQ_BRIDGE_IDENTIFIED_CODE = c.BRIDGE_IDENTITY_CODE and br.VALID_FLAG = 1 and ROWNUM = 1) bridgeName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_PERIODIC_DETECTION c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>

  <select id="getBridgeSpecialInspect"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqSpecialDetection">
    select c.*,
    (select br.BRDG_NAME from  T_BRDG_BRDGRECOG br
    where br.BRDGRECOG_ID = c.BRIDGE_IDENTITY_CODE and br.VALID_FLAG = 1 and ROWNUM = 1) bridgeName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_SPECIAL_DETECTION c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>

  <select id="getBridgeMaintenanceRecord"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqMaintenanceRecord">
    select c.*,
    (select br.BRDG_NAME from  T_BRDG_BRDGRECOG br
    where br.BRDGRECOG_ID = c.BRIDGE_IDENTITY_CODE and br.VALID_FLAG = 1 and ROWNUM = 1) bridgeName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) orgName
    from BCTCMSDB.XQ_MAINTENANCE_RECORD c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY = #{secondCompany}
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>
</mapper>