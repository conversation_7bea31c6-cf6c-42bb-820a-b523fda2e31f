<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.oftenCheck.mapper.DmFinspMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.oftenCheck.entity.DmFinsp">
            <id property="finspId" column="FINSP_ID" jdbcType="VARCHAR"/>
            <result property="finspCode" column="FINSP_CODE" jdbcType="VARCHAR"/>
            <result property="facilityCat" column="FACILITY_CAT" jdbcType="VARCHAR"/>
            <result property="mntOrgId" column="MNT_ORG_ID" jdbcType="VARCHAR"/>
            <result property="mntnOrgNm" column="MNTN_ORG_NM" jdbcType="VARCHAR"/>
            <result property="inspDate" column="INSP_DATE" jdbcType="TIMESTAMP"/>
            <result property="inspPerson" column="INSP_PERSON" jdbcType="VARCHAR"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="lineDirect" column="LINE_DIRECT" jdbcType="VARCHAR"/>
            <result property="startStake" column="START_STAKE" jdbcType="DECIMAL"/>
            <result property="endStake" column="END_STAKE" jdbcType="DECIMAL"/>
            <result property="structId" column="STRUCT_ID" jdbcType="VARCHAR"/>
            <result property="structName" column="STRUCT_NAME" jdbcType="VARCHAR"/>
            <result property="tcComment" column="TC_COMMENT" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="processinstid" column="PROCESSINSTID" jdbcType="DECIMAL"/>
            <result property="fileId" column="FILE_ID" jdbcType="VARCHAR"/>
            <result property="roadSection" column="ROAD_SECTION" jdbcType="VARCHAR"/>
            <result property="searchDept" column="SEARCH_DEPT" jdbcType="VARCHAR"/>
            <result property="inspEndDate" column="INSP_END_DATE" jdbcType="TIMESTAMP"/>
            <result property="finVersion" column="FIN_VERSION" jdbcType="VARCHAR"/>
            <result property="opinions" column="OPINIONS" jdbcType="VARCHAR"/>
            <result property="results" column="RESULTS" jdbcType="VARCHAR"/>
            <result property="weather" column="WEATHER" jdbcType="VARCHAR"/>
            <result property="dssNum" column="DSS_NUM" jdbcType="DECIMAL"/>
            <result property="slopeType" column="SLOPE_TYPE" jdbcType="VARCHAR"/>
            <result property="slopePosition" column="SLOPE_POSITION" jdbcType="VARCHAR"/>
            <result property="slopeFxdj" column="SLOPE_FXDJ" jdbcType="VARCHAR"/>
            <result property="slopeZhlx" column="SLOPE_ZHLX" jdbcType="VARCHAR"/>
            <result property="slopePhHywz" column="SLOPE_PH_HYWZ" jdbcType="VARCHAR"/>
            <result property="slopePhCkwz" column="SLOPE_PH_CKWZ" jdbcType="VARCHAR"/>
            <result property="slopeZkFw" column="SLOPE_ZK_FW" jdbcType="VARCHAR"/>
            <result property="slopeCzdx" column="SLOPE_CZDX" jdbcType="VARCHAR"/>
            <result property="projectType" column="PROJECT_TYPE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        FINSP_ID,FINSP_CODE,FACILITY_CAT,
        MNT_ORG_ID,MNTN_ORG_NM,INSP_DATE,
        INSP_PERSON,LINE_CODE,LINE_DIRECT,
        START_STAKE,END_STAKE,STRUCT_ID,
        STRUCT_NAME,TC_COMMENT,REMARK,
        STATUS,CREATE_USER_ID,CREATE_TIME,
        UPDATE_USER_ID,UPDATE_TIME,PROCESSINSTID,
        FILE_ID,ROAD_SECTION,SEARCH_DEPT,
        INSP_END_DATE,FIN_VERSION,OPINIONS,
        RESULTS,WEATHER,DSS_NUM,
        SLOPE_TYPE,SLOPE_POSITION,SLOPE_FXDJ,
        SLOPE_ZHLX,SLOPE_PH_HYWZ,SLOPE_PH_CKWZ,
        SLOPE_ZK_FW,SLOPE_CZDX,PROJECT_TYPE
    </sql>
</mapper>
