<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.DecisionRoadAgeMapper">

    <select id="selectDecisionRoadAge" resultType="com.hualu.highwaymaintenance.module.decision.domain.RoadAge">
        select PROJECT_NAME                                                                 as org_name,
               substr(min(a.BUILT_DATE), 0, 7)                                                 as open_date,
               min(LANE_NO)                                                                  as line_no,
               round(months_between(to_date(to_char(sysdate,'yyyy-MM'),'yyyy-MM'),
                                    case when length(replace(min(BUILT_DATE),'-')) = 7 then to_date(substr(min(BUILT_DATE),0,6),'yyyy-fmMM')
                                         else to_date(substr(min(BUILT_DATE),0,7),'yyyy-MM') end)/12) as road_age,
               sum(LINE_MILEAGE) as route_length
        from gdgs.PRO_ROUTE a
                 inner join gdgs.FW_RIGHT_ORG o on a.OPRT_ORG_CODE = o.ORG_CODE
                 inner join gdgs.FW_RIGHT_ORG oo on o.PARENT_ID = oo.ORG_CODE
        where oo.ORG_CODE = #{orgCode}
        group by PROJECT_NAME
    </select>

</mapper>
