<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper">
    <select id="getYearMeaSureMap" resultType="java.util.Map">
        /*SELECT PM.YEAR,ME.MEASURE_NAME,PM.START_STAKE as startStake,PM.END_STAKE,ME.THICKNESS,PM.ROUTE_CODE,D.START_STAKE AS MAXSTARTSTAKE FROM PBD_PAVEMENT_MAINTAIN PM
        JOIN PBD_PAVEMENT_MEASURE ME  ON ME.MEASURE_ID = PM.MEASURE_ID
        JOIN GDGS.BASE_LINE L ON PM.LINE_CODE=L.LINE_CODE
        JOIN PBD_STRUCT_INTRVL_UNIT D ON
        PM.ROUTE_CODE = D.ROUTE_CODE AND
        ((PM.START_STAKE - D.end_stake) * (PM.START_STAKE - D.start_stake) <![CDATA[<]]> 0 or
        (D.end_stake = PM.START_STAKE or D.start_stake = PM.START_STAKE))
        AND EXISTS(
                SELECT 1 FROM PBD_STRUCT_INTRVL_UNIT_HUN HUN WHERE D.UNIT_MARGE_ID = HUN.UNIT_MARGE_ID
            AND HUN.USERED=22
            )
        WHERE (PM.LINE_CODE='${lineCode}' OR L.LINE_ID='${lineCode}')
        AND PM.LINE_DIRECTION='${lane}'
        AND CASE WHEN PM.LINE_DIRECTION = '1' AND PM.LANE_TYPE='快车道'
                THEN '上行行车道2(A)' WHEN PM.LINE_DIRECTION = '2' AND PM.LANE_TYPE='快车道'
                    THEN '上行行车道2(B)' end = '${laneType}'
        AND ((PM.START_STAKE - ${endStake}) * (PM.START_STAKE- ${startStake}) <![CDATA[<]]> 0
        or (${endStake} = PM.START_STAKE or ${startStake} = PM.START_STAKE))
        ORDER BY PM.START_STAKE*/
        SELECT distinct PM.YEAR,
        ME.MEASURE_NAME,
        PM.START_STAKE as startStake,
        PM.END_STAKE,
        ME.THICKNESS,
        PM.ROUTE_CODE,
        D.START_STAKE  AS MAXSTARTSTAKE
        FROM PTCMSDB.PBD_PAVEMENT_MAINTAIN PM
        JOIN PTCMSDB.PBD_PAVEMENT_MEASURE ME ON ME.MEASURE_ID = PM.MEASURE_ID
        JOIN GDGS.BASE_LINE L ON PM.LINE_CODE = L.LINE_CODE
        JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT D ON
        PM.ROUTE_CODE = D.ROUTE_CODE AND
        ((PM.START_STAKE - D.end_stake) * (PM.START_STAKE - D.start_stake) <![CDATA[<]]> 0 or
        (D.end_stake = PM.START_STAKE or D.start_stake = PM.START_STAKE))
        AND EXISTS(
        SELECT 1
        FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_HUN HUN
        WHERE D.UNIT_MARGE_ID = HUN.UNIT_MARGE_ID
        AND HUN.USERED = 22
        )
        WHERE (PM.LINE_CODE = '${lineCode}' OR L.LINE_ID = '${lineCode}')
        AND PM.LINE_DIRECTION = '1'
        AND PM.LANE_TYPE = '${laneType}'
        AND ((PM.START_STAKE - ${endStake}) * (PM.START_STAKE - ${startStake}) <![CDATA[<]]> 0
        or (${endStake} = PM.START_STAKE or ${startStake} = PM.START_STAKE))
        ORDER BY PM.START_STAKE
    </select>

    <!--查询桥梁位置情况-->
    <select id="getBrigeStakeSureMap" resultType="java.util.Map">
        SELECT g.BRDG_NAME as MEASURE_NAME,g.CNTR_STAKE_NUM as startStake,round(g.CNTR_STAKE_NUM - ((br.BRDG_LEN / 2) /1000),2) startStake_substr,
               round(g.CNTR_STAKE_NUM + ((br.BRDG_LEN / 2) /1000),2) endStake
        FROM BCTCMSDB.t_brdg_brdgrecog g
                 join BCTCMSDB.t_brdg_techindex br on br.BRDGRECOG_ID=g.BRDGRECOG_ID
        where g.LINE_ID = '${lineId}' and G.BRDG_LINE_TYPE='L'
          AND g.OPRT_ORG_CODE = '${orgId}' and br.BRDG_LEN >= 100
          AND ((g.CNTR_STAKE_NUM - ${endStake}) * (g.CNTR_STAKE_NUM- ${startStake}) <![CDATA[<]]> 0
        or (${endStake} = g.CNTR_STAKE_NUM or ${startStake} = g.CNTR_STAKE_NUM))
        order by startStake
    </select>

    <select id="getLineId" resultType="java.lang.String">
        SELECT L.LINE_ID FROM GDGS.BASE_LINE L WHERE L.LINE_CODE='${lineId}' OR L.LINE_ID='${lineId}'
    </select>

    <select id="getClvrtStakeSureMap" resultType="java.util.Map">
       select DISTINCT mtb.TUNNEL_NAME as MEASURE_NAME,
                mtb.CNTR_STAKE_NUM     as startStake,
                mtb.TUNNEL_LINE_DIRECT as lineDirect
            from mtmsdb.mtms_tunnel_basic mtb
                     join GDGS.BASE_ROUTE_LOGIC L ON mtb.OPRT_ORG_CODE = L.OPRT_ORG_CODE
                     JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
            where LS.LINE_ID = '${lineId}'
              AND mtb.OPRT_ORG_CODE = '${orgId}'
              AND mtb.TUNNEL_LENGTH > 100
              AND mtb.TUNNEL_LINE_DIRECT=1
              AND ((mtb.CNTR_STAKE_NUM - ${endStake}) * (mtb.CNTR_STAKE_NUM- ${startStake}) <![CDATA[<]]> 0
                or (${endStake} = g.CNTR_STAKE_NUM or ${startStake} = g.CNTR_STAKE_NUM))
            order by CNTR_STAKE_NUM
    </select>
    <select id="getDesionDssTypeList" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        select F.REL_TASK_CODE as relTaskCode,u.lane,max(u.year) as year,u.pavement_type as pavementType,
        sum(decode(F.Dss_l,null,F.dss_a,DECODE(F.DSS_L,0,F.DSS_A,F.DSS_L))) as dr,F.DSS_TYPE as dssType,
        F.Dss_Degree as dssDegree,u.start_stake as stake,abs(u.start_stake-u.end_stake)*1000 as length,
        u.unit_marge_id as unitMargeId
        from  ptcmsdb.PBD_STRUCT_INTRVL_UNIT_NEW u join MEMSDB.DSS_INFO_DESION F on u.route_code=F.ROUTECODE
        join ptcmsdb.PTCD_HPAS_SD_PAVEMENT p on p.facility_cat=F.facility_cat and p.dss_type=F.dss_type
        and p.dss_degree=decode(f.dss_degree,null,'01','重','03','中','02','轻','01',f.dss_degree)
        join GDGS.BASE_ROUTE_LOGIC L ON F.ROUTECODE=L.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
        JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        WHERE u.PAVEMENT_TYPE=SUBSTR('${type}',3,4)
        and p.facility_cat='LM' and p.dss_type like'${type}%'
        and ORG.ID='${orgId}'
        and ((f.stake-u.start_stake)*(f.stake-u.end_stake)<![CDATA[<]]>0 or (case when (u.start_stake-u.end_stake) <![CDATA[<]]>0 then u.start_stake
        when (u.start_stake-u.end_stake)>0 then u.end_stake end=f.stake)) and u.lane=f.lane
        and ((f.stake - u.end_stake ) * (f.stake - u.start_stake) <![CDATA[<]]> 0 or (u.end_stake = f.stake or u.start_stake=f.stake))
        and p.MEANS = 0
        and u.usered=${usered} AND F.YEAR = '${year}'
        group by u.lane,F.REL_TASK_CODE,u.start_stake,u.end_stake,u.pavement_type,u.unit_marge_id,F.DSS_TYPE,
        F.Dss_Degree
    </select>
    <!--沥青百米检测数据抽取-->
    <select id="getDssInfoHunLq" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        select F.REL_TASK_CODE as relTaskCode,u.lane,max(u.year) as year,u.pavement_type as pavementType,
        sum(decode(F.Dss_l,null,F.dss_a,DECODE(F.DSS_L,0,F.DSS_A,F.DSS_L))) as dr,F.DSS_TYPE as dssType,F.Dss_Degree as dssDegree,u.start_stake as stake,
        abs(u.start_stake-u.end_stake)*1000 as length,u.unit_marge_id as unitMargeId,u.hun_marge_id as hunMargeId
        from  ptcmsdb.PBD_STRUCT_INTRVL_HUN_DESION u join MEMSDB.DSS_INFO_DESION F on u.routecode=F.ROUTECODE
        join ptcmsdb.PTCD_HPAS_SD_PAVEMENT p on p.facility_cat=F.facility_cat and p.dss_type=F.dss_type
        and p.dss_degree=decode(f.dss_degree,null,'01','重','03','中','02','轻','01',f.dss_degree)
        WHERE F.REL_TASK_CODE ='${orgId}'
        and p.facility_cat='LM' and p.dss_type like'${type}%'
        and ((f.stake-u.start_stake)*(f.stake-u.end_stake)<![CDATA[<]]>0 or (case when (u.start_stake-u.end_stake) <![CDATA[<]]>0 then u.start_stake
        when (u.start_stake-u.end_stake)<![CDATA[>]]>0 then u.end_stake end=f.stake)) and u.lane=f.lane
        and ((f.stake - u.end_stake) * (f.stake - u.start_stake) <![CDATA[<]]> 0 or (u.end_stake = f.stake or u.start_stake=f.stake))
        and p.MEANS = 0
        and F.YEAR ='${year}'
        and U.USERED = 22
        and exists(
          SELECT 1 FROM ptcmsdb.PBD_STRUCT_INTRVL_UNIT_NEW D WHERE U.UNIT_MARGE_ID=D.UNIT_MARGE_ID
          AND D.USERED=21
        )
        group by u.lane,F.REL_TASK_CODE,u.start_stake,u.end_stake,u.pavement_type,u.unit_marge_id,u.hun_marge_id,F.DSS_TYPE,
        F.Dss_Degree
    </select>
    <select id="getAllParentOrgName" resultType="com.hualu.highwaymaintenance.module.user.domain.FwRightOrg">
        SELECT ORG.ID as id, ORG.ORG_FULLNAME as orgFullName
        FROM GDGS.FW_RIGHT_ORG ORG
        WHERE ORG.ORG_LEVEL = 2
        AND ORG.ID <![CDATA[<>]]> '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
    </select>
    <select id="getOrgIdToAllPrj" resultType="java.util.Map">
        SELECT DISTINCT ORG.ID, D.PRJ_SNAME,ORG.ORG_NAME
        FROM PTCMSDB.TCC_INSP_PRJ_DESION D
            JOIN GDGS.BASE_ROUTE_LOGIC L ON D.MNG_ORG_ID=L.OPRT_ORG_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
        WHERE ORG.PARENT_ID = '${orgId}' ORDER BY ORG.ORG_NAME
    </select>
    <select id="getTcDetailDataList" resultType="com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000">
        SELECT PORG.ORG_NAME as orgId,ORG.ORG_NAME as prjId,
        LS.LINE_ALLNAME as lineName,
        PTCMSDB.FUNC_ZHZ(TC.START_STAKE) as rlStartStake,
        PTCMSDB.FUNC_ZHZ(TC.END_STAKE) as rlEndStake,
        abs(TC.START_STAKE-TC.END_STAKE) * 1000 AS length,
        TC.lane,
        '${year}' as year,
        DECODE(TC.PAVEMENT_TYPE,NULL,'沥青路面', DECODE(TC.PAVEMENT_TYPE,'LQ','沥青路面','SN','水泥路面')) as pavementType,
        '高速公路' as techGrade,
        '1200000' AS aadt,
        TC.STURTNAME as sturtName,
        20.5 as PAVEMENTTHK,
        20.5 as EFFECTWIDTH,
        3.75 as CALWIDTH,
        TC.pqi,
        TC.sci,
        TC.bci,
        TC.tci,
        TC.PCI_FRONT as pciFront,
        TC.PCI_BEHIND as pciBehind,
        TC.PR AS pr,
        TC.LCD AS lcd,
        TC.TCS as tcs,
        TC.rqi,
        TC.rdi,
        TC.sri,
        TC.pssi,
        TC.mqi,
        TC.PQI_TC_CODE as pqiTcCode,
        TC.SCI_TC_CODE as sciTcCode,
        TC.BCI_TC_CODE as bciTcCode,
        TC.TCI_TC_CODE as tciTcCode,
        TC.PCI_FRONT_TC_CODE as pciFrontTcCode,
        TC.PCI_BEHIND_TC_CODE AS pciBehindTcCode,
        TC.RQI_TC_CODE as rqiTcCode,
        TC.RDI_TC_CODE as rdiTcCode,
        TC.SRI_TC_CODE as sriTcCode,
        TC.PSSI_TC_CODE as pssiTcCode,
        TC.MQI_TC_CODE as mqiTcCode,
        TC.UNIT_MARGE_ID as unitMargeId,
        TC.pbi,
        TC.PBI_TC_CODE as pbiTcCode,
        TC.pwi,
        TC.PWI_TC_CODE as pwiTcCode,
        TC.RD_M as rd,
        TC.SFC_M as sfc,
        TC.BACKMAN_M as ssr,
        TC.IRI_M as iri,
        TC.PWI_WR as wr,
        TC.DR_FRONT as drFront,
        TC.DR_BEHIND AS drBehind
        FROM PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC
        JOIN GDGS.BASE_ROUTE_LOGIC L ON TC.ROUTE_CODE = L.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
        JOIN GDGS.FW_RIGHT_ORG PORG ON ORG.PARENT_ID = PORG.ID
        JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        WHERE ORG.ID = '${orgId}' AND TC.YEAR= '${year}'
        <if test="lineCode != null and lineCode != ''">
            AND (LS.LINE_CODE = '${lineCode}' OR LS.LINE_ID = '${lineCode}')
        </if> AND TC.USERED='${usered}'
        ORDER BY TC.lane, TC.START_STAKE
    </select>
    <select id="getHunsTcDetailDataList" resultType="com.hualu.highwaymaintenance.decision.entity.TcaTcDetail100">
        SELECT PORG.ORG_NAME as orgId,ORG.ORG_NAME as prjId,
               LS.LINE_SNAME as lineName,
               PTCMSDB.FUNC_ZHZ(DE.START_STAKE) as rlStartStake,
               PTCMSDB.FUNC_ZHZ(DE.END_STAKE) as rlEndStake,
               abs(DE.START_STAKE-DE.END_STAKE) * 1000 AS length,
               DE.lane,
               '${year}' as year,
               DECODE(DE.PAVEMENT_TYPE,NULL,'沥青路面', DECODE(DE.PAVEMENT_TYPE,'LQ','沥青路面','SN','水泥路面')) as pavementType,
               '高速公路' as techGrade,
               NVL(DE.MEASURENAME || DE.STRUTNAME || DE.REMARK, ' ') as sturtName,
               TC.pqi,
               TC.PCI_FRONT as pciFront,
               TC.PCI_BEHIND as pciBehind,
               TC.PR AS pr,
               TC.LCD AS lcd,
               TC.TCS as tcs,
               TC.rqi,
               TC.rdi,
               TC.sri,
               TC.pssi,
               TC.PQI_TC_CODE as pqiTcCode,
               TC.PCI_FRONT_TC_CODE as pciFrontTcCode,
               TC.PCI_BEHIND_TC_CODE AS pciBehindTcCode,
               TC.RQI_TC_CODE as rqiTcCode,
               TC.RDI_TC_CODE as rdiTcCode,
               TC.SRI_TC_CODE as sriTcCode,
               TC.PSSI_TC_CODE as pssiTcCode,
               TC.UNIT_MARGE_ID as unitMargeId,
               TC.pbi,
               TC.PBI_TC_CODE as pbiTcCode,
               TC.pwi,
               TC.PWI_TC_CODE as pwiTcCode,
               TC.RD_M as rd,
               TC.SFC_M as sfc,
               TC.BACKMAN_M as ssr,
               TC.IRI_M as iri,
               TC.PWI_WR as wr,
               TC.DR_FRONT as drFront,
               TC.DR_BEHIND AS drBehind
        FROM PTCMSDB.PTCD_TC_DETAIL_100_DESION TC
                 JOIN PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION DE
                      ON TC.HUN_MARGE_ID = DE.HUN_MARGE_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON DE.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                 JOIN GDGS.FW_RIGHT_ORG PORG ON ORG.PARENT_ID = PORG.ID
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        WHERE ORG.ID = '${orgId}' AND TC.YEARS= '${year}'
          AND (LS.LINE_CODE = '${lineCode}' OR LS.LINE_ID = '${lineCode}') AND DE.USERED='22'
                ORDER BY DE.lane, DE.START_STAKE
    </select>
    <select id="findGroupScore" resultType="java.util.Map">
        SELECT t.PRJ_ID                                            as prjId,
        l.LINE_CODE                                           as LINECODE,
        round(avg(t.${index}), 2)                                as PQI,
        '${index}' as INDEXS,
        round(sum(case
        when t.${index} >= 90 then
        abs(u.start_stake - u.end_stake) * 1000
        end) / 1000, 2)                                 as good,
        round(sum(case
        when t.${index} <![CDATA[<]]> 90 and
        t.${index} >= 80 then
        abs(u.start_stake - u.end_stake) * 1000
        end) / 1000, 2)                                 as find,
        round(sum(case
        when t.${index} <![CDATA[<]]> 80 and
        t.${index} >= 70 then
        abs(u.start_stake - u.end_stake) * 1000
        end) / 1000, 2)                                 as well,
        round(sum(case
        when t.${index} <![CDATA[<]]> 70 and
        t.${index} >= 60 then
        abs(u.start_stake - u.end_stake) * 1000
        end) / 1000, 2)                                 as cone,
        round(sum(case
        when t.${index} <![CDATA[<]]> 60 then
        abs(u.start_stake - u.end_stake) * 1000
        end) / 1000, 2)                                    rence,
        sum(abs(u.start_stake - u.end_stake) * 1000) / 1000 as sumCount,
        to_char(nvl(round(((sum(case
        when t.${index} <![CDATA[>=]]>90
        then abs(u.start_stake - u.end_stake) * 1000 end) / 1000) /
        (sum(case when t.${index} is not null then abs(u.start_stake - u.end_stake) * 1000 end) /
        1000)) * 100, 2), 0))           as goodLevel,
        to_char(nvl(round(((sum(case
        when t.${index} <![CDATA[>=]]>80
        then abs(u.start_stake - u.end_stake) * 1000 end) / 1000) /
        (sum(case when t.${index} is not null then abs(u.start_stake - u.end_stake) * 1000 end) /
        1000)) * 100, 2), 0))                             as goodFindLevel,
        to_char(nvl(round(((sum(case
        when t.${index} <![CDATA[<]]>70
        then abs(u.start_stake - u.end_stake) * 1000 end) / 1000) /
        (sum(case when t.${index} is not null then abs(u.start_stake - u.end_stake) * 1000 end) /
        1000)) * 100, 2), 0))                            as coneLevel
        from ptcmsdb.PTCD_TC_DETAIL_1000_DESION t
        join ptcmsdb.PBD_STRUCT_INTRVL_UNIT_DESION u
        on u.unit_marge_id = t.unit_marge_id
        join gdgs.base_route_logic logic on u.route_code = logic.route_code
        join GDGS.BASE_LINE L ON logic.LINE_CODE = l.LINE_CODE
        WHERE t.PRJ_ID = '${orgId}'
        AND u.lane = 'A'
        and u.USERED = '21' AND T.YEAR='${years}'
        and (L.line_id='${lineCode}' or L.line_code='${lineCode}')
        group by t.PRJ_ID, l.LINE_CODE
    </select>
    <select id="getPavementLqData" resultType="java.util.Map">
        select LOGIC.Line_Code,ptcmsdb.func_zhz(us.start_stake) as start_stake,ptcmsdb.func_zhz(us.end_stake) as end_stake,
        abs(us.start_stake-us.end_stake)*1000 as LEN,
        decode(tcd.${index}_tc_code, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ,us.lane,
        DECODE(us.pavement_type,'LQ','沥青','SN','水泥') as pavement_type,tcd.${typeNumber},tcd.${index},us.${unitMargeId}
        from ${tablaNameFirst} tcd
        join ${tablaNameSecond} us
        on tcd.${unitMargeId} = us.${unitMargeId}
        join GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE=us.${routeCode}
        where tcd.prj_id='${prjId}' AND us.PAVEMENT_TYPE='LQ' and LOGIC.LINE_CODE='${lineCode}'
        and us.lane in
        <foreach collection="lane" item="item" index="index" separator="," open="(" close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_1000_DESION'.toString()">
            AND tcd.YEAR='${years}'
        </if>
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_100_DESION'.toString()">
            AND tcd.YEARS='${years}'
        </if>
        order by us.lane,us.start_stake
    </select>

    <select id="leftJoinDssInfo" resultType="java.util.Map">
        SELECT  u.${unitMargeId} as ug,
        round(sum(decode( 'LMLQ-0001', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2) AS A1,
        round(sum(decode( 'LMLQ-0001', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'中','02','02','02'), '02', DSS_A, 0 ), null )),2) AS A2,
        round(sum(decode( 'LMLQ-0001', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2) AS A3,
        round(sum(decode( 'LMLQ-0002', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2) AS B1,
        round(sum(decode( 'LMLQ-0002', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2) AS B3,
        round(sum(decode( 'LMLQ-0003', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2) AS C1,
        round(sum(decode( 'LMLQ-0003', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2) AS C3,
        round(sum(decode( 'LMLQ-0004', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2) AS D1,
        round(sum(decode( 'LMLQ-0004', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2) AS D3,
        round(sum(decode( 'LMLQ-0007', p.DSS_TYPE, decode(DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2) AS E1,
        round(sum(decode( 'LMLQ-0007', p.DSS_TYPE, decode(DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2) AS E3,
        round(sum(decode( 'LMLQ-0008', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2) AS F1,
        round(sum(decode( 'LMLQ-0008', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2) AS F3,
        round(sum(decode( 'LMLQ-0009', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2) AS G1,
        round(sum(decode( 'LMLQ-0009', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2) AS G3,
        round(sum(decode( 'LMLQ-0005', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2) AS H1,
        round(sum(decode( 'LMLQ-0005', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2) AS H3,
        round(sum(decode( 'LMLQ-0006', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2) AS I1,
        round(sum(decode( 'LMLQ-0006', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2) AS I3,
        round(sum(decode( 'LMLQ-0010', p.DSS_TYPE, DSS_A, null )),2) AS J1,
        round(sum(decode( 'LMLQ-0011', p.DSS_TYPE, DSS_A, null )),2) AS K1,
        round(sum(decode( 'LMLQ-0012', p.DSS_TYPE, DSS_L, null )),2) AS L1
        from  ${tablaNameFirst} a
                             join ${tablaNameSecond} u
                                       on a.${unitMargeId} = u.${unitMargeId}
                             JOIN GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE = u.${routeCode}
                            JOIN (
                            SELECT
                            O.DSS_TYPE,O.DSS_A,O.DSS_L,O.DSS_DEGREE,O.LANE,O.facility_cat,O.STAKE
                            FROM MEMSDB.DSS_INFO_DESION O WHERE O.REL_TASK_CODE='${prjId}'
                            AND O.dss_type like 'LMLQ%'
                            and O.facility_cat = 'LM'
                            ) AA ON ((AA.stake - u.start_stake) * (AA.stake - u.end_stake) <![CDATA[<]]> 0 or u.start_stake = AA.stake) AND AA.LANE=U.LANE
                            JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.dss_type = AA.dss_type
                            and p.dss_degree =decode(AA.dss_degree,null,'01','重','03','中','02','轻','01',AA.dss_degree)
        WHERE  a.prj_id='${prjId}'
        and p.means=0
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_1000_DESION'.toString()">
            AND tcd.YEAR='${years}'
        </if>
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_100_DESION'.toString()">
            AND tcd.YEARS='${years}'
        </if>
        GROUP BY logic.LINE_ID, logic.LINE_CODE, logic.line_direct,u.LANE, u.${unitMargeId},u.start_stake, u.end_stake
        ORDER BY u.lane,u.start_stake
    </select>

    <select id="leftJoinSnDssInfo" resultType="java.util.Map">
        SELECT  u.${unitMargeId} as ug,
        to_number(round(sum(decode( 'LMSN-0001', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2)) AS A1,
        to_number(round(sum(decode( 'LMSN-0001', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2)) AS A3,
        to_number(round(sum(decode( 'LMSN-0002', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2)) AS B1,
        to_number(round(sum(decode( 'LMSN-0002', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'中','02','02','02'), '02', DSS_L, 0 ), null )),2)) AS B2,
        to_number(round(sum(decode( 'LMSN-0002', p.DSS_TYPE, decode(DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2)) AS B3,
        to_number(round(sum(decode( 'LMSN-0003', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_A, 0 ), null )),2)) AS C1,
        to_number(round(sum(decode( 'LMSN-0003', p.DSS_TYPE, decode(DECODE(AA.DSS_DEGREE,'中','02','02','02'), '02', DSS_A, 0 ), null )),2))AS C2,
        to_number(round(sum(decode( 'LMSN-0003', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_A, 0 ), null )),2)) AS C3,
        to_number(round(sum(decode( 'LMSN-0004', p.DSS_TYPE, decode(DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2)) AS D1,
        to_number(round(sum(decode( 'LMSN-0004', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2)) AS D3,
        to_number(round(sum(decode( 'LMSN-0009', p.DSS_TYPE, DSS_A, 0 )),1)) AS E1,
        to_number(round(sum(decode( 'LMSN-0006', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2))  AS F1,
        to_number(round(sum(decode( 'LMSN-0006', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'中','02','02','02'), '02', DSS_L, 0 ), null )),2))  AS F2,
        to_number(round(sum(decode( 'LMSN-0006', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2))  AS F3,
        to_number(round(sum(decode( 'LMSN-0007', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'轻','01','01','01'), '01', DSS_L, 0 ), null )),2))  AS G1,
        to_number(round(sum(decode( 'LMSN-0007', p.DSS_TYPE, decode( DECODE(AA.DSS_DEGREE,'重','03','03','03'), '03', DSS_L, 0 ), null )),2))  AS G3,
        to_number(round(sum(decode( 'LMSN-0008', p.DSS_TYPE, DSS_A, null )),2)) AS H1,
        to_number(round(sum(decode( 'LMSN-0005', p.DSS_TYPE, DSS_L, null )),2)) AS I1,
        to_number(round(sum(decode( 'LMSN-0010', p.DSS_TYPE, DSS_A, null )),2))  AS J1,
        to_number(round(sum(decode( 'LMSN-0011', p.DSS_TYPE, DSS_A, null )),2))  AS K1,
        to_number(round(sum(decode( 'LMSN-0012', p.DSS_TYPE, DSS_L, null )),2))  AS L1
        from  ${tablaNameFirst} a
                             join ${tablaNameSecond} u
                                       on a.${unitMargeId} = u.${unitMargeId}
                             JOIN GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE = u.${routeCode}
                            JOIN (
                            SELECT
                            O.DSS_TYPE,O.DSS_A,O.DSS_L,O.DSS_DEGREE,O.LANE,O.facility_cat,O.STAKE
                            FROM MEMSDB.DSS_INFO_DESION O WHERE O.REL_TASK_CODE='${prjId}'
                            AND O.dss_type like 'LMSN%'
                            and O.facility_cat = 'LM'
                            ) AA ON ((AA.stake - u.start_stake) * (AA.stake - u.end_stake) <![CDATA[<]]> 0 or u.start_stake = AA.stake) AND AA.LANE=U.LANE
                            JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.dss_type = AA.dss_type
                            and p.dss_degree =decode(AA.dss_degree,null,'01','重','03','中','02','轻','01',AA.dss_degree)
        WHERE  a.prj_id='${prjId}'
        and p.means=0
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_1000_DESION'.toString()">
            AND tcd.YEAR='${years}'
        </if>
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_100_DESION'.toString()">
            AND tcd.YEARS='${years}'
        </if>
        GROUP BY logic.LINE_ID, logic.LINE_CODE, logic.line_direct,u.LANE, u.${unitMargeId},u.start_stake, u.end_stake
        ORDER BY u.lane,u.start_stake
    </select>
    <select id="getPavementSnData" resultType="java.util.Map">
        select
        abs(us.start_stake-us.end_stake)*1000 as LEN,
        line.line_allname,
        us.LANE,
        LOGIC.LINE_CODE,
        DECODE(us.pavement_type,'LQ','沥青','SN','水泥') as pavement_type,
        ptcmsdb.func_ZHZ ( us.start_stake ) AS SS,
        ptcmsdb.func_ZHZ ( us.start_stake ) AS start_stake,
        ptcmsdb.func_ZHZ ( us.end_stake ) AS END_STAKE,
        decode(tcd.${index}_tc_code, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ,
        us.lane,
        tcd.${typeNumber} as DR,
        tcd.${index} as PCI,
        us.${unitMargeId}
        from ${tablaNameFirst} tcd join ${tablaNameSecond} us on tcd.${unitMargeId}=us.${unitMargeId}
        join GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE=us.${routeCode}
        join gdgs.base_line line on LOGIC.line_code=line.line_code
        where tcd.prj_id='${prjId}' and LOGIC.LINE_CODE='${lineCode}'
        and us.lane in
        <foreach collection="lane" item="item" index="index" separator="," open="(" close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_1000_DESION'.toString()">
            AND tcd.YEAR='${years}'
        </if>
        <if test="tablaNameFirst == 'PTCD_TC_DETAIL_100_DESION'.toString()">
            AND tcd.YEARS='${years}'
        </if>
        AND us.PAVEMENT_TYPE='SN'
        order by us.lane,us.start_stake
    </select>

    <select id="getHunTcDetailExcelExport" resultType="java.util.Map">
        SELECT ptcmsdb.func_zhz(HUN.START_STAKE) as START_STAKE1,HUN.START_STAKE,ptcmsdb.func_zhz(HUN.END_STAKE) as END_STAKE, HUN.LANE, TC.${typeNumber},TC.${index},
        DECODE(TC.${index}_TC_CODE,1,'优',2,'良',3,'中',4,'次',5,'差', TC.${index}_TC_CODE) AS ${index}_TC_CODE,
        DECODE(HUN.PAVEMENT_TYPE,'LQ','沥青','SN','水泥') AS PAVEMENT_TYPE,'' AS REMARK FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN
        JOIN PTCMSDB.PTCD_TC_DETAIL_100_DESION TC ON HUN.HUN_MARGE_ID=TC.HUN_MARGE_ID
        JOIN GDGS.BASE_ROUTE_PHYSICS PCS ON HUN.ROUTECODE=PCS.ROUTE_CODE
        AND HUN.USERED=22 AND TC.PRJ_ID='${prjId}' and PCS.LINE_CODE='${lineCode}'
        AND HUN.LANE IN ('${laneUp}','${laneDown}')
        <if test="index == 'RDI'.toString()">
            AND HUN.PAVEMENT_TYPE <![CDATA[<>]]>'SN'
        </if>
        AND TC.YEARS='${years}'
        ORDER BY HUN.LANE,HUN.START_STAKE
    </select>

    <select id="getTcDetailFail" resultType="java.util.Map">
        SELECT ptcmsdb.func_zhz(HUN.START_STAKE) as START_STAKE ,
        HUN.LANE,
        ABS(HUN.START_STAKE - HUN.END_STAKE)*1000 as LENGTH,
        TC.${typeNumber},
        TC.${index},
        DECODE(TC.${index}_TC_CODE, 1, '优', 2, '良', 3, '中', 4, '次', 5, '差', TC.${index}_TC_CODE) ${index}_TC_CODE,
        DECODE(HUN.PAVEMENT_TYPE,'LQ','沥青','水泥') AS PAVEMENT_TYPE
        FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN
        JOIN PTCMSDB.PTCD_TC_DETAIL_100_DESION TC ON HUN.HUN_MARGE_ID = TC.HUN_MARGE_ID
        JOIN GDGS.BASE_ROUTE_PHYSICS PCS ON HUN.ROUTECODE = PCS.ROUTE_CODE
        AND HUN.USERED = 22 AND TC.PRJ_ID ='${prjId}' and PCS.LINE_CODE='${lineCode}'
        AND TC.${index}_TC_CODE >=3 and HUN.LANE IN
        <foreach collection="arr" item="item" index="index" separator="," open="(" close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="index == 'RDI'.toString()">
            AND HUN.PAVEMENT_TYPE <![CDATA[<>]]>'SN'
        </if>
        AND TC.YEARS='${years}'
        ORDER BY HUN.LANE, HUN.START_STAKE
    </select>

    <select id="getHistorySecore" resultType="java.util.Map">
        SELECT TC.${typeNumber},HUN.UNIT_MARGE_ID
        FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION HUN
        JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC ON HUN.UNIT_MARGE_ID = TC.UNIT_MARGE_ID
        JOIN GDGS.BASE_ROUTE_PHYSICS PCS ON HUN.ROUTE_CODE = PCS.ROUTE_CODE
        JOIN PTCMSDB.TCC_INSP_PRJ TIP on TC.PRJ_ID = TIP.PRJ_ID
        JOIN GDGS.BASE_LINE L ON PCS.LINE_CODE=L.LINE_CODE
        WHERE TIP.PRJYEAR='${prjYear}'
        AND PCS.ROUTE_CODE  IN
        <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
            '${lineId}'
        </foreach>
        AND (PCS.LINE_CODE='${lineCode}' OR L.LINE_ID='${lineCode}')
        AND HUN.LANE='${lane}'
        ORDER BY HUN.LANE, HUN.START_STAKE
    </select>

    <select id="getYearMeaSureNewMap" resultType="java.util.Map">
        SELECT PM.YEAR,ME.MEASURE_NAME,PM.START_STAKE,PM.END_STAKE,ME.THICKNESS FROM PTCMSDB.PBD_PAVEMENT_MAINTAIN PM
        JOIN PTCMSDB.PBD_PAVEMENT_MEASURE ME  ON ME.MEASURE_ID = PM.MEASURE_ID
        JOIN GDGS.BASE_LINE L ON PM.LINE_CODE=L.LINE_CODE
        WHERE (PM.LINE_CODE='${lineCode}' OR L.LINE_ID='${lineCode}') AND PM.LINE_DIRECTION='${lane}' AND PM.LANE_TYPE='${laneType}'
        AND PM.ROUTE_CODE IN
        <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
            '${lineId}'
        </foreach>
    </select>

    <select id="getTcDetailExcelExport" resultType="java.util.Map">
        select ptcmsdb.func_zhz(u.start_stake) as start_stake,
        ptcmsdb.func_zhz(u.end_stake) as end_stake,
        line.line_allname as lineName,
        abs(u.start_stake-u.end_stake)*1000 as length,
        decode(u.pavement_type,'LQ','沥青','SN','水泥') as PAVEMENT_TYPE,
        da.${index},
        decode(da.${index}_tc_code,'1','优','2','良','3','中','4','次','5','差','') as ${index}_tc_code_,
        u.lane,
        a.*
        from ptcmsdb.pbd_struct_intrvl_unit_DESION u join (select
        hun.UNIT_MARGE_ID,hun.LANE,
        to_char(avg(d.${typeNumber}),'fm90.099') as avgiri_m,
        to_char(sum((case hun.index_length when 0 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri0,
        to_char(sum((case hun.index_length when 1 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri1,
        to_char(sum((case hun.index_length when 2 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri2,
        to_char(sum((case hun.index_length when 3 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri3,
        to_char(sum((case hun.index_length when 4 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri4,
        to_char(sum((case hun.index_length when 5 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri5,
        to_char(sum((case hun.index_length when 6 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri6,
        to_char(sum((case hun.index_length when 7 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri7,
        to_char(sum((case hun.index_length when 8 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri8,
        to_char(sum((case hun.index_length when 9 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri9,
        to_char(sum((case hun.index_length when 10 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri10,
        to_char(sum((case hun.index_length when 11 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri11,
        to_char(sum((case hun.index_length when 12 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri12,
        to_char(sum((case hun.index_length when 13 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri13,
        to_char(sum((case hun.index_length when 14 then NVL(d.${typeNumber},null) else null end)),'fm90.099') as iri14
        from ptcmsdb.ptcd_tc_detail_100_DESION d join ptcmsdb.pbd_struct_intrvl_hun_DESION hun on d.hun_marge_id=hun.hun_marge_id
        left join GDGS.base_datathird_dic dic on dic.attribute_code=hun.lane and attribute_item='LANE'
        where d.prj_id='${prjId}' and hun.lane in ('${lane}') and d.YEARS='${years}'
        group by hun.unit_marge_id,hun.lane,dic.attribute_value) a on u.unit_marge_id=a.UNIT_MARGE_ID
        join ptcmsdb.PTCD_TC_DETAIL_1000_DESION da on u.unit_marge_id= a.unit_marge_id
        join gdgs.base_route_logic logic on u.route_code=logic.route_code
        join gdgs.base_line line on logic.line_id=line.line_id
        where da.unit_marge_id=a.unit_marge_id
        and da.prj_id='${prjId}' and logic.LINE_CODE='${lineCode}'  and da.YEAR='${years}'
        <if test="index == 'RDI'.toString()">
            AND u.PAVEMENT_TYPE <![CDATA[<>]]>'SN'
        </if>
        ORDER BY logic.line_code,logic.Line_Direct,a.lane,u.start_stake
    </select>

    <select id="getUnitToStake" resultType="java.util.Map">
        SELECT D.UNIT_MARGE_ID,D.START_STAKE,D.END_STAKE
        FROM PBD_STRUCT_INTRVL_UNIT_DESION D
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON D.ROUTE_CODE = L.ROUTE_NAME
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        WHERE (LS.LINE_CODE='${lineCode}' OR LS.LINE_ID='${lineCode}')
    </select>

    <select id="getTcDetailSumReport" resultType="java.util.Map">
        SELECT
        tc.lane,
        <if test="pavement != null and pavement != ''">
            TO_CHAR(wm_concat(distinct decode(unit.pavement_type,'LQ','沥青','SN','水泥'))) as PAVEMENT_TYPE,
        </if>
        nvl(round(avg(tc.${index}),2),0) as ${index},
        nvl(sum(case when tc.${index}<![CDATA[>=]]>90 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) as good,
        nvl(sum(case when tc.${index}<![CDATA[<]]>90 and tc.${index}<![CDATA[>=]]>80 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) as find,
        nvl(sum(case when tc.${index}<![CDATA[<]]>80 and tc.${index}<![CDATA[>=]]>70 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) as well,
        nvl(sum(case when tc.${index}<![CDATA[<]]>70 and tc.${index}<![CDATA[>=]]>60 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) as cone,
        nvl(sum(case when tc.${index}<![CDATA[<]]>60 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) rence,
        nvl(sum(DECODE(unit.LANE, 'A', abs(unit.start_stake - unit.end_stake) * 1000, 'B',
        abs(unit.start_stake - unit.end_stake) * 1000 , 'C',
        abs(unit.start_stake - unit.end_stake) * 1000,'D',
        abs(unit.start_stake - unit.end_stake) * 1000,'E',
        abs(unit.start_stake - unit.end_stake) * 1000,'F',
        abs(unit.start_stake - unit.end_stake) * 1000,'G',
        abs(unit.start_stake - unit.end_stake) * 1000,'H',
        abs(unit.start_stake - unit.end_stake) * 1000
        )) / 1000, 0)                       as PD_length,
        nvl(sum(case when tc.${index}<![CDATA[>=]]>80 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) as top_find,
        nvl(sum(case when tc.${index}<![CDATA[<]]>70 then abs(unit.start_stake-unit.end_stake)*1000 end)/1000,0) as Secondary
        from ptcmsdb.PTCD_TC_DETAIL_1000_desion tc
        join  ptcmsdb.pbd_struct_intrvl_unit_desion unit on unit.unit_marge_id =tc.unit_marge_id
        join gdgs.base_route_logic logic on unit.route_code=logic.route_code
        join gdgs.base_line line on logic.line_id=line.line_id
        WHERE (tc.PRJ_ID ='${prjId}')  and logic.LINE_CODE='${lineCode}'
        AND tc.lane in
        <foreach collection="lane" item="item" index="index" separator="," open="(" close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        <if test="index == 'RDI'.toString()">
            AND unit.PAVEMENT_TYPE <![CDATA[<>]]>'SN'
        </if>
        and tc.${index} is not null and tc.year='${years}'
        group by tc.PRJ_ID, logic.LINE_ID,logic.LINE_CODE,line.line_allname,logic.line_direct${pavement},tc.lane
        order by logic.LINE_ID,tc.lane
    </select>
    <select id="getCarCheckMessage" resultType="java.util.Map">
        SELECT ptcmsdb.func_zhz(D.START_STAKE) as START_STAKE,ptcmsdb.func_zhz(D.END_STAKE) as END_STAKE
        ,D.PBI_HIGHT,D.PB_HIGHT_LEFT,D.PB_HIGHT_RIGHT FROM PTCMSDB.PTCD_CAR_DATA_DESION D JOIN GDGS.BASE_ROUTE_PHYSICS P ON D.ROUTECODE=P.ROUTE_CODE
        JOIN GDGS.BASE_LINE LINE ON P.LINE_CODE=LINE.LINE_CODE
        WHERE D.PRJ_ID='${prjId}' AND D.LANE='${lane}' and (P.LINE_CODE='${lineCode}' OR LINE.LINE_ID='${lineCode}')
        AND D.YEAR='${years}'
        ORDER BY D.START_STAKE
    </select>
    <select id="getPbiCarCheckOrder" resultType="java.util.Map">
        SELECT distinct
        ptcmsdb.func_zhz(d.START_STAKE) as copyStartStake,
        u.START_STAKE as USTART_STAKE,
        ptcmsdb.func_zhz(u.START_STAKE) as START_STAKE,
        ptcmsdb.func_zhz(u.END_STAKE) as END_STAKE,
        abs(u.START_STAKE - u.END_STAKE)*1000 as LEN,
        u.lane,
        DECODE(LOGIC.LINE_DIRECT,'1','上行','下行') as LINE_DIRECT,
        u.unit_marge_id           as unitMargeId,
        nvl(d.pbi_hight,0) pbi_hight,
        DECODE(u.pavement_type,'LQ','沥青路面','水泥路面')           as pavementType,
        tc.PBI,
        decode(tc.PBI_TC_CODE, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ
        FROM PTCMSDB.PTCD_TC_DETAIL_1000_DESION tc
        join PTCMSDB.pbd_struct_intrvl_unit_DESION u on u.UNIT_MARGE_ID=tc.UNIT_MARGE_ID
        and exists(
        SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN WHERE u.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID
        and HUN.USERED=22
        )
        left join PTCMSDB.ptcd_car_data_DESION d on u.ROUTE_CODE=d.ROUTECODE and d.PRJ_ID=tc.PRJ_ID
        and ((d.start_stake - u.end_stake) * (d.start_stake - u.start_stake) <![CDATA[<]]> 0 or
        u.end_stake = d.start_stake)
        and substr(d.lane, -1, 1)=u.lane and u.route_code = d.routecode
        join GDGS.BASE_ROUTE_LOGIC LOGIC ON u.ROUTE_CODE=LOGIC.ROUTE_CODE
        where tc.prj_id = '${prjId}'
        <if test="code != null">
            and tc.PBI_TC_CODE >= 3
            and nvl(d.PBI_HIGHT,0)>=50
        </if>
        and u.lane in
        <foreach collection="lane" item="lineId" index="index" open="(" close=")" separator=",">
            '${lineId}'
        </foreach>
        and LOGIC.LINE_CODE='${lineCode}'
        ORDER BY USTART_STAKE,U.UNIT_MARGE_ID,copyStartStake
    </select>
    <select id="getTcDetailTableMqi" resultType="java.util.Map">
        SELECT LINE.LINE_ALLNAME,UN.LANE,UN.LANE || ptcmsdb.func_zhz(UN.START_STAKE) AS START_STAKE,L.LINE_CODE,
        abs(un.START_STAKE-un.END_STAKE) * 1000 as LENGTH,DECODE(UN.PAVEMENT_TYPE,'LQ','沥青','水泥') AS PAVEMENTYPE,
       TC.MQI,decode(tc.PQI_TC_CODE, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS PQIDJ,
       decode(tc.MQI_TC_CODE, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ,
       TC.SCI,TC.PQI,TC.PCI_FRONT,TC.PCI_BEHIND,TC.RQI,TC.RDI,TC.PBI,TC.SRI,TC.PSSI,TC.BCI,TC.TCI,PORG.ORG_FULLNAME
       FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION UN JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC ON UN.UNIT_MARGE_ID=TC.UNIT_MARGE_ID
       JOIN GDGS.BASE_ROUTE_LOGIC L ON UN.ROUTE_CODE=L.ROUTE_CODE
       JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
       JOIN GDGS.FW_RIGHT_ORG PORG ON ORG.PARENT_ID=PORG.ID
       JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE=LINE.LINE_CODE
       WHERE TC.PRJ_ID='${prjId}' AND UN.LANE IN ('A','B') AND TC.YEAR='${years}'
       ORDER BY LINE.LINE_ALLNAME,UN.LANE,UN.START_STAKE
    </select>

    <select id="getPssiCheckOrder" resultType="java.util.Map">
		SELECT distinct
		L.LINE_ALLNAME,
		ptcmsdb.func_zhz(d.STAKE) as copyStartStake,
		u.START_STAKE as USTART_STAKE,
		u.lane || ptcmsdb.func_zhz(u.START_STAKE) as START_STAKE,
		u.lane || ptcmsdb.func_zhz(u.END_STAKE) as END_STAKE,
		abs(u.START_STAKE - u.END_STAKE)*1000 as LEN,
		u.lane,
		DECODE(LOGIC.LINE_DIRECT,'1','上行','下行') as LINE_DIRECT,
		u.unit_marge_id           as unitMargeId,
		to_char(d.BACKMAN_M, 'fm990.90') as BACKMAN_M,
		DECODE(u.pavement_type,'LQ','沥青路面','水泥路面')           as pavementType,
		decode(tc.PSSI, 0,'0',100,'100',to_char(tc.PSSI,'fm990.90')) as PSSI,
		decode(tc.PSSI_TC_CODE, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ,
		tc.BACKMAN_M as BACKMAN_M_CODE,u."NeNumber"
		FROM PTCMSDB.PTCD_DEFLECTION_DATA_DESION d
		join PTCMSDB.pbd_struct_intrvl_unit_DESION u on u.route_code = d.routecode
		and exists(
		SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN WHERE u.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID
		and HUN.USERED=22
		)
		join PTCMSDB.PTCD_TC_DETAIL_1000_DESION tc on u.UNIT_MARGE_ID=tc.UNIT_MARGE_ID and d.PRJ_ID=tc.PRJ_ID
		join GDGS.BASE_ROUTE_LOGIC LOGIC ON u.ROUTE_CODE=LOGIC.ROUTE_CODE
		join GDGS.BASE_LINE L ON LOGIC.LINE_CODE = L.LINE_CODE
		where
		((d.STAKE - u.end_stake) * (d.STAKE - u.start_stake) <![CDATA[<]]> 0 or
		u.end_stake = d.STAKE)
		and u.route_code = d.routecode
		and d.prj_id = '${prjId}'
		and substr(d.lane
		, -1
		, 1)=u.lane
		and (L.LINE_CODE='${lineCode}' or L.LINE_ID='${lineCode}')
		and u.lane in ('A','B') and tc.YEAR='${years}'
		ORDER BY U.LANE,U.UNIT_MARGE_ID,copyStartStake
	</select>

    <select id="getExportExcelSci" resultType="java.util.Map">
        select DECODE(LOGIC.LINE_DIRECT,'1','上行','2','下行') as LINEDIRECT,LINE.LINE_ALLNAME,LOGIC.Line_Code,ptcmsdb.func_zhz(us.start_stake) as start_stake,ptcmsdb.func_zhz(us.end_stake) as end_stake ,abs(us.start_stake-us.end_stake)*1000 as LENGTH,
        decode(tcd.SCI_tc_code, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ,
        us.pavement_type,tcd.sci,us.unit_marge_id,nvl(ljbg,0) as ljbg, nvl(lj1,0) as lj1, nvl(lj2,0) as lj2, nvl(bp1,0) as bp1,
        nvl(bp2,0) as bp2, nvl(bp3,0) as bp3, nvl(sh1,0) as sh1,nvl(sh2,0) as sh2, nvl(sh3,0) as sh3, nvl(lg1,0) as lg1, nvl(lg2,0) as lg2,
        nvl(lg3,0) as lg3, nvl(lys,0) as lys, nvl(lc1,0) as lc1, nvl(lc2,0) as lc2, nvl(lc3,0) as lc3, nvl(ps1,0) as ps1, nvl(ps2,0) as ps2, nvl(ps3,0) as ps3
        from ptcmsdb.PTCD_TC_DETAIL_1000_desion tcd
        join ptcmsdb.pbd_struct_intrvl_unit_desion us
        on tcd.unit_marge_id=us.unit_marge_id
        left  join (SELECT  u.unit_marge_id as ug,
        sum((case when f.DSS_TYPE = 'LJ-0001' then f.DSS_N else 0 end)) as ljbg
        ,sum((case when f.DSS_TYPE='LJ-0002' and p.DSS_DEGREE='01' then f.DSS_A else 0 end)) as lj1
        ,sum((case when f.DSS_TYPE='LJ-0002' and p.DSS_DEGREE='03' then f.DSS_A else 0 end)) as lj2
        ,sum((case when f.DSS_TYPE='LJ-0003' and p.DSS_DEGREE='01' then f.DSS_N else 0 end)) as bp1
        ,sum((case when f.DSS_TYPE='LJ-0003' and p.DSS_DEGREE='02' then f.DSS_N else 0 end)) as bp2
        ,sum((case when f.DSS_TYPE='LJ-0003' and p.DSS_DEGREE='03' then f.DSS_N else 0 end)) as bp3
        ,sum((case when f.DSS_TYPE='LJ-0004' and p.DSS_DEGREE='01' then f.DSS_N else 0 end)) as sh1
        ,sum((case when f.DSS_TYPE='LJ-0004' and p.DSS_DEGREE='02' then f.DSS_N else 0 end)) as sh2
        ,sum((case when f.DSS_TYPE='LJ-0004' and p.DSS_DEGREE='03' then f.DSS_N else 0 end)) as sh3
        ,sum((case when f.DSS_TYPE='LJ-0005' and p.DSS_DEGREE='01' then f.DSS_N else 0 end)) as lg1
        ,sum((case when f.DSS_TYPE='LJ-0005' and p.DSS_DEGREE='02' then f.DSS_N else 0 end)) as lg2
        ,sum((case when f.DSS_TYPE='LJ-0005' and p.DSS_DEGREE='03' then f.DSS_N else 0 end)) as lg3
        ,sum((case when f.DSS_TYPE='LJ-0006' then f.DSS_L else 0 end)) as lys
        ,sum((case when f.DSS_TYPE='LJ-0007' and p.DSS_DEGREE='01' then f.DSS_N else 0 end)) as lc1
        ,sum((case when f.DSS_TYPE='LJ-0007' and p.DSS_DEGREE='02' then f.DSS_N else 0 end)) as lc2
        ,sum((case when f.DSS_TYPE='LJ-0007' and p.DSS_DEGREE='03' then f.DSS_N else 0 end)) as lc3
        ,sum((case when f.DSS_TYPE='LJ-0008' and p.DSS_DEGREE='01' then f.DSS_N else 0 end)) as ps1
        ,sum((case when f.DSS_TYPE='LJ-0008' and p.DSS_DEGREE='02' then f.DSS_N else 0 end)) as ps2
        ,sum((case when f.DSS_TYPE='LJ-0008' and p.DSS_DEGREE='03' then f.DSS_N else 0 end)) as ps3
        from  ptcmsdb.PTCD_TC_DETAIL_1000_desion a left join ptcmsdb.pbd_struct_intrvl_unit_desion u
        on a.unit_marge_id = u.unit_marge_id
        JOIN GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE=u.ROUTE_CODE
        left JOIN MEMSDB.DSS_INFO_desion f ON  f.routecode =LOGIC.route_code
        left JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.dss_type=f.dss_type
        WHERE  a.prj_id='${prjId}'
        and f.rel_task_code='${prjId}'
        AND f.facility_cat='LJ' and p.means=0
        and ((f.stake-u.start_stake)*(f.stake-u.end_stake)<![CDATA[<]]>0 or u.start_stake=f.stake)
        and p.facility_cat=f.facility_cat
        and p.dss_degree =decode(f.dss_degree,null,'01','重','03','中','02','轻','01',f.dss_degree)
        GROUP BY logic.LINE_ID, logic.LINE_CODE, logic.line_direct,u.LANE, u.unit_marge_id,u.start_stake, u.end_stake
        ORDER BY u.lane,u.start_stake) bb on tcd.unit_marge_id=bb.ug
        join GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE=us.ROUTE_CODE
        JOIN GDGS.BASE_LINE LINE ON LOGIC.LINE_ID=LINE.LINE_ID
        where tcd.prj_id='${prjId}' and LOGIC.LINE_CODE='${lineCode}'
        and us.LANE in ('A', 'B') and tcd.year='${years}'
        order by us.lane,us.start_stake
    </select>

    <select id="getTciExcelExportMessage" resultType="java.util.Map">
        select LINE.LINE_ALLNAME,DECODE(LOGIC.LINE_DIRECT, '1', '上行','2','下行') as LINE_DIRECT,LOGIC.Line_Code,us.lane,
        ptcmsdb.func_zhz(us.start_stake) as start_stake,ptcmsdb.func_zhz(us.end_stake) as end_stake ,abs(us.start_stake-us.end_stake)*1000 as LENGTH,
        decode(tcd.TCI_tc_code, '1', '优', '2', '良', '3', '中', '4', '次', '5', '差', '' ) AS DJ,
        us.pavement_type,tcd.tci,us.unit_marge_id,
        bb.ug, NVL(A1, 0) as A1,
        NVL(A2, 0) as A2,
        NVL(A3, 0) A3,
        NVL(A4, 0) A4,
        NVL(A5, 0) A5,
        NVL(A6, 0) A6,
        NVL(A7, 0) A7,
        NVL(A8, 0) A8,
        NVL(A9, 0) A9,
        NVL(A10, 0) A10,
        NVL(A11, 0) A11,
        NVL(A12, 0)A12,
        NVL(A13, 0) A13,
        NVL(A14, 0) A14,
        NVL(A15, 0) A15,
        NVL(A16, 0) A16,
        NVL(A17, 0) A17,
        NVL(A18, 0) A18,
        NVL(A19, 0) A19,
        NVL(A20, 0) A20,
        NVL(A21, 0)A21,
        NVL(A22, 0)A22,
        NVL(A23, 0)A23,
        NVL(A1,0)+NVL(A3,0)+NVL(A5,0)+NVL(A7,0)+NVL(A9,0)+NVL(A11,0) AS TOTALNUMBERLIGHT,
        NVL(A2,0)+NVL(A4,0)+NVL(A6,0)+NVL(A8,0)+NVL(A10,0)+NVL(A12,0) AS TOTALNUMBERWEIGHT,
        NVL(A14,0)+NVL(A15,0)+NVL(A16,0)+NVL(A17,0)+NVL(A18,0)+NVL(A19,0)+NVL(A20,0) AS TOTALNUMBERLACK,
        NVL(A21,0)+NVL(A22,0) AS TOTALNUMBERLINE
        from ptcmsdb.PTCD_TC_DETAIL_1000_DESION tcd
        join ptcmsdb.pbd_struct_intrvl_unit_DESION us
        on tcd.unit_marge_id=us.unit_marge_id
        left  join (SELECT  u.unit_marge_id as ug,
        round(sum(case when f.DSS_TYPE='JA-0010' then decode(decode(f.dss_degree,'轻','01',f.dss_degree),'01', f.dss_n, null) end),2) as A1,
        round(sum(case when f.DSS_TYPE='JA-0010'  then decode(decode(f.dss_degree,'重','03',f.dss_degree),'03', f.dss_n, null) end),2) as A2,
        round(sum(case when f.DSS_TYPE='JA-0009'  then decode(decode(f.dss_degree,'轻','01',f.dss_degree),'01', f.dss_n, null) end),2) as A3,
        round(sum(case when f.DSS_TYPE='JA-0009'  then decode(decode(f.dss_degree,'重','03',f.dss_degree),'03', f.dss_n, null) end),2) as A4,
        round(sum(case when f.DSS_TYPE='JA-0008' then decode(decode(f.dss_degree,'轻','01',f.dss_degree),'01', f.dss_n, null) end),2) as A5,
        round(sum(case when f.DSS_TYPE='JA-0008'  then decode(decode(f.dss_degree,'重','03',f.dss_degree),'03', f.dss_n, null) end),2) as A6,
        round(sum(case when f.DSS_TYPE='JA-0007'  then decode(decode(f.dss_degree,'轻','01',f.dss_degree),'01', f.dss_n, null) end),2) as A7,
        round(sum(case when f.DSS_TYPE='JA-0007'  then decode(decode(f.dss_degree,'重','03',f.dss_degree),'03', f.dss_n, null) end),2) as A8,
        round(sum(case when f.DSS_TYPE='JA-0006' then decode(decode(f.dss_degree,'轻','01',f.dss_degree),'01', f.dss_n, null) end),2) as A9,
        round(sum(case when f.DSS_TYPE='JA-0006'  then decode(decode(f.dss_degree,'重','03',f.dss_degree),'03', f.dss_n, null) end),2) as A10,
        round(sum(case when f.DSS_TYPE='JA-0001' then decode(decode(f.dss_degree,'轻','01',f.dss_degree),'01',f.dss_n,null) end),2) as A11,
        round(sum(case when f.DSS_TYPE='JA-0001' then decode(decode(f.dss_degree,'重','03',f.dss_degree),'03',f.dss_n,null) end),2) as A12,
        round(sum(case when f.DSS_TYPE='JA-0002'  then f.dss_n end),2) as A13,
        round(sum(case when f.DSS_TYPE='JA-0016' then f.dss_n end),2) as A14,
        round(sum(case when f.DSS_TYPE='JA-0015' then f.dss_n end),2) as A15,
        round(sum(case when f.DSS_TYPE='JA-0014' then f.dss_n end),2) as A16,
        round(sum(case when f.DSS_TYPE='JA-0013' then f.dss_n end),2) as A17,
        round(sum(case when f.DSS_TYPE='JA-0012' then f.dss_n end),2) as A18,
        round(sum(case when f.DSS_TYPE='JA-0011' then f.dss_n end),2) as A19,
        round(sum(case when f.DSS_TYPE='JA-0003' then f.dss_n end),2) as A20,
        round(sum(case when f.DSS_TYPE='JA-0017' then f.dss_l end),2) as A21,
        round(sum(case when f.DSS_TYPE='JA-0004' then f.dss_l end),2) as A22,
        round(sum(case when f.DSS_TYPE='JA-0005' then f.dss_l end),2) as A23
        from  ptcmsdb.PTCD_TC_DETAIL_1000_DESION a left join ptcmsdb.pbd_struct_intrvl_unit u
        on a.unit_marge_id = u.unit_marge_id
        JOIN GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE=u.ROUTE_CODE
        left JOIN MEMSDB.DSS_INFO_DESION f ON  f.routecode =LOGIC.route_code
        left JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.dss_type=f.dss_type
        WHERE  a.prj_id='${prjId}'
        and f.rel_task_code='${prjId}'
        AND f.facility_cat='JA' and a.YEAR='${years}'
        and u.lane=f.lane and p.means=0
        and ((f.stake-u.start_stake)*(f.stake-u.end_stake)<![CDATA[<]]>0 or u.start_stake=f.stake)
        and p.facility_cat=f.facility_cat
        and p.dss_degree =decode(f.dss_degree,null,'01','重','03','中','02','轻','01',f.dss_degree)
        GROUP BY logic.LINE_ID, logic.LINE_CODE, logic.line_direct,u.LANE, u.unit_marge_id,u.start_stake, u.end_stake
        ORDER BY u.lane,u.start_stake) bb on tcd.unit_marge_id=bb.ug
        join GDGS.BASE_ROUTE_LOGIC LOGIC ON LOGIC.ROUTE_CODE=us.ROUTE_CODE
        JOIN GDGS.BASE_LINE LINE ON LOGIC.LINE_ID=LINE.LINE_ID
        where tcd.prj_id='${prjId}'
        and LOGIC.LINE_CODE='${lineCode}' and us.LANE in ('A', 'B') and tcd.YEAR='${years}'
        order by us.lane,us.start_stake
    </select>
    <select id="getDataSumTcDetail" resultType="java.util.Map">
        SELECT
        to_char(wm_concat(distinct decode(unit.pavement_type,'LQ','沥青','SN','水泥'))) as PAVEMENT_TYPE,
        round(avg(CASE WHEN unit.LANE='${laneUp}' THEN tc.${index} ELSE NULL END),2) as RQIA,
        round(avg(CASE WHEN unit.LANE='${laneDown}' THEN tc.${index} ELSE NULL END),2) as RQIB,
        round(avg(CASE WHEN unit.LANE='${laneUp}' THEN tc.${typeNumber} ELSE NULL END),2) as IRI_MA,
        round(avg(CASE WHEN unit.LANE='${laneDown}' THEN tc.${typeNumber} ELSE NULL END),2) as IRI_MB
        from ptcmsdb.PTCD_TC_DETAIL_1000_DESION tc
        join ptcmsdb.TCC_INSP_PRJ prj
        on tc.PRJ_ID = prj.PRJ_ID
        join gdgs.fw_right_org
        on prj.MNG_ORG_ID = gdgs.fw_right_org.id
        join  ptcmsdb.pbd_struct_intrvl_unit_DESION unit on unit.unit_marge_id =tc.unit_marge_id
        join gdgs.base_route_logic logic on unit.route_code=logic.route_code
        join gdgs.base_line line on logic.line_id=line.line_id
        WHERE (tc.PRJ_ID ='${prjId}')  and logic.LINE_CODE='${lineCode}'
        and ((tc.lane = '${laneUp}') or (tc.lane = '${laneDown}'))
        <if test="index == 'RDI'.toString()">
            AND unit.PAVEMENT_TYPE <![CDATA[<>]]>'SN'
        </if>
        group by tc.PRJ_ID, logic.LINE_ID,logic.LINE_CODE,line.line_allname,unit.PAVEMENT_TYPE
        order by logic.LINE_ID
    </select>

    <select id="getPrjSearchRoute" resultType="java.lang.String">
        SELECT DISTINCT PC.ROUTE_CODE
        FROM PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC
                 JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION UN ON TC.UNIT_MARGE_ID = UN.UNIT_MARGE_ID
                 JOIN GDGS.BASE_ROUTE_PHYSICS PC ON UN.ROUTE_CODE=PC.ROUTE_CODE
        WHERE TC.PRJ_ID='${prjId}' AND PC.LINE_CODE='${lineCode}'
    </select>

    <select id="getMeaSureDataType" resultType="java.util.Map">
        SELECT DECODE(HUN.PAVEMENT_TYPE,'LQ','沥青','水泥') AS PAVEMENT_TYPE, ptcmsdb.func_zhz(HUN.START_STAKE) as START_STAKE1,
        ptcmsdb.func_zhz(HUN.END_STAKE) as END_STAKE1,HUN.START_STAKE,HUN.END_STAKE,TC.${typeNumber},
        <if test="typeNumber == 'PCI'.toString()">
            CASE WHEN TC.${typeNumber} <![CDATA[<]]> 1 THEN 0 ELSE TC.${typeNumber} - 1 END AS ${typeNumber}second,
            CASE WHEN TC.${typeNumber} <![CDATA[<]]> 2 THEN 0 ELSE TC.${typeNumber} - 2 END AS ${typeNumber}third,
        </if>
        <if test="typeNumber != 'PCI'.toString()">
            CASE WHEN TC.${typeNumber} <![CDATA[<]]> 0.1 THEN 0 ELSE TC.${typeNumber} - 0.1 END AS ${typeNumber}second,
            CASE WHEN TC.${typeNumber} <![CDATA[<]]> 0.2 THEN 0 ELSE TC.${typeNumber} - 0.2 END AS ${typeNumber}third,
        </if>
        PCS.LINE_CODE, '' AS REMARK,HUN.UNIT_MARGE_ID,PCS.LINE_DIRECT AS LINE1
        FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION HUN
        JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC ON HUN.UNIT_MARGE_ID = TC.UNIT_MARGE_ID
        JOIN GDGS.BASE_ROUTE_PHYSICS PCS ON HUN.ROUTE_CODE = PCS.ROUTE_CODE
        JOIN GDGS.BASE_LINE L ON PCS.LINE_CODE=L.LINE_CODE
        WHERE TC.PRJ_ID ='${prjId}' AND (PCS.LINE_CODE='${lineCode}' OR L.LINE_ID='${lineCode}') AND HUN.LANE='${lane}'
        <if test="typeNumber == 'RDI'.toString()">
            AND HUN.PAVEMENT_TYPE <![CDATA[<>]]>'SN'
        </if> and TC.YEAR='${years}'
        ORDER BY HUN.LANE, HUN.START_STAKE
    </select>

    <select id="findDssInfoGroup" resultType="java.util.Map">
            select F.REL_TASK_CODE as prjId,
               F.LANE as lane,
               case
                   when  F.lane_direction = 1 then
                       '上行'
                   when  F.lane_direction = 2 then
                       '下行'
                   end as lineDirect,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0001' THEN
                                     F.DSS_A
                   end),2),0) as crazing,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0002' THEN
                                     F.DSS_A
                   end),2),0)  as cracks,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0003' THEN
                                     F.DSS_L
                   end),2),0)  as longitudinal,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0004' THEN
                                     F.DSS_L
                   end),2),0)  as transverse,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0005' THEN
                                     F.DSS_A
                   end),2),0)  as pothole,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0006' THEN
                                     F.DSS_A
                   end),2),0)  as loose,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0012' THEN
                                     F.DSS_L
                   end),2),0)  as strip,
               nvl(round(sum(case
                                 when F.DSS_TYPE = 'LMLQ-0011' THEN
                                     F.DSS_A
                   end),2),0)  as patch
        FROM MEMSDB.DSS_INFO_DESION F
                 join ptcmsdb.PTCD_HPAS_SD_PAVEMENT p
                      on p.dss_type = F.dss_type
                 join gdgs.base_route_logic logic on F.Routecode=logic.route_code
        join gdgs.BASE_LINE L ON logic.LINE_CODE=L.LINE_CODE
        where F.Rel_Task_Code = '${orgId}'
          and (L.line_id='${lineCode}' or L.line_code='${lineCode}')
          AND p.means=0 and p.dss_degree=decode(F.DSS_DEGREE,null,'01','重','03','中','02','轻','01',F.Dss_Degree)
          and F.facility_cat = 'LM'
          and F.dss_type like 'LMLQ%' and f.YEAR='${years}'
        GROUP BY F.REL_TASK_CODE,F.lane_direction,F.LANE
        ORDER BY F.lane_direction,F.LANE
    </select>
    <select id="getRouteAll" resultType="java.lang.String">
        /*select DISTINCT o.Routecode as main_road_id
        from memsdb.dss_info o JOIN PTCMSDB.TCC_INSP_PRJ P ON o.REL_TASK_CODE = P.PRJ_ID
        join gdgs.BASE_ROUTE_LOGIC L ON o.ROUTECODE=L.ROUTE_CODE
        join GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
        where o.facility_cat = 'LM'
        AND ORG.ID='${orgId}' AND P.PRJYEAR='${year}'
        and o.Routecode is not null*/
        select main_road_id from PTCMSDB.PBD_ALL_ROUTE_CODE ORG WHERE ORG.ID='${orgId}' AND ORG.PRJYEAR='${year}'
    </select>
    <select id="getRuttingRouteAll" resultType="java.lang.String">
        select distinct o.routecode as main_road_id from ${tableName} o
        JOIN PTCMSDB.TCC_INSP_PRJ P ON o.PRJ_ID = P.PRJ_ID
        join gdgs.BASE_ROUTE_LOGIC L ON o.ROUTECODE=L.ROUTE_CODE
        join GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
        where ORG.ID='${orgId}' AND P.PRJYEAR='${year}'
        and o.routecode is not null
    </select>
    <insert id="saveTempRoute">
        insert into ptcmsdb.pbd_struct_intrvl_unit_new n
        ( STRUCT_INTRVL_UID,
                                                   STRUCT_INTRVL_ID,
                                                   RP_INTRVL_ID,
                                                   TECH_GRADE,
                                                   PAVEMENT_TYPE,
                                                   PAVEMENT_STRUCT_ID,
                                                   DESIGN_LD,
                                                   EFFECT_WIDTH,
                                                   START_STAKE,
                                                   END_STAKE,
                                                   LENGTH,
                                                   YEAR,
                                                   USERED,
                                                   UNIT_MARGE_ID,
                                                   INTRVL_MARGE_ID,
                                                   LANE,
                                                   PRJ_ID,
                                                   VERSION,
                                                   CREATE_DATE_UNIT,
                                                   ORG_ID,
                                                   SOURCE,
                                                   LINE_ID,
                                                   LINE_DIRECT,
                                                   TYPE,
                                                   ROUTE_CODE,
                                                   ROUTE_VERSION,
                                                   PHYSICS_CNTR_STAKE,
                                                   OPRTORGCODE,
                                                   PRJORGCODE,
                                                   REMARK,
                                                   "NeNumber")
select u.STRUCT_INTRVL_UID,
       u.STRUCT_INTRVL_ID,
       u.RP_INTRVL_ID,
       u.TECH_GRADE,
       u.PAVEMENT_TYPE,
       u.PAVEMENT_STRUCT_ID,
       u.DESIGN_LD,
       u.EFFECT_WIDTH,
       u.START_STAKE,
       u.END_STAKE,
       u.LENGTH,
       u.YEAR,
       u.USERED,
       u.UNIT_MARGE_ID,
       u.INTRVL_MARGE_ID,
       u.LANE,
       u.PRJ_ID,
       u.VERSION,
       u.CREATE_DATE_UNIT,
       u.ORG_ID,
       u.SOURCE,
       u.LINE_ID,
       u.LINE_DIRECT,
       u.TYPE,
       u.ROUTE_CODE,
       u.ROUTE_VERSION,
       u.PHYSICS_CNTR_STAKE,
       u.OPRTORGCODE,
       u.PRJORGCODE,
       u.REMARK,
       u."NeNumber"
        from ptcmsdb.PBD_STRUCT_INTRVL_UNIT_DESION u
        join GDGS.BASE_ROUTE_LOGIC L ON U.ROUTE_CODE=L.ROUTE_CODE
        JOIN GDGS.BASE_LINE LS ON L.LINE_CODE=LS.LINE_CODE
        where u.route_code='${routeCode}' AND (LS.LINE_CODE='${lineCode}' OR LS.LINE_ID='${lineCode}')
        AND u.LANE IN ('A','B') and u.USERED=${usered}
    </insert>
    <delete id="removeNewUnitLine">
        delete from ptcmsdb.pbd_struct_intrvl_unit_new
    </delete>

    <delete id="removeNewHunDataList">
        delete from ptcmsdb.PBD_STRUCT_INTRVL_HUN_DESION H WHERE H.ROUTECODE IN
        <foreach collection="list" item="routeCode" index="index" open="(" close=")" separator=",">
            '${routeCode}'
        </foreach> AND H.USERED='22'
    </delete>

    <delete id="removeTcDetail">
        DELETE FROM  PTCMSDB.PTCD_TC_DETAIL_1000_DESION D WHERE EXISTS(
        SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION U
        JOIN GDGS.BASE_ROUTE_LOGIC L ON U.ROUTE_CODE=L.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
        WHERE D.UNIT_MARGE_ID=U.UNIT_MARGE_ID AND U.USERED='${usered}'
        AND ORG.id='${orgName}' AND D.YEAR='${year}')
    </delete>

    <delete id="removeTcHunDetail">
        DELETE FROM  PTCMSDB.PTCD_TC_DETAIL_100_DESION D WHERE EXISTS(
        SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION U
        JOIN GDGS.BASE_ROUTE_LOGIC L ON U.ROUTECODE=L.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
        WHERE D.HUN_MARGE_ID=U.HUN_MARGE_ID  AND U.USERED='${usered}'
        AND ORG.id='${orgName}' AND D.YEARs='${year}')
    </delete>

    <select id="getFlatessData" resultType="com.hualu.highwaymaintenance.decision.entity.FlatnessData">
        SELECT * FROM (select d.left_iri_m as leftIriM, d.right_iri_m as rightIriM,
               d.prj_id                                             as prjId,
               d.lane                                               as lane,
               D.START_STAKE as startStake,D.END_STAKE as endStake,
               D.ROUTECODE as routeCode
        from PTCMSDB.PTCD_FLATNESS_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
        where d.year = '${year}'
          AND ORG.ID = '${orgId}') A
          WHERE A.LANE IN ('A','B')
    </select>
    <select id="getRdiUnitData" resultType="com.hualu.highwaymaintenance.decision.entity.RuttingData">
        select * from (select /*+ PARALLEL(d, 4) */d.prj_id as prjId,
               d.lane,
               d.RIGHT_RD_m as rightRdM, d.LEFT_RD_m as leftRdM,
               d.START_STAKE as startStake,d.END_STAKE as endStake,L.ROUTE_CODE as routeCode
        from ptcmsdb.PTCD_RUTTING_DATA_DESION d
        JOIN GDGS.BASE_ROUTE_LOGIC L ON D.ROUTECODE=L.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
        JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE=LINE.LINE_CODE
        WHERE ORG.ID='${orgId}' AND d.year='${year}' AND LINE.LINE_ID='${lineId}') A
        WHERE A.LANE IN ('A','B')
    </select>
    <select id="getSriUnitData" resultType="com.hualu.highwaymaintenance.decision.entity.AntiSlipData">
        SELECT * FROM (select D.LANE,SFC_M as sfcM,D.START_STAKE as startStake,D.END_STAKE as endStake,D.ROUTECODE as routeCode
        from PTCMSDB.PTCD_ANTI_SLIP_DATA_DESION d
                 join gdgs.BASE_ROUTE_LOGIC L ON d.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
        where d.year = '${year}'
        AND ORG.ID = '${orgId}') A
        WHERE A.LANE IN ('A', 'B')
    </select>
    <select id="getTotalPssiScoreUnit" resultType="com.hualu.highwaymaintenance.decision.entity.DeflectionData">
        select d.prj_id as prjId,
        d.lane,
        u.ROUTE_CODE as routeCode,
        round(u.design_ld / (avg(d.backman_m) + STDDEV(d.backman_m) * 1.645), 2) as ssr,
        u.unit_marge_id as unitMargeId,
        max(u.year)                                                              as years,
        u.pavement_type as pavementType
        from ptcmsdb.PBD_STRUCT_INTRVL_UNIT_NEW u
        join ptcmsdb.PTCD_DEFLECTION_DATA d
        on u.ROUTE_CODE=d.ROUTECODE
        JOIN GDGS.BASE_ROUTE_LOGIC L ON U.ROUTE_CODE=L.ROUTE_CODE
        JOIN PTCMSDB.TCC_INSP_PRJ PRJ ON D.PRJ_ID=PRJ.PRJ_ID
        where (d.STAKE = u.start_stake or (d.STAKE - u.start_stake) * (d.STAKE - u.end_stake) <![CDATA[<]]> 0)
        AND L.OPRT_ORG_CODE='${orgId}' AND PRJ.PRJYEAR='${year}'
        and d.lane = u.lane and U.LANE IN ('A','B')
        group by u.pavement_type,u.unit_marge_id, d.prj_id, u.ROUTE_CODE, d.lane, u.length,u.design_ld
    </select>
    <select id="getPbiUnitData" resultType="com.hualu.highwaymaintenance.decision.entity.PtcdCarData">
        SELECT distinct sys_guid(),
        d.prj_id                  as prjId,
        d.routecode               as routeCode,
        u.lane,
        u.unit_marge_id           as unitMargeId,
        100 - sum(case
        when (d.pbi_hight / 10) <![CDATA[>=]]> 5 and (d.pbi_hight / 10) <![CDATA[<]]> 8
        then 25
        when (d.pbi_hight / 10) <![CDATA[>]]> 8 then 50
        else 0 end) as PBI,
        max(u.year)               as year,
        u.pavement_type           as pavementType
        FROM PTCMSDB.ptcd_car_data_DESION d
        join PTCMSDB.pbd_struct_intrvl_unit_new u on u.route_code = d.routecode
        JOIN GDGS.BASE_ROUTE_LOGIC L ON U.ROUTE_CODE=L.ROUTE_CODE
        where (d.End_Stake = u.end_stake or (d.End_Stake - u.start_stake) * (d.End_Stake - u.end_stake) <![CDATA[<]]> 0)
        and ((d.start_stake - u.end_stake) * (d.start_stake - u.start_stake) <![CDATA[<]]> 0 or
        u.end_stake = d.start_stake)
        and u.route_code = d.routecode
        and substr(d.lane, -1, 1) = u.lane
        AND L.OPRT_ORG_CODE='${orgId}' AND d.YEAR ='${year}'
        AND u.LANE in ('A', 'B')
        group by d.prj_id, d.routecode, u.lane, u.pavement_type, u.unit_marge_id, u.route_code
    </select>
    <select id="getPbiHunsData" resultType="com.hualu.highwaymaintenance.decision.entity.PtcdCarData">
        SELECT distinct sys_guid(),
        d.prj_id                  as prjId,
        d.routecode               as routeCode,
        u.lane,
        u.unit_marge_id           as unitMargeId,
        HUN.HUN_MARGE_ID AS hunMargeId,
        100 - sum(case
        when (d.pbi_hight / 10) <![CDATA[>=]]> 5 and (d.pbi_hight / 10) <![CDATA[<]]> 8
        then 25
        when (d.pbi_hight / 10) <![CDATA[>]]> 8 then 50
        else 0 end) as PBI,
        max(u.year)               as year,
        u.pavement_type           as pavementType
        FROM PTCMSDB.ptcd_car_data_DESION d
        join PTCMSDB.pbd_struct_intrvl_unit_new u on u.route_code = d.routecode
        join PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN ON U.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID AND HUN.USERED=22
        JOIN GDGS.BASE_ROUTE_LOGIC L ON U.ROUTE_CODE=L.ROUTE_CODE
        where (d.Start_Stake=hun.start_stake or(d.Start_Stake-hun.start_stake)*(d.Start_Stake-hun.end_stake)<![CDATA[<]]>0)
        and ((d.start_stake - u.end_stake) * (d.start_stake - u.start_stake) <![CDATA[<]]> 0 or
        u.end_stake = d.start_stake)
        and u.route_code = d.routecode
        and substr(d.lane, -1, 1) = u.lane
        AND L.OPRT_ORG_CODE='${orgId}' AND d.YEAR ='${year}'
        AND u.LANE in ('A', 'B')
        group by d.prj_id, d.routecode, u.lane, u.pavement_type, u.unit_marge_id, u.route_code,HUN.HUN_MARGE_ID
    </select>
    <select id="getDamageDate" resultType="com.hualu.highwaymaintenance.decision.entity.DrainageData">
        WITH TABLEDSSINFO AS(
        SELECT CASE
        WHEN p.measurement_unit = 'm' AND f.dss_l IS NOT NULL THEN f.dss_l * p.deduction
        WHEN p.measurement_unit = 'm2' AND f.dss_a IS NOT NULL THEN f.dss_a * p.deduction
        WHEN f.dss_n IS NOT NULL THEN f.dss_n * p.deduction
        ELSE 0
        END AS sci,p.dss_type as drainDssType,p.weight,F.STAKE,F.ROUTECODE,f.REL_TASK_CODE,DECODE(L.LINE_DIRECT,'1','A','2','B') AS LANE
        FROM MEMSDB.DSS_INFO_DESION F JOIN GDGS.BASE_ROUTE_LOGIC L ON F.ROUTECODE=L.ROUTE_CODE
        JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.dss_type = f.dss_type
        and p.dss_degree = decode(f.dss_degree, null, '01', '重', '03', '中', '02', '轻', '01', f.dss_degree)
        WHERE F.YEAR='${year}' AND L.OPRT_ORG_CODE='${orgId}'
        AND F.facility_cat = 'LJ' AND P.MEANS=0
        ) select distinct F.sci,
        F.drainDssType                                              as drainDssType,
        F.weight,
        u.unit_marge_id                                         as unitMargeId,
        F.Rel_Task_Code                                         as prjId,
        abs(u.start_stake - u.end_stake)                        as length,
        u.pavement_type                                         as pavementType,
        u.lane,
        u.year
        from ptcmsdb.pbd_struct_intrvl_unit_new u JOIN
        TABLEDSSINFO F ON u.route_code = f.routecode AND U.LANE=F.LANE
        WHERE  ((f.stake - u.start_stake) * (f.stake - u.end_stake) <![CDATA[<]]> 0 or u.start_stake = f.stake)
    </select>
    <select id="getDssTypePrList" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        select F.REL_TASK_CODE                                                           as relTaskCode,
        u.lane,
        max(u.year)                                                               as year,
        u.pavement_type                                                           as pavementType,
        nvl(SUM(CASE
        WHEN P.DSS_TYPE_NAME = '坑槽' THEN F.DSS_A
        WHEN P.DSS_TYPE_NAME = '块状修补' THEN F.DSS_A
        END),0)                                   dr,
        F.DSS_TYPE                                                                as dssType,
        F.Dss_Degree                                                              as dssDegree,
        u.start_stake                                                             as stake,
        abs(u.start_stake - u.end_stake) * 1000                                   as length,
        u.unit_marge_id                                                           as unitMargeId,
        <if test="type == 0">
            abs(HUN.start_stake - HUN.end_stake) * 1000                                   as hunLength,
            hun.HUN_MARGE_ID as hunMargeId,
        </if>
        1
        from ptcmsdb.pbd_struct_intrvl_unit_new u
        <if test="type == 0">
            JOIN PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN ON U.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID AND HUN.USERED=22
        </if>
        join MEMSDB.DSS_INFO_DESION F on u.route_code = F.ROUTECODE
        join ptcmsdb.PTCD_HPAS_SD_PAVEMENT p on p.facility_cat = F.facility_cat and p.dss_type = F.dss_type
        and p.dss_degree = decode(f.dss_degree, null, '01', '重', '03', '中', '02', '轻', '01', f.dss_degree)
        join GDGS.BASE_ROUTE_LOGIC L ON F.ROUTECODE = L.ROUTE_CODE
        WHERE F.YEAR = '${year}'
        AND L.OPRT_ORG_CODE = '${orgId}'
        AND u.PAVEMENT_TYPE = SUBSTR('LMLQ', 3, 4)
        and p.facility_cat = 'LM'
        and p.dss_type like 'LMLQ%'
        AND P.DSS_TYPE_NAME IN ('坑槽', '块状修补')
        and u.lane = f.lane
        <if test="type == 0">
            and ((f.stake - HUN.end_stake) * (f.stake - HUN.start_stake) <![CDATA[<]]> 0 or (HUN.end_stake = f.stake or HUN.start_stake = f.stake))
        </if>
        <if test="type == 1">
            and ((f.stake - u.end_stake) * (f.stake - u.start_stake) <![CDATA[<]]> 0 or (u.end_stake = f.stake or u.start_stake = f.stake))
        </if>
        and p.MEANS = 0
        <if test="type == 0">
            group by u.lane, F.REL_TASK_CODE, u.start_stake, u.end_stake, hun.start_stake, hun.end_stake, u.pavement_type, u.unit_marge_id,HUN.HUN_MARGE_ID, F.DSS_TYPE,
            F.Dss_Degree
        </if>
        <if test="type == 1">
            group by u.lane, F.REL_TASK_CODE, u.start_stake, u.end_stake, u.pavement_type, u.unit_marge_id, F.DSS_TYPE,
            F.Dss_Degree
        </if>
    </select>
    <select id="getNewBrigeBci" resultType="java.util.Map">
        select to_char(NEWS.START_STAKE)  as START_STAKE,to_char(NEWS.END_STAKE) as END_STAKE,NEWS.ROUTE_CODE,to_char(sum(DECODE(grade.GRADE,1,1,0))) AS CODE1,
        to_char(sum(DECODE(grade.GRADE,2,1,0))) AS CODE2,
        to_char(sum(DECODE(grade.GRADE,3,1,0))) AS CODE3,
        to_char(sum(DECODE(grade.GRADE,4,1,0))) AS CODE4,
        to_char(sum(DECODE(grade.GRADE,5,1,0))) AS CODE5,'QL' as type,NEWS.LINE_DIRECT,NEWS.UNIT_MARGE_ID
        from BCTCMSDB.brdg_grade grade
        join PTCMSDB.PBD_STRUCT_INTRVL_UNIT_NEW NEWS
        ON grade.ROUTE_CODE=NEWS.ROUTE_CODE
        AND ((grade.LOGIC_CNTR_STAKE_NUM-NEWS.start_stake)*(grade.LOGIC_CNTR_STAKE_NUM-NEWS.end_stake)<![CDATA[<]]>0
        or NEWS.start_stake=grade.LOGIC_CNTR_STAKE_NUM)
        where grade.BRDG_LINE_TYPE in ('L', 'R') AND grade.GRADE_YEAR= '${year}'
        AND NEWS.LANE IN ('A','B')
        GROUP BY NEWS.LINE_DIRECT,NEWS.ROUTE_CODE,NEWS.START_STAKE,NEWS.END_STAKE,NEWS.UNIT_MARGE_ID
        UNION ALL
        select to_char(NEWS.START_STAKE),to_char(NEWS.END_STAKE),NEWS.ROUTE_CODE,to_char(sum(DECODE(grade.GRADE,0,1,0))) AS CODE1,
        to_char(sum(DECODE(grade.GRADE,1,1,0))) AS CODE2,
        to_char(sum(DECODE(grade.GRADE,2,1,0))) AS CODE3,
        to_char(sum(DECODE(grade.GRADE,3,1,0))) AS CODE4,
        to_char(sum(DECODE(grade.GRADE,4,1,0))) AS CODE5,'HD' as type,NEWS.LINE_DIRECT,NEWS.UNIT_MARGE_ID
        from BCTCMSDB.clvrt_grade grade
        join PTCMSDB.PBD_STRUCT_INTRVL_UNIT_NEW NEWS
        ON grade.ROUTE_CODE=NEWS.ROUTE_CODE
        AND ((grade.LOGIC_CNTR_STAKE_NUM-NEWS.start_stake)*(grade.LOGIC_CNTR_STAKE_NUM-NEWS.end_stake)<![CDATA[<]]>0
        or NEWS.start_stake=grade.LOGIC_CNTR_STAKE_NUM)
        AND NEWS.LANE IN ('A','B')
        where   grade.GRADE_YEAR='${year}'
        GROUP BY NEWS.LINE_DIRECT,NEWS.ROUTE_CODE,NEWS.LINE_DIRECT,NEWS.START_STAKE,NEWS.END_STAKE,NEWS.UNIT_MARGE_ID
    </select>
    <select id="getTciData" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        select MAX(F.DSS_ID),F.REL_TASK_CODE as relTaskCode,u.lane,max(u.year) as year,u.pavement_type as pavementType,
        nvl(ROUND(sum(decode(CASE WHEN F.DSS_L_UNIT='m' then F.Dss_l_Unit else F.Dss_N_Unit end,
        'm',
        F.Dss_l, F.Dss_N)),5),0) as dr,p.REMARK as dssType,decode(f.dss_degree,null,'01','01','01','02','02','03','03','轻','01','中','02','重','03',f.dss_degree) as dssDegree,u.start_stake as stake,abs(u.start_stake-u.end_stake)*1000 as length,
        u.unit_marge_id as unitMargeId
        from ptcmsdb.pbd_struct_intrvl_unit_new u
        join MEMSDB.DSS_INFO_DESION F on u.route_code=F.ROUTECODE
        join ptcmsdb.PTCD_HPAS_SD_PAVEMENT p on p.facility_cat=F.facility_cat and p.dss_type=F.dss_type
        and p.dss_degree=decode(f.dss_degree,null,'01','重','03','中','02','轻','01',f.dss_degree)
        join GDGS.BASE_ROUTE_LOGIC L ON F.ROUTECODE=L.ROUTE_CODE
        WHERE F.YEAR='${year}' AND L.OPRT_ORG_CODE='${orgId}'
        and p.facility_cat='JA'
        and ((f.stake-u.start_stake)*(f.stake-u.end_stake)<![CDATA[<]]>0 or (case when (u.start_stake-u.end_stake) <![CDATA[<]]>0 then u.start_stake
        when (u.start_stake-u.end_stake)>0 then u.end_stake end=f.stake)) and u.lane=f.lane
        and ((f.stake - u.end_stake ) * (f.stake - u.start_stake) <![CDATA[<]]> 0 or (u.end_stake = f.stake or u.start_stake=f.stake))
        and p.MEANS = 0
        <if test="type == 0">
            and exists (SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_HUN HUN
            WHERE u.unit_marge_id=HUN.UNIT_MARGE_ID AND HUN.USERED=22)
        </if>
        group by u.lane,F.REL_TASK_CODE,u.start_stake,u.end_stake,u.pavement_type,u.unit_marge_id,F.DSS_TYPE,p.REMARK,
        F.Dss_Degree
    </select>
    <select id="getDssTypeTcsList" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        SELECT F.REL_TASK_CODE AS relTaskCode,
        u.lane,
        MAX(u.year) AS year,
        u.pavement_type AS pavementType,
        nvl(SUM(CASE WHEN P.DSS_TYPE_NAME = '横向裂缝' THEN F.DSS_L
        WHEN P.DSS_TYPE_NAME = '条状修补' AND F.DSS_CAUSE = '横向裂缝' THEN F.DSS_L
        END),0) dr,
        F.DSS_TYPE AS dssType,
        F.Dss_Degree AS dssDegree,
        u.start_stake AS stake,
        abs(u.start_stake - u.end_stake) * 1000                                   as length,
        u.unit_marge_id                                                           as unitMargeId,
        <if test="type == 0">
            abs(HUN.start_stake - HUN.end_stake) * 1000                                   as hunLength,
            hun.HUN_MARGE_ID as hunMargeId,
        </if>
        1
        from ptcmsdb.pbd_struct_intrvl_unit_new u
        <if test="type == 0">
            JOIN PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN ON U.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID AND HUN.USERED=22
        </if>
        JOIN MEMSDB.DSS_INFO_DESION F ON u.route_code = F.ROUTECODE
        JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.facility_cat = F.facility_cat
        AND p.dss_type = F.dss_type
        AND p.dss_degree = DECODE(f.dss_degree, NULL, '01', '重', '03', '中', '02', '轻', '01', f.dss_degree)
        JOIN GDGS.BASE_ROUTE_LOGIC L ON F.ROUTECODE = L.ROUTE_CODE
        WHERE F.YEAR = '${year}'
        AND L.OPRT_ORG_CODE = '${orgId}'
        AND u.PAVEMENT_TYPE = SUBSTR('LMLQ', 3, 4)
        AND p.facility_cat = 'LM'
        AND p.dss_type LIKE 'LMLQ%'
        AND P.DSS_TYPE_NAME IN ('横向裂缝', '条状修补')
        AND u.lane = f.lane
        <if test="type == 0">
            and ((f.stake - HUN.end_stake) * (f.stake - HUN.start_stake) <![CDATA[<]]> 0 or (HUN.end_stake = f.stake or HUN.start_stake = f.stake))
        </if>
        <if test="type == 1">
            and ((f.stake - u.end_stake) * (f.stake - u.start_stake) <![CDATA[<]]> 0 or (u.end_stake = f.stake or u.start_stake = f.stake))
        </if>
        and p.MEANS = 0
        <if test="type == 0">
            group by u.lane, F.REL_TASK_CODE, u.start_stake, u.end_stake, hun.start_stake, hun.end_stake, u.pavement_type, u.unit_marge_id,HUN.HUN_MARGE_ID, F.DSS_TYPE,
            F.Dss_Degree
        </if>
        <if test="type == 1">
            group by u.lane, F.REL_TASK_CODE, u.start_stake, u.end_stake, u.pavement_type, u.unit_marge_id, F.DSS_TYPE,
            F.Dss_Degree
        </if>
    </select>
    <select id="getDssTypeLcrList" resultType="com.hualu.highwaymaintenance.module.task.domain.DssInfo">
        SELECT F.REL_TASK_CODE AS relTaskCode,
        u.lane,
        MAX(u.year) AS year,
        u.pavement_type AS pavementType,
        nvl(SUM(CASE WHEN P.DSS_TYPE_NAME = '纵向裂缝' THEN F.DSS_L
        WHEN P.DSS_TYPE_NAME = '条状修补' AND F.DSS_CAUSE = '纵向裂缝' THEN F.DSS_L
        END),0) dr,
        F.DSS_TYPE AS dssType,
        F.Dss_Degree AS dssDegree,
        u.start_stake AS stake,
        abs(u.start_stake - u.end_stake) * 1000                                   as length,
        u.unit_marge_id                                                           as unitMargeId,
        <if test="type == 0">
            abs(HUN.start_stake - HUN.end_stake) * 1000                                   as hunLength,
            hun.HUN_MARGE_ID as hunMargeId,
        </if>
        1
        from ptcmsdb.pbd_struct_intrvl_unit_new u
        <if test="type == 0">
            JOIN PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION HUN ON U.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID AND HUN.USERED=22
        </if>
        JOIN MEMSDB.DSS_INFO_DESION F ON u.route_code = F.ROUTECODE
        JOIN ptcmsdb.PTCD_HPAS_SD_PAVEMENT p ON p.facility_cat = F.facility_cat
        AND p.dss_type = F.dss_type
        AND p.dss_degree = DECODE(f.dss_degree, NULL, '01', '重', '03', '中', '02', '轻', '01', f.dss_degree)
        JOIN GDGS.BASE_ROUTE_LOGIC L ON F.ROUTECODE = L.ROUTE_CODE
        WHERE F.YEAR = '${year}'
        AND L.OPRT_ORG_CODE = '${orgId}'
        AND u.PAVEMENT_TYPE = SUBSTR('LMLQ', 3, 4)
        AND p.facility_cat = 'LM'
        AND p.dss_type LIKE 'LMLQ%'
        AND P.DSS_TYPE_NAME IN ('纵向裂缝', '条状修补')
        AND u.lane = f.lane
        <if test="type == 0">
            and ((f.stake - HUN.end_stake) * (f.stake - HUN.start_stake) <![CDATA[<]]> 0 or (HUN.end_stake = f.stake or HUN.start_stake = f.stake))
        </if>
        <if test="type == 1">
            and ((f.stake - u.end_stake) * (f.stake - u.start_stake) <![CDATA[<]]> 0 or (u.end_stake = f.stake or u.start_stake = f.stake))
        </if>
        and p.MEANS = 0
        <if test="type == 0">
            group by u.lane, F.REL_TASK_CODE, u.start_stake, u.end_stake, hun.start_stake, hun.end_stake, u.pavement_type, u.unit_marge_id,HUN.HUN_MARGE_ID, F.DSS_TYPE,
            F.Dss_Degree
        </if>
        <if test="type == 1">
            group by u.lane, F.REL_TASK_CODE, u.start_stake, u.end_stake, u.pavement_type, u.unit_marge_id, F.DSS_TYPE,
            F.Dss_Degree
        </if>
    </select>
    <select id="getUnitsListData" resultType="com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit">
        SELECT N.UNIT_MARGE_ID as unitMargeId,N.START_STAKE as startStake,N.END_STAKE as endStake,N.LANE as lane,
        N.ROUTE_CODE as routeCode FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_NEW N
        WHERE N.LANE IN ('A', 'B')
    </select>
    <select id="getHunsListData" resultType="com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun">
        SELECT U.UNIT_MARGE_ID as unitMargeId,U.HUN_MARGE_ID AS hunMargeId,U.START_STAKE as startStake,U.END_STAKE as endStake,U.LANE as lane,
       U.ROUTECODE as routeCode FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION U
       WHERE EXISTS(
            SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_NEW HUN WHERE U.UNIT_MARGE_ID=HUN.UNIT_MARGE_ID
                                                           AND HUN.USERED=21
        ) ORDER BY U.LANE,U.START_STAKE
    </select>
    <select id="getOrgIdToLineId" resultType="java.util.Map">
        SELECT distinct  BL.LINE_ID,BL.LINE_SNAME || '(' || BL.LINE_CODE || ')' as LINE_SNAME FROM GDGS.BASE_ROUTE_LOGIC L JOIN GDGS.BASE_LINE BL ON L.LINE_CODE=BL.LINE_CODE
        WHERE L.OPRT_ORG_CODE='${orgId}' AND L.IS_ENABLE=1
    </select>
    <select id="getFromUnitList" resultType="com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit">
        SELECT ROW_NUMBER() OVER (ORDER BY lane, D.START_STAKE) as version,ORG.ORG_NAME  AS structIntrvlId,
                     PTCMSDB.FUNC_ZHZ(D.START_STAKE) AS rlStartStake,
                     PTCMSDB.FUNC_ZHZ(D.END_STAKE)   AS rlEndStake,
                     D.LENGTH      AS length,
                     D.LANE        AS lane,
                     LS.LINE_CODE as lineCode,
                     D.STRUTNAME   AS strutName,
                     D.MEASURENAME AS measureName,
                     D.REMARK      AS typeName,
                     LS.LINE_ALLNAME AS lineName,
                     D.strutName || decode(D.strutName, null, null, ',') || D.measureName ||
                     decode(D.measureName, null, null, ',') || D.REMARK as mergeName
              FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION D
                       JOIN GDGS.BASE_ROUTE_LOGIC L ON D.ROUTE_CODE = L.ROUTE_CODE
                       JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                       JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
              WHERE ORG.ID = '${orgId}' AND D.USERED='${usered}'
                AND (LS.LINE_CODE = '${lineId}' OR LS.LINE_ID = '${lineId}')
              order by lane, D.START_STAKE
        /*SELECT ROWNUM version ,A.*,A.strutName || ',' || A.measureName || A.typeName || ',' as mergeName FROM (SELECT distinct ORG.ORG_NAME  as structIntrvlId,
        D.START_STAKE as                                      startStake,
        D.END_STAKE   as                                      endStake,
        D.LENGTH      as                                      length,
        D.LANE        as                                      lane,
        max(decode(A.BRDG_NAME, null, B.TUNNEL_NAME, A.BRDG_NAME)) strutName,
        max(decode(C.MEASURE_NAME, null, null, C.MEASURE_NAME)) measureName,
        max(decode(E.TYPENAME, null, null, E.TYPENAME)) typeName,
        D.ROUTE_CODE
        FROM PBD_STRUCT_INTRVL_UNIT_DESION D
        JOIN GDGS.BASE_ROUTE_LOGIC L ON D.ROUTE_CODE = L.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
        JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        LEFT JOIN (
        SELECT DISTINCT B.BRDG_NAME,
        B.CNTR_STAKE_NUM as STAKE,
        b.brdg_line_type as lineDirect
        FROM GDGS.BASE_BRDG b
        join GDGS.BASE_ROUTE_LOGIC L ON b.OPRT_ORG_CODE = L.OPRT_ORG_CODE
        JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        where LS.LINE_ID = '${lineId}'
        AND B.OPRT_ORG_CODE = '${orgId}'
        AND B.BRDG_LINE_TYPE IN ('1', '2')
        AND B.BRDG_LENGTH >= 500
        ) A ON A.lineDirect = L.LINE_DIRECT AND ((A.stake - D.end_stake) * (A.stake - D.start_stake) <![CDATA[<]]> 0
        or (D.end_stake = A.stake or D.start_stake = A.stake))
        LEFT JOIN (
        select DISTINCT mtb.TUNNEL_NAME,
        mtb.CNTR_STAKE_NUM     as STAKE,
        mtb.TUNNEL_LINE_DIRECT as lineDirect
        from mtmsdb.mtms_tunnel_basic mtb
        join GDGS.BASE_ROUTE_LOGIC L ON mtb.OPRT_ORG_CODE = L.OPRT_ORG_CODE
        JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        where LS.LINE_ID = '${lineId}'
        AND mtb.OPRT_ORG_CODE = '${orgId}'
        AND mtb.TUNNEL_LINE_DIRECT IN ('1', '2')
        AND mtb.TUNNEL_LENGTH >= 500
        order by CNTR_STAKE_NUM
        ) B ON B.lineDirect = L.LINE_DIRECT AND ((B.stake - D.end_stake) * (B.stake - D.start_stake) <![CDATA[<]]> 0
        or (D.end_stake = B.stake or D.start_stake = B.stake))
        LEFT JOIN (
        SELECT PM.START_STAKE as startStake, PM.END_STAKE AS STAKE_RANGE,
               PM.YEAR || ME.MEASURE_NAME AS MEASURE_NAME,PM.ROUTE_CODE,
               PM.LANE_TYPE,
               PM.LINE_DIRECTION
        FROM PBD_PAVEMENT_MAINTAIN PM
                 JOIN PBD_PAVEMENT_MEASURE ME ON ME.MEASURE_ID = PM.MEASURE_ID
                 JOIN GDGS.BASE_LINE L ON PM.LINE_CODE = L.LINE_CODE
        WHERE (PM.LINE_CODE = '${lineId}' OR L.LINE_ID = '${lineId}')
          AND PM.LINE_DIRECTION = '1'
          AND (PM.LANE_TYPE = '上行行车道2(A)' OR PM.LANE_TYPE = '上行行车道2(B)')
        GROUP BY PM.ROUTE_CODE,PM.LINE_DIRECTION,PM.LANE_TYPE,PM.START_STAKE, PM.END_STAKE, PM.YEAR, ME.MEASURE_NAME
        ) C ON C.ROUTE_CODE=D.ROUTE_CODE AND ((C.startStake - D.end_stake) * (C.startStake - D.start_stake) <![CDATA[<]]> 0
        or (D.end_stake = C.startStake or D.start_stake = C.startStake))
            AND C.LANE_TYPE  =
                   DECODE(D.LANE, 'A', '上行行车道2(A)', 'B', '上行行车道2(B)') and c.LINE_DIRECTION=L.LINE_DIRECT
        LEFT JOIN (
              SELECT T.STARTSTAKE,T.ENDSTAKE as END_STAKE,T.TYPENAME,T.LANE,T.LINE_CODE
              FROM PTCMSDB.PBD_MAIN_POINT_PAVEMENT T JOIN GDGS.BASE_LINE L ON T.LINE_CODE=L.LINE_CODE
              WHERE (T.LINE_CODE = '${lineId}' OR L.LINE_ID='${lineId}')
              AND T.ORG_ID='${orgId}'
        ) E ON ((E.startStake - D.end_stake) * (E.startStake - D.start_stake) <![CDATA[<]]> 0
        or (D.start_stake = E.startStake)) AND E.LANE = D.LANE AND L.LINE_CODE = E.LINE_CODE
        WHERE ORG.ID = '${orgId}'
        AND (LS.LINE_CODE = '${lineId}' OR LS.LINE_ID = '${lineId}')
        GROUP BY ORG.ORG_NAME, D.START_STAKE, D.END_STAKE, D.LENGTH, D.LANE, D.ROUTE_CODE
        order by lane, startStake)A*/
    </select>

    <select id="getFromHunsList" resultType="com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit">
        SELECT ROWNUM        version,
               A.*,
               A.strutName || decode(A.strutName, null, null, ',') || A.measureName || decode(A.measureName, null, null, ',') ||
               A.typeName as mergeName
        FROM (SELECT ORG.ORG_NAME  AS structIntrvlId,
                     PTCMSDB.FUNC_ZHZ(D.START_STAKE) AS rlStartStake,
                     PTCMSDB.FUNC_ZHZ(D.END_STAKE)   AS rlEndStake,
                     D.LENGTH      AS length,
                     D.LANE        AS lane,
                     D.STRUTNAME   AS strutName,
                     D.MEASURENAME AS measureName,
                     D.REMARK      AS typeName
              FROM PTCMSDB.PBD_STRUCT_INTRVL_HUN_DESION D
                       JOIN GDGS.BASE_ROUTE_LOGIC L ON D.ROUTECODE = L.ROUTE_CODE
                       JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE = ORG.ID
                       JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
              WHERE ORG.ID = '${orgId}' AND D.USERED='22'
                AND (LS.LINE_CODE = '${lineId}' OR LS.LINE_ID = '${lineId}')
              order by lane, D.START_STAKE) A
    </select>

    <delete id="deleteOldUnitList">
        delete
            FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION D
            where exists(
                          select 1
                          from GDGS.BASE_ROUTE_LOGIC L
                                   join GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
                          where D.ROUTE_CODE = L.ROUTE_CODE
                            and (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
                AND L.OPRT_ORG_CODE='${orgId}'
                      )
    </delete>

    <select id="getLogicRouteCodeVersion" resultType="java.util.Map">
        select to_char(logic.start_stake) as start_stake,
        to_char(logic.end_stake) as end_stake,
        logic.oprt_org_code,
        logic.prj_org_code,
        logic.route_code,
        logic.route_version
        from gdgs.base_route_logic logic join GDGS.FW_RIGHT_ORG org on logic.OPRT_ORG_CODE=org.ID
        JOIN GDGS.BASE_LINE LINE ON logic.LINE_CODE=LINE.LINE_CODE
        where logic.line_direct = #{lineDirect, jdbcType=VARCHAR} and (org.ORG_NAME='${orgName}' or org.ID='${orgName}'
        or org.PARENT_ID='${orgName}')
        <if test="lineDirect.toString() == '1'.toString()">
            <if test="floor != null">
                and (#{startStake, jdbcType=NUMERIC} = logic.start_stake or
                (#{startStake, jdbcType=NUMERIC} - logic.start_stake) * (#{startStake, jdbcType=NUMERIC} - logic.end_stake) <![CDATA[<]]> 1)
            </if>
            <if test="floor == null">
                and (#{startStake, jdbcType=NUMERIC} = logic.start_stake or
                (#{startStake, jdbcType=NUMERIC} - logic.start_stake) * (#{startStake, jdbcType=NUMERIC} - logic.end_stake) <![CDATA[<]]> 0)
                <if test="endStake != null and endStake != ''">
                    and (#{endStake, jdbcType=NUMERIC} = logic.end_stake or
                    (#{endStake, jdbcType=NUMERIC} - logic.end_stake) * (#{endStake, jdbcType=NUMERIC} - logic.start_stake) <![CDATA[<]]> 0)
                </if>
            </if>
        </if>
        <if test="lineDirect.toString() == '2'.toString">
            <if test="floor != null">
                and (#{startStake, jdbcType=NUMERIC} = logic.end_stake or
                (#{startStake, jdbcType=NUMERIC} - logic.start_stake) <![CDATA[<]]> 1)
            </if>
            <if test="floor == null">
                and (#{startStake, jdbcType=NUMERIC} = logic.end_stake or
                (#{startStake, jdbcType=NUMERIC} - logic.end_stake) * (#{startStake, jdbcType=NUMERIC} - logic.start_stake) <![CDATA[<]]> 0)
            </if>

        </if>
        and (LINE.line_id = #{lineId, jdbcType=VARCHAR} or LINE.LINE_CODE=#{lineId, jdbcType=VARCHAR})  and ROWNUM<![CDATA[<=]]>1
    </select>
    <select id="getStartStake" resultType="java.util.Map">
        SELECT MIN(L.START_STAKE) as START_STAKE,MAX(L.END_STAKE) as END_STAKE
        FROM GDGS.BASE_ROUTE_LOGIC L
                 JOIN GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        WHERE L.OPRT_ORG_CODE = '${orgId}'
        AND (L.LINE_CODE = '${lineId}' OR L.LINE_ID='${lineId}')  AND L.LINE_DIRECT=1
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO PBD_MAIN_POINT_PAVEMENT
        (ID, LINE_CODE, STARTSTAKE, ENDSTAKE, TYPENAME, REMARK, ORG_ID)
        SELECT A.* FROM (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT #{item.id}, #{item.lineCode}, #{item.startStake}, #{item.endStake}, #{item.typeName}, #{item.remark},
            #{item.orgId} from dual
        </foreach>
        )
    </insert>

    <select id="getTypeNameByDssinfoPavement" resultType="java.util.Map">
        SELECT T.STARTSTAKE,T.ENDSTAKE as END_STAKE,T.TYPENAME as MEASURE_NAME
        FROM PTCMSDB.PBD_MAIN_POINT_PAVEMENT T JOIN GDGS.BASE_LINE L ON T.LINE_CODE=L.LINE_CODE
        WHERE (T.LINE_CODE = '${lineCode}' OR L.LINE_ID='${lineCode}')
        AND T.ORG_ID='${orgId}' AND T.LANE='${lane}'
        AND ((T.STARTSTAKE - ${endStake}) * (T.STARTSTAKE - ${startStake}) <![CDATA[<]]> 0
        or (${startStake} = T.STARTSTAKE))
    </select>

    <select id="getOrgIdStakeRange" resultType="com.hualu.highwaymaintenance.util.BaseStake">
        SELECT L.START_STAKE as lineStartStake,L.END_STAKE as lineEndStake,L.ROUTE_CODE as routeCode
        FROM GDGS.BASE_ROUTE_LOGIC L JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE=LINE.LINE_CODE
        WHERE (L.LINE_CODE = '${lineCode}' OR LINE.LINE_ID='${lineCode}')
          AND L.OPRT_ORG_CODE = '${orgId}'
          AND L.LINE_DIRECT = '${lineDirect}'
    </select>
    <delete id="removeOldUnitsList">
        delete
            FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION D
            where exists(
                          select 1
                          from GDGS.BASE_ROUTE_LOGIC L
                                   join GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
                          where D.ROUTE_CODE = L.ROUTE_CODE
                            and (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
                AND L.OPRT_ORG_CODE='${orgId}'
                      ) and D.ROUTE_CODE IN
        <foreach collection="arr" item="routeCode" index="index" open="(" close=")" separator=",">
            '${routeCode}'
        </foreach> AND D.USERED='21'
    </delete>

    <delete id="removeOldZeroUnitsList">
        delete
        FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION D
        where exists(
        select 1
        from GDGS.BASE_ROUTE_LOGIC L
        join GDGS.BASE_LINE LS ON L.LINE_CODE = LS.LINE_CODE
        where D.ROUTE_CODE = L.ROUTE_CODE
        and (LS.LINE_CODE='${lineId}' OR LS.LINE_ID='${lineId}')
        AND L.OPRT_ORG_CODE='${orgId}'
        ) and D.ROUTE_CODE IN
        <foreach collection="arr" item="routeCode" index="index" open="(" close=")" separator=",">
            '${routeCode}'
        </foreach> AND D.USERED='22'
    </delete>

    <select id="getHistoryTcDetail" resultType="java.util.Map">
        SELECT to_char(D.YEAR) as YEAR,D.${typeName}
        FROM PTCMSDB.PTCD_TC_DETAIL_1000_DESION D
                 JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION PS ON D.UNIT_MARGE_ID = PS.UNIT_MARGE_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON PS.ROUTE_CODE = L.ROUTE_CODE
                 JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE = LINE.LINE_CODE
        WHERE D.YEAR = ${year}
          AND L.OPRT_ORG_CODE = '${orgId}'
          AND (LINE.LINE_CODE = '${lineCode}' OR LINE.LINE_ID = '${lineCode}')
    </select>
    <select id="getDssTypeNum" resultType="com.hualu.highwaymaintenance.decision.entity.PieChartsEntity">
        SELECT SD.DSS_TYPE_NAME as name,COUNT(1) as value
        FROM MEMSDB.DSS_INFO O
                 JOIN GDGS.BASE_ROUTE_LOGIC L ON O.ROUTECODE = L.ROUTE_CODE
                 JOIN GDGS.FW_RIGHT_ORG ORG ON L.OPRT_ORG_CODE=ORG.ID
                 JOIN GDGS.BASE_LINE LINE ON L.LINE_CODE=LINE.LINE_CODE
                 JOIN PTCMSDB.TCC_INSP_PRJ P ON O.REL_TASK_CODE=P.PRJ_ID
                 JOIN PTCMSDB.PTCD_HPAS_SD_PAVEMENT SD ON SD.FACILITY_CAT=O.FACILITY_CAT AND SD.DSS_TYPE=O.DSS_TYPE
            AND SD.DSS_DEGREE=DECODE(O.DSS_DEGREE,NULL,'01','重','03','中','02','轻','01',O.DSS_DEGREE)
        WHERE ORG.ID='${orgId}' AND (LINE.LINE_CODE = '${lineCode}' OR LINE.LINE_ID = '${lineCode}')
        AND P.PRJYEAR='${year}' AND O.FACILITY_CAT='LM'
        GROUP BY O.DSS_TYPE,SD.DSS_TYPE_NAME
    </select>
    <select id="getAllPrjId" resultType="java.util.Map">
        SELECT D.MNG_ORG_ID,D.PRJ_ID FROM PTCMSDB.TCC_INSP_PRJ_DESION D WHERE D.PRJYEAR='2022'
    </select>
    <select id="getOrgNameList" resultType="java.util.Map">
        select O.ID,O.ORG_FULLNAME as ORG_NAME from GDGS.FW_RIGHT_ORG O WHERE O.ORG_FULLNAME IN
        <foreach collection="arr" item="routeCode" index="index" open="(" close=")" separator=",">
            '${routeCode}'
        </foreach>  and o.ORG_LEVEL=3
    </select>
    <select id="getAllPrjLineCode" resultType="java.util.Map">
        SELECT DISTINCT P.OPRT_ORG_CODE AS MNG_ORG_ID,L.LINE_ID
        FROM GDGS.BASE_ROUTE_PHYSICS P
                 JOIN GDGS.BASE_LINE L ON P.LINE_CODE = L.LINE_CODE
        WHERE NOT EXISTS(
            SELECT 1 FROM PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION D JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION T ON D.UNIT_MARGE_ID=T.UNIT_MARGE_ID
            WHERE D.USERED=22 AND P.ROUTE_CODE=D.ROUTE_CODE
        ) AND P.OPRT_ORG_CODE='N000038' AND L.LINE_CODE='G94'
    </select>
</mapper>
