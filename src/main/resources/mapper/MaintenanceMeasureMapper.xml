<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.MaintenanceMeasureMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.MaintenanceMeasure">
    <!--@mbg.generated-->
    <!--@Table PMSDB.MAINTENANCE_MEASURE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MAINTENANCE_TYPE" jdbcType="VARCHAR" property="maintenanceType" />
    <result column="MAINTENANCE_POLICY_NAME1" jdbcType="VARCHAR" property="maintenancePolicyName1" />
    <result column="MAINTENANCE_POLICY_NAME2" jdbcType="VARCHAR" property="maintenancePolicyName2" />
    <result column="MAINTENANCE_MEASURE_NAME" jdbcType="VARCHAR" property="maintenanceMeasureName" />
    <result column="ORDERS" jdbcType="DECIMAL" property="orders" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MAINTENANCE_TYPE, MAINTENANCE_POLICY_NAME1, MAINTENANCE_POLICY_NAME2, MAINTENANCE_MEASURE_NAME, 
    ORDERS
  </sql>

  <select id="findOriginalTcDetail" resultType="java.util.Map">
      select t.START_STAKE,
             t.END_STAKE,
             L.LINE_CODE,
             d.LANE,
             abs(d.START_STAKE-d.END_STAKE)* 1000 as length,
             t.CUSTOM_BETA,
             t.CUSTOM_ALPHA,
             L.LINE_CODE,
             d.UNIT_MARGE_ID as UNITCODE,
             <if test="index == 'PCI'.toString()">
                 de.PCI_FRONT
             </if>
             <if test="index != 'PCI'.toString()">
                 de.${index}
             </if>
      from PMSDB.T_FORECAST_RESULT t
               JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION d
                    on t.START_STAKE = d.START_STAKE and t.ROUTE_CODE = d.ROUTE_CODE and t.LANE = d.LANE
               JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION DE ON D.UNIT_MARGE_ID = DE.UNIT_MARGE_ID
               JOIN PTCMSDB.TCC_INSP_PRJ_DESION P ON DE.PRJ_ID = P.MNG_ORG_ID
               JOIN GDGS.BASE_ROUTE_LOGIC L ON D.ROUTE_CODE=L.ROUTE_CODE
      WHERE P.PRJ_ID = '${prjId}'
        AND P.PRJYEAR = '${years}'
        AND T.FORECAST_TYPE = '${index}'
        <if test="lane != null and lane != ''">
            AND d.LANE='${lane}'
        </if>
      ORDER BY D.LANE,D.START_STAKE
  </select>
  <select id="findCacheOriginalTcDetail" resultType="java.util.Map">
      SELECT L.START_STAKE,
               L.END_STAKE,
               LOGIC.LINE_CODE,
               L.UNIT_MARGIN_ID,
               L.LANE,
               l.MEANSURE_TYPE,
               abs(L.START_STAKE - L.END_STAKE) * 1000 as length,
               L.CUSTOM_ALPHA,
               L.CUSTOM_BETA,
               L.INDEX_JSON_SCORE,
               L.INDEX_JSON_SCORE_BEHIND,
               L.WHETHER,
              <if test="index == 'PCI'.toString()">
                  DEcode(de.PCI_FRONT,null, SUBSTR(L.INDEX_JSON_SCORE, INSTR(L.INDEX_JSON_SCORE, ':') + 1, INSTR(L.INDEX_JSON_SCORE, '，') - INSTR(L.INDEX_JSON_SCORE, ':') - 1)
                  , de.PCI_FRONT) as PCI_FRONT
              </if>
              <if test="index != 'PCI'.toString()">
                  de.${index}
              </if>
        FROM PTCMSDB.PBD_MANYEARS_MEANSURE_LIST L
                 JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION U ON U.UNIT_MARGE_ID = L.UNIT_MARGIN_ID
                 LEFT JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION DE ON U.UNIT_MARGE_ID = DE.UNIT_MARGE_ID
                 JOIN GDGS.BASE_ROUTE_LOGIC LOGIC ON U.ROUTE_CODE = LOGIC.ROUTE_CODE
        WHERE L.PRJ_ID = '${prjId}'
          AND L.INDEX_TYPE ='${index}'
      <if test="lane != null and lane != ''">
          AND L.LANE='${lane}'
      </if>
      <if test="arr != null and arr.size() > 0">
          AND L.UNIT_MARGIN_ID IN
          <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
              '${lineId}'
          </foreach>
      </if>
      ORDER BY L.LANE,L.START_STAKE
  </select>
  <update id="updateMeaSureWhether">
      UPDATE PTCMSDB.PBD_MANYEARS_MEANSURE_LIST L SET L.WHETHER=${whether} WHERE
      <if test="arr != null and arr.size() > 0">
      L.UNIT_MARGIN_ID IN
      <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
          '${lineId}'
      </foreach>
  </if>
  </update>
  <update id="saveBehindScore">
      UPDATE PTCMSDB.PBD_MANYEARS_MEANSURE_LIST LI SET LI.INDEX_JSON_SCORE_BEHIND='${behind}',LI.MEANSURE_TYPE='${type}' WHERE LI.PRJ_ID='${prjId}'
      AND LI.UNIT_MARGIN_ID IN
      <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
          '${lineId}'
      </foreach>
      AND LI.YEARS='${years}' and LI.INDEX_TYPE='${index}'
  </update>
  <update id="saveBehindAnalysisScore">
      UPDATE PTCMSDB.PBD_MANYEARS_MEANSURE_LIST LI SET LI.SUBTRACT=${subtract},LI.SUBTRACT_FRONT=${subtractFront},LI.SUBTRACT_BEHIND=${subtractBehind}
      ,LI.MAXMONEY=${money},LI.MINMONEY=${minMoney},LI.GOOD_NAME='${goodName}'
      WHERE LI.PRJ_ID='${prjId}' AND LI.UNIT_MARGIN_ID='${arr}' AND LI.YEARS='${years}' and LI.INDEX_TYPE='${index}'
   </update>
    <select id="findMeanFundTableList" resultType="java.util.Map">
        SELECT L.START_STAKE,
           L.END_STAKE,
           L.UNIT_MARGIN_ID,
           LOGIC.LINE_CODE,
           L.LANE,
           abs(L.START_STAKE - L.END_STAKE) * 1000 as LENGTH,
           L.CUSTOM_ALPHA,
           L.CUSTOM_BETA,
           L.SUBTRACT_FRONT,
           l.SUBTRACT_BEHIND,
           l.SUBTRACT,
           l.MEANSURE_TYPE,
           L.MAXMONEY,
           L.MINMONEY,
           l.PRJ_ID,
           L.GOOD_NAME
        FROM PTCMSDB.PBD_MANYEARS_MEANSURE_LIST L
        JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION U ON U.UNIT_MARGE_ID = L.UNIT_MARGIN_ID
        JOIN GDGS.BASE_ROUTE_LOGIC LOGIC ON U.ROUTE_CODE = LOGIC.ROUTE_CODE
        WHERE L.PRJ_ID = '${prjId}'
        AND L.INDEX_TYPE ='${index}'
        AND L.YEARS = '${year}'
        <if test="lane != null and lane != ''">
            AND L.LANE='${lane}'
        </if>
        AND L.SUBTRACT IS NOT NULL AND L.YEARNUMBER=${years}
        ORDER BY L.LANE,L.START_STAKE
    </select>
    <select id="getMeaSurePrice" resultType="java.util.Map">
        SELECT m.PRICE,m.id,m.MAINTENANCE_MEASURE_NAME
        FROM PMSDB.MAINTENANCE_MEASURE_MONEY M
        WHERE M.PROJECT_ID = '${prjId}'
        AND M.ID IN
        <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
            '${lineId}'
        </foreach>
    </select>
    <select id="getAllGroupIndexScore" resultType="java.util.Map">
        SELECT * FROM PTCMSDB.PTCD_MONEY_WEIGHT_DETAIL L WHERE L.PRJYEAR = '${year}'
        <!--select
          ORG.ORG_NAME,ORG.ID,P.PRJ_ID,L.LINE_CODE,
            <if test="index == 'PCI'.toString()">
                ROUND(avg(de.PCI_FRONT),2) as PCI,
            </if>
            <if test="index != 'PCI'.toString()">
                ROUND(avg(de.${index}),2) as ${index},
            </if>
          abs(TO_NUMBER(MIN(SUBSTR(L.BUILT_DATE,0,4)))-to_number(TO_CHAR(SYSDATE, 'YYYY'))) AS PASSCAR
          from PMSDB.T_FORECAST_RESULT t
                   JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION d
                        on t.START_STAKE = d.START_STAKE and t.ROUTE_CODE = d.ROUTE_CODE and t.LANE = d.LANE
                   JOIN PTCMSDB.PTCD_TC_DETAIL_1000_DESION DE ON D.UNIT_MARGE_ID = DE.UNIT_MARGE_ID
                   JOIN PTCMSDB.TCC_INSP_PRJ_DESION P ON DE.PRJ_ID = P.MNG_ORG_ID
                   JOIN GDGS.BASE_ROUTE_PHYSICS L ON D.ROUTE_CODE=L.ROUTE_CODE
                   JOIN GDGS.FW_RIGHT_ORG ORG ON ORG.ID=L.OPRT_ORG_CODE
          WHERE P.PRJYEAR = '${year}'
          AND T.FORECAST_TYPE ='${index}'
          GROUP BY ORG.ID,ORG.ORG_NAME,P.PRJ_ID,L.LINE_CODE-->
    </select>
    <select id="getToTraffic" resultType="java.util.Map">
        SELECT ROUND(sum(D.TYPE_TOTAL)/365,0) as TYPE_TOTAL,d.YEAR,D.OPRT_ORG_CODE,D.LINE_CODE FROM MEMSDB.TRAFFIC_DECISION D WHERE
        D.OPRT_ORG_CODE IN
        <foreach collection="arr" item="lineId" index="index" open="(" close=")" separator=",">
            '${lineId}'
        </foreach> AND D.LINE_DIRECT=1
        GROUP BY D.OPRT_ORG_CODE,D.YEAR,D.LINE_CODE ORDER BY D.OPRT_ORG_CODE,D.LINE_CODE,D.YEAR DESC
    </select>
    <update id="updateTrafficNumber">
        UPDATE PTCMSDB.TC_DETAIL_CONDITION C SET C.WEIGHT=${WEIGHT},C.CONDITION=${CONDITION},C.DETAIL_CODE=${DETAIL_CODE}
        WHERE C.INDEX_TYPE='${INDEX_TYPE}' AND C.ID='${ID}'
    </update>
    <update id="updateTrafficCode">
        UPDATE PTCMSDB.TRAFFIC_GRADE_CODE D SET D.ADDT_CONDITION=${condition},D.TRAFFIC_NUMBER=${number},D.ADDT_WEIGHT=${weight}
        WHERE D.TRAFFIC_GRADE_CODE='${code}'
    </update>
    <update id="updateAdministrationCode">
        update PTCMSDB.ADMINISTRATION_GRADLE E SET E.GRADLE_SCORE=${score}, E.GRADLE_WEIGHT=${weight} WHERE GRADLE_CODE=${code}
    </update>
    <select id="getConditionMap" resultType="java.util.Map">
        SELECT * FROM PTCMSDB.TC_DETAIL_CONDITION C WHERE C.INDEX_TYPE='${index}'
    </select>
    <select id="getTrafficMap" resultType="java.util.Map">
        SELECT M.TRAFFIC_GRADE_NAME, TRAFFIC_NUMBER, ADDT_CONDITION, ADDT_WEIGHT, TRAFFIC_GRADE_CODE FROM PTCMSDB.TRAFFIC_GRADE_CODE M
    </select>
    <select id="getAdministrationMap" resultType="java.util.Map">
        SELECT * FROM PTCMSDB.ADMINISTRATION_GRADLE M
    </select>
</mapper>