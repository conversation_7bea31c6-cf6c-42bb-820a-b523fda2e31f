<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.tunnel.mapper.GpTunnelBaseInfoMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.tunnel.entity.GpTunnelBaseInfo">
            <result property="tunnelid" column="TUNNELID" jdbcType="VARCHAR"/>
            <result property="areacode" column="AREACODE" jdbcType="VARCHAR"/>
            <result property="routecode" column="ROUTECODE" jdbcType="VARCHAR"/>
            <result property="routename" column="ROUTENAME" jdbcType="VARCHAR"/>
            <result property="routetechnicalgrade" column="ROUTETECHNICALGRADE" jdbcType="VARCHAR"/>
            <result property="tunnelcode" column="TUNNELCODE" jdbcType="VARCHAR"/>
            <result property="tunnelname" column="TUNNELNAME" jdbcType="VARCHAR"/>
            <result property="tunnelentrancestake" column="TUNNELENTRANCESTAKE" jdbcType="DECIMAL"/>
            <result property="routetype" column="ROUTETYPE" jdbcType="VARCHAR"/>
            <result property="trafficdirection" column="TRAFFICDIRECTION" jdbcType="VARCHAR"/>
            <result property="tunnelcategory" column="TUNNELCATEGORY" jdbcType="VARCHAR"/>
            <result property="buildyear" column="BUILDYEAR" jdbcType="DECIMAL"/>
            <result property="openingtime" column="OPENINGTIME" jdbcType="DECIMAL"/>
            <result property="whetherunderwatertunnel" column="WHETHERUNDERWATERTUNNEL" jdbcType="VARCHAR"/>
            <result property="maintenancelevel" column="MAINTENANCELEVEL" jdbcType="VARCHAR"/>
            <result property="whethercrossprovincialtunnel" column="WHETHERCROSSPROVINCIALTUNNEL" jdbcType="VARCHAR"/>
            <result property="tunnelbelongtoprovinces" column="TUNNELBELONGTOPROVINCES" jdbcType="VARCHAR"/>
            <result property="whethertogethermanagement" column="WHETHERTOGETHERMANAGEMENT" jdbcType="VARCHAR"/>
            <result property="currentprovincestartstake" column="CURRENTPROVINCESTARTSTAKE" jdbcType="DECIMAL"/>
            <result property="currentprovinceendstake" column="CURRENTPROVINCEENDSTAKE" jdbcType="DECIMAL"/>
            <result property="currentprovincelength" column="CURRENTPROVINCELENGTH" jdbcType="DECIMAL"/>
            <result property="companyproperty" column="COMPANYPROPERTY" jdbcType="VARCHAR"/>
            <result property="managementunitname" column="MANAGEMENTUNITNAME" jdbcType="VARCHAR"/>
            <result property="maintenanceunitname" column="MAINTENANCEUNITNAME" jdbcType="VARCHAR"/>
            <result property="buildunit" column="BUILDUNIT" jdbcType="VARCHAR"/>
            <result property="designunit" column="DESIGNUNIT" jdbcType="VARCHAR"/>
            <result property="constructionunit" column="CONSTRUCTIONUNIT" jdbcType="VARCHAR"/>
            <result property="supervisionunit" column="SUPERVISIONUNIT" jdbcType="VARCHAR"/>
            <result property="monitorunitname" column="MONITORUNITNAME" jdbcType="VARCHAR"/>
            <result property="whetherinlongspancatalog" column="WHETHERINLONGSPANCATALOG" jdbcType="VARCHAR"/>
            <result property="seismicgrade" column="SEISMICGRADE" jdbcType="VARCHAR"/>
            <result property="designfloodfrequency" column="DESIGNFLOODFREQUENCY" jdbcType="VARCHAR"/>
            <result property="designspeed" column="DESIGNSPEED" jdbcType="DECIMAL"/>
            <result property="lanenumber" column="LANENUMBER" jdbcType="DECIMAL"/>
            <result property="constructionmethod" column="CONSTRUCTIONMETHOD" jdbcType="VARCHAR"/>
            <result property="entrytype" column="ENTRYTYPE" jdbcType="VARCHAR"/>
            <result property="exittype" column="EXITTYPE" jdbcType="VARCHAR"/>
            <result property="longitude" column="LONGITUDE" jdbcType="DECIMAL"/>
            <result property="latitude" column="LATITUDE" jdbcType="DECIMAL"/>
            <result property="tunnellength" column="TUNNELLENGTH" jdbcType="DECIMAL"/>
            <result property="tunnelwidth" column="TUNNELWIDTH" jdbcType="DECIMAL"/>
            <result property="tunnelheight" column="TUNNELHEIGHT" jdbcType="DECIMAL"/>
            <result property="singlelanewidth" column="SINGLELANEWIDTH" jdbcType="DECIMAL"/>
            <result property="lanetotalwidth" column="LANETOTALWIDTH" jdbcType="DECIMAL"/>
            <result property="overhaulroad" column="OVERHAULROAD" jdbcType="VARCHAR"/>
            <result property="overhaulroadwidth" column="OVERHAULROADWIDTH" jdbcType="DECIMAL"/>
            <result property="maxgradient" column="MAXGRADIENT" jdbcType="DECIMAL"/>
            <result property="surroundrockgrade" column="SURROUNDROCKGRADE" jdbcType="VARCHAR"/>
            <result property="liningmaterials" column="LININGMATERIALS" jdbcType="VARCHAR"/>
            <result property="liningtypes" column="LININGTYPES" jdbcType="VARCHAR"/>
            <result property="crosssectiontypes" column="CROSSSECTIONTYPES" jdbcType="VARCHAR"/>
            <result property="antidrainagetype" column="ANTIDRAINAGETYPE" jdbcType="VARCHAR"/>
            <result property="tunnelroadtypes" column="TUNNELROADTYPES" jdbcType="VARCHAR"/>
            <result property="walkcrossnumber" column="WALKCROSSNUMBER" jdbcType="DECIMAL"/>
            <result property="trafficcrossnumber" column="TRAFFICCROSSNUMBER" jdbcType="DECIMAL"/>
            <result property="emergencyparknumber" column="EMERGENCYPARKNUMBER" jdbcType="DECIMAL"/>
            <result property="whetherescapepassage" column="WHETHERESCAPEPASSAGE" jdbcType="VARCHAR"/>
            <result property="configlevel" column="CONFIGLEVEL" jdbcType="VARCHAR"/>
            <result property="facilitytype" column="FACILITYTYPE" jdbcType="VARCHAR"/>
            <result property="communalelecfacilitytunnel" column="COMMUNALELECFACILITYTUNNEL" jdbcType="VARCHAR"/>
            <result property="ventilationinstallations" column="VENTILATIONINSTALLATIONS" jdbcType="VARCHAR"/>
            <result property="lightingcontrollermode" column="LIGHTINGCONTROLLERMODE" jdbcType="VARCHAR"/>
            <result property="entranceprotection" column="ENTRANCEPROTECTION" jdbcType="VARCHAR"/>
            <result property="exitprotectionandtransition" column="EXITPROTECTIONANDTRANSITION" jdbcType="VARCHAR"/>
            <result property="entrancephotopath" column="ENTRANCEPHOTOPATH" jdbcType="VARCHAR"/>
            <result property="insidephotopath" column="INSIDEPHOTOPATH" jdbcType="VARCHAR"/>
            <result property="typicalphotopath" column="TYPICALPHOTOPATH" jdbcType="VARCHAR"/>
            <result property="designfilespath" column="DESIGNFILESPATH" jdbcType="VARCHAR"/>
            <result property="finishpaperspath" column="FINISHPAPERSPATH" jdbcType="VARCHAR"/>
            <result property="areaname" column="AREANAME" jdbcType="VARCHAR"/>
            <result property="city" column="CITY" jdbcType="VARCHAR"/>
            <result property="ligntype" column="LIGNTYPE" jdbcType="VARCHAR"/>
            <result property="grade" column="GRADE" jdbcType="VARCHAR"/>
            <result property="civilgrade" column="CIVILGRADE" jdbcType="VARCHAR"/>
            <result property="elegrade" column="ELEGRADE" jdbcType="VARCHAR"/>
            <result property="orgcode" column="ORGCODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        TUNNELID,AREACODE,ROUTECODE,
        ROUTENAME,ROUTETECHNICALGRADE,TUNNELCODE,
        TUNNELNAME,TUNNELENTRANCESTAKE,ROUTETYPE,
        TRAFFICDIRECTION,TUNNELCATEGORY,BUILDYEAR,
        OPENINGTIME,WHETHERUNDERWATERTUNNEL,MAINTENANCELEVEL,
        WHETHERCROSSPROVINCIALTUNNEL,TUNNELBELONGTOPROVINCES,WHETHERTOGETHERMANAGEMENT,
        CURRENTPROVINCESTARTSTAKE,CURRENTPROVINCEENDSTAKE,CURRENTPROVINCELENGTH,
        COMPANYPROPERTY,MANAGEMENTUNITNAME,MAINTENANCEUNITNAME,
        BUILDUNIT,DESIGNUNIT,CONSTRUCTIONUNIT,
        SUPERVISIONUNIT,MONITORUNITNAME,WHETHERINLONGSPANCATALOG,
        SEISMICGRADE,DESIGNFLOODFREQUENCY,DESIGNSPEED,
        LANENUMBER,CONSTRUCTIONMETHOD,ENTRYTYPE,
        EXITTYPE,LONGITUDE,LATITUDE,
        TUNNELLENGTH,TUNNELWIDTH,TUNNELHEIGHT,
        SINGLELANEWIDTH,LANETOTALWIDTH,OVERHAULROAD,
        OVERHAULROADWIDTH,MAXGRADIENT,SURROUNDROCKGRADE,
        LININGMATERIALS,LININGTYPES,CROSSSECTIONTYPES,
        ANTIDRAINAGETYPE,TUNNELROADTYPES,WALKCROSSNUMBER,
        TRAFFICCROSSNUMBER,EMERGENCYPARKNUMBER,WHETHERESCAPEPASSAGE,
        CONFIGLEVEL,FACILITYTYPE,COMMUNALELECFACILITYTUNNEL,
        VENTILATIONINSTALLATIONS,LIGHTINGCONTROLLERMODE,ENTRANCEPROTECTION,
        EXITPROTECTIONANDTRANSITION,ENTRANCEPHOTOPATH,INSIDEPHOTOPATH,
        TYPICALPHOTOPATH,DESIGNFILESPATH,FINISHPAPERSPATH,
        AREANAME,CITY,LIGNTYPE,
        GRADE,CIVILGRADE,ELEGRADE,
        ORGCODE
    </sql>
</mapper>
