<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.BridgeRepositoryImpMapper">

    <select id="getBridgeRepositoryImp"
            resultType="com.hualu.highwaymaintenance.module.national.entity.BridgeRepositoryImp">
        select a.ID, ORDERS, ORG_NAME, ORG_CODE, LINE_CODE, LINE_NAME, STAKE, REPAIR_COUNT, PREVENT_COUNT, PREVENT_QUANTITY, REPAIR_QUANTITY, PREVENT_FEE, REPAIR_FEE, TOTAL_FEE, IS_PLAN, PLAN_INVEST, PROJECT_EVOLVE, START_TIME, FINISH_TIME, FINISH_QUANTITY, FINISH_INVEST, REMAR<PERSON>, YEA<PERSON>, IMPORT_TIME,PLAN_REPAIR_COUNT, PLAN_PREVENT_COUNT, PLAN_PREVENT_QUANTITY, PLAN_REPAIR_QUANTITY, PLAN_PREVENT_FEE, PLAN_REPAIR_FEE, TARGET
        from MEMSDB.BRIDGE_REPOSITORY_IMP a
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="lineType != null and lineType != ''">
                and a.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
        order by orders
        </where>
    </select>

    <select id="getBridgeRepositoryImpPage"
            resultType="com.hualu.highwaymaintenance.module.national.entity.BridgeRepositoryImp">
        select a.ID, ORDERS, ORG_NAME, ORG_CODE, LINE_CODE, LINE_NAME, STAKE, REPAIR_COUNT, PREVENT_COUNT, PREVENT_QUANTITY, REPAIR_QUANTITY, PREVENT_FEE, REPAIR_FEE, TOTAL_FEE, IS_PLAN, PLAN_INVEST, PROJECT_EVOLVE, START_TIME, FINISH_TIME, FINISH_QUANTITY, FINISH_INVEST, REMARK, YEAR, IMPORT_TIME,PLAN_REPAIR_COUNT, PLAN_PREVENT_COUNT, PLAN_PREVENT_QUANTITY, PLAN_REPAIR_QUANTITY, PLAN_PREVENT_FEE, PLAN_REPAIR_FEE, TARGET,b.total from MEMSDB.BRIDGE_REPOSITORY_IMP a
        left join (select NATIONAL_ID,count(1) as total from MEMSDB.NATIONAL_ANNEX
        where NATIONAL_SOURCE = 'QL_REPO'
        group by NATIONAL_ID) b
        on a.id = b.NATIONAL_ID
        <where>
            ORG_CODE in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
            <if test="lineType != null and lineType != ''">
                and a.line_code like concat(concat('',#{lineType,jdbcType=VARCHAR}),'%')
            </if>
            <if test="startYear != null">
                and YEAR >= #{startYear,jdbcType=INTEGER}
            </if>

            <if test="endYear != null">
                and YEAR <![CDATA[<=]]> #{endYear,jdbcType=INTEGER}
            </if>
        </where>
        order by orders
    </select>
</mapper>
