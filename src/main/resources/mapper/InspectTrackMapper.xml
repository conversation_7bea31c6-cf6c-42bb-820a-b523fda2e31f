<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.inspect.mapper.InspectTrackMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.inspect.domain.InspectTrack">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="inspectId" column="INSPECT_ID" jdbcType="VARCHAR"/>
            <result property="longitude" column="LONGITUDE" jdbcType="DECIMAL"/>
            <result property="latitude" column="LATITUDE" jdbcType="DECIMAL"/>
            <result property="orders" column="ORDERS" jdbcType="DECIMAL"/>
            <result property="speed" column="SPEED" jdbcType="DECIMAL"/>
            <result property="cardNo" column="CARD_NO" jdbcType="VARCHAR"/>
            <result property="time" column="TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,INSPECT_ID,LONGITUDE,
        LATITUDE,ORDERS,SPEED,
        CARD_NO,TIME
    </sql>
</mapper>
