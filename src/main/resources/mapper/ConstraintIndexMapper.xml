<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.ConstraintIndexMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.ConstraintIndex">
            <id property="constraintIndexId" column="CONSTRAINT_INDEX_ID" jdbcType="VARCHAR"/>
            <result property="constraintIndexName" column="CONSTRAINT_INDEX_NAME" jdbcType="VARCHAR"/>
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONSTRAINT_INDEX_ID,CONSTRAINT_INDEX_NAME,TYPE
    </sql>
</mapper>
