<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.MaintainStandardMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.MaintainStandard">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="prjId" column="PRJ_ID" jdbcType="VARCHAR"/>
            <result property="indexType" column="INDEX_TYPE" jdbcType="VARCHAR"/>
            <result property="standardScore" column="STANDARD_SCORE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PRJ_ID,INDEX_TYPE,
        STANDARD_SCORE
    </sql>
</mapper>
