<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.birdgeMonitor.mapper.OpenApiMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.OpenApi">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="appKey" column="APP_KEY" jdbcType="VARCHAR"/>
            <result property="appSecret" column="APP_SECRET" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createdAt" column="CREATED_AT" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="UPDATED_AT" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,APP_KEY,APP_SECRET,
        STATUS,CREATED_AT,UPDATED_AT
    </sql>
</mapper>
