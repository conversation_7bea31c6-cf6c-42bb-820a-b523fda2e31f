<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqReportDelBridgeMapper">
  <select id="getDelBridgeData"
    resultType="com.hualu.highwaymaintenance.module.datareport.entity.XqReportDelBridge">
    select c.*,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.SECOND_COMPANY_CODE and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) secondCompanyName,
    (select o.ORG_NAME from GDGS.FW_RIGHT_ORG o
    where o.ID = c.org_code and o.IS_ENABLE = 1 and o.IS_DELETED = 0 and ROWNUM = 1) companyName
    from BCTCMSDB.XQ_REPORT_DEL_BRIDGE c
    <where>
      <if test="reportId != null and reportId != ''">
        c.REPORT_ID = #{reportId,jdbcType=VARCHAR}
      </if>

      <if test="orgCode != null and orgCode != ''">
        AND c.ORG_CODE = #{orgCode}
      </if>

      <if test="secondCompany != null and secondCompany != ''">
        AND c.SECOND_COMPANY_CODE = #{secondCompany}
      </if>

      <if test="bridgeName != null and bridgeName != ''">
        and  c.BRIDGE_NAME like concat(concat('%',#{bridgeName}),'%')
      </if>
    </where>
    and exists(
    select 1 from (
    select l.OPRT_ORG_CODE from GDGS.BASE_ROUTE_LOGIC l
    where exists(
    select 1 from (
    select * from GDGS.FW_RIGHT_ORG o
    where o.IS_DELETED = 0 and o.IS_ENABLE = 1
    start with o.ID = (select u.ORG_ID from GDGS.FW_RIGHT_USER u where u.USER_CODE = #{userCode})
    connect by prior o.ID = o.PARENT_ID) m where m.ID = l.OPRT_ORG_CODE
    )) p where p.OPRT_ORG_CODE = c.ORG_CODE
    )
  </select>
</mapper>