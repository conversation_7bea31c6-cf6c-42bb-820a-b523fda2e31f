<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.birdgeMonitor.mapper.BridgeMonitorPointMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorPoint">
    <!--@mbg.generated-->
    <!--@Table BRIDGE_MONITOR_POINT-->
    <id column="POINT_ID" jdbcType="VARCHAR" property="pointId" />
    <result column="POINT_ID_SG" jdbcType="VARCHAR" property="pointIdSg" />
    <result column="CONTRACT_NAME" jdbcType="VARCHAR" property="contractName" />
    <result column="BRIDGE_NAME" jdbcType="VARCHAR" property="bridgeName" />
    <result column="CAMERA_NAME" jdbcType="VARCHAR" property="cameraName" />
    <result column="POINT_NAME" jdbcType="VARCHAR" property="pointName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    POINT_ID, POINT_ID_SG, CONTRACT_NAME, BRIDGE_NAME, CAMERA_NAME, POINT_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from BRIDGE_MONITOR_POINT
    where POINT_ID = #{pointId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from BRIDGE_MONITOR_POINT
    where POINT_ID = #{pointId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorPoint">
    <!--@mbg.generated-->
    insert into BRIDGE_MONITOR_POINT (POINT_ID, POINT_ID_SG, CONTRACT_NAME, 
      BRIDGE_NAME, CAMERA_NAME, POINT_NAME
      )
    values (#{pointId,jdbcType=VARCHAR}, #{pointIdSg,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR}, 
      #{bridgeName,jdbcType=VARCHAR}, #{cameraName,jdbcType=VARCHAR}, #{pointName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorPoint">
    <!--@mbg.generated-->
    insert into BRIDGE_MONITOR_POINT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointId != null">
        POINT_ID,
      </if>
      <if test="pointIdSg != null">
        POINT_ID_SG,
      </if>
      <if test="contractName != null">
        CONTRACT_NAME,
      </if>
      <if test="bridgeName != null">
        BRIDGE_NAME,
      </if>
      <if test="cameraName != null">
        CAMERA_NAME,
      </if>
      <if test="pointName != null">
        POINT_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointId != null">
        #{pointId,jdbcType=VARCHAR},
      </if>
      <if test="pointIdSg != null">
        #{pointIdSg,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="bridgeName != null">
        #{bridgeName,jdbcType=VARCHAR},
      </if>
      <if test="cameraName != null">
        #{cameraName,jdbcType=VARCHAR},
      </if>
      <if test="pointName != null">
        #{pointName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorPoint">
    <!--@mbg.generated-->
    update BRIDGE_MONITOR_POINT
    <set>
      <if test="pointIdSg != null">
        POINT_ID_SG = #{pointIdSg,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        CONTRACT_NAME = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="bridgeName != null">
        BRIDGE_NAME = #{bridgeName,jdbcType=VARCHAR},
      </if>
      <if test="cameraName != null">
        CAMERA_NAME = #{cameraName,jdbcType=VARCHAR},
      </if>
      <if test="pointName != null">
        POINT_NAME = #{pointName,jdbcType=VARCHAR},
      </if>
    </set>
    where POINT_ID = #{pointId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorPoint">
    <!--@mbg.generated-->
    update BRIDGE_MONITOR_POINT
    set POINT_ID_SG = #{pointIdSg,jdbcType=VARCHAR},
      CONTRACT_NAME = #{contractName,jdbcType=VARCHAR},
      BRIDGE_NAME = #{bridgeName,jdbcType=VARCHAR},
      CAMERA_NAME = #{cameraName,jdbcType=VARCHAR},
      POINT_NAME = #{pointName,jdbcType=VARCHAR}
    where POINT_ID = #{pointId,jdbcType=VARCHAR}
  </update>
</mapper>