<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TTunnelDmageDicMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDmageDic">
    <!--@mbg.generated-->
    <!--@Table T_TUNNEL_DMAGE_DIC-->
    <id column="DIC_ID" jdbcType="DECIMAL" property="dicId" />
    <result column="DMAGE_POST" jdbcType="VARCHAR" property="dmagePost" />
    <result column="DMAGE_PART" jdbcType="VARCHAR" property="dmagePart" />
    <result column="DMAGE_TYPE" jdbcType="VARCHAR" property="dmageType" />
    <result column="DMAGE_UNIT" jdbcType="VARCHAR" property="dmageUnit" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DIC_ID, DMAGE_POST, DMAGE_PART, DMAGE_TYPE, DMAGE_UNIT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TUNNEL_DMAGE_DIC
    where DIC_ID = #{dicId,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from T_TUNNEL_DMAGE_DIC
    where DIC_ID = #{dicId,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDmageDic">
    <!--@mbg.generated-->
    insert into T_TUNNEL_DMAGE_DIC (DIC_ID, DMAGE_POST, DMAGE_PART, 
      DMAGE_TYPE, DMAGE_UNIT)
    values (#{dicId,jdbcType=DECIMAL}, #{dmagePost,jdbcType=VARCHAR}, #{dmagePart,jdbcType=VARCHAR}, 
      #{dmageType,jdbcType=VARCHAR}, #{dmageUnit,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDmageDic">
    <!--@mbg.generated-->
    insert into T_TUNNEL_DMAGE_DIC
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dicId != null">
        DIC_ID,
      </if>
      <if test="dmagePost != null">
        DMAGE_POST,
      </if>
      <if test="dmagePart != null">
        DMAGE_PART,
      </if>
      <if test="dmageType != null">
        DMAGE_TYPE,
      </if>
      <if test="dmageUnit != null">
        DMAGE_UNIT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dicId != null">
        #{dicId,jdbcType=DECIMAL},
      </if>
      <if test="dmagePost != null">
        #{dmagePost,jdbcType=VARCHAR},
      </if>
      <if test="dmagePart != null">
        #{dmagePart,jdbcType=VARCHAR},
      </if>
      <if test="dmageType != null">
        #{dmageType,jdbcType=VARCHAR},
      </if>
      <if test="dmageUnit != null">
        #{dmageUnit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDmageDic">
    <!--@mbg.generated-->
    update T_TUNNEL_DMAGE_DIC
    <set>
      <if test="dmagePost != null">
        DMAGE_POST = #{dmagePost,jdbcType=VARCHAR},
      </if>
      <if test="dmagePart != null">
        DMAGE_PART = #{dmagePart,jdbcType=VARCHAR},
      </if>
      <if test="dmageType != null">
        DMAGE_TYPE = #{dmageType,jdbcType=VARCHAR},
      </if>
      <if test="dmageUnit != null">
        DMAGE_UNIT = #{dmageUnit,jdbcType=VARCHAR},
      </if>
    </set>
    where DIC_ID = #{dicId,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDmageDic">
    <!--@mbg.generated-->
    update T_TUNNEL_DMAGE_DIC
    set DMAGE_POST = #{dmagePost,jdbcType=VARCHAR},
      DMAGE_PART = #{dmagePart,jdbcType=VARCHAR},
      DMAGE_TYPE = #{dmageType,jdbcType=VARCHAR},
      DMAGE_UNIT = #{dmageUnit,jdbcType=VARCHAR}
    where DIC_ID = #{dicId,jdbcType=DECIMAL}
  </update>
</mapper>