<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.mpItem.mapper.MpcMpitemMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.mpItem.domain.MpcMpitem">
            <id property="mpitemId" column="MPITEM_ID" jdbcType="VARCHAR"/>
            <result property="mpitemCode" column="MPITEM_CODE" jdbcType="VARCHAR"/>
            <result property="mpitemName" column="MPITEM_NAME" jdbcType="VARCHAR"/>
            <result property="pMpitemId" column="P_MPITEM_ID" jdbcType="VARCHAR"/>
            <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
            <result property="measureUnit" column="MEASURE_UNIT" jdbcType="VARCHAR"/>
            <result property="mpitemBigClass" column="MPITEM_BIG_CLASS" jdbcType="VARCHAR"/>
            <result property="constTech" column="CONST_TECH" jdbcType="VARCHAR"/>
            <result property="constMat" column="CONST_MAT" jdbcType="VARCHAR"/>
            <result property="mechEquip" column="MECH_EQUIP" jdbcType="VARCHAR"/>
            <result property="fieldMgmt" column="FIELD_MGMT" jdbcType="VARCHAR"/>
            <result property="staffCfg" column="STAFF_CFG" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="mpitemSysId" column="MPITEM_SYS_ID" jdbcType="VARCHAR"/>
            <result property="mpitemSname" column="MPITEM_SNAME" jdbcType="VARCHAR"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        MPITEM_ID,MPITEM_CODE,MPITEM_NAME,
        P_MPITEM_ID,ORG_ID,MEASURE_UNIT,
        MPITEM_BIG_CLASS,CONST_TECH,CONST_MAT,
        MECH_EQUIP,FIELD_MGMT,STAFF_CFG,
        REMARK,MPITEM_SYS_ID,MPITEM_SNAME,
        CREATE_USER_ID,UPDATE_USER_ID,CREATE_TIME,
        UPDATE_TIME
    </sql>
    <select id="queryMpitemByContrId2DssType"
            resultMap="BaseResultMap">
        SELECT distinct MPC.mpitem_id,
                        nvl(PRC.MMP_CODE, MPC.mpitem_code) as mpitem_code,
                        MPC.mpitem_name,
                        MPC.p_mpitem_id,
                        MPC.org_id,
                        MPC.measure_unit,
                        MPC.mpitem_big_class,
                        MPC.const_tech,
                        MPC.const_mat,
                        MPC.mech_equip,
                        MPC.field_mgmt,
                        MPC.staff_cfg,
                        MPC.remark,
                        MPC.mpitem_sys_id,
                        MPC.mpitem_sname,
                        MPC.create_user_id,
                        MPC.update_user_id,
                        MPC.create_time,
                        MPC.update_time,
                        PRC.rate,
                        PRC.Mmp_Code
        FROM MEMSDB.MPC_MPITEM MPC,
        MEMSDB.MPC_DSS_MEASURE SURE,
        MEMSDB.MPC_MPITEM_PRICE PRC,
        MEMSDB.MPC_CONTRACT MM
        WHERE mpc.mpitem_sys_id = MM.MPITEM_SYS_ID
          AND MM.CONTR_ID = PRC.CONTR_ID
          and MPC.MPITEM_ID = PRC.MP_ITEM_ID
          AND PRC.CONTR_ID = #{contrId}
          AND MPC.MPITEM_ID = SURE.MPITEM_ID
          AND SURE.DSS_TYPE = #{dssType}
          AND SURE.ORG_ID IN ('N000001', #{token}, (select f.PARENT_ID from GDGS.FW_RIGHT_ORG f where f.id=#{token}))
        <if test="mpitemName != null and mpitemName != ''">
          and mpc.MPITEM_NAME  like concat(concat('%',#{mpitemName}),'%')
        </if>
    </select>
</mapper>
