<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.RepositoryPackageMapper">
  <select id="queryPackage"
          resultType="com.hualu.highwaymaintenance.module.national.entity.RepositoryPackage">
    <choose>
      <when test="level == 1 or level == 2">
        <if test="status != null">
          select (select p.org_name from gdgs.FW_RIGHT_ORG p where p.id=o.parent_id) as second_org,a.id, a.package_name, a.create_date, a.package_type, a.org_code, f.status, a.remark, a.user_code,
          decode(a.status,-1,'退回',0,'待办',1,'审核中','办结') as status_name,a.FLOW_TYPE,a.version,
          (select count(1) from MEMSDB.REPOSITORY_PACKAGE_ANNEX b where a.ID = b.PACKAGE_ID) as total,
          nvl((select DEAL_PERSON from (
          select DEAL_PERSON,PACKAGE_ID, row_number() over (partition by PACKAGE_ID order by FLOW_LEVEL desc) as rn from MEMSDB.REPOSITORY_FLOW ) b
          where rn = 1 and b.PACKAGE_ID = a.ID),u.USER_NAME) as deal_person,
          o.ORG_FULLNAME as org_name,(select b.id from MEMSDB.repository_package b where b.ADJUST=a.ID) as ad from MEMSDB.repository_package a
          inner join gdgs.FW_RIGHT_ORG o on a.org_code = o.org_code
          inner join MEMSDB.REPOSITORY_FLOW f on f.PACKAGE_ID = a.ID
          inner join gdgs.FW_RIGHT_USER u on a.USER_CODE = u.USER_CODE
          where a.package_type = #{packageType}
            and a.version >= '2023'
          <if test="orgCode != null and orgCode != ''">
            and a.org_code in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
          </if>
          <if test="orgCode == null or orgCode == '' or orgCode == 'N000001'">
            and a.ORG_CODE not in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
            start with oo.ID = '90eebfbd-af23-4dd4-b5e5-d5eb7cbeccba'
            connect by prior oo.ID = oo.PARENT_ID)
          </if>
          and decode(f.status,-1,0,f.status) = #{status}
          and f.deal_user_code = #{userCode}
        </if>
        <if test="status == null">
          select (select p.org_name from gdgs.FW_RIGHT_ORG p where p.id=o.parent_id) as second_org,a.id, a.package_name, a.create_date, a.package_type, a.org_code, a.status, a.remark, a.user_code,
          decode(a.status,-1,'退回',0,'待办',1,'审核中','办结') as status_name,a.FLOW_TYPE,a.version,
          (select count(1) from MEMSDB.REPOSITORY_PACKAGE_ANNEX b where a.ID = b.PACKAGE_ID) as total,
          nvl((select DEAL_PERSON from (
          select DEAL_PERSON,PACKAGE_ID, row_number() over (partition by PACKAGE_ID order by FLOW_LEVEL desc) as rn from MEMSDB.REPOSITORY_FLOW ) b
          where rn = 1 and b.PACKAGE_ID = a.ID),u.USER_NAME) as deal_person,
          o.ORG_FULLNAME as org_name,(select b.id from MEMSDB.repository_package b where b.ADJUST=a.ID) as ad   from MEMSDB.repository_package a
          inner join gdgs.FW_RIGHT_ORG o on a.org_code = o.org_code
          inner join gdgs.FW_RIGHT_USER u on a.USER_CODE = u.USER_CODE
          where a.package_type = #{packageType}
          and a.version >= '2023'
          <if test="orgCode != null and orgCode != ''">
            and a.org_code in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
          </if>
        </if>
        order by second_org,org_name,package_name
      </when>
      <otherwise>
        <if test="status == null">
          select (select p.org_name from gdgs.FW_RIGHT_ORG p where p.id=o.parent_id) as second_org,a.id, a.package_name, a.create_date, a.package_type, a.org_code, a.status, a.remark, a.user_code,
          decode(a.status,-1,'退回',0,'待办',1,'审核中','办结') as status_name,a.FLOW_TYPE,a.version,
          (select count(1) from MEMSDB.REPOSITORY_PACKAGE_ANNEX b where a.ID = b.PACKAGE_ID) as total,
          nvl((select DEAL_PERSON from (
          select DEAL_PERSON,PACKAGE_ID, row_number() over (partition by PACKAGE_ID order by FLOW_LEVEL desc) as rn from MEMSDB.REPOSITORY_FLOW ) b
          where rn = 1 and b.PACKAGE_ID = a.ID),u.USER_NAME) as deal_person,
          o.ORG_FULLNAME as org_name,(select b.id from MEMSDB.repository_package b where b.ADJUST=a.ID) as ad  from MEMSDB.repository_package a
          inner join gdgs.FW_RIGHT_ORG o on a.org_code = o.org_code
          inner join gdgs.FW_RIGHT_USER u on a.USER_CODE = u.USER_CODE
          where a.package_type = #{packageType}
          and a.version >= '2023'
          <if test="orgCode != null and orgCode != ''">
            and a.org_code in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
          </if>
          order by second_org,org_name,package_name
        </if>
        <if test="status != null">
          select (select p.org_name from gdgs.FW_RIGHT_ORG p where p.id=o.parent_id) as second_org,a.id, a.package_name, a.create_date, a.package_type, a.org_code, a.status, a.remark, a.user_code,
          decode(a.status,-1,'退回',0,'待办',1,'审核中','办结') as status_name,a.FLOW_TYPE,a.version,
          (select count(1) from MEMSDB.REPOSITORY_PACKAGE_ANNEX b where a.ID = b.PACKAGE_ID) as total,
          nvl((select DEAL_PERSON from (
          select DEAL_PERSON,PACKAGE_ID, row_number() over (partition by PACKAGE_ID order by FLOW_LEVEL desc) as rn from MEMSDB.REPOSITORY_FLOW ) b
          where rn = 1 and b.PACKAGE_ID = a.ID),u.USER_NAME) as deal_person,
          o.ORG_FULLNAME as org_name,(select b.id from MEMSDB.repository_package b where b.ADJUST=a.ID) as ad  from MEMSDB.repository_package a
          inner join gdgs.FW_RIGHT_ORG o on a.org_code = o.org_code
          inner join gdgs.FW_RIGHT_USER u on a.USER_CODE = u.USER_CODE
          where a.package_type = #{packageType}
          and a.user_code = #{userCode}
          and a.version >= '2023'
          <if test="orgCode != null and orgCode != ''">
            and a.org_code in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
          </if>
          <if test="status != null">
            and decode(a.status,-1,0,a.status) = #{status}
          </if>
          union all
          select (select p.org_name from gdgs.FW_RIGHT_ORG p where p.id=o.parent_id) as second_org,a.id, a.package_name, a.create_date, a.package_type, a.org_code, f.status, a.remark, a.user_code,
          decode(a.status,-1,'退回',0,'待办',1,'审核中','办结') as status_name,a.FLOW_TYPE,a.version,
          (select count(1) from MEMSDB.REPOSITORY_PACKAGE_ANNEX b where a.ID = b.PACKAGE_ID) as total,
          nvl((select DEAL_PERSON from (
          select DEAL_PERSON,PACKAGE_ID, row_number() over (partition by PACKAGE_ID order by FLOW_LEVEL desc) as rn from MEMSDB.REPOSITORY_FLOW ) b
          where rn = 1 and b.PACKAGE_ID = a.ID),u.USER_NAME) as deal_person,
          o.ORG_FULLNAME as org_name,(select b.id from MEMSDB.repository_package b where b.ADJUST=a.ID) as ad   from MEMSDB.repository_package a
          inner join gdgs.FW_RIGHT_ORG o on a.org_code = o.org_code
          inner join MEMSDB.REPOSITORY_FLOW f on f.PACKAGE_ID = a.ID
          inner join gdgs.FW_RIGHT_USER u on a.USER_CODE = u.USER_CODE
          where a.package_type = #{packageType}
          and f.deal_user_code = #{userCode}
          and a.version >= '2023'
          <if test="orgCode != null and orgCode != ''">
            and a.org_code in (select oo.ORG_CODE
            from gdgs.FW_RIGHT_ORG oo
            where oo.IS_ENABLE = 1
            and oo.IS_DELETED = 0
            start with oo.ID = #{orgCode,jdbcType=VARCHAR}
            connect by prior oo.ID = oo.PARENT_ID)
          </if>
          <if test="status != null">
            and decode(f.status,-1,0,f.status) = #{status}
          </if>
          order by second_org,org_name,package_name
        </if>
      </otherwise>
    </choose>
  </select>
</mapper>