<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.bridge.mapper.SsfMaintainHisMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.bridge.entity.SsfMaintainHis">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="ssfId" column="SSF_ID" jdbcType="VARCHAR"/>
            <result property="ssfGap" column="SSF_GAP" jdbcType="DECIMAL"/>
            <result property="maintainReason" column="MAINTAIN_REASON" jdbcType="VARCHAR"/>
            <result property="producer" column="PRODUCER" jdbcType="VARCHAR"/>
            <result property="picBefore" column="PIC_BEFORE" jdbcType="VARCHAR"/>
            <result property="picAfter" column="PIC_AFTER" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,SSF_ID,SSF_GAP,
        MAINTAIN_REASON,PRODUCER,PIC_BEFORE,
        PIC_AFTER
    </sql>
</mapper>
