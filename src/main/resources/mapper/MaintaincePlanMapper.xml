<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.national.mapper.MaintaincePlanMapper">
  <select id="getBridgeMaintainPlanPage"
          resultType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
    select * from MEMSDB.BRIDGE_MAINTAIN_PLAN_23 a
    where a.ORG_CODE in (select oo.ORG_CODE
                         from gdgs.FW_RIGHT_ORG oo
                         where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
    start with oo.ID = #{orgCode,jdbcType=VARCHAR}
    connect by prior oo.ID = oo.PARENT_ID)
           and a.year = #{year,jdbcType=INTEGER}
    order by orders
  </select>

  <select id="getPavementMaintainPlanPage"
          resultType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
    select * from MEMSDB.PAVEMENT_MAINTAIN_PLAN_23 a
    where a.ORG_CODE in (select oo.ORG_CODE
                         from gdgs.FW_RIGHT_ORG oo
                         where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
    start with oo.ID = #{orgCode,jdbcType=VARCHAR}
    connect by prior oo.ID = oo.PARENT_ID)
           and a.year = #{year,jdbcType=INTEGER}
    order by orders
  </select>

  <select id="getTunnelMaintainPlanPage"
          resultType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
    select * from MEMSDB.TUNNEL_MAINTAIN_PLAN_23 a
    where a.ORG_CODE in (select oo.ORG_CODE
                         from gdgs.FW_RIGHT_ORG oo
                         where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
    start with oo.ID = #{orgCode,jdbcType=VARCHAR}
    connect by prior oo.ID = oo.PARENT_ID)
           and a.year = #{year,jdbcType=INTEGER}
    order by orders
  </select>

    <select id="getBridgeMaintainPlanList"
            resultType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
        select * from MEMSDB.BRIDGE_MAINTAIN_PLAN_23 a
        where a.ORG_CODE in (select oo.ORG_CODE
                             from gdgs.FW_RIGHT_ORG oo
                             where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
        start with oo.ID = #{orgCode,jdbcType=VARCHAR}
        connect by prior oo.ID = oo.PARENT_ID)
               and a.year = #{year,jdbcType=INTEGER}
        order by orders
    </select>

    <select id="getPavementMaintainPlanList"
            resultType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
        select * from MEMSDB.PAVEMENT_MAINTAIN_PLAN_23 a
        where a.ORG_CODE in (select oo.ORG_CODE
                             from gdgs.FW_RIGHT_ORG oo
                             where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
        start with oo.ID = #{orgCode,jdbcType=VARCHAR}
        connect by prior oo.ID = oo.PARENT_ID)
               and a.year = #{year,jdbcType=INTEGER}
        order by orders
    </select>

    <select id="getTunnelMaintainPlanList"
            resultType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
        select * from MEMSDB.TUNNEL_MAINTAIN_PLAN_23 a
        where a.ORG_CODE in (select oo.ORG_CODE
                             from gdgs.FW_RIGHT_ORG oo
                             where oo.IS_ENABLE = 1 and oo.IS_DELETED = 0
        start with oo.ID = #{orgCode,jdbcType=VARCHAR}
        connect by prior oo.ID = oo.PARENT_ID)
               and a.year = #{year,jdbcType=INTEGER}
        order by orders
    </select>

    <!-- 更新路面养护计划 -->
    <update id="updatePavementMaintainPlan" parameterType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
        UPDATE MEMSDB.PAVEMENT_MAINTAIN_PLAN_23
        <set>
            <if test="orders != null">ORDERS = #{orders,jdbcType=NUMERIC},</if>
            <if test="orgCode != null">ORG_CODE = #{orgCode,jdbcType=VARCHAR},</if>
            <if test="orgName != null">ORG_NAME = #{orgName,jdbcType=VARCHAR},</if>
            <if test="lineCode != null">LINE_CODE = #{lineCode,jdbcType=VARCHAR},</if>
            <if test="lineName != null">LINE_NAME = #{lineName,jdbcType=VARCHAR},</if>
            <if test="stake != null">STAKE = #{stake,jdbcType=VARCHAR},</if>
            <if test="direction != null">DIRECTION = #{direction,jdbcType=VARCHAR},</if>
            <if test="lane != null">LANE = #{lane,jdbcType=VARCHAR},</if>
            <if test="preventKm != null">PREVENT_KM = #{preventKm,jdbcType=NUMERIC},</if>
            <if test="repairKm != null">REPAIR_KM = #{repairKm,jdbcType=NUMERIC},</if>
            <if test="preventCost != null">PREVENT_COST = #{preventCost,jdbcType=NUMERIC},</if>
            <if test="repairCost != null">REPAIR_COST = #{repairCost,jdbcType=NUMERIC},</if>
            <if test="totalCost != null">TOTAL_COST = #{totalCost,jdbcType=NUMERIC},</if>
            <if test="preventKmRel != null">PREVENT_KM_REL = #{preventKmRel,jdbcType=NUMERIC},</if>
            <if test="repairKmRel != null">REPAIR_KM_REL = #{repairKmRel,jdbcType=NUMERIC},</if>
            <if test="preventCostRel != null">PREVENT_COST_REL = #{preventCostRel,jdbcType=NUMERIC},</if>
            <if test="repairCostRel != null">REPAIR_COST_REL = #{repairCostRel,jdbcType=NUMERIC},</if>
            <if test="plannedInvestment != null">PLANNED_INVESTMENT = #{plannedInvestment,jdbcType=NUMERIC},</if>
            <if test="progress != null">PROGRESS = #{progress,jdbcType=VARCHAR},</if>
            <if test="isPreventPlanned != null">IS_PREVENT_PLANNED = #{isPreventPlanned,jdbcType=VARCHAR},</if>
            <if test="isRepairPlanned != null">IS_REPAIR_PLANNED = #{isRepairPlanned,jdbcType=VARCHAR},</if>
            <if test="year != null">YEAR = #{year,jdbcType=NUMERIC}</if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 更新桥梁养护计划 -->
    <update id="updateBridgeMaintainPlan" parameterType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
        UPDATE MEMSDB.BRIDGE_MAINTAIN_PLAN_23
        <set>
            <if test="orders != null">ORDERS = #{orders,jdbcType=NUMERIC},</if>
            <if test="orgCode != null">ORG_CODE = #{orgCode,jdbcType=VARCHAR},</if>
            <if test="orgName != null">ORG_NAME = #{orgName,jdbcType=VARCHAR},</if>
            <if test="lineCode != null">LINE_CODE = #{lineCode,jdbcType=VARCHAR},</if>
            <if test="lineName != null">LINE_NAME = #{lineName,jdbcType=VARCHAR},</if>
            <if test="stake != null">STAKE = #{stake,jdbcType=VARCHAR},</if>
            <if test="preventSeats != null">PREVENT_SEATS = #{preventSeats,jdbcType=NUMERIC},</if>
            <if test="repairSeats != null">REPAIR_SEATS = #{repairSeats,jdbcType=NUMERIC},</if>
            <if test="preventLength != null">PREVENT_LENGTH = #{preventLength,jdbcType=NUMERIC},</if>
            <if test="repairLength != null">REPAIR_LENGTH = #{repairLength,jdbcType=NUMERIC},</if>
            <if test="preventCost != null">PREVENT_COST = #{preventCost,jdbcType=NUMERIC},</if>
            <if test="repairCost != null">REPAIR_COST = #{repairCost,jdbcType=NUMERIC},</if>
            <if test="totalCost != null">TOTAL_COST = #{totalCost,jdbcType=NUMERIC},</if>
            <if test="preventSeatsRel != null">PREVENT_SEATS_REL = #{preventSeatsRel,jdbcType=NUMERIC},</if>
            <if test="repairSeatsRel != null">REPAIR_SEATS_REL = #{repairSeatsRel,jdbcType=NUMERIC},</if>
            <if test="preventLengthRel != null">PREVENT_LENGTH_REL = #{preventLengthRel,jdbcType=NUMERIC},</if>
            <if test="repairLengthRel != null">REPAIR_LENGTH_REL = #{repairLengthRel,jdbcType=NUMERIC},</if>
            <if test="preventCostRel != null">PREVENT_COST_REL = #{preventCostRel,jdbcType=NUMERIC},</if>
            <if test="repairCostRel != null">REPAIR_COST_REL = #{repairCostRel,jdbcType=NUMERIC},</if>
            <if test="plannedInvestment != null">PLANNED_INVESTMENT = #{plannedInvestment,jdbcType=NUMERIC},</if>
            <if test="progress != null">PROGRESS = #{progress,jdbcType=VARCHAR},</if>
            <if test="isPreventPlanned != null">IS_PREVENT_PLANNED = #{isPreventPlanned,jdbcType=VARCHAR},</if>
            <if test="isRepairPlanned != null">IS_REPAIR_PLANNED = #{isRepairPlanned,jdbcType=VARCHAR},</if>
            <if test="year != null">YEAR = #{year,jdbcType=NUMERIC}</if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 更新隧道养护计划 -->
    <update id="updateTunnelMaintainPlan" parameterType="com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo">
        UPDATE MEMSDB.TUNNEL_MAINTAIN_PLAN_23
        <set>
            <if test="orders != null">ORDERS = #{orders,jdbcType=NUMERIC},</if>
            <if test="orgCode != null">ORG_CODE = #{orgCode,jdbcType=VARCHAR},</if>
            <if test="orgName != null">ORG_NAME = #{orgName,jdbcType=VARCHAR},</if>
            <if test="lineCode != null">LINE_CODE = #{lineCode,jdbcType=VARCHAR},</if>
            <if test="lineName != null">LINE_NAME = #{lineName,jdbcType=VARCHAR},</if>
            <if test="stake != null">STAKE = #{stake,jdbcType=VARCHAR},</if>
            <if test="preventSeats != null">PREVENT_SEATS = #{preventSeats,jdbcType=NUMERIC},</if>
            <if test="repairSeats != null">REPAIR_SEATS = #{repairSeats,jdbcType=NUMERIC},</if>
            <if test="preventLength != null">PREVENT_LENGTH = #{preventLength,jdbcType=NUMERIC},</if>
            <if test="repairLength != null">REPAIR_LENGTH = #{repairLength,jdbcType=NUMERIC},</if>
            <if test="civilPreventCost != null">CIVIL_PREVENT_COST = #{civilPreventCost,jdbcType=NUMERIC},</if>
            <if test="civilRepairCost != null">CIVIL_REPAIR_COST = #{civilRepairCost,jdbcType=NUMERIC},</if>
            <if test="civilTotalCost != null">CIVIL_TOTAL_COST = #{civilTotalCost,jdbcType=NUMERIC},</if>
            <if test="mechanicalCost != null">MECHANICAL_COST = #{mechanicalCost,jdbcType=NUMERIC},</if>
            <if test="totalCost != null">TOTAL_COST = #{totalCost,jdbcType=NUMERIC},</if>
            <if test="preventSeatsRel != null">PREVENT_SEATS_REL = #{preventSeatsRel,jdbcType=NUMERIC},</if>
            <if test="repairSeatsRel != null">REPAIR_SEATS_REL = #{repairSeatsRel,jdbcType=NUMERIC},</if>
            <if test="preventLengthRel != null">PREVENT_LENGTH_REL = #{preventLengthRel,jdbcType=NUMERIC},</if>
            <if test="repairLengthRel != null">REPAIR_LENGTH_REL = #{repairLengthRel,jdbcType=NUMERIC},</if>
            <if test="civilPreventCostRel != null">CIVIL_PREVENT_COST_REL = #{civilPreventCostRel,jdbcType=NUMERIC},</if>
            <if test="civilRepairCostRel != null">CIVIL_REPAIR_COST_REL = #{civilRepairCostRel,jdbcType=NUMERIC},</if>
            <if test="civilTotalCostRel != null">CIVIL_TOTAL_COST_REL = #{civilTotalCostRel,jdbcType=NUMERIC},</if>
            <if test="mechanicalCostRel != null">MECHANICAL_COST_REL = #{mechanicalCostRel,jdbcType=NUMERIC},</if>
            <if test="plannedInvestment != null">PLANNED_INVESTMENT = #{plannedInvestment,jdbcType=NUMERIC},</if>
            <if test="progress != null">PROGRESS = #{progress,jdbcType=VARCHAR},</if>
            <if test="isPreventPlanned != null">IS_PREVENT_PLANNED = #{isPreventPlanned,jdbcType=VARCHAR},</if>
            <if test="isRepairPlanned != null">IS_REPAIR_PLANNED = #{isRepairPlanned,jdbcType=VARCHAR},</if>
            <if test="year != null">YEAR = #{year,jdbcType=NUMERIC}</if>
        </set>
        WHERE ID = #{id,jdbcType=VARCHAR}
    </update>
</mapper>