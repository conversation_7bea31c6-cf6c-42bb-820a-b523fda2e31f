<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.birdgeMonitor.mapper.BridgeMonitorLogMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorLog">
    <!--@mbg.generated-->
    <!--@Table BRIDGE_MONITOR_LOG-->
    <id column="DATA_ID" jdbcType="VARCHAR" property="dataId" />
    <result column="APP_KEY" jdbcType="VARCHAR" property="appKey" />
    <result column="SIGNATURE" jdbcType="VARCHAR" property="signature" />
    <result column="TIMES" jdbcType="VARCHAR" property="times" />
    <result column="MONITOR_DATA" jdbcType="VARCHAR" property="monitorData" />
    <result column="API_CALL_TIME" jdbcType="TIMESTAMP" property="apiCallTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DATA_ID, APP_KEY, SIGNATURE, TIMES, MONITOR_DATA, API_CALL_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from BRIDGE_MONITOR_LOG
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from BRIDGE_MONITOR_LOG
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorLog">
    <!--@mbg.generated-->
    insert into BRIDGE_MONITOR_LOG (DATA_ID, APP_KEY, SIGNATURE, 
      TIMES, MONITOR_DATA, API_CALL_TIME
      )
    values (#{dataId,jdbcType=VARCHAR}, #{appKey,jdbcType=VARCHAR}, #{signature,jdbcType=VARCHAR}, 
      #{times,jdbcType=VARCHAR}, #{monitorData,jdbcType=VARCHAR}, #{apiCallTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorLog">
    <!--@mbg.generated-->
    insert into BRIDGE_MONITOR_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        DATA_ID,
      </if>
      <if test="appKey != null">
        APP_KEY,
      </if>
      <if test="signature != null">
        SIGNATURE,
      </if>
      <if test="times != null">
        TIMES,
      </if>
      <if test="monitorData != null">
        MONITOR_DATA,
      </if>
      <if test="apiCallTime != null">
        API_CALL_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataId != null">
        #{dataId,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="signature != null">
        #{signature,jdbcType=VARCHAR},
      </if>
      <if test="times != null">
        #{times,jdbcType=VARCHAR},
      </if>
      <if test="monitorData != null">
        #{monitorData,jdbcType=VARCHAR},
      </if>
      <if test="apiCallTime != null">
        #{apiCallTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorLog">
    <!--@mbg.generated-->
    update BRIDGE_MONITOR_LOG
    <set>
      <if test="appKey != null">
        APP_KEY = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="signature != null">
        SIGNATURE = #{signature,jdbcType=VARCHAR},
      </if>
      <if test="times != null">
        TIMES = #{times,jdbcType=VARCHAR},
      </if>
      <if test="monitorData != null">
        MONITOR_DATA = #{monitorData,jdbcType=VARCHAR},
      </if>
      <if test="apiCallTime != null">
        API_CALL_TIME = #{apiCallTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.hualu.highwaymaintenance.module.birdgeMonitor.entity.BridgeMonitorLog">
    <!--@mbg.generated-->
    update BRIDGE_MONITOR_LOG
    set APP_KEY = #{appKey,jdbcType=VARCHAR},
      SIGNATURE = #{signature,jdbcType=VARCHAR},
      TIMES = #{times,jdbcType=VARCHAR},
      MONITOR_DATA = #{monitorData,jdbcType=VARCHAR},
      API_CALL_TIME = #{apiCallTime,jdbcType=TIMESTAMP}
    where DATA_ID = #{dataId,jdbcType=VARCHAR}
  </update>
</mapper>