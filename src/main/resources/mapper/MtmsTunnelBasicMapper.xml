<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.tunnel.mapper.MtmsTunnelBasicMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.tunnel.entity.MtmsTunnelBasic">
            <id property="tunnelId" column="TUNNEL_ID" jdbcType="VARCHAR"/>
            <result property="tunnelCode" column="TUNNEL_CODE" jdbcType="VARCHAR"/>
            <result property="tunnelName" column="TUNNEL_NAME" jdbcType="VARCHAR"/>
            <result property="tunnelLineDirect" column="TUNNEL_LINE_DIRECT" jdbcType="VARCHAR"/>
            <result property="tunnelRange" column="TUNNEL_RANGE" jdbcType="VARCHAR"/>
            <result property="cntrStake" column="CNTR_STAKE" jdbcType="VARCHAR"/>
            <result property="cntrStakeNum" column="CNTR_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="cntrSenssionNum" column="CNTR_SENSSION_NUM" jdbcType="DECIMAL"/>
            <result property="startStakeNum" column="START_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="endStakeNum" column="END_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="designStartStakeNum" column="DESIGN_START_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="designEndStakeNum" column="DESIGN_END_STAKE_NUM" jdbcType="DECIMAL"/>
            <result property="tunnelLength" column="TUNNEL_LENGTH" jdbcType="DECIMAL"/>
            <result property="tubeCultureUnit" column="TUBE_CULTURE_UNIT" jdbcType="VARCHAR"/>
            <result property="commutDate" column="COMMUT_DATE" jdbcType="TIMESTAMP"/>
            <result property="tunnelWidth" column="TUNNEL_WIDTH" jdbcType="DECIMAL"/>
            <result property="tunnelHeight" column="TUNNEL_HEIGHT" jdbcType="DECIMAL"/>
            <result property="msnrMtrl" column="MSNR_MTRL" jdbcType="VARCHAR"/>
            <result property="inHoleStruct" column="IN_HOLE_STRUCT" jdbcType="VARCHAR"/>
            <result property="outHoleStruct" column="OUT_HOLE_STRUCT" jdbcType="VARCHAR"/>
            <result property="roadSurface" column="ROAD_SURFACE" jdbcType="VARCHAR"/>
            <result property="fireFacilities" column="FIRE_FACILITIES" jdbcType="VARCHAR"/>
            <result property="caveLightForm" column="CAVE_LIGHT_FORM" jdbcType="VARCHAR"/>
            <result property="caveVntltForm" column="CAVE_VNTLT_FORM" jdbcType="VARCHAR"/>
            <result property="isSepType" column="IS_SEP_TYPE" jdbcType="VARCHAR"/>
            <result property="isUnderwaterFlag" column="IS_UNDERWATER_FLAG" jdbcType="VARCHAR"/>
            <result property="sectionForm" column="SECTION_FORM" jdbcType="VARCHAR"/>
            <result property="electronDevice" column="ELECTRON_DEVICE" jdbcType="VARCHAR"/>
            <result property="drainageType" column="DRAINAGE_TYPE" jdbcType="VARCHAR"/>
            <result property="safeAccessNum" column="SAFE_ACCESS_NUM" jdbcType="DECIMAL"/>
            <result property="sidewalk" column="SIDEWALK" jdbcType="DECIMAL"/>
            <result property="roadwayWide" column="ROADWAY_WIDE" jdbcType="DECIMAL"/>
            <result property="buildOrgName" column="BUILD_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="designOrgName" column="DESIGN_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="constructionOrgName" column="CONSTRUCTION_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="supervisorOrgName" column="SUPERVISOR_ORG_NAME" jdbcType="VARCHAR"/>
            <result property="evaluateData" column="EVALUATE_DATA" jdbcType="VARCHAR"/>
            <result property="evaluationUnit" column="EVALUATION_UNIT" jdbcType="VARCHAR"/>
            <result property="completionDate" column="COMPLETION_DATE" jdbcType="VARCHAR"/>
            <result property="remouldPart" column="REMOULD_PART" jdbcType="VARCHAR"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="isEnable" column="IS_ENABLE" jdbcType="VARCHAR"/>
            <result property="tunnelClassified" column="TUNNEL_CLASSIFIED" jdbcType="VARCHAR"/>
            <result property="dstrctCode" column="DSTRCT_CODE" jdbcType="VARCHAR"/>
            <result property="enginProperty" column="ENGIN_PROPERTY" jdbcType="VARCHAR"/>
            <result property="isKeyLabel" column="IS_KEY_LABEL" jdbcType="VARCHAR"/>
            <result property="oprtOrgCode" column="OPRT_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="prjOrgCode" column="PRJ_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="rpIntrvlId" column="RP_INTRVL_ID" jdbcType="VARCHAR"/>
            <result property="mainlineId" column="MAINLINE_ID" jdbcType="VARCHAR"/>
            <result property="startOffset" column="START_OFFSET" jdbcType="DECIMAL"/>
            <result property="mainRampLineId" column="MAIN_RAMP_LINE_ID" jdbcType="VARCHAR"/>
            <result property="threadRpid" column="THREAD_RPID" jdbcType="VARCHAR"/>
            <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserId" column="UPDATE_USER_ID" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="VARCHAR"/>
            <result property="gisId" column="GIS_ID" jdbcType="VARCHAR"/>
            <result property="gisX" column="GIS_X" jdbcType="DECIMAL"/>
            <result property="gisY" column="GIS_Y" jdbcType="DECIMAL"/>
            <result property="gisBdx" column="GIS_BDX" jdbcType="DECIMAL"/>
            <result property="gisBdy" column="GIS_BDY" jdbcType="DECIMAL"/>
            <result property="tunnelMaintainGrade" column="TUNNEL_MAINTAIN_GRADE" jdbcType="VARCHAR"/>
            <result property="builtDate" column="BUILT_DATE" jdbcType="TIMESTAMP"/>
            <result property="laneNum" column="LANE_NUM" jdbcType="DECIMAL"/>
            <result property="prjId" column="PRJ_ID" jdbcType="VARCHAR"/>
            <result property="mechatronicsGrade" column="MECHATRONICS_GRADE" jdbcType="DECIMAL"/>
            <result property="tunnelTcGrade" column="TUNNEL_TC_GRADE" jdbcType="VARCHAR"/>
            <result property="reportNumber" column="REPORT_NUMBER" jdbcType="VARCHAR"/>
            <result property="trafficFacGrade" column="TRAFFIC_FAC_GRADE" jdbcType="VARCHAR"/>
            <result property="rangeEvaluator" column="RANGE_EVALUATOR" jdbcType="VARCHAR"/>
            <result property="rangeGradeDate" column="RANGE_GRADE_DATE" jdbcType="TIMESTAMP"/>
            <result property="mechatronicsEvaluator" column="MECHATRONICS_EVALUATOR" jdbcType="VARCHAR"/>
            <result property="mechatronicsDate" column="MECHATRONICS_DATE" jdbcType="TIMESTAMP"/>
            <result property="otherGrade" column="OTHER_GRADE" jdbcType="VARCHAR"/>
            <result property="otherEvaluator" column="OTHER_EVALUATOR" jdbcType="VARCHAR"/>
            <result property="otherGradeDate" column="OTHER_GRADE_DATE" jdbcType="TIMESTAMP"/>
            <result property="isTunnel" column="IS_TUNNEL" jdbcType="VARCHAR"/>
            <result property="lineCode" column="LINE_CODE" jdbcType="VARCHAR"/>
            <result property="physicsCntrStake" column="PHYSICS_CNTR_STAKE" jdbcType="DECIMAL"/>
            <result property="logicCntrStake" column="LOGIC_CNTR_STAKE" jdbcType="DECIMAL"/>
            <result property="routeCode" column="ROUTE_CODE" jdbcType="VARCHAR"/>
            <result property="routeVersion" column="ROUTE_VERSION" jdbcType="VARCHAR"/>
            <result property="dssType" column="DSS_TYPE" jdbcType="VARCHAR"/>
            <result property="dssPosition" column="DSS_POSITION" jdbcType="VARCHAR"/>
            <result property="lineName" column="LINE_NAME" jdbcType="VARCHAR"/>
            <result property="completionYear" column="COMPLETION_YEAR" jdbcType="VARCHAR"/>
            <result property="roadLvl" column="ROAD_LVL" jdbcType="VARCHAR"/>
            <result property="supervisorOrg" column="SUPERVISOR_ORG" jdbcType="VARCHAR"/>
            <result property="jdBak" column="JD_BAK" jdbcType="DECIMAL"/>
            <result property="wdBak" column="WD_BAK" jdbcType="DECIMAL"/>
            <result property="logicStartStake" column="LOGIC_START_STAKE" jdbcType="DECIMAL"/>
            <result property="logicEndStake" column="LOGIC_END_STAKE" jdbcType="DECIMAL"/>
            <result property="yhid" column="YHID" jdbcType="VARCHAR"/>
            <result property="designStartStake" column="DESIGN_START_STAKE" jdbcType="VARCHAR"/>
            <result property="designEndStake" column="DESIGN_END_STAKE" jdbcType="VARCHAR"/>
            <result property="logicRampCntrStake" column="LOGIC_RAMP_CNTR_STAKE" jdbcType="DECIMAL"/>
            <result property="routeName" column="ROUTE_NAME" jdbcType="VARCHAR"/>
            <result property="isProvincial" column="IS_PROVINCIAL" jdbcType="VARCHAR"/>
            <result property="tccPrjId" column="TCC_PRJ_ID" jdbcType="VARCHAR"/>
            <result property="tunnelType" column="TUNNEL_TYPE" jdbcType="VARCHAR"/>
            <result property="isAdmin" column="IS_ADMIN" jdbcType="VARCHAR"/>
            <result property="designSpeed" column="DESIGN_SPEED" jdbcType="DECIMAL"/>
            <result property="constructionMethod" column="CONSTRUCTION_METHOD" jdbcType="VARCHAR"/>
            <result property="inGcjX" column="IN_GCJ_X" jdbcType="DECIMAL"/>
            <result property="inGcjY" column="IN_GCJ_Y" jdbcType="DECIMAL"/>
            <result property="outGcjX" column="OUT_GCJ_X" jdbcType="DECIMAL"/>
            <result property="outGcjY" column="OUT_GCJ_Y" jdbcType="DECIMAL"/>
            <result property="singleLaneWidth" column="SINGLE_LANE_WIDTH" jdbcType="DECIMAL"/>
            <result property="totalLaneWidth" column="TOTAL_LANE_WIDTH" jdbcType="DECIMAL"/>
            <result property="overhaulLane" column="OVERHAUL_LANE" jdbcType="VARCHAR"/>
            <result property="overhaulLaneWidth" column="OVERHAUL_LANE_WIDTH" jdbcType="DECIMAL"/>
            <result property="slopeRadio" column="SLOPE_RADIO" jdbcType="DECIMAL"/>
            <result property="emergencyParkingStrip" column="EMERGENCY_PARKING_STRIP" jdbcType="DECIMAL"/>
            <result property="escapeTrunk" column="ESCAPE_TRUNK" jdbcType="VARCHAR"/>
            <result property="liningformType" column="LININGFORM_TYPE" jdbcType="VARCHAR"/>
            <result property="footway" column="FOOTWAY" jdbcType="DECIMAL"/>
            <result property="roadway" column="ROADWAY" jdbcType="DECIMAL"/>
            <result property="floodProtection" column="FLOOD_PROTECTION" jdbcType="VARCHAR"/>
            <result property="antiSeismic" column="ANTI_SEISMIC" jdbcType="VARCHAR"/>
            <result property="supervision" column="SUPERVISION" jdbcType="VARCHAR"/>
            <result property="attr1" column="ATTR1" jdbcType="VARCHAR"/>
            <result property="nbHoleStruct" column="NB_HOLE_STRUCT" jdbcType="VARCHAR"/>
            <result property="nbSectionForm" column="NB_SECTION_FORM" jdbcType="VARCHAR"/>
            <result property="nbMsnrMtrl" column="NB_MSNR_MTRL" jdbcType="VARCHAR"/>
            <result property="nbCaveVntltForm" column="NB_CAVE_VNTLT_FORM" jdbcType="VARCHAR"/>
            <result property="nbElectronDevice" column="NB_ELECTRON_DEVICE" jdbcType="VARCHAR"/>
            <result property="nbDrainageType" column="NB_DRAINAGE_TYPE" jdbcType="VARCHAR"/>
            <result property="separatDirect" column="SEPARAT_DIRECT" jdbcType="VARCHAR"/>
            <result property="allWidth" column="ALL_WIDTH" jdbcType="DECIMAL"/>
            <result property="maintenanceUnitNature" column="MAINTENANCE_UNIT_NATURE" jdbcType="VARCHAR"/>
            <result property="colStartLatitude" column="COL_START_LATITUDE" jdbcType="DECIMAL"/>
            <result property="colStartLongitude" column="COL_START_LONGITUDE" jdbcType="DECIMAL"/>
            <result property="colStartDate" column="COL_START_DATE" jdbcType="TIMESTAMP"/>
            <result property="colStartPerson" column="COL_START_PERSON" jdbcType="VARCHAR"/>
            <result property="colEndLatitude" column="COL_END_LATITUDE" jdbcType="DECIMAL"/>
            <result property="colEndLongitude" column="COL_END_LONGITUDE" jdbcType="DECIMAL"/>
            <result property="colEndDate" column="COL_END_DATE" jdbcType="TIMESTAMP"/>
            <result property="colEndPerson" column="COL_END_PERSON" jdbcType="VARCHAR"/>
            <result property="colStartAltitude" column="COL_START_ALTITUDE" jdbcType="DECIMAL"/>
            <result property="colEndAltitude" column="COL_END_ALTITUDE" jdbcType="DECIMAL"/>
            <result property="addt" column="ADDT" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        TUNNEL_ID,TUNNEL_CODE,TUNNEL_NAME,
        TUNNEL_LINE_DIRECT,TUNNEL_RANGE,CNTR_STAKE,
        CNTR_STAKE_NUM,CNTR_SENSSION_NUM,START_STAKE_NUM,
        END_STAKE_NUM,DESIGN_START_STAKE_NUM,DESIGN_END_STAKE_NUM,
        TUNNEL_LENGTH,TUBE_CULTURE_UNIT,COMMUT_DATE,
        TUNNEL_WIDTH,TUNNEL_HEIGHT,MSNR_MTRL,
        IN_HOLE_STRUCT,OUT_HOLE_STRUCT,ROAD_SURFACE,
        FIRE_FACILITIES,CAVE_LIGHT_FORM,CAVE_VNTLT_FORM,
        IS_SEP_TYPE,IS_UNDERWATER_FLAG,SECTION_FORM,
        ELECTRON_DEVICE,DRAINAGE_TYPE,SAFE_ACCESS_NUM,
        SIDEWALK,ROADWAY_WIDE,BUILD_ORG_NAME,
        DESIGN_ORG_NAME,CONSTRUCTION_ORG_NAME,SUPERVISOR_ORG_NAME,
        EVALUATE_DATA,EVALUATION_UNIT,COMPLETION_DATE,
        REMOULD_PART,REMARK,IS_ENABLE,
        TUNNEL_CLASSIFIED,DSTRCT_CODE,ENGIN_PROPERTY,
        IS_KEY_LABEL,OPRT_ORG_CODE,PRJ_ORG_CODE,
        RP_INTRVL_ID,MAINLINE_ID,START_OFFSET,
        MAIN_RAMP_LINE_ID,THREAD_RPID,CREATE_USER_ID,
        CREATE_TIME,UPDATE_USER_ID,UPDATE_TIME,
        IS_DELETED,GIS_ID,GIS_X,
        GIS_Y,GIS_BDX,GIS_BDY,
        TUNNEL_MAINTAIN_GRADE,BUILT_DATE,LANE_NUM,
        PRJ_ID,MECHATRONICS_GRADE,TUNNEL_TC_GRADE,
        REPORT_NUMBER,TRAFFIC_FAC_GRADE,RANGE_EVALUATOR,
        RANGE_GRADE_DATE,MECHATRONICS_EVALUATOR,MECHATRONICS_DATE,
        OTHER_GRADE,OTHER_EVALUATOR,OTHER_GRADE_DATE,
        IS_TUNNEL,LINE_CODE,PHYSICS_CNTR_STAKE,
        LOGIC_CNTR_STAKE,ROUTE_CODE,ROUTE_VERSION,
        DSS_TYPE,DSS_POSITION,LINE_NAME,
        COMPLETION_YEAR,ROAD_LVL,SUPERVISOR_ORG,
        JD_BAK,WD_BAK,LOGIC_START_STAKE,
        LOGIC_END_STAKE,YHID,DESIGN_START_STAKE,
        DESIGN_END_STAKE,LOGIC_RAMP_CNTR_STAKE,ROUTE_NAME,
        IS_PROVINCIAL,TCC_PRJ_ID,TUNNEL_TYPE,
        IS_ADMIN,DESIGN_SPEED,CONSTRUCTION_METHOD,
        IN_GCJ_X,IN_GCJ_Y,OUT_GCJ_X,
        OUT_GCJ_Y,SINGLE_LANE_WIDTH,TOTAL_LANE_WIDTH,
        OVERHAUL_LANE,OVERHAUL_LANE_WIDTH,SLOPE_RADIO,
        EMERGENCY_PARKING_STRIP,ESCAPE_TRUNK,LININGFORM_TYPE,
        FOOTWAY,ROADWAY,FLOOD_PROTECTION,
        ANTI_SEISMIC,SUPERVISION,ATTR1,
        NB_HOLE_STRUCT,NB_SECTION_FORM,NB_MSNR_MTRL,
        NB_CAVE_VNTLT_FORM,NB_ELECTRON_DEVICE,NB_DRAINAGE_TYPE,
        SEPARAT_DIRECT,ALL_WIDTH,MAINTENANCE_UNIT_NATURE,
        COL_START_LATITUDE,COL_START_LONGITUDE,COL_START_DATE,
        COL_START_PERSON,COL_END_LATITUDE,COL_END_LONGITUDE,
        COL_END_DATE,COL_END_PERSON,COL_START_ALTITUDE,
        COL_END_ALTITUDE,ADDT
    </sql>
</mapper>
