<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dailycheck.mapper.DmDinspItemMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinspItem">
            <result property="type" column="TYPE" jdbcType="VARCHAR"/>
            <result property="checkProject" column="CHECK_PROJECT" jdbcType="VARCHAR"/>
            <result property="defectType" column="DEFECT_TYPE" jdbcType="VARCHAR"/>
            <result property="dealSituation" column="DEAL_SITUATION" jdbcType="VARCHAR"/>
            <result property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="orders" column="ORDERS" jdbcType="DECIMAL"/>
            <result property="repairDegree" column="REPAIR_DEGREE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        TYPE,CHECK_PROJECT,DEFECT_TYPE,
        DEAL_SITUATION,ID,ORDERS,
        REPAIR_DEGREE
    </sql>
</mapper>
