<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.maintenance.mapper.OftenInspectionMapper">
	<select id="getOftenInspectionDataByLevelTwo" resultType="com.hualu.highwaymaintenance.module.maintenance.entity.OftenInspection" parameterType="java.util.HashMap" fetchSize="1000">
		with orgtable as (
		    select o.ID
		    from GDGS.FW_RIGHT_ORG o
		    where o.IS_ENABLE = 1
		    and o.IS_DELETED = 0
		    start with o.ID ='${orgId}'
		    connect by prior o.ID = o.PARENT_ID
		)
		select max(ORG_NAME) AS ORG_NAME,max(ORG_ID) AS ORG_ID,
        sum(ONE_LEVEL_TUNNEL_ALL) AS ONE_LEVEL_TUNNEL_ALL,
        sum(TWO_LEVEL_TUNNEL_ALL) AS TWO_LEVEL_TUNNEL_ALL,
        sum(THREE_LEVEL_TUNNEL_ALL) AS THREE_LEVEL_TUNNEL_ALL,
        sum(SLOPE_ALL) || ' × 2' AS SLOPE_ALL,sum(CULVERT_ALL) AS CULVERT_ALL,
        sum(BRIDGE_ALL) AS BRIDGE_ALL,sum(case when BRIDGE > BRIDGE_ALL then BRIDGE_ALL ELSE BRIDGE END) AS BRIDGE,
        sum(TWO_BRIDGE_ALL) AS TWO_BRIDGE_ALL,sum(case when TWO_BRIDGE > TWO_BRIDGE_ALL then TWO_BRIDGE_ALL ELSE TWO_BRIDGE END) AS TWO_BRIDGE,
        sum(case when CULVERT > CULVERT_ALL then CULVERT_ALL ELSE CULVERT END) AS CULVERT,
        sum(case when ONE_LEVEL_TUNNEL > ONE_LEVEL_TUNNEL_ALL then ONE_LEVEL_TUNNEL_ALL ELSE ONE_LEVEL_TUNNEL END) AS ONE_LEVEL_TUNNEL,
        sum(case when TWO_LEVEL_TUNNEL > TWO_LEVEL_TUNNEL_ALL then TWO_LEVEL_TUNNEL_ALL ELSE TWO_LEVEL_TUNNEL END) AS TWO_LEVEL_TUNNEL,
        sum(case when THREE_LEVEL_TUNNEL > THREE_LEVEL_TUNNEL_ALL then THREE_LEVEL_TUNNEL_ALL ELSE THREE_LEVEL_TUNNEL END) AS THREE_LEVEL_TUNNEL,sum(SLOPE) AS SLOPE
		from(
		select a.ORG_NAME,a.PARENT_ID AS ORG_ID,b.ONE_LEVEL_TUNNEL_ALL,b.TWO_LEVEL_TUNNEL_ALL,ee.TWO_BRIDGE_ALL,gg.TWO_BRIDGE,
		       b.THREE_LEVEL_TUNNEL_ALL,c.SLOPE_ALL,d.CULVERT_ALL,e.BRIDGE_ALL,SLOPE,ONE_LEVEL_TUNNEL,
		       THREE_LEVEL_TUNNEL,TWO_LEVEL_TUNNEL,CULVERT,BRIDGE
		from (select distinct e.OPRT_ORG_CODE as ORG_CODE,oo.org_name as ORG_NAME,o.PARENT_ID as PARENT_ID
		from gdgs.BASE_ROUTE_LOGIC e
		inner join gdgs.FW_RIGHT_ORG o on e.OPRT_ORG_CODE = o.ORG_CODE
		inner join gdgs.FW_RIGHT_ORG oo on o.PARENT_ID = oo.ORG_CODE
		where o.PARENT_ID = '${orgId}') a left join
		(select oprt_org_code,sum(one) AS ONE_LEVEL_TUNNEL_ALL,sum(two) AS TWO_LEVEL_TUNNEL_ALL,
		sum(three) AS THREE_LEVEL_TUNNEL_ALL
		from ( select p.OPRT_ORG_CODE,
		case when TUNNEL_MAINTAIN_GRADE = 1 then count(*) end as one,
		case when TUNNEL_MAINTAIN_GRADE = 2 then count(*) end as two,
		case when TUNNEL_MAINTAIN_GRADE = 3 then count(*) end as three
		from mtmsdb.mtms_tunnel_basic mtb
		inner join GDGS.BASE_ROUTE_LOGIC p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
		and to_char(mtb.BUILT_DATE, 'yyyy-MM') &lt;= (${year} + 1) || to_char(mtb.BUILT_DATE, '-MM')
		group by p.OPRT_ORG_CODE,TUNNEL_MAINTAIN_GRADE)
		group by oprt_org_code) b on a.ORG_CODE = b.OPRT_ORG_CODE
		left join
		(select p.OPRT_ORG_CODE as OPT_ORG_ID,count(*) as SLOPE_ALL
		from hsmsdb.hsms_slope_info hsi 
		inner join GDGS.BASE_ROUTE_LOGIC p on p.ROUTE_CODE = hsi.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where hsi.is_deleted =0
		and (hsi.SLOPE_LEVEL &gt;= '3' or hsi.IS_IMPORTANT = 1)
		group by p.OPRT_ORG_CODE) c on a.ORG_CODE = c.OPT_ORG_ID
		
		left join
		(select cr.oprt_org_code,count(*) AS CULVERT_ALL
		from BCTCMSDB.t_clvrt_clvrtrecog cr
		inner join gdgs.BASE_ROUTE_LOGIC r on cr.ROUTE_CODE = r.ROUTE_CODE
		where cr.valid_flag=1 and substr(cr.COMPLETED_YEARS, 1, 4) &lt;= (substr('${month}',1,4) + 1)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = cr.ROUTE_CODE
   		)
		group by cr.oprt_org_code) d on a.ORG_CODE = d.OPRT_ORG_CODE
		left join
		(select br.oprt_org_code,count(*) AS BRIDGE_ALL
		from BCTCMSDB.t_brdg_brdgrecog br
		where br.valid_flag = 1
		and substr(to_char(COMP_TIME,'yyyy-MM-dd'), 1, 7) &lt;= (substr('${month}',1,4) + 1) || substr('${month}',5,7)
		and exists
		(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
		where brdgrecogm1_.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
		and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 1)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		group by br.oprt_org_code) e on a.ORG_CODE = e.OPRT_ORG_CODE
		
		left join
		(select br.oprt_org_code,count(*) AS TWO_BRIDGE_ALL
		from BCTCMSDB.t_brdg_brdgrecog br
		where br.valid_flag = 1
		and substr(to_char(COMP_TIME,'yyyy-MM-dd'), 1, 7) &lt;= (substr('${month}',1,4) + 1) || substr('${month}',5,7)
		and exists
		(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
		where brdgrecogm1_.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
		and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 2)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		group by br.oprt_org_code) ee on a.ORG_CODE = ee.OPRT_ORG_CODE
		
		left join
		(select p.OPRT_ORG_CODE,count(*) AS ONE_LEVEL_TUNNEL from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0' 
		and TUNNEL_MAINTAIN_GRADE = 1
		and df.INSP_DATE = '${month}'
		group by p.oprt_org_code) f on a.ORG_CODE = f.oprt_org_code
		left join 
		(select OPRT_ORG_CODE,count(*) AS BRIDGE from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'QL') df
		inner join BCTCMSDB.t_brdg_brdgrecog br on df.struct_id = br.BRDGRECOG_ID
		where df.INSP_DATE = '${month}'
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
        where brdgrecogm1_.MAIN_BRDGRECOG_ID = df.STRUCT_ID
        and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 1)
		group by OPRT_ORG_CODE) g on a.ORG_CODE = g.OPRT_ORG_CODE
		left join 
		(select OPRT_ORG_CODE,count(*) AS TWO_BRIDGE from
		(select STRUCT_ID ,FACILITY_CAT,
		case when to_char(INSP_DATE,'mm') = '02' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-02','-01')
        when to_char(INSP_DATE,'mm') = '04' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-04','-03')
        when to_char(INSP_DATE,'mm') = '06' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-06','-05')
        when to_char(INSP_DATE,'mm') = '08' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-08','-07')
        when to_char(INSP_DATE,'mm') = '10' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-10','-09')
        when to_char(INSP_DATE,'mm') = '12' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-12','-11')
        else to_char(INSP_DATE,'yyyy-MM') end AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'QL' and to_char(INSP_DATE,'yyyy-MM') in 
			(#{monthList2})
		) df
		inner join BCTCMSDB.t_brdg_brdgrecog br on df.struct_id = br.BRDGRECOG_ID
		where exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
        where brdgrecogm1_.MAIN_BRDGRECOG_ID = df.STRUCT_ID
        and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 2)
		group by OPRT_ORG_CODE) gg on a.ORG_CODE = gg.OPRT_ORG_CODE
		
		left join 
		(select p.oprt_org_code,count(*) AS TWO_LEVEL_TUNNEL from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
		and TUNNEL_MAINTAIN_GRADE = 2
		and df.INSP_DATE in
		(#{monthList2})
		group by p.oprt_org_code) h on a.ORG_CODE = h.oprt_org_code
		left join
		(select p.oprt_org_code,count(*) AS THREE_LEVEL_TUNNEL from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0' 
		and TUNNEL_MAINTAIN_GRADE = 3
		and df.INSP_DATE in
		(#{monthList3})
		group by p.oprt_org_code) i on a.ORG_CODE = i.oprt_org_code
		left join 
		(select p.OPRT_ORG_CODE as ORG_CODE,count(*) AS SLOPE from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'BP') df
		inner join hsmsdb.hsms_slope_info hsi on df.struct_id = hsi.SLOPE_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = hsi.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = '${year}'
		group by p.OPRT_ORG_CODE) j on a.ORG_CODE = j.ORG_CODE
		left join 
		(select c.OPRT_ORG_CODE as OPRT_ORG_CODE ,count(*) AS CULVERT from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'HD') df
		inner join BCTCMSDB.T_CLVRT_CLVRTRECOG c on df.STRUCT_ID = c.CLVRTRECOG_ID
		where df.INSP_DATE in
		(#{monthList3})
		 and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = c.ROUTE_CODE
   		)
		group by c.OPRT_ORG_CODE) k on a.ORG_CODE = k.OPRT_ORG_CODE
		where a.PARENT_ID = '${orgId}'
		)
	</select>
	
	<select id="getOftenInspectionDataByLevelThree" resultType="com.hualu.highwaymaintenance.module.maintenance.entity.OftenInspection" parameterType="java.util.HashMap" fetchSize="1000">
		with orgtable as (
		    select o.ID
		    from GDGS.FW_RIGHT_ORG o
		    where o.IS_ENABLE = 1
		    and o.IS_DELETED = 0
		    start with o.ID = #{orgId}
		    connect by prior o.ID = o.PARENT_ID
		)
		select a.ORG_NAME,a.ORG_CODE AS ORG_ID,b.ONE_LEVEL_TUNNEL_ALL,b.TWO_LEVEL_TUNNEL_ALL,h.TWO_LEVEL_TUNNEL,j.SLOPE,k.CULVERT,
        b.THREE_LEVEL_TUNNEL_ALL,c.SLOPE_ALL,d.CULVERT_ALL,e.BRIDGE_ALL,ee.TWO_BRIDGE_ALL,f.ONE_LEVEL_TUNNEL,g.BRIDGE,gg.TWO_BRIDGE,i.THREE_LEVEL_TUNNEL
		from (select distinct e.OPRT_ORG_CODE as ORG_CODE,o.ORG_NAME,SECOND_ORG_CODE as PARENT_ID
		from gdgs.EXPRESSWAY_SITUATION e
		inner join gdgs.FW_RIGHT_ORG o on e.OPRT_ORG_CODE = o.ORG_CODE
		where SECOND_ORG_CODE = '${orgId}') a left join
		(select oprt_org_code,sum(one) AS ONE_LEVEL_TUNNEL_ALL,sum(two) AS TWO_LEVEL_TUNNEL_ALL,
		sum(three) AS THREE_LEVEL_TUNNEL_ALL
		from ( select p.oprt_org_code,
		case when TUNNEL_MAINTAIN_GRADE = 1 then count(*) end as one,
		case when TUNNEL_MAINTAIN_GRADE = 2 then count(*) end as two,
		case when TUNNEL_MAINTAIN_GRADE = 3 then count(*) end as three
		from mtmsdb.mtms_tunnel_basic mtb
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
		and to_char(mtb.BUILT_DATE, 'yyyy-MM') &lt;= (#{year} + 1) || to_char(mtb.BUILT_DATE, '-MM')
		group by p.oprt_org_code,TUNNEL_MAINTAIN_GRADE)
		group by oprt_org_code) b on a.ORG_CODE = b.OPRT_ORG_CODE
		left join
		(select hsi.OPT_ORG_ID,count(*) as SLOPE_ALL
		from hsmsdb.hsms_slope_info hsi
		where hsi.is_deleted =0
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = hsi.ROUTE_CODE
   		)
		and (hsi.SLOPE_LEVEL &gt;= '3' or hsi.IS_IMPORTANT = 1)
		group by hsi.OPT_ORG_ID) c on a.ORG_CODE = c.OPT_ORG_ID
		left join
		(select cr.oprt_org_code,count(*) AS CULVERT_ALL
		from BCTCMSDB.t_clvrt_clvrtrecog cr
		inner join gdgs.BASE_ROUTE_PHYSICS r on cr.ROUTE_CODE = r.ROUTE_CODE
		where cr.valid_flag=1 and substr(cr.COMPLETED_YEARS, 1, 4) &lt;= (substr('${month}',1,4) + 1) 
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = cr.ROUTE_CODE
   		)
		group by cr.oprt_org_code) d on a.ORG_CODE = d.OPRT_ORG_CODE
		left join
		(select br.oprt_org_code,count(*) AS BRIDGE_ALL
		from BCTCMSDB.t_brdg_brdgrecog br
		where br.valid_flag = 1
		and substr(to_char(COMP_TIME,'yyyy-MM-dd'), 1, 7) &lt;= (substr('${month}',1,4) + 1) || substr('${month}',5,7)
		and exists
		(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
		where brdgrecogm1_.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
		and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 1)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		group by br.oprt_org_code) e on a.ORG_CODE = e.OPRT_ORG_CODE
		
		left join
		(select br.oprt_org_code,count(*) AS TWO_BRIDGE_ALL
		from BCTCMSDB.t_brdg_brdgrecog br
		where br.valid_flag = 1
		and substr(to_char(COMP_TIME,'yyyy-MM-dd'), 1, 7) &lt;= (substr('${month}',1,4) + 1) || substr('${month}',5,7)
		and exists
		(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
		where brdgrecogm1_.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
		and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 2)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		group by br.oprt_org_code) ee on a.ORG_CODE = ee.OPRT_ORG_CODE
		
		left join
		(select p.oprt_org_code,count(*) AS ONE_LEVEL_TUNNEL from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
		and TUNNEL_MAINTAIN_GRADE = 1
		and df.INSP_DATE = '${month}'
		group by p.oprt_org_code) f on a.ORG_CODE = f.oprt_org_code
		left join 
		(select oprt_org_code,count(*) AS BRIDGE from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'QL') df
		inner join BCTCMSDB.t_brdg_brdgrecog br on df.struct_id = br.BRDGRECOG_ID
		where df.INSP_DATE = '${month}'
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
        where brdgrecogm1_.MAIN_BRDGRECOG_ID = df.STRUCT_ID
        and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 1)
		group by OPRT_ORG_CODE) g on a.ORG_CODE = g.oprt_org_code
		
		left join 
		(select oprt_org_code,count(*) AS TWO_BRIDGE from
		(select  STRUCT_ID ,FACILITY_CAT,
		case when to_char(INSP_DATE,'mm') = '02' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-02','-01')
        when to_char(INSP_DATE,'mm') = '04' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-04','-03')
        when to_char(INSP_DATE,'mm') = '06' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-06','-05')
        when to_char(INSP_DATE,'mm') = '08' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-08','-07')
        when to_char(INSP_DATE,'mm') = '10' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-10','-09')
        when to_char(INSP_DATE,'mm') = '12' then REPLACE(to_char(INSP_DATE,'yyyy-MM'),'-12','-11')
        else to_char(INSP_DATE,'yyyy-MM') end AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'QL' and to_char(INSP_DATE,'yyyy-MM') in
		(#{monthList2})
		) df
		inner join BCTCMSDB.t_brdg_brdgrecog br on df.struct_id = br.BRDGRECOG_ID
		where exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
        where brdgrecogm1_.MAIN_BRDGRECOG_ID = df.STRUCT_ID
        and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 2)
		group by OPRT_ORG_CODE) gg on a.ORG_CODE = gg.oprt_org_code
		
		left join 
		(select p.oprt_org_code,count(*) AS TWO_LEVEL_TUNNEL from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0' 
		and TUNNEL_MAINTAIN_GRADE = 2
		and df.INSP_DATE in
		(#{monthList2})
		group by p.oprt_org_code) h on a.ORG_CODE = h.oprt_org_code
		left join
		(select p.oprt_org_code,count(*) AS THREE_LEVEL_TUNNEL from
		(select STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
		and TUNNEL_MAINTAIN_GRADE = 3
		and df.INSP_DATE in
		(#{monthList3})
		group by p.oprt_org_code) i on a.ORG_CODE = i.oprt_org_code
		left join 
		(select hsi.OPT_ORG_ID as ORG_CODE ,count(*) AS SLOPE from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'BP') df
		inner join hsmsdb.hsms_slope_info hsi on df.struct_id = hsi.SLOPE_ID
		where to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = '${year}'
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = hsi.ROUTE_CODE
   		)
		group by hsi.OPT_ORG_ID) j on a.ORG_CODE = j.ORG_CODE
		left join 
		(select c.oprt_org_code AS OPRT_ORG_CODE ,count(*) AS CULVERT from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'HD') df
		inner join BCTCMSDB.T_CLVRT_CLVRTRECOG c on df.STRUCT_ID = c.CLVRTRECOG_ID
		where df.INSP_DATE in
		(#{monthList3})
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = c.ROUTE_CODE
   		)
		group by c.oprt_org_code) k on a.ORG_CODE = k.OPRT_ORG_CODE
		where a.PARENT_ID = '${orgId}' 
	</select>
	
	
	<select id="getOftenInspectionDataAllYear" resultType="com.hualu.highwaymaintenance.module.maintenance.entity.OftenInspection">
		<![CDATA[
        with fworg as (
        select distinct e.OPRT_ORG_CODE as ORG_CODE,o.ORG_NAME,o.PARENT_ID
		from gdgs.BASE_ROUTE_LOGIC e
		inner join gdgs.FW_RIGHT_ORG o on e.OPRT_ORG_CODE = o.ORG_CODE
		where o.PARENT_ID = '${orgId}' ),orgtable as (
		    select o.ID
		    from GDGS.FW_RIGHT_ORG o
		    where o.IS_ENABLE = 1
		    and o.IS_DELETED = 0
		    start with o.ID = #{orgId}
		    connect by prior o.ID = o.PARENT_ID
		),
        time as (select lpad(level, 2, 0) month from dual connect by level < 13)
        select a.ORG_CODE AS ORG_ID,a.ORG_NAME,
        sum(b.BRIDGE_ALL) as BRIDGE_ALL,sum(bb.TWO_BRIDGE_ALL) as TWO_BRIDGE_ALL,sum(d.ONE_LEVEL_TUNNEL_ALL) as ONE_LEVEL_TUNNEL_ALL,
        sum(d.TWO_LEVEL_TUNNEL_ALL) as TWO_LEVEL_TUNNEL_ALL,sum(d.THREE_LEVEL_TUNNEL_ALL) as THREE_LEVEL_TUNNEL_ALL,
        sum(h.CULVERT_ALL) as CULVERT_ALL,sum(j.SLOPE_ALL) as SLOPE_ALL,
        sum(case when c.BRIDGE >= b.BRIDGE_ALL then 1 else 0 end) AS BRIDGE,sum(case when cc.TWO_BRIDGE >= bb.TWO_BRIDGE_ALL then 1 else 0 end) AS TWO_BRIDGE,
        sum(case when e.ONE_LEVEL_TUNNEL >= d.ONE_LEVEL_TUNNEL_ALL then 1 else 0 end) AS ONE_LEVEL_TUNNEL,
        sum(case when f.TWO_LEVEL_TUNNEL >= d.TWO_LEVEL_TUNNEL_ALL then 1 else 0 end) AS TWO_LEVEL_TUNNEL,
        sum(case when g.THREE_LEVEL_TUNNEL >= d.THREE_LEVEL_TUNNEL_ALL then 1 else 0 end) AS THREE_LEVEL_TUNNEL,
        sum(case when i.CULVERT >= h.CULVERT_ALL then 1 else 0 end) AS CULVERT,
        case when sum(k.SLOPE) >= sum(j.SLOPE_ALL * 2) then 2
        when sum(k.SLOPE) >= sum(j.SLOPE_ALL) then 1 else 0 end AS SLOPE 
        from (select ORG_CODE,ORG_NAME,#{year} AS years,#{year} || '-' || month AS months,month,
        case when month in ('01', '02', '03') then '第一季'
        when month in ('04', '05', '06') then '第二季'
        when month in ('07', '08', '09') then '第三季'
        else '第四季' end as season,
        '全年' as year from time
        left join fworg on 1=1) a  ]]>
        left join
		(select OPRT_ORG_CODE as ORG_CODE,count(*) AS BRIDGE_ALL
		from BCTCMSDB.t_brdg_brdgrecog br
		where br.valid_flag = 1
		and substr(to_char(COMP_TIME,'yyyy-MM-dd'), 1, 7) &lt;= (#{year} + 1) || substr(br.COMP_TIME,5,7)
		and exists
		(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
		where brdgrecogm1_.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
		and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 1)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		) group by OPRT_ORG_CODE) b on a.ORG_CODE = b.ORG_CODE
   		
        left join
		(select OPRT_ORG_CODE as ORG_CODE,count(*) AS TWO_BRIDGE_ALL
		from BCTCMSDB.t_brdg_brdgrecog br
		where br.valid_flag = 1
		and substr(to_char(COMP_TIME,'yyyy-MM-dd'), 1, 7) &lt;= (#{year} + 1) || substr(br.COMP_TIME,5,7)
		and exists
		(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
		where brdgrecogm1_.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID
		and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 2)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		) group by OPRT_ORG_CODE) bb on a.ORG_CODE = bb.ORG_CODE
        left join
		(select df.INSP_DATE,br.OPRT_ORG_CODE as ORG_CODE,count(*) AS BRIDGE from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'QL') df
		inner join BCTCMSDB.t_brdg_brdgrecog br on df.struct_id = br.BRDGRECOG_ID
		where to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year}
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
        where brdgrecogm1_.MAIN_BRDGRECOG_ID = df.STRUCT_ID
        and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 1)
		group by df.INSP_DATE,br.OPRT_ORG_CODE) c on a.months = c.INSP_DATE and a.ORG_CODE = c.ORG_CODE
		
        left join
		(select ORG_CODE,count(*) AS TWO_BRIDGE, INSP_DATE from
		(select distinct br.OPRT_ORG_CODE as ORG_CODE ,case when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '02' then REPLACE(INSP_DATE,'-02','-01')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '04' then REPLACE(INSP_DATE,'-04','-03')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '06' then REPLACE(INSP_DATE,'-06','-05')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '08' then REPLACE(INSP_DATE,'-08','-07')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '10' then REPLACE(INSP_DATE,'-10','-09')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '12' then REPLACE(INSP_DATE,'-12','-11')
        else INSP_DATE end AS INSP_DATE,STRUCT_ID
        from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'QL') df
		inner join BCTCMSDB.t_brdg_brdgrecog br on df.struct_id = br.BRDGRECOG_ID
		where to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year}
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = br.ROUTE_CODE
   		)
		and exists(select 1 from BCTCMSDB.T_BRDG_RECOGMANAGE brdgrecogm1_
        where brdgrecogm1_.MAIN_BRDGRECOG_ID = df.STRUCT_ID
        and brdgrecogm1_.VALID_FLAG = 1 and MAINTAIN_GRADE = 2))
		group by INSP_DATE,ORG_CODE) cc on a.months = cc.INSP_DATE and a.ORG_CODE = cc.ORG_CODE
		
        left join
        (select ORG_CODE, sum(one) AS ONE_LEVEL_TUNNEL_ALL,sum(two) AS TWO_LEVEL_TUNNEL_ALL,
		sum(three) AS THREE_LEVEL_TUNNEL_ALL
		from ( select p.OPRT_ORG_CODE as ORG_CODE, case when TUNNEL_MAINTAIN_GRADE = 1 then count(*) end as one,
		case when TUNNEL_MAINTAIN_GRADE = 2 then count(*) end as two,
		case when TUNNEL_MAINTAIN_GRADE = 3 then count(*) end as three
		from mtmsdb.mtms_tunnel_basic mtb
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted = '0'
		and to_char(mtb.BUILT_DATE, 'yyyy-MM') &lt;= (#{year} + 1) || to_char(mtb.BUILT_DATE, '-MM')
		group by TUNNEL_MAINTAIN_GRADE,p.OPRT_ORG_CODE) group by ORG_CODE) d on a.ORG_CODE = d.ORG_CODE
        left join
        (select df.INSP_DATE,p.OPRT_ORG_CODE as ORG_CODE,count(*) AS ONE_LEVEL_TUNNEL from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
		and TUNNEL_MAINTAIN_GRADE = 1
		and to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year}
		group by df.INSP_DATE,p.OPRT_ORG_CODE) e on a.months = e.INSP_DATE and a.ORG_CODE = e.ORG_CODE
        left join
        (select ORG_CODE,count(*) AS TWO_LEVEL_TUNNEL, INSP_DATE from
        (select p.OPRT_ORG_CODE as ORG_CODE ,case when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '02' then REPLACE(INSP_DATE,'-02','-01')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '04' then REPLACE(INSP_DATE,'-04','-03')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '06' then REPLACE(INSP_DATE,'-06','-05')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '08' then REPLACE(INSP_DATE,'-08','-07')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '10' then REPLACE(INSP_DATE,'-10','-09')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '12' then REPLACE(INSP_DATE,'-12','-11')
        else INSP_DATE end AS INSP_DATE,STRUCT_ID
        from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0' 
        and TUNNEL_MAINTAIN_GRADE = 2
        and to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year})
		group by INSP_DATE,ORG_CODE) f on a.months = f.INSP_DATE and a.ORG_CODE = f.ORG_CODE
        left join
        (select ORG_CODE,count(*) AS THREE_LEVEL_TUNNEL, INSP_DATE from
        (select p.OPRT_ORG_CODE as ORG_CODE,case when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '02' then REPLACE(INSP_DATE,'-02','-01')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '03' then REPLACE(INSP_DATE,'-03','-01')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '05' then REPLACE(INSP_DATE,'-05','-04')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '06' then REPLACE(INSP_DATE,'-06','-04')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '08' then REPLACE(INSP_DATE,'-08','-07')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '09' then REPLACE(INSP_DATE,'-09','-07')
		when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '11' then REPLACE(INSP_DATE,'-11','-10')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '12' then REPLACE(INSP_DATE,'-12','-10')
        else INSP_DATE end AS INSP_DATE,STRUCT_ID
        from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'SD') df
		inner join mtmsdb.mtms_tunnel_basic mtb on df.STRUCT_ID = mtb.TUNNEL_ID
		inner join GDGS.FW_RIGHT_DATA_PERMISSION p on p.ROUTE_CODE = mtb.ROUTE_CODE
	    inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
		where mtb.is_deleted='0'
        and TUNNEL_MAINTAIN_GRADE = 3
        and to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year})
		group by INSP_DATE,ORG_CODE) g on a.months = g.INSP_DATE and a.ORG_CODE = g.ORG_CODE
        left join
        (select OPRT_ORG_CODE as ORG_CODE,count(*) AS CULVERT_ALL
		from BCTCMSDB.t_clvrt_clvrtrecog cr
		where cr.valid_flag = 1
		and substr(cr.COMPLETED_YEARS, 1, 4) &lt;= (#{year} + 1)
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = cr.ROUTE_CODE
   		)
        group by OPRT_ORG_CODE) h on a.ORG_CODE = h.ORG_CODE
        left join
        (select ORG_CODE,INSP_DATE ,count(*) AS CULVERT from(
        (select OPRT_ORG_CODE as ORG_CODE,case when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '02' then REPLACE(INSP_DATE,'-02','-01')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '03' then REPLACE(INSP_DATE,'-03','-01')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '05' then REPLACE(INSP_DATE,'-05','-04')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '06' then REPLACE(INSP_DATE,'-06','-04')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '08' then REPLACE(INSP_DATE,'-08','-07')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '09' then REPLACE(INSP_DATE,'-09','-07')
		when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '11' then REPLACE(INSP_DATE,'-11','-10')
        when to_char(to_date(INSP_DATE,'yyyy-MM'),'mm') = '12' then REPLACE(INSP_DATE,'-12','-10')
        else INSP_DATE end AS INSP_DATE,STRUCT_ID from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'HD') df
		inner join BCTCMSDB.T_CLVRT_CLVRTRECOG c on df.STRUCT_ID = c.CLVRTRECOG_ID
		where to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year}
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = c.ROUTE_CODE
   		))
		)group by INSP_DATE,ORG_CODE) i on a.months = i.INSP_DATE and a.ORG_CODE = i.ORG_CODE
        left join
        (select hsi.OPT_ORG_ID as ORG_CODE,count(*) as SLOPE_ALL
		from hsmsdb.hsms_slope_info hsi
		where hsi.is_deleted =0
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = hsi.ROUTE_CODE
   		)
		and (hsi.SLOPE_LEVEL &gt;= '3' or hsi.IS_IMPORTANT = 1)
        group by hsi.OPT_ORG_ID) j on a.ORG_CODE = j.ORG_CODE
        left join
        (select ORG_CODE,count(*) AS SLOPE,INSP_DATE from(
		(select OPT_ORG_ID as ORG_CODE,to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') as INSP_DATE,STRUCT_ID from
		(select  STRUCT_ID ,FACILITY_CAT,to_char(INSP_DATE,'yyyy-MM') AS INSP_DATE ,MNT_ORG_ID from MEMSDB.DM_FINSP
		where FACILITY_CAT = 'BP') df
		inner join hsmsdb.hsms_slope_info hsi on df.STRUCT_ID = hsi.SLOPE_ID
		where to_char(to_date(df.INSP_DATE,'yyyy-MM'),'yyyy') = #{year}
		and exists( select 1
	        from GDGS.FW_RIGHT_DATA_PERMISSION p
	        inner join orgtable uo on p.OPRT_ORG_CODE = uo.ID
	        where p.ROUTE_CODE = hsi.ROUTE_CODE
   		)))
        group by INSP_DATE,ORG_CODE) k on a.years = k.INSP_DATE and a.ORG_CODE = k.ORG_CODE
        group by a.ORG_CODE,a.ORG_NAME
        order by a.ORG_CODE
	</select>
</mapper>