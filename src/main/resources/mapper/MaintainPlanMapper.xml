<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.MaintainPlanMapper">
    <select id="queryMaintainPlans" resultType="com.hualu.highwaymaintenance.module.decision.domain.MaintainPlan">
        select '段落' || ROWNUM as seq,aa.* from (
        select distinct prj_org_code,a.LINE_DIRECT, a.START_STAKE,a.LENGTH,MEASURENAME as history,
        tc.PQI,tc.RQI,tc.RDI,tc.SRI,decode(a.LANE,'A',PCI_FRONT,'B',PCI_FRONT,'') MAIN_PCI_FRONT,
        decode(a.LANE,'C',PCI_FRONT,'D',PCI_FRONT,'') PCI_FRONT,
        decode(a.LANE,'A',PCI_BEHIND,'B',PCI_BEHIND,'') MAIN_PCI_BEHIND,
        decode(a.LANE,'C',PCI_BEHIND,'D',PCI_BEHIND,'') PCI_BEHIND,
        ((case when PCI_FRONT &gt;= 85 AND PCI_FRONT &lt;90 then '85&lt;PCI&lt;90 '
        when PCI_FRONT &gt;= 90 AND PCI_FRONT &lt; 92 then '90&lt;PCI&lt;92 '
        when PCI_FRONT &gt;= 80 AND PCI_FRONT &lt; 85 then '80&lt;PCI&lt;85 '
        when PCI_FRONT &lt; 80 then 'PCI&lt;80' else ' ' end) ||
        (case when SRI &gt;= 85 AND SRI &lt;90 then '85&lt;SRI&lt;90'
        when SRI &gt;= 90 AND SRI &lt;92 then '90&lt;SRI&lt;92'
        when SRI &gt;= 80 AND SRI &lt;85 then '80&lt;SRI&lt;85'
        when SRI &lt; 80 then 'SRI&lt;80' else '' end)) as reason
        ,MEASURE_NAME plan,PRICE * a.LENGTH as fee
        from PTCMSDB.PBD_STRUCT_INTRVL_UNIT_DESION a
        inner join PTCMSDB.PTCD_TC_DETAIL_1000_DESION TC ON a.UNIT_MARGE_ID=TC.UNIT_MARGE_ID
        inner join gdgs.BASE_ROUTE r on r.ROUTE_CODE = a.ROUTE_CODE
        JOIN GDGS.FW_RIGHT_ORG ORG ON r.PRJ_ORG_CODE=ORG.ID
        inner join (select * from
        (select * from
        (select ROW_NUMBER ( ) OVER ( PARTITION BY a.MEASURE_ID,LANE_TYPE,ROUTE_CODE,
        START_STAKE,END_STAKE,LINE_DIRECTION ORDER BY year DESC ) rn,year,m.MEASURE_NAME,m.PRICE,
        LANE_TYPE,ROUTE_CODE,START_STAKE,END_STAKE,LINE_DIRECTION
        from PTCMSDB.PBD_PAVEMENT_MAINTAIN a
        inner join PTCMSDB.PBD_PAVEMENT_MEASURE m on m.MEASURE_ID = a.MEASURE_ID
        ) where rn = 1)) b on a.ROUTE_CODE = b.ROUTE_CODE and b.YEAR &lt;= a.YEAR
        and a.LINE_DIRECT = b.LINE_DIRECTION and ((a.START_STAKE &gt;= b.START_STAKE and a.END_STAKE &lt;= b.END_STAKE and LINE_DIRECT = 1)
        or (a.START_STAKE &lt;= b.START_STAKE and a.END_STAKE &gt;= b.END_STAKE and LINE_DIRECT = 2))
        where prj_org_code = #{orgCode} and a.YEAR = #{year}
        order by prj_org_code,a.LINE_DIRECT,a.START_STAKE) aa
    </select>
</mapper>
