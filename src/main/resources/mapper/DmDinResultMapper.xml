<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.dailycheck.mapper.DmDinResultMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.dailycheck.domain.DmDinResult">
            <id property="rId" column="R_ID" jdbcType="VARCHAR"/>
            <result property="dmContent" column="DM_CONTENT" jdbcType="VARCHAR"/>
            <result property="inspectionsBak" column="INSPECTIONS_BAK" jdbcType="VARCHAR"/>
            <result property="dssTrea" column="DSS_TREA" jdbcType="VARCHAR"/>
            <result property="remarks" column="REMARKS" jdbcType="VARCHAR"/>
            <result property="dmDinspId" column="DM_DINSP_ID" jdbcType="VARCHAR"/>
            <result property="xuhao" column="XUHAO" jdbcType="VARCHAR"/>
            <result property="degree" column="DEGREE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        R_ID,DM_CONTENT,INSPECTIONS_BAK,
        DSS_TREA,REMARKS,DM_DINSP_ID,
        XUHAO,DEGREE,INSPECTIONS
    </sql>
</mapper>
