<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.decision.mapper.ConstraintOperatorMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.decision.entity.ConstraintOperator">
            <id property="constraintOperatorId" column="CONSTRAINT_OPERATOR_ID" jdbcType="VARCHAR"/>
            <result property="constraintOperatorLabel" column="CONSTRAINT_OPERATOR_LABEL" jdbcType="VARCHAR"/>
            <result property="constraintOperatorName" column="CONSTRAINT_OPERATOR_NAME" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CONSTRAINT_OPERATOR_ID,CONSTRAINT_OPERATOR_LABEL,CONSTRAINT_OPERATOR_NAME
    </sql>
</mapper>
