<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.elec.mapper.ElecRepairPicMapper">

  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.elec.entity.ElecRepairPic">
    <id property="id" column="ID" jdbcType="VARCHAR"/>
    <result property="type" column="TYPE" jdbcType="DECIMAL"/>
    <result property="fileId" column="FILE_ID" jdbcType="VARCHAR"/>
    <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
    <result property="recordId" column="RECORD_ID" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="Base_Column_List">
    ID,TYPE,FILE_ID,
      FILE_NAME,RECORD_ID
  </sql>
</mapper>
