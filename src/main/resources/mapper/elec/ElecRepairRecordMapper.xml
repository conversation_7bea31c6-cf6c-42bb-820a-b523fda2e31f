<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.elec.mapper.ElecRepairRecordMapper">

  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.elec.entity.ElecRepairRecord">
    <id property="id" column="ID" jdbcType="VARCHAR"/>
    <result property="code" column="CODE" jdbcType="VARCHAR"/>
    <result property="reportDate" column="REPORT_DATE" jdbcType="TIMESTAMP"/>
    <result property="faultLocation" column="FAULT_LOCATION" jdbcType="VARCHAR"/>
    <result property="repairPerson" column="REPAIR_PERSON" jdbcType="VARCHAR"/>
    <result property="repairDate" column="REPAIR_DATE" jdbcType="TIMESTAMP"/>
    <result property="faultReason" column="FAULT_REASON" jdbcType="VARCHAR"/>
    <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
    <result property="createDate" column="CREATE_DATE" jdbcType="TIMESTAMP"/>
    <result property="createUserId" column="CREATE_USER_ID" jdbcType="VARCHAR"/>
    <result property="createOrgCode" column="CREATE_ORG_CODE" jdbcType="VARCHAR"/>
  </resultMap>

  <sql id="Base_Column_List">
    ID,CODE,REPORT_DATE,
      FAULT_LOCATION,REPAIR_PERSON,REPAIR_DATE,
      FAULT_REASON,REMARK,CREATE_DATE,
      CREATE_USER_ID,CREATE_ORG_CODE
  </sql>

  <select id="queryRecords" resultType="com.hualu.highwaymaintenance.module.elec.entity.ElecRepairRecord">
    select a.*,b.ATTRIBUTE_VALUE type_name,be.files as before,af.files as after from MEMSDB.ELEC_REPAIR_RECORD a
    inner join gdgs.BASE_DATATHIRD_DIC b on a.TYPE = b.ATTRIBUTE_CODE
    and ATTRIBUTE_ITEM = 'ELEC_REPAIR_TYPE'
    and ATTRIBUTE_ACTIVE = 0
    left join (select listagg(FILE_ID,',') within group (order by TYPE) files,RECORD_ID
    from MEMSDB.ELEC_REPAIR_PIC where TYPE = '1' group by RECORD_ID) be
    on a.ID = be.RECORD_ID
    left join (select listagg(FILE_ID,',') within group (order by TYPE) files,RECORD_ID
    from MEMSDB.ELEC_REPAIR_PIC where TYPE = '2' group by RECORD_ID) af
    on a.ID = af.RECORD_ID
    where a.CREATE_ORG_CODE = #{orgCode} and a.code like concat(concat('%', #{keyword}), '%')
  </select>

  <select id="queryRecord" resultType="com.hualu.highwaymaintenance.module.elec.entity.ElecRepairRecord">
    select a.*,b.ATTRIBUTE_VALUE type_name from MEMSDB.ELEC_REPAIR_RECORD a
    inner join gdgs.BASE_DATATHIRD_DIC b on a.TYPE = b.ATTRIBUTE_CODE
    and ATTRIBUTE_ITEM = 'ELEC_REPAIR_TYPE'
    and ATTRIBUTE_ACTIVE = 0
    where a.id = #{id}
  </select>


</mapper>
