<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.datareport.mapper.XqMaintenanceRecordMapper">
  <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.datareport.entity.XqMaintenanceRecord">
    <!--@mbg.generated-->
    <!--@Table BCTCMSDB.XQ_MAINTENANCE_RECORD-->
    <id column="RECORD_ID" jdbcType="VARCHAR" property="recordId" />
    <result column="BRIDGE_IDENTITY_CODE" jdbcType="VARCHAR" property="bridgeIdentityCode" />
    <result column="START_DATE" jdbcType="VARCHAR" property="startDate" />
    <result column="END_DATE" jdbcType="VARCHAR" property="endDate" />
    <result column="BUILD_TYPE" jdbcType="VARCHAR" property="buildType" />
    <result column="BUILD_REASON" jdbcType="VARCHAR" property="buildReason" />
    <result column="SCOPE_OF_WORK" jdbcType="VARCHAR" property="scopeOfWork" />
    <result column="COST" jdbcType="DECIMAL" property="cost" />
    <result column="SOURCE_OF_INCOME" jdbcType="VARCHAR" property="sourceOfIncome" />
    <result column="QUALITY_ASSESSMMENT" jdbcType="VARCHAR" property="qualityAssessmment" />
    <result column="BUILD_UNIT" jdbcType="VARCHAR" property="buildUnit" />
    <result column="DESIGN_UNIT" jdbcType="VARCHAR" property="designUnit" />
    <result column="DESIGN_METHOD" jdbcType="VARCHAR" property="designMethod" />
    <result column="DESIGN_PAPERS_AUDIT_UNIT" jdbcType="VARCHAR" property="designPapersAuditUnit" />
    <result column="DESIGN_PAPERS_AUDIT_TIME" jdbcType="VARCHAR" property="designPapersAuditTime" />
    <result column="CONSTRUCTION_DETERMINE_METHOD" jdbcType="VARCHAR" property="constructionDetermineMethod" />
    <result column="CONSTRUCTION_CONTROL_UNIT" jdbcType="VARCHAR" property="constructionControlUnit" />
    <result column="MAINTENANCE_CLASSIFY" jdbcType="VARCHAR" property="maintenanceClassify" />
    <result column="IMPLEMENTAION_REGION" jdbcType="VARCHAR" property="implementaionRegion" />
    <result column="HANDOVER_ACCEPTANCE_UNIT" jdbcType="VARCHAR" property="handoverAcceptanceUnit" />
    <result column="HANDOVER_ACCEPTANCE_TIME" jdbcType="VARCHAR" property="handoverAcceptanceTime" />
    <result column="ACCEPTANCE_METHOD" jdbcType="VARCHAR" property="acceptanceMethod" />
    <result column="COMPLETION_UNIT" jdbcType="VARCHAR" property="completionUnit" />
    <result column="COMPLETION_TIME" jdbcType="VARCHAR" property="completionTime" />
    <result column="PRJQUALITY_SUPERVISION_UNIT" jdbcType="VARCHAR" property="prjqualitySupervisionUnit" />
    <result column="AFTER_MAINTENANCED_LEVEL" jdbcType="DECIMAL" property="afterMaintenancedLevel" />
    <result column="CONSTRUCTION_UNIT" jdbcType="VARCHAR" property="constructionUnit" />
    <result column="SECOND_COMPANY" jdbcType="VARCHAR" property="secondCompany" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="REPORT_ID" jdbcType="VARCHAR" property="reportId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    RECORD_ID, BRIDGE_IDENTITY_CODE, START_DATE, END_DATE, BUILD_TYPE, BUILD_REASON, 
    SCOPE_OF_WORK, COST, SOURCE_OF_INCOME, QUALITY_ASSESSMMENT, BUILD_UNIT, DESIGN_UNIT, 
    DESIGN_METHOD, DESIGN_PAPERS_AUDIT_UNIT, DESIGN_PAPERS_AUDIT_TIME, CONSTRUCTION_DETERMINE_METHOD, 
    CONSTRUCTION_CONTROL_UNIT, MAINTENANCE_CLASSIFY, IMPLEMENTAION_REGION, HANDOVER_ACCEPTANCE_UNIT, 
    HANDOVER_ACCEPTANCE_TIME, ACCEPTANCE_METHOD, COMPLETION_UNIT, COMPLETION_TIME, PRJQUALITY_SUPERVISION_UNIT, 
    AFTER_MAINTENANCED_LEVEL, CONSTRUCTION_UNIT, SECOND_COMPANY, ORG_CODE, REPORT_ID
  </sql>
</mapper>