<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.highwaymaintenance.module.user.mapper.FwRightUserMapper">

    <resultMap id="BaseResultMap" type="com.hualu.highwaymaintenance.module.user.domain.FwRightUser">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="userCode" column="USER_CODE" jdbcType="VARCHAR"/>
            <result property="password" column="PASSWORD" jdbcType="VARCHAR"/>
            <result property="userType" column="USER_TYPE" jdbcType="VARCHAR"/>
            <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
            <result property="orgId" column="ORG_ID" jdbcType="VARCHAR"/>
            <result property="userName" column="USER_NAME" jdbcType="VARCHAR"/>
            <result property="postId" column="POST_ID" jdbcType="VARCHAR"/>
            <result property="isEnable" column="IS_ENABLE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="VARCHAR"/>
            <result property="isOnline" column="IS_ONLINE" jdbcType="VARCHAR"/>
            <result property="isLocked" column="IS_LOCKED" jdbcType="VARCHAR"/>
            <result property="loginTimes" column="LOGIN_TIMES" jdbcType="DECIMAL"/>
            <result property="loginIp" column="LOGIN_IP" jdbcType="VARCHAR"/>
            <result property="lastLoginTime" column="LAST_LOGIN_TIME" jdbcType="TIMESTAMP"/>
            <result property="loginTime" column="LOGIN_TIME" jdbcType="TIMESTAMP"/>
            <result property="regTime" column="REG_TIME" jdbcType="TIMESTAMP"/>
            <result property="createUserCode" column="CREATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="createUserName" column="CREATE_USER_NAME" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateUserCode" column="UPDATE_USER_CODE" jdbcType="VARCHAR"/>
            <result property="updateUserName" column="UPDATE_USER_NAME" jdbcType="VARCHAR"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="departId" column="DEPART_ID" jdbcType="VARCHAR"/>
            <result property="telephone" column="TELEPHONE" jdbcType="VARCHAR"/>
            <result property="expireDate" column="EXPIRE_DATE" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,USER_CODE,PASSWORD,
        USER_TYPE,EMAIL,ORG_ID,
        USER_NAME,POST_ID,IS_ENABLE,
        IS_DELETED,IS_ONLINE,IS_LOCKED,
        LOGIN_TIMES,LOGIN_IP,LAST_LOGIN_TIME,
        LOGIN_TIME,REG_TIME,CREATE_USER_CODE,
        CREATE_USER_NAME,CREATE_TIME,UPDATE_USER_CODE,
        UPDATE_USER_NAME,UPDATE_TIME,REMARK,
        DEPART_ID,TELEPHONE,EXPIRE_DATE
    </sql>
    <select id="getUserInfo" resultType="java.util.Map">
      select  f.USER_CODE, o.ORG_NAME_EN, o.ORG_NAME as ONAME, decode(p.ID,null,'1','2')||t.DCCODE as DC ,o.ORG_FULLNAME,
        f.ID as USER_ID,f.ORG_ID,f.USER_NAME,oo.ORG_NAME as SECOND_ORG_NAME,f.TELEPHONE,
        oo.org_code as SECOND_ORG_CODE, o.ORG_LEVEL,o.ORG_GRP_FLAG
      from GDGS.FW_RIGHT_USER f inner join GDGS.FW_RIGHT_ORG o on f.ORG_ID=o.ID
      left join gdgs.FW_RIGHT_ORG d on nvl(f.DEPART_ID, f.ORG_ID) = d.ID
      left join GDGS.FW_RIGHT_ORG oo on o.parent_id = oo.id
      inner join GDGS.MG_DCCODE_ORG t on t.ORG_ID=o.ID left join GDGS.MG_ORG_IP p
      on t.ORG_ID=p.ORG_CODE
      where
        o.IS_DELETED = 0 and o.IS_ENABLE = 1 and
        LOWER(f.USER_CODE)= LOWER(#{userCode,jdbcType=VARCHAR}) and ROWNUM = 1
    </select>
</mapper>
