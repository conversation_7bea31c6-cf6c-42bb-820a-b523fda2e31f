package com.hualu.highwaymaintenance.decision.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.decision.entity.CheckParent;
import com.hualu.highwaymaintenance.decision.service.TdService;
import com.hualu.highwaymaintenance.util.BeanUtils;
import com.hualu.highwaymaintenance.util.CheckResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping(value = "check")
public class TdServiceCollection {

    @Autowired
    @Qualifier(value = "tdServiceImp")
    private TdService tdService;

    @RequestMapping(value = "uploadCheckFile")
    public List<CheckResult> importExcelListData(MultipartFile file, String prjId, String year, String index, String scree)
    {
        return tdService.importExcelListData(file, prjId, year, index, scree);
    }

    /**
     *
     * @param request
     * @param prjId
     * @param year
     * @param lineId
     * @param type
     * @param limit 多少条
     * @param offset 多少页
     * @return
     */
    @GetMapping(value = "findCheckDssInfoList")
    public IPage<CheckParent> findCheckDssInfoList(HttpServletRequest request, String prjId, String year, String lineId, String type, int limit, int offset)
    {
        IPage<CheckParent> desionTcDetiailList = (IPage<CheckParent>) tdService.findDssTypeGroupList(limit, offset, prjId, lineId, year, type);
        return desionTcDetiailList;
    }

    @GetMapping(value = "downCheckDataList")
    public void  downCheckDataList(HttpServletResponse response, String prjId, String year, String lineId, String type)
    {
        IPage<CheckParent> desionTcDetiailList = (IPage<CheckParent>) tdService.findDssTypeGroupList(50000, 0, prjId, lineId, year, type);
        tdService.downTempFileCheck(response, desionTcDetiailList, type);
    }

    @GetMapping(value = "loadCheckDataList")
    public RestResult loadCheckDataList(HttpServletRequest request, String prjId, String year, String lineId, String type)
    {
        Map<String, String> maps = BeanUtils.pavementAllIndex(null);
        Set<String> strings = maps.keySet();

        Iterator<String> iterator = strings.iterator();

        while (iterator.hasNext())
        {
            String next = iterator.next();
            boolean b = tdService.loadCheckDataList(10, 0, prjId, lineId, year, next);

            if (!b)
            {
                return RestResult.success("数据抽取异常,请联系管理员");
            }
        }

        return RestResult.success("数据抽取成功");
    }
    @GetMapping(value = "testCheckDataList")
    public void test1()
    {
        List<Map<String, String>> allGroupPrjId = tdService.getAllGroupPrjId("2022");
        for (int i = 0; i < allGroupPrjId.size(); i++) {
            Map<String, String> v = allGroupPrjId.get(i);
            String mng_org_id = v.get("MNG_ORG_ID");
            String LINE_ID = v.get("LINE_ID");
            Map<String, String> maps = BeanUtils.pavementAllIndex(null);
            Set<String> strings = maps.keySet();

            Iterator<String> iterator = strings.iterator();

            while (iterator.hasNext())
            {
                String next = iterator.next();
                boolean b = tdService.loadCheckDataList(10, 0, mng_org_id, LINE_ID, "2022", next);

                if (!b)
                {
                    System.out.println("抽取异常");
                }
            }
        }
    }
}