package com.hualu.highwaymaintenance.decision.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.common.vo.RestResultBoot;
import com.hualu.highwaymaintenance.decision.ImportExcelUtils.ExelFileZipLoop;
import com.hualu.highwaymaintenance.decision.entity.PbdMainPointPavement;
import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail100;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.decision.service.PbdStructUnitService;
import com.hualu.highwaymaintenance.module.user.domain.FwRightOrg;
import com.hualu.highwaymaintenance.util.BeanUtils;
import net.sf.json.JSONArray;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "desion")
public class PavementDesionCollection {

    @Autowired
    @Qualifier(value = "pbdStructUnitServiceImp")
    private PbdStructUnitService structUnitService;

    @GetMapping(value = "getAllOrgName")
    public List<FwRightOrg> getAllParentOrgName()
    {
        return structUnitService.getAllParentOrgName();
    }
    @GetMapping(value = "getOrgIdToAllPrj")
    public List<Map<String, Object>> getOrgIdToAllPrj(String orgId)
    {
        return structUnitService.getOrgIdToAllPrj(orgId);
    }

    @GetMapping(value = "getOrgToPrj")
    @ResponseBody
    public String getDesionTcDetiailList(HttpServletRequest request, String prjId, String year, String lineId, String usered, int limit, int offset)
    {
        int start = (offset + 1);
        offset = (int)Math.floor((start - 1) / limit) + 1;
        IPage<TcaTcDetail1000> page = new Page<>(offset, limit);
        IPage<TcaTcDetail1000> desionTcDetiailList = structUnitService.getDesionTcDetiailList(page, prjId, year, lineId, usered);
        ObjectMapper mapper = new ObjectMapper();
        String jsonData = null;
        try {
            jsonData = mapper.writeValueAsString(RestResultBoot.success((int)desionTcDetiailList.getTotal(), desionTcDetiailList.getRecords(), "查询成功"));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return jsonData;
    }

    @GetMapping(value = "getHunsTcDetailDataList")
    @ResponseBody
    public String getHunsTcDetailDataList(HttpServletRequest request, String prjId, String year, String lineId, String usered, int limit, int offset)
    {
        int start = (offset + 1);
        offset = (int)Math.floor((start - 1) / limit) + 1;
        IPage<TcaTcDetail100> page = new Page<>(offset, limit);
        IPage<TcaTcDetail100> desionTcDetiailList = structUnitService.getHunsTcDetailDataList(page, prjId, year, lineId);
        ObjectMapper mapper = new ObjectMapper();
        String jsonData = null;
        try {
            jsonData = mapper.writeValueAsString(RestResultBoot.success((int)desionTcDetiailList.getTotal(), desionTcDetiailList.getRecords(), "查询成功"));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return jsonData;
    }

    @GetMapping(value = "findGroupScore")
    @ResponseBody
    public Map<String, List<Map<String, Object>>> findGroupScore(HttpServletRequest request, String prjId, String year, String lineId)
    {
        Map<String, List<Map<String, Object>>> maps = new HashMap<String, List<Map<String, Object>>>();
        maps.put("Z1", structUnitService.findGroupScore(prjId, year, lineId));
        maps.put("Z2", structUnitService.findDssInfoGroup(prjId, year, lineId));
        return maps;
    }


    /**
     *
     * @param response
     * @param prjId 单位ID
     * @param year 年份数据
     * @param lineId 线路ID
     */
    @GetMapping(value = "downloadTcDetail")
    @ResponseBody
    public void downloadTcDetail(HttpServletResponse response, String prjId, String year, String lineId, String usered)
    {
        // 设置响应内容类型为 Excel 文件
        response.setContentType("application/x-msdownload");
        response.setHeader("content-Type","applicationvnd.ms-excel:charset=utf-8");
        // 设置响应头，表示文件是一个附件，文件名为 PbdStreamUnitExport.xlsx

        try {
            IPage<TcaTcDetail1000> page = new Page<>(0, 1000);
            IPage<TcaTcDetail1000> desionTcDetiailList = structUnitService.getDesionTcDetiailList(page, prjId, year, lineId, usered);
            response.setHeader("Content-Disposition", "attachment;filename" + URLEncoder.encode("决策单元","UTF-8") + ".xls");
            String templateFilePath = "templates/decision/tcDetail.xlsx";
            TemplateExportParams templateUrl = new TemplateExportParams(templateFilePath);
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("t1", desionTcDetiailList.getRecords());

            Workbook workbook = ExcelExportUtil.exportExcel(templateUrl, dataMap);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @GetMapping(value = "downloadHunsTcDetail")
    @ResponseBody
    public void downloadHunsTcDetail(HttpServletResponse response, String prjId, String year, String lineId)
    {
        // 设置响应内容类型为 Excel 文件
        response.setContentType("application/x-msdownload");
        response.setHeader("content-Type","applicationvnd.ms-excel:charset=utf-8");
        // 设置响应头，表示文件是一个附件，文件名为 PbdStreamUnitExport.xlsx

        try {
            IPage<TcaTcDetail100> page = new Page<>(0, 10000);
            IPage<TcaTcDetail100> desionTcDetiailList = structUnitService.getHunsTcDetailDataList(page, prjId, year, lineId);
            response.setHeader("Content-Disposition", "attachment;filename" + URLEncoder.encode("决策单元","UTF-8") + ".xls");
            String templateFilePath = "templates/decision/tcDetailHuns.xlsx";
            TemplateExportParams templateUrl = new TemplateExportParams(templateFilePath);

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("t1", desionTcDetiailList.getRecords());

            Workbook workbook = ExcelExportUtil.exportExcel(templateUrl, dataMap);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @GetMapping(value = "getOrgIdToLineId")
    public List<Map<String, String>> getOrgIdToLineId(String prjId)
    {
        return structUnitService.getOrgIdToLineId(prjId);
    }

    @GetMapping(value = "getFromUnitList")
    public String getFromUnitList(String prjId, String lineId, String usered, int limit, int offset)
    {
        int start = (offset + 1);
        offset = (int)Math.floor((start - 1) / limit) + 1;
        IPage<PbdStreamUnit> page = new Page<>(offset, limit);
        Page<PbdStreamUnit> fromUnitList = structUnitService.getFromUnitList(page, prjId, lineId, usered);
        ObjectMapper mapper = new ObjectMapper();
        String jsonData = null;
        try {
            jsonData = mapper.writeValueAsString(RestResultBoot.success((int)fromUnitList.getTotal(), fromUnitList.getRecords(), "查询成功"));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return jsonData;
    }

    @GetMapping(value = "getFromHunsList")
    public List<PbdStreamUnit> getFromHunsList(String prjId, String lineId)
    {
        return structUnitService.getFromHunsList(prjId, lineId);
    }

 /*   @GetMapping(value = "getFromUnitList")
    public List<PbdStreamUnit> getFromHunList(String prjId, String lineId)
    {
        return structUnitService.getFromUnitList(prjId, lineId);
    }*/

    @GetMapping(value = "ktSplitUnit")
    public void ktSplitUnit(String prjId, String lineId, String startStake, String endStake)
    {
        structUnitService.ktSplitUnit(lineId, prjId, null, null);
    }

    @GetMapping(value = "downLoadUnitList")
    public void downLoadUnitList(HttpServletResponse response, String prjId, String lineId, String usered)
    {
        structUnitService.downLoadUnitList(response, prjId, lineId, usered);
    }

    @GetMapping(value = "downLoadHunsList")
    public void downLoadHunsList(HttpServletResponse response, String prjId, String lineId)
    {
        structUnitService.downLoadHunsList(response, prjId, lineId);
    }

    @GetMapping(value = "uploadMainPointFiles")
    public void downLoadUnitList(@Param("file") MultipartFile file, String prjId, String lineId)
    {
        try {
            ImportParams params = new ImportParams();
            params.setHeadRows(1); // 第一行是表头
            params.setTitleRows(0); // 没有标题行

            List<PbdMainPointPavement> userList = ExcelImportUtil.importExcel(
                    file.getInputStream(), PbdMainPointPavement.class, params);

            structUnitService.saveMainPointPavement(userList, prjId, lineId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @PostMapping(value = "uploadUnitsFiles")
    public RestResult uploadUnitsFiles(HttpServletRequest request, @Param("file") MultipartFile file, String prjId, String lineId)
    {
        return structUnitService.uploadUnitsFile(file, prjId, lineId);
    }

    @PostMapping(value = "uploadUnitsTcDetailFiles")
    public RestResult uploadUnitsTcDetailFiles(@Param("file") MultipartFile file, String prjId, String lineId, String year)
    {
        return structUnitService.uploadUnitsTcDetailFiles(file, prjId, lineId, year);
    }

    @RequestMapping(value = "getChartsDataList")
    @ResponseBody
    public Map<String, Object> getChartsDataList(Integer year, String orgId, String lineCode, String typeName)
    {
        return structUnitService.getHistoryTcDetail(year, orgId, lineCode, typeName);
    }

    @RequestMapping(value = "getPieChartsDataList")
    @ResponseBody
    public JSONArray getPieChartsDataList(String year, String orgId, String lineCode)
    {
        return structUnitService.getDssTypeNum(year, orgId, lineCode);
    }

    @RequestMapping(value = "downloaCheckFileTemp")
    public void downloadPavementCheckFileTemp(HttpServletResponse response, String index)
    {
        structUnitService.getFileTemp(response, index);
    }

    @RequestMapping(value = "downBigDataListCheckFile")
    public void downBigDataListCheckFile()
    {
        String prj = "N000177";
        String lineCode = "G35";
        String lane = "A,B";
        String years = "2022";

        try {
            Map<String, String> map = BeanUtils.pavementAllIndex(null);
            String[] split = lane.split(",");
            for (int i = 0; i < (split.length / 2); i++)
            {
                String laneUp = split[i * 2];
                String laneDown = split[i * 2 + 1];
                for (Map.Entry<String, String> v : map.entrySet())
                {
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    XSSFWorkbook sheets = null;
                    if ("PBI".equals(v.getKey())) {
                        sheets = this.structUnitService.getExportExcelPbi(prj, v.getKey(), v.getValue(), lineCode, laneUp, laneDown, years);
                    } else if ("SCI".equals(v.getKey()) && "A".equals(laneUp) && "B".equals(laneDown)) {
                        sheets = this.structUnitService.getExportExcelSci(prj, v.getKey(), v.getValue(), lineCode, years);
                    } else if ("TCI".equals(v.getKey()) && "A".equals(laneUp) && "B".equals(laneDown)) {
                        sheets = this.structUnitService.getExportExcelTci(prj, v.getKey(), v.getValue(), lineCode, years);
                    } else if ("PSSI".equals(v.getKey()) && "A".equals(laneUp) && "B".equals(laneDown)) {
                        sheets = this.structUnitService.getExportExcelPssi(prj, v.getKey(), v.getValue(), lineCode, laneUp, laneDown, years);
                    } else if("MQI".equals(v.getKey())){
                        sheets = this.structUnitService.getExportExcelAllTable(prj, v.getKey(), v.getValue(), lineCode, laneUp, laneDown, years);
                    } else if ("PCI_FRONT".equals(v.getKey()) || "RQI".equals(v.getKey())
                            || "RDI".equals(v.getKey()) || "SRI".equals(v.getKey())){
                        sheets = this.structUnitService.importTcDetailExcelData(prj, v.getKey(), v.getValue(), lineCode, laneUp, laneDown, years);
                    }
                    if (sheets != null) {
                        sheets.write(bos);
                        ExelFileZipLoop.putNext("技术状况指标" + v.getKey() + laneUp + laneDown + ".xlsx", bos.toByteArray());
                        bos.flush();
                        bos.close();
                    }
                }
            }
            ExelFileZipLoop.loopZipSave(prj, "zip");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @RequestMapping(value = "importAllGroupUnit")
    public void importUnitTcDetail()
    {
        this.structUnitService.importUnitTcDetail();
    }

    @RequestMapping(value = "ktSplitCompleteUnit")
    public void  ktSplitCompleteUnit()
    {
        List<Map<String, Object>> allGroupStakeRange = this.structUnitService.getAllGroupStakeRange();
        for (int i = 0; i < allGroupStakeRange.size(); i++) {
            Map<String, Object> map = allGroupStakeRange.get(i);
            String line_code = map.get("LINE_CODE").toString();
            String ID = map.get("ID").toString();
            String START_STAKE = map.get("START_STAKE").toString();
            String END_STAKE = map.get("END_STAKE").toString();
            this.structUnitService.ktSplitCompleteUnit(line_code, ID, Double.valueOf(START_STAKE), Double.valueOf(END_STAKE), null, null);
        }
    }

    @RequestMapping(value = "getAllPrjLineCode")
    public void  getAllPrjLineCode()
    {
        List<Map<String, Object>> allGroupStakeRange = this.structUnitService.getAllPrjLineCode();
        for (int i = 0; i < allGroupStakeRange.size(); i++) {
            try {
                Map<String, Object> map = allGroupStakeRange.get(i);
                String line_code = map.get("LINE_ID").toString();
                String MNG_ORG_ID = map.get("MNG_ORG_ID").toString();
                this.structUnitService.savePavementScore(null, line_code, MNG_ORG_ID, "2022", "22");
            }catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }
}
