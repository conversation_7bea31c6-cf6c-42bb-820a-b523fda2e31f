package com.hualu.highwaymaintenance.decision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface TdServiceMapper extends BaseMapper<DssInfo> {

    @Select("SELECT DSS_TYPE,DSS_TYPE_NAME,measurement_unit  from ptcmsdb.PTCD_HPAS_SD_PAVEMENT WHERE DSS_TYPE LIKE #{type,jdbcType=VARCHAR}")
    List<Map<String,String>> getLMDssTypeNews(@Param("type") String type);

    @Delete("delete from MEMSDB.DSS_INFO_DESION where REL_TASK_CODE = #{prjId,jdbcType=VARCHAR} AND DSS_TYPE LIKE #{facilityCat,jdbcType=VARCHAR}")
    void deleteTestDssInfoData(@Param("prjId") String prjId, @Param("facilityCat")String facilityCat);

    @Delete("delete from PTCMSDB.PTCD_FLATNESS_DATA_DESION where PRJ_ID = #{prjId,jdbcType=VARCHAR}")
    void deleteTestFlatnessData(@Param("prjId") String prjId);

    IPage<DssInfo> findDssTypeGroupList(IPage<DssInfo> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    IPage<FlatnessData> findRqiGroupList(IPage<FlatnessData> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    IPage<RuttingData> findRdiGroupList(IPage<RuttingData> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    IPage<AntiSlipData> findSriGroupList(IPage<AntiSlipData> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    IPage<PtcdCarData> findPbiGroupList(IPage<PtcdCarData> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    IPage<DeflectionData> findPssiGroupList(IPage<DeflectionData> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    IPage<BctCheckData> findBciGroupList(IPage<BctCheckData> page, @Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    List<DssInfo> findPavementHistoryDssType(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year, @Param("type")String type);

    List<Map<String, String>> getAllGroupPrjId(@Param("year")String year);

    void saveHistoryRqiDataList(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year);

    void saveHistoryRdiDataList(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year);

    void saveHistorySriDataList(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year);

    void saveHistoryPbiDataList(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year);

    void saveHistoryPssiDataList(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year);

    void saveHistoryBciDataList(@Param("orgId")String orgId, @Param("lineId")String lineId, @Param("year")String year);

    void insertTestFlatnessData(@Param("list")List<FlatnessData> list);

    @Delete("delete from PTCMSDB.PTCD_CAR_DATA where PRJ_ID = #{prjId,jdbcType=VARCHAR}")
    void deleteTestPtcdCarData(@Param("prjId") String prjId);

    @Delete("delete from PTCMSDB.PTCD_DEFLECTION_DATA_DESION where PRJ_ID = #{prjId,jdbcType=VARCHAR}")
    void deleteTestDeflectionData(@Param("prjId") String prjId);

    @Delete("delete from PTCMSDB.PTCD_ANTI_SLIP_DATA_DESION where PRJ_ID = #{prjId,jdbcType=VARCHAR}")
    void deleteTestAntiSlipData(@Param("prjId") String prjId);

    @Delete("delete from PTCMSDB.PTCD_RUTTING_DATA_DESION where PRJ_ID = #{prjId,jdbcType=VARCHAR}")
    void deleteTestRuttingData(@Param("prjId") String prjId);

    @Delete("delete from PTCMSDB.PTCD_BCT_CHECK_DATA_DESION where PRJ_ID = #{prjId,jdbcType=VARCHAR}")
    void deleteTestBctCheckData(@Param("prjId") String prjId);

    void insertTestBctCheckData(@Param("item") BctCheckData item);

    void insertTestDeflectionData(@Param("item") DeflectionData item);

    @Insert({
            "<script>",
            "INSERT INTO PTCD_CAR_DATA (PBI_ID, prj_id, rp_intrvl_id, START_STAKE, END_STAKE, LANE, PAVEMENT_TYPE, PBI_HIGHT, REMARK, LINE_ID, LANE_DIRECTION, YEAR, UNIT_MARGE_ID, routecode, routeversion, PB_HIGHT_LEFT, PB_HIGHT_RIGHT) VALUES ",
            "<foreach item='item' collection='list' separator=','>",
            "(#{item.pbiId}, '${prjId}', #{item.rpIntrvlId}, #{item.startStake}, #{item.endStake}, #{item.lane}, #{item.pavementType}, #{item.pbiHight}, #{item.remark}, #{item.lineId}, #{item.laneDirection}, #{item.year}, #{item.unitMargeId}, #{item.routecode}, #{item.routeversion}, #{item.pbHightLeft}, #{item.pbHightRight})",
            "</foreach>",
            "</script>"
    })
    void batchInsertTestPtcdCarData(@Param("list") List<PtcdCarData> list, @Param("prjId") String prjId);

}
