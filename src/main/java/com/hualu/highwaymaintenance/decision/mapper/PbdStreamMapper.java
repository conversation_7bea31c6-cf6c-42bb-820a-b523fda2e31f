package com.hualu.highwaymaintenance.decision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.module.user.domain.FwRightOrg;
import com.hualu.highwaymaintenance.util.BaseStake;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface PbdStreamMapper extends BaseMapper<PbdStreamUnit> {

    List<Map<String, Object>> getYearMeaSureMap(@Param("lineCode") String lineCode,
                                                @Param("lane") String lane,
                                                @Param("laneType") String laneType,
                                                @Param("startStake") Double startStake,
                                                @Param("endStake") Double endStake);

    List<Map<String, Object>> getBrigeStakeSureMap(@Param("lineId") String lineId,
                                                   @Param("orgId") String orgId,
                                                   @Param("startStake") Double startStake,
                                                   @Param("endStake") Double endStake);

    List<Map<String, Object>> getClvrtStakeSureMap(@Param("lineId") String lineId,
                                                   @Param("orgId") String orgId,
                                                   @Param("startStake") Double startStake,
                                                   @Param("endStake") Double endStake);

    List<Map<String, Object>> getYearMeaSureNewMap(@Param("lane") String lane,
                                                   @Param("lineCode") String lineCode,
                                                   @Param("arr") List<String> arr,
                                                   @Param("laneType") String laneType);

    List<Map<String, Object>> getTcDetailExcelExport(@Param("typeNumber") String typeNumber,
                                                     @Param("prjId") String prjId,
                                                     @Param("lane") String lane,
                                                     @Param("index") String index,
                                                     @Param("lineCode") String lineCode,
                                                     @Param("years") String years);

    List<Map<String, Object>> getTcDetailSumReport(@Param("prjId") String prjId,
                                                   @Param("index") String index,
                                                   @Param("lineCode") String lineCode,
                                                   @Param("pavement") String pavement,
                                                   @Param("lane") String[] lane,
                                                   @Param("years") String years);

    List<Map<String, Object>> getDataSumTcDetail(@Param("prjId") String prjId,
                                                 @Param("index") String index,
                                                 @Param("typeNumber") String typeNumber,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("laneUp") String laneUp,
                                                 @Param("laneDown") String laneDown);

    String getLineId(@Param("lineId") String lineId);

    List<DssInfo> getDesionDssTypeList(@Param("orgId") String orgId,
                                       @Param("type") String type,
                                       @Param("lineCode") String lineCode,
                                       @Param("year") String year,
                                       @Param("usered") String usered);

    List<DssInfo> getDssInfoHunLq(@Param("orgId") String orgId,
                                  @Param("type") String type,
                                  @Param("year") String year);

    List<Map<String, Object>> getCarCheckMessage(@Param("prjId") String prjId,
                                                 @Param("lane") String lane,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("years") String years);

    List<Map<String, Object>> getPbiCarCheckOrder(@Param("prjId") String prjId,
                                                  @Param("lane") String[] lane,
                                                  @Param("code") String code,
                                                  @Param("lineCode") String lineCode);

    List<Map<String, Object>> getTcDetailTableMqi(@Param("prjId") String prjId,
                                                  @Param("years") String years);

    List<Map<String, Object>> getTciExcelExportMessage(@Param("prjId") String prjId,
                                                       @Param("lineCode") String lineCode,
                                                       @Param("years") String years);

    List<FwRightOrg> getAllParentOrgName();

    List<Map<String, Object>> getOrgIdToAllPrj(@Param("orgId") String orgId);

    void removeNewHunDataList(@Param("list") List<String> list);

    Page<TcaTcDetail1000> getTcDetailDataList(IPage<TcaTcDetail1000> page,
                                              @Param("lineCode") String lineCode,
                                              @Param("orgId") String orgId,
                                              @Param("year") Integer year,
                                              @Param("usered") String usered);

    Page<TcaTcDetail100> getHunsTcDetailDataList(IPage<TcaTcDetail100> page,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("orgId") String orgId,
                                                 @Param("year") Integer year);

    List<Map<String, Object>> findGroupScore(@Param("lineCode") String lineCode,
                                             @Param("orgId") String orgId,
                                             @Param("index") String index,
                                             @Param("years") String years);

    List<Map<String, Object>> getPavementLqData(@Param("prjId") String prjId,
                                                @Param("lineCode") String lineCode,
                                                @Param("tablaNameFirst") String tablaNameFirst,
                                                @Param("tablaNameSecond") String tablaNameSecond,
                                                @Param("unitMargeId") String unitMargeId,
                                                @Param("routeCode") String routeCode,
                                                @Param("lane") String[] lane,
                                                @Param("years") String years,
                                                @Param("index") String index,
                                                @Param("typeNumber") String typeNumber);

    List<Map<String, Object>> leftJoinDssInfo(@Param("unitMargeId") String unitMargeId,
                                              @Param("tablaNameFirst") String tablaNameFirst,
                                              @Param("tablaNameSecond") String tablaNameSecond,
                                              @Param("routeCode") String routeCode,
                                              @Param("prjId") String prjId,
                                              @Param("years") String years);

    List<Map<String, Object>> leftJoinSnDssInfo(@Param("unitMargeId") String unitMargeId,
                                                @Param("tablaNameFirst") String tablaNameFirst,
                                                @Param("tablaNameSecond") String tablaNameSecond,
                                                @Param("routeCode") String routeCode,
                                                @Param("prjId") String prjId,
                                                @Param("years") String years);

    List<Map<String, Object>> getPavementSnData(@Param("prjId") String prjId,
                                                @Param("lineCode") String lineCode,
                                                @Param("tablaNameFirst") String tablaNameFirst,
                                                @Param("tablaNameSecond") String tablaNameSecond,
                                                @Param("unitMargeId") String unitMargeId,
                                                @Param("routeCode") String routeCode,
                                                @Param("lane") String[] lane,
                                                @Param("years") String years,
                                                @Param("index") String index,
                                                @Param("typeNumber") String typeNumber);

    List<Map<String, Object>> getPssiCheckOrder(@Param("prjId") String prjId,
                                                @Param("lineCode") String lineCode,
                                                @Param("lane") String[] lane,
                                                @Param("years") String years);

    List<Map<String, Object>> getExportExcelSci(@Param("prjId") String prjId,
                                                @Param("lineCode") String lineCode,
                                                @Param("years") String years);

    List<Map<String, Object>> getHunTcDetailExcelExport(@Param("prjId") String prjId,
                                                        @Param("index") String index,
                                                        @Param("typeNumber") String typeNumber,
                                                        @Param("lineCode") String lineCode,
                                                        @Param("laneUp") String laneUp,
                                                        @Param("laneDown") String laneDown,
                                                        @Param("years") String years);

    List<Map<String, Object>> getTcDetailFail(@Param("prjId") String prjId,
                                              @Param("index") String index,
                                              @Param("typeNumber") String typeNumber,
                                              @Param("lineCode") String lineCod,
                                              @Param("arr") String[] arr,
                                              @Param("years") String years);

    List<Map<String, Object>> getHistorySecore(@Param("typeNumber") String typeNumber,
                                               @Param("prjYear") String prjYear,
                                               @Param("arr") List<String> arr,
                                               @Param("lineCode") String lineCode,
                                               @Param("lane") String lane);

    List<String> getPrjSearchRoute(@Param("prjId") String prjId,
                                   @Param("lineCode") String lineCode);

    List<Map<String, Object>> getMeaSureDataType(@Param("typeNumber") String typeNumber,
                                                 @Param("prjId") String prjId,
                                                 @Param("lane") String lane,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("index") String index,
                                                 @Param("years") String years);

    List<Map<String, Object>> findDssInfoGroup(@Param("lineCode") String lineCode,
                                               @Param("orgId") String orgId,
                                               @Param("years") String years);

    List<String> getRouteAll(@Param("orgId") String orgId,
                             @Param("year") Integer year);

    List<String> getRuttingRouteAll(@Param("orgId") String orgId,
                                    @Param("year") Integer year,
                                    @Param("tableName") String tableName);

    void saveTempRoute(@Param("routeCode") String routeCode,
                       @Param("lineCode") String lineCode,
                       @Param("usered") String usered);

    void removeNewUnitLine();

    void removeTcDetail(@Param("orgName") String orgName,
                        @Param("year") String year,
                        @Param("usered") String usered);

    void removeTcHunDetail(@Param("orgName") String orgName,
                           @Param("year") String year,
                           @Param("usered") String usered);

    List<FlatnessData> getFlatessData(@Param("orgId") String orgId,
                                      @Param("year") Integer year);

    List<RuttingData> getRdiUnitData(@Param("orgId") String orgId,
                                     @Param("year") Integer year,
                                     @Param("lineId") String lineId);

    List<AntiSlipData> getSriUnitData(@Param("orgId") String orgId,
                                      @Param("year") Integer year);

    List<DeflectionData> getTotalPssiScoreUnit(@Param("orgId") String orgId,
                                               @Param("year") Integer year);

    List<PtcdCarData> getPbiUnitData(@Param("orgId") String orgId,
                                     @Param("year") Integer year);

    List<PtcdCarData> getPbiHunsData(@Param("orgId") String orgId,
                                     @Param("year") Integer year);

    List<DrainageData> getDamageDate(@Param("orgId") String orgId,
                                     @Param("year") Integer year);

    List<Map<String, String>> getNewBrigeBci(@Param("year") Integer year);

    List<DssInfo> getTciData(@Param("orgId") String orgId,
                             @Param("year") Integer year,
                             @Param("type") Integer type);

    List<DssInfo> getDssTypePrList(@Param("orgId") String orgId,
                                   @Param("year") Integer year,
                                   @Param("type") Integer type);

    List<DssInfo> getDssTypeTcsList(@Param("orgId") String orgId,
                                    @Param("year") Integer year,
                                    @Param("type") Integer type);

    List<DssInfo> getDssTypeLcrList(@Param("orgId") String orgId,
                                    @Param("year") Integer year,
                                    @Param("type") Integer type);

    List<PbdStreamUnit> getUnitsListData();

    List<PbdStructIntrvlHun> getHunsListData();

    List<Map<String, String>> getOrgIdToLineId(@Param("orgId") String orgId);

    Page<PbdStreamUnit> getFromUnitList(IPage<PbdStreamUnit> page,
                                        @Param("orgId") String orgId,
                                        @Param("lineId") String lineId,
                                        @Param("usered") String usered);

    List<PbdStreamUnit> getFromHunsList(@Param("orgId") String orgId,
                                        @Param("lineId") String lineId);

    void deleteOldUnitList(@Param("orgId") String orgId,
                           @Param("lineId") String lineId);

    Map<String, String> getLogicRouteCodeVersion(@Param("lineId") String lineId,
                                                 @Param("startStake") Double startStake,
                                                 @Param("lineDirect") String lineDirect,
                                                 @Param("endStake") Double endStake,
                                                 @Param("orgName") String orgName,
                                                 @Param("floor") String floor,
                                                 @Param("prj") String prj);

    Map<String, BigDecimal> getStartStake(@Param("orgId") String orgId,
                                          @Param("lineId") String lineId);

    void batchInsert(@Param("list") List<PbdMainPointPavement> list);

    List<Map<String, Object>> getTypeNameByDssinfoPavement(@Param("lineCode") String lineCode,
                                                           @Param("orgId") String orgId,
                                                           @Param("startStake") Double startStake,
                                                           @Param("endStake") Double endStake,
                                                           @Param("lane") String lane);

    List<BaseStake> getOrgIdStakeRange(@Param("lineCode") String lineCode,
                                       @Param("orgId") String orgId,
                                       @Param("lineDirect") String lineDirect);

    void removeOldUnitsList(@Param("lineId") String lineId,
                            @Param("orgId") String orgId,
                            @Param("arr") List<String> arr);

    void removeOldZeroUnitsList(@Param("lineId") String lineId,
                                @Param("orgId") String orgId,
                                @Param("arr") List<String> arr);

    List<Map<String, String>> getHistoryTcDetail(@Param("year") Integer year,
                                                 @Param("orgId") String orgId,
                                                 @Param("lineCode") String lineCode,
                                                 @Param("typeName") String typeName);

    List<PieChartsEntity> getDssTypeNum(@Param("year") String year,
                                        @Param("orgId") String orgId,
                                        @Param("lineCode") String lineCode);

    List<Map<String, Object>> getUnitToStake(@Param("lineCode") String lineCode);

    List<Map<String, Object>> getOrgNameList(@Param("arr") List<String> arr);

    List<Map<String, Object>> getAllPrjId();

    List<Map<String, Object>> getAllGroupStakeRange();

    List<Map<String, Object>> getAllPrjLineCode();
}
