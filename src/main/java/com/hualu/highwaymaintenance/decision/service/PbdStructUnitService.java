package com.hualu.highwaymaintenance.decision.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.decision.entity.PbdMainPointPavement;
import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail100;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.module.user.domain.FwRightOrg;
import com.hualu.highwaymaintenance.util.BaseStake;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface PbdStructUnitService extends IService<PbdStreamUnit> {

    RestResult<String> ktSplitUnit(String lineCode, String orgId, Double startStake, Double endStake);

    RestResult<String> ktSplitCompleteUnit(String lineCode, String orgId, Double startStake, Double endStake
            , List<BaseStake> orgIdStakeRange, List<BaseStake> downOrgIdStakeRange);

    //生成百米单元的国评单元划分
    RestResult<String> ktSplitCompleteHun(List<PbdStreamUnit> listUnits, String lineCode, String orgId, Double startStake, Double endStake);

    List<FwRightOrg> getAllParentOrgName();

    List<Map<String, Object>> getOrgIdToAllPrj(String orgId);

    void savePavementScore(String prjId, String lineCode, String orgId, String year, String usered);

    Page<TcaTcDetail1000> getDesionTcDetiailList(IPage<TcaTcDetail1000> page, String orgId, String year, String lineId, String usered);

    Page<TcaTcDetail100> getHunsTcDetailDataList(IPage<TcaTcDetail100> page, String orgId, String year, String lineId);

    List<Map<String, Object>> findGroupScore(String orgId, String year, String lineCode);

    XSSFWorkbook importTcDetailExcelData(String prj, String index, String typeNumber, String lineCode, String laneUp, String laneDown, String years);

    XSSFWorkbook getExportExcelPbi(String prj, String index, String typeNumber, String lineCode
            , String laneUp, String laneDown, String years);

    XSSFWorkbook getExportExcelAllTable(String prj, String key, String value, String lineCode, String laneUp, String laneDown, String years);

    XSSFWorkbook getExportExcelPssi(String prj, String index, String typeNumber, String lineCode, String laneUp, String laneDown, String years);

    void importUnitTcDetail();

    XSSFWorkbook getExportExcelTci(String prj, String index, String typeNumber, String lineCode, String years);

    XSSFWorkbook getExportExcelSci(String prj, String index, String typeNumber, String lineCode, String years);

    List<Map<String, Object>> findDssInfoGroup(String orgId, String year, String lineCode);

    List<Map<String, String>> getOrgIdToLineId(String orgId);

    Page<PbdStreamUnit> getFromUnitList(IPage<PbdStreamUnit> page, String prjId, String lineId, String usered);

    List<PbdStreamUnit> getFromHunsList(String prjId, String lineId);

    void downLoadUnitList(HttpServletResponse response, String prjId, String lineId, String usered);

    void downLoadHunsList(HttpServletResponse response, String prjId, String lineId);

    void saveMainPointPavement(List<PbdMainPointPavement> userList, String prjId, String lineId);

    RestResult uploadUnitsFile(MultipartFile file, String orgId, String lineCode);

    Map<String, Object> getHistoryTcDetail(Integer year, String orgId, String lineCode, String typeName);

    net.sf.json.JSONArray getDssTypeNum(String year, String orgId, String lineCode);

    void getFileTemp(HttpServletResponse response, String index);

    RestResult uploadUnitsTcDetailFiles(MultipartFile file, String prjId, String lineId, String year);

    List<Map<String, Object>> getAllGroupStakeRange();

    List<Map<String, Object>> getAllPrjLineCode();
}