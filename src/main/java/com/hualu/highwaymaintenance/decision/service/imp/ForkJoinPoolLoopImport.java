package com.hualu.highwaymaintenance.decision.service.imp;

import com.hualu.highwaymaintenance.decision.entity.AntiSlipData;
import com.hualu.highwaymaintenance.decision.entity.RuttingData;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.decision.mapper.TdServiceMapper;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.BeanUtils;
import com.hualu.highwaymaintenance.util.CheckResult;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.RecursiveTask;
import java.util.concurrent.atomic.AtomicInteger;

public class ForkJoinPoolLoopImport extends RecursiveTask<List<CheckResult>> {
    private int startRow;

    private int endRow;

    private List<Map<String, Object>> list;

    private Integer total; //分了多少行

    private PbdStreamMapper lineService;

    private TdServiceMapper tdMapper;

    private Map<String, String> maps; //线路

    private String orgUnitId;

    private Map<String, String> dssTypeGroup;

    private String prjId;

    private String facilityCat;

    private String screening;

    private AtomicInteger integer;

    private JdbcTemplate jdbcTemplate;

    private String year;

    public ForkJoinPoolLoopImport(int startRow, int endRow, List<Map<String, Object>> list, Integer total
            , PbdStreamMapper lineService, TdServiceMapper tdMapper, Map<String, String> maps, String orgUnitId, Map<String, String> dssTypeGroup
            , String prjId, String facilityCat, String screening, AtomicInteger integer, JdbcTemplate jdbcTemplate, String year) {
        this.startRow = startRow;
        this.endRow = endRow;
        this.list = list;
        this.total = total;
        this.lineService = lineService;
        this.tdMapper = tdMapper;
        this.maps = maps;
        this.orgUnitId = orgUnitId;
        this.dssTypeGroup = dssTypeGroup;
        this.prjId = prjId;
        this.facilityCat = facilityCat;
        this.screening = screening;
        this.integer = integer;
        this.jdbcTemplate = jdbcTemplate;
        this.year = year;
    }

    @Override
    protected List<CheckResult> compute() {
        List<CheckResult> join = new ArrayList<>();
        if ((endRow - startRow) <= 1000) {
            List<CheckResult> checkResults = new ArrayList<CheckResult>();
            try {
                if ("LQ".equals(facilityCat) || "SN".equals(facilityCat)) {
                    this.importMultiThreadCheckMessage(checkResults);
                } else if ("RDI".equals(facilityCat)) {
                    this.importRdExcelMessage(checkResults);
                } else if ("SRI".equals(facilityCat)) {
                    this.newPavementSriCheckData(checkResults);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            join.addAll(checkResults);
            return join;
        } else {
            int newTemp = (startRow + endRow) / 2;
            ForkJoinPoolLoopImport loopImportLeft = new ForkJoinPoolLoopImport(startRow, newTemp, list, list.size()
                    , lineService, tdMapper, maps, orgUnitId, dssTypeGroup, prjId, facilityCat, screening, integer, jdbcTemplate, year);
            loopImportLeft.fork();
            join = loopImportLeft.join();
            List<CheckResult> join1 = null;
            if (newTemp < endRow) {
                endRow = endRow > list.size() ? list.size() : endRow;
                ForkJoinPoolLoopImport loopImportRight = new ForkJoinPoolLoopImport(newTemp, endRow, list, list.size()
                        , lineService, tdMapper, maps, orgUnitId, dssTypeGroup, prjId, facilityCat, screening, integer, jdbcTemplate, year);
                loopImportRight.fork();

                join1 = loopImportRight.join();

            }
            if (join1 != null) {
                join.addAll(join1);
            }
            return join;
        }
    }

    public void importMultiThreadCheckMessage(List<CheckResult> result) {
        List<DssInfo> dssInfos = new ArrayList<DssInfo>();
        try {
            BigDecimal startStakeTemp = new BigDecimal(0);
            BigDecimal endStakeTemp = new BigDecimal(0);
            String routeCode_temp = null;
            for (int i = startRow; i < endRow; i++) {
                Map<String, Object> lq = list.get(i);
                if (lq.get("起点桩号") == null) {
                    continue;
                }

                BigDecimal startStake = new BigDecimal(String.valueOf(lq.get("起点桩号")));
                BigDecimal endStake = new BigDecimal(String.valueOf(lq.get("终点桩号")));
                if (startStake != null && startStake.doubleValue() > 4000) {
                    startStake = startStake.divide(new BigDecimal("1000"));
                    endStake = endStake.divide(new BigDecimal("1000"));
                }
                String pavement = String.valueOf(lq.get("路面类型"));
                String lineCode = String.valueOf(lq.get("路线编码"));
                String lineId = null;
                if (maps != null && maps.containsKey(lineCode)) {
                    lineId = maps.get(lineCode);
                } else {
                    lineCode = lineCode.toUpperCase();
                    lineId = lineService.getLineId(lineCode);
                    maps.put(lineCode, lineCode);
                }
                String dssTyep = String.valueOf(lq.get("病害名称"));
                dssTyep = "修补".equals(dssTyep) || "条状".equals(dssTyep) ? "条状修补" : dssTyep;
                String dssDegree = lq.containsKey("程度") ? BeanUtils.isBank((String) lq.get("程度")) : "无";
                dssDegree = "条状".equals(dssDegree) ? "无" : dssDegree;
                String dssLength = lq.containsKey("长度(m)") ? BeanUtils.isBank((String) lq.get("长度(m)")) : "无";
                String width = lq.containsKey("宽度(m)") ? BeanUtils.isBank((String) lq.get("宽度(m)")) : "无";
                String height = lq.containsKey("深度(mm)") ? BeanUtils.isBank((String) lq.get("深度(mm)")) : "无";
                String m2 = lq.containsKey("面积(㎡)") ? BeanUtils.isBank((String) lq.get("面积(㎡)")) : "无";
                String lane = String.valueOf(lq.get("车道"));
                String remark = String.valueOf(lq.get("备注"));
                String routeCode = null;
                Double start_stake = null;
                Double end_stake = null;
                String dss_type = null;
                BigDecimal substr = new BigDecimal(String.valueOf(startStake));
                BigDecimal subtract = substr.subtract(endStakeTemp);
                BigDecimal subtractStart = substr.subtract(startStakeTemp);
                if (startStakeTemp != null && endStakeTemp != null
                        && !StringUtils.isBlank(routeCode) && ((subtract.multiply(subtractStart).doubleValue()) < 0
                        || substr.doubleValue() == endStakeTemp.doubleValue())) {
                    routeCode = routeCode_temp;
                } else {
                    Map<String, String> logicRouteCodeVersion = lineService.getLogicRouteCodeVersion(lineId
                            , startStake.doubleValue(), BeanUtils.landirectType(lane), null, orgUnitId, null, null);
                    if (logicRouteCodeVersion == null || logicRouteCodeVersion.size() == 0) {
                        logicRouteCodeVersion = lineService.getLogicRouteCodeVersion(lineId
                                , startStake.doubleValue(), BeanUtils.landirectType(lane), null, orgUnitId, "floor", null);
                    }
                    if (logicRouteCodeVersion != null && logicRouteCodeVersion.containsKey("ROUTE_CODE")) {
                        routeCode = logicRouteCodeVersion.get("ROUTE_CODE");
                        routeCode_temp = routeCode;
                        startStakeTemp = new BigDecimal(logicRouteCodeVersion.get("START_STAKE").toString());
                        endStakeTemp = new BigDecimal(logicRouteCodeVersion.get("END_STAKE").toString());
                    }
                }
                if (null == lineId || StringUtils.isBlank(lineId.toString())) {
                    result.add(new CheckResult(i + 3, "路线编码", "线路为空"));
                }
                if (null == startStake || StringUtils.isBlank(startStake.toString())) {
                    result.add(new CheckResult(i + 3, "桩号", "桩号为空"));
                }
                if (null == lane || StringUtils.isBlank(lane.toString())) {
                    result.add(new CheckResult(i + 3, "路线方向", "路线方向为空"));
                }
                if (null == dssTyep || StringUtils.isBlank(dssTyep.toString())) {
                    result.add(new CheckResult(i + 3, "病害类型", "病害类型为空"));
                } else {
                    if (dssTypeGroup != null && !dssTypeGroup.isEmpty()) {
                        if (dssTypeGroup.containsKey(dssTyep)) {
                            dss_type = dssTypeGroup.get(dssTyep);
                        } else {
                            result.add(new CheckResult(i + 3, "病害类型错误", "请检测对应行的病害类型" + dssTyep));
                        }
                    }
                    if (StringUtils.isBlank(dss_type)) {
                        result.add(new CheckResult(i + 3, "病害类型", "病害类型跟路面类型不匹配"));
                    }
                }
                String degree = BeanUtils.stringToDssDegree(dssDegree);
                String unit = BeanUtils.dssTypeToDssTypeUnit(dss_type);
                String m2Unit = null;
                DssInfo di = new DssInfo();
                if ("m".equals(unit) && !"LMLQ-0012".equals(dss_type) && !"LMLQ-0011".equals(dss_type)) {
                    m2 = null;
                    di.setDssL(new BigDecimal(dssLength));
                } else if ("LMLQ-0012".equals(dss_type) && unit != null) {
                    m2 = null;
                    di.setDssL(new BigDecimal(dssLength));
                } else if ((unit == null || "LMLQ-0011".equals(dss_type)) && !StringUtils.isBlank(m2)) {
                    dssLength = null;
                    di.setDssA(new BigDecimal(m2));
                    m2Unit = "m2";
                } else {
                    result.add(new CheckResult(i + 3, "病害类型", "请检查病害类型计量单位是否正确"));
                }
                String id = UUID.randomUUID().toString();
                di.setDssId(id);
                di.setDssCode(id);
                di.setDssDegree(degree == null ? "" : degree.toString());
                di.setDssCause(remark);
                di.setDssDesc("00");
                di.setStake(startStake.doubleValue());
                di.setLane(BeanUtils.matchResult(lane.toString()));
                di.setMainRoadId(lineId);
                di.setLaneDirection(BeanUtils.landirectType(di.getLane()));
                di.setRelTaskCode(prjId);
                if ("LQ".equals(facilityCat) || "SN".equals(facilityCat)) {
                    di.setFacilityCat("LM");
                } else {
                    di.setFacilityCat(facilityCat);
                }
                di.setDssLUnit(unit);
                di.setDssAUnit(m2Unit);
                di.setDealStatus(0);
                di.setRepairStatus(0);
                di.setDssImpFlag(0);
                di.setDssSource(3);
                di.setDssType(dss_type);
                di.setFoundDate(new Date());
                di.setRoutecode(routeCode);
                di.setRouteversion("2020");
                di.setMeans("0");

                if (result.size() == 0) {
                    if (!lineId.isEmpty() && "1"
                            .equals(screening) && integer.compareAndSet(0, 1)) {
                        tdMapper.deleteTestDssInfoData(prjId, "%" + facilityCat + "%");
                    }
                    dssInfos.add(di);
                } else {
                    break;
                }
            }
            insertMemsDateList(dssInfos);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void importRdExcelMessage(List<CheckResult> result) {
        try {
            if (startRow < endRow) {
                List<RuttingData> listRutt = new ArrayList<>();
                for (int i = startRow; i < endRow; i++) {
                    Map<String, Object> ruts = list.get(i);
                    if (ruts.get("开始桩号") == null) {
                        continue;
                    }
                    String lineCode = String.valueOf(ruts.get("路线编码"));
                    BigDecimal startStake = new BigDecimal(ruts.get("开始桩号").toString());
                    BigDecimal endStake = new BigDecimal(ruts.get("结束桩号").toString());
                    BigDecimal left = new BigDecimal(ruts.get("左车辙").toString());
                    BigDecimal right = new BigDecimal(ruts.get("右车辙").toString());
                    String lane = String.valueOf(ruts.get("车道"));
                    String line_id = null;

                    if (null == lineCode || StringUtils.isBlank(lineCode.toString())) {
                        result.add(new CheckResult(i + 3, "线路", "线路为空"));
                    } else {
                        lineCode = lineCode.toUpperCase();
                    }
                    if (null == startStake || StringUtils.isBlank(startStake.toString())) {
                        result.add(new CheckResult(i + 3, "起点桩号", "起点桩号为空"));
                    }
                    if (null == endStake || StringUtils.isBlank(endStake.toString())) {
                        result.add(new CheckResult(i + 3, "终点桩号", "终点桩号为空"));
                    }
                    if (!maps.isEmpty() && maps.containsKey(lineCode)) {
                        line_id = maps.get(lineCode);
                    } else {
                        line_id = lineService.getLineId(lineCode);
                        if (StringUtils.isNotBlank(line_id)) {
                            maps.put(lineCode, line_id);
                        } else {
                            result.add(new CheckResult(i + 3, "线路", "未找到此线路"));
                        }
                    }
                    RuttingData rut = new RuttingData();
                    rut.setRuttingId(UUID.randomUUID().toString());
                    rut.setStartStake(startStake.doubleValue());
                    rut.setEndStake(endStake.doubleValue());
                    rut.setLane(BeanUtils.matchResult(lane.toString()));
                    rut.setLineId(line_id);
                    rut.setLaneDirection(BeanUtils.landirectType(rut.getLane()));
                    rut.setPrjId(prjId);
                    rut.setLeftRdM(left);
                    rut.setRightRdM(right);
                    Map<String, String> logicRouteCodeVersion = lineService.getLogicRouteCodeVersion(line_id
                            , startStake.doubleValue(), rut.getLaneDirection(), null, orgUnitId, null, null);
                    if (logicRouteCodeVersion == null || logicRouteCodeVersion.size() == 0) {
                        logicRouteCodeVersion = lineService.getLogicRouteCodeVersion(line_id
                                , startStake.doubleValue(), rut.getLaneDirection(), null, orgUnitId, "floor", null);
                    }
                    if (logicRouteCodeVersion != null && logicRouteCodeVersion.size() > 0) {
                        rut.setRouteCode(logicRouteCodeVersion.get("ROUTE_CODE"));
                    }
                    if (result.size() == 0) {
                        if (line_id != null && !line_id.isEmpty()
                                && "1".equals(screening) && integer.compareAndSet(0, 1)) {
                            tdMapper.deleteTestRuttingData(prjId);
                        }
                        listRutt.add(rut);
                    }
                }
                this.insertRuttDateList(listRutt);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void newPavementSriCheckData(List<CheckResult> result) {
        try {
            Map<String, String> lineMaps = new HashMap<String, String>();
            String lineId = null;
            if (startRow < endRow) {
                List<AntiSlipData> saveTemp = new ArrayList<AntiSlipData>();
                BigDecimal startStakeTemp = new BigDecimal(0);
                BigDecimal endStakeTemp = new BigDecimal(0);
                String routeCode = null;
                for (int i = startRow; i < endRow; i++) {
                    Map<String, Object> ruts = list.get(i);
                    if (ruts.get("起点桩号") == null) {
                        continue;
                    }
                    String line_code = String.valueOf(ruts.get("路线编码"));
                    String lane = String.valueOf(ruts.get("车道"));
                    Double startStake = Double.valueOf(String.valueOf(ruts.get("起点桩号")));
                    Double endStake = Double.valueOf(String.valueOf(ruts.get("终点桩号")));
                    Double left = StringUtils.isBlank(String.valueOf(ruts.get("测试速度(km/h)"))) ? null : Double.valueOf(String.valueOf(ruts.get("测试速度(km/h)")));
                    Double right = StringUtils.isBlank(String.valueOf(ruts.get("路表温度℃"))) ? null : Double.valueOf(String.valueOf(ruts.get("路表温度℃")));
                    Double overSfc = StringUtils.isBlank(String.valueOf(ruts.get("原始SFC"))) ? null : Double.valueOf(String.valueOf(ruts.get("原始SFC")));
                    Double sfc = Double.valueOf(String.valueOf(ruts.get("SFC")));
                    String remark = String.valueOf(String.valueOf(ruts.get("备注")));

                /*if (Math.abs(startStake.doubleValue() - endStake.doubleValue()) > 0.1)
                {
                    result.add(new CheckResult(i + 3, "检测数据点", "检测数据长度过大,应为十米一个检测值"));
                }*/
                    if (null == line_code || StringUtils.isBlank(line_code.toString())) {
                        result.add(new CheckResult(i, "线路", "线路为空"));
                    } else {
                        line_code = line_code.toUpperCase();
                    }
                    if (null == startStake || StringUtils.isBlank(startStake.toString())) {
                        result.add(new CheckResult(i, "起点桩号", "起点桩号为空"));
                    }
                /*if (null == endStake || StringUtils.isBlank(endStake.toString())) {
                    result.add(new CheckResult(i, "终点桩号", "终点桩号为空"));
                }*/
                    if (null == lane || StringUtils.isBlank(lane.toString())) {
                        result.add(new CheckResult(i, "车道	", "车道为空"));
                    }
                    if (null == sfc || StringUtils.isBlank(sfc.toString())) {
                        result.add(new CheckResult(i, "SFC修正值", "SFC修正值为空"));
                    }
                    if (!lineMaps.isEmpty() && lineMaps.containsKey(line_code)) {
                        lineId = lineMaps.get(line_code);
                    } else {
                        if (line_code != null) {
                            lineId = lineService.getLineId(line_code);
                        } else {
                            result.add(new CheckResult(i + 3, "线路", "路线编码为空"));
                        }
                        if (StringUtils.isNotBlank(lineId)) {
                            lineMaps.put(line_code, lineId);
                        } else {
                            result.add(new CheckResult(i + 3, "线路", "未找到此线路"));
                        }
                    }
                    AntiSlipData an = new AntiSlipData();
                    an.setAntiSlipId(UUID.randomUUID().toString());
                    an.setStartStake(startStake);
                    an.setEndStake(endStake);
                    an.setLane(BeanUtils.matchResult(lane.toString()));
                    an.setLineId(lineId);
                    an.setLaneDirection(BeanUtils.landirectType(an.getLane()));
                    an.setSfcS(overSfc);
                    an.setSfcM(sfc);
                    an.setYear(Integer.valueOf(this.year));
                    an.setTestSpeed(left);
                    an.setPaveTemp(right);
                    an.setPrjId(prjId);
                    BigDecimal substr = new BigDecimal(String.valueOf(an.getStartStake()));
                    BigDecimal subtract = substr.subtract(endStakeTemp);
                    BigDecimal subtractStart = substr.subtract(startStakeTemp);
                    if (startStakeTemp != null && endStakeTemp != null
                            && !StringUtils.isBlank(routeCode) && ((subtract.multiply(subtractStart).doubleValue()) < 0
                            || substr.doubleValue() == endStakeTemp.doubleValue())) {
                        an.setRouteCode(routeCode);
                    } else {
                        Map<String, String> route = lineService.getLogicRouteCodeVersion(lineId
                                , an.getStartStake(), an.getLaneDirection(), null, orgUnitId, null, null);
                        if (route == null || route.size() == 0) {
                            route = lineService.getLogicRouteCodeVersion(lineId
                                    , an.getStartStake(), an.getLaneDirection(), null, orgUnitId, "floor", null);
                        }
                        if (route != null && route.containsKey("ROUTE_CODE")) {
                            routeCode = route.get("ROUTE_CODE");
                            an.setRouteCode(routeCode);
                            an.setRouteversion(route.get("ROUTE_VERSION"));
                            startStakeTemp = new BigDecimal(route.get("START_STAKE").toString());
                            endStakeTemp = new BigDecimal(route.get("END_STAKE").toString());
                        }
                    }
                    an.setRemark(remark == null ? "" : remark.toString());
                    if (result.size() == 0) {
                        if (an.getLineId() != null && !an.getLineId().isEmpty()
                                && "1".equals(screening) && integer.compareAndSet(0, 1)) {
                            tdMapper.deleteTestAntiSlipData(prjId);
                        }
                        saveTemp.add(an);
                    }
                }
                insertSRIDateList(saveTemp);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void insertRdDataList() {
       /* INSERT INTO PTCMSDB.PTCD_RUTTING_DATA (RUTTING_ID,PRJ_ID,RP_INTRVL_ID,START_STAKE,END_STAKE,LANE,
            PAVEMENT_TYPE,LEFT_RD,RIGHT_RD,LEFT_RD_M,RIGHT_RD_M,REMARK,LINE_ID,LANE_DIRECTION,YEAR
            ,UNIT_MARGE_ID,routecode,routeversion);
            StringBuffer sql = new StringBuffer();
            sql.append("");*/

    }

    private void insertSRIDateList(List<AntiSlipData> slipData) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("INSERT INTO PTCMSDB.PTCD_ANTI_SLIP_DATA_DESION ")
                .append("(ANTI_SLIP_ID,START_STAKE,END_STAKE,LANE,LINE_ID,LANE_DIRECTION,SFC_S,")
                .append("SFC_M,PAVEMENT_TYPE,UNIT_MARGE_ID,YEAR,TEST_SPEED,PAVE_TEMP,PRJ_ID")
                .append(",REMARK,routecode)VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
        this.jdbcTemplate.batchUpdate(buffer.toString(), new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, StringUtil.getUUID());
                ps.setDouble(2, slipData.get(i).getStartStake());
                ps.setDouble(3, slipData.get(i).getEndStake());
                ps.setString(4, slipData.get(i).getLane());
                ps.setString(5, slipData.get(i).getLineId());
                ps.setString(6, slipData.get(i).getLaneDirection());
                ps.setDouble(7, slipData.get(i).getSfcS() == null ? 0 : slipData.get(i).getSfcS());
                ps.setDouble(8, slipData.get(i).getSfcM() == null ? 0 : slipData.get(i).getSfcM());
                ps.setString(9, slipData.get(i).getPavementType());
                ps.setString(10, StringUtil.getUUID());
                ps.setInt(11, Integer.valueOf(year));
                ps.setDouble(12, slipData.get(i).getTestSpeed() == null ? 0 : slipData.get(i).getTestSpeed());
                ps.setDouble(13, slipData.get(i).getPaveTemp() == null ? 0 : slipData.get(i).getPaveTemp());
                ps.setString(14, slipData.get(i).getPrjId());
                ps.setString(15, slipData.get(i).getRemark());
                ps.setString(16, slipData.get(i).getRouteCode());
            }

            @Override
            public int getBatchSize() {
                return slipData.size();
            }
        });
    }

    private void insertRuttDateList(List<RuttingData> slipData) {
        StringBuffer buffer = new StringBuffer();
        String sql = "INSERT INTO PTCMSDB.PTCD_RUTTING_DATA_DESION (RUTTING_ID, PRJ_ID, START_STAKE, END_STAKE, LANE," +
                " PAVEMENT_TYPE, LEFT_RD, RIGHT_RD, LEFT_RD_M, RIGHT_RD_M, REMARK, LINE_ID, LANE_DIRECTION, YEAR, UNIT_MARGE_ID," +
                " routecode) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        this.jdbcTemplate.batchUpdate(buffer.toString(), new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, StringUtil.getUUID());
                ps.setString(2, slipData.get(i).getPrjId());
                ps.setDouble(3, slipData.get(i).getStartStake());
                ps.setDouble(4, slipData.get(i).getEndStake());
                ps.setString(5, slipData.get(i).getLane());
                ps.setString(6, slipData.get(i).getPavementType());
                ps.setDouble(7, slipData.get(i).getLeftRd() == null ? 0 : slipData.get(i).getLeftRd());
                ps.setDouble(8, slipData.get(i).getLeftRd() == null ? 0 : slipData.get(i).getLeftRd());
                ps.setDouble(9, slipData.get(i).getLeftRdM() == null ? 0 : slipData.get(i).getLeftRdM().doubleValue());
                ps.setDouble(10, slipData.get(i).getRightRdM() == null ? 0 : slipData.get(i).getRightRdM().doubleValue());
                ps.setString(11, slipData.get(i).getRemark());
                ps.setString(12, slipData.get(i).getLineId());
                ps.setString(13, slipData.get(i).getLaneDirection());
                ps.setInt(14, slipData.get(i).getYear());
                ps.setString(15, slipData.get(i).getUnitMargeId());
                ps.setString(16, slipData.get(i).getRouteCode());
            }

            @Override
            public int getBatchSize() {
                return slipData.size();
            }
        });
    }

    private void insertMemsDateList(List<DssInfo> dssInfos) {
        StringBuffer buffer = new StringBuffer();
        buffer.append("INSERT INTO MEMSDB.DSS_INFO_DESION (DSS_ID,REL_TASK_CODE,STAKE,FACILITY_CAT,DSS_CODE,")
                .append("DSS_DEGREE,DSS_DESC,DSS_L,DSS_L_UNIT,DSS_A,DSS_A_UNIT,DSS_N,DSS_N_UNIT,MNTN_ADVICE,")
                .append("DSS_CAUSE,DEAL_STATUS,REPAIR_STATUS,DSS_IMP_FLAG,DSS_SOURCE,DSS_TYPE,FOUND_DATE,")
                .append("LANE,MAIN_ROAD_ID,LANE_DIRECTION,YEAR,UNIT_MARGE_ID,means,routecode) VALUES ")
                .append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
        this.jdbcTemplate.batchUpdate(buffer.toString(), new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, StringUtil.getUUID());
                ps.setString(2, dssInfos.get(i).getRelTaskCode());
                ps.setDouble(3, dssInfos.get(i).getStake().doubleValue());
                ps.setString(4, dssInfos.get(i).getFacilityCat());
                ps.setString(5, dssInfos.get(i).getDssCode());
                ps.setString(6, dssInfos.get(i).getDssDegree());
                ps.setString(7, dssInfos.get(i).getDssDesc());
                ps.setDouble(8, dssInfos.get(i).getDssL() == null ? 0 : dssInfos.get(i).getDssL().doubleValue());
                ps.setString(9, dssInfos.get(i).getDssLUnit());
                ps.setDouble(10, dssInfos.get(i).getDssA() == null ? 0 : dssInfos.get(i).getDssA().doubleValue());
                ps.setString(11, dssInfos.get(i).getDssAUnit());
                ps.setDouble(12, dssInfos.get(i).getDssN() == null ? 0 : dssInfos.get(i).getDssN().doubleValue());
                ps.setString(13, dssInfos.get(i).getDssNUnit());
                ps.setString(14, dssInfos.get(i).getMntnAdvice());
                ps.setString(15, dssInfos.get(i).getDssCause());
                ps.setInt(16, dssInfos.get(i).getDealStatus());
                ps.setInt(17, dssInfos.get(i).getRepairStatus());
                ps.setInt(18, dssInfos.get(i).getDssImpFlag());
                ps.setInt(19, dssInfos.get(i).getDssSource());
                ps.setString(20, dssInfos.get(i).getDssType());
                ps.setDate(21, new java.sql.Date(dssInfos.get(i).getFoundDate().getTime()));
                ps.setString(22, dssInfos.get(i).getLane());
                ps.setString(23, dssInfos.get(i).getMainRoadId());
                ps.setString(24, dssInfos.get(i).getLaneDirection());
                ps.setInt(25, Integer.valueOf(year));
                ps.setString(26, dssInfos.get(i).getUnitMargeId());
                ps.setString(27, dssInfos.get(i).getMeans());
                ps.setString(28, dssInfos.get(i).getRoutecode());
            }

            @Override
            public int getBatchSize() {
                return dssInfos.size();
            }
        });
    }
}
