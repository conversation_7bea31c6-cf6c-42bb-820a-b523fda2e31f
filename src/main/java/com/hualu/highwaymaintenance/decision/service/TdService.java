package com.hualu.highwaymaintenance.decision.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.highwaymaintenance.decision.entity.CheckParent;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.CheckResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface TdService extends IService<DssInfo> {
    List<CheckResult> importExcelListData(MultipartFile file, String orgId, String year, String index, String scree);

    void downTempFileCheck(HttpServletResponse response, IPage<CheckParent> desionTcDetiailList, String type);

    IPage<?> findDssTypeGroupList(int limit, int offset, String orgId, String lineCode, String year, String type);

    boolean loadCheckDataList(int limit, int offset, String orgId, String lineCode, String year, String type);

    List<Map<String, String>> getAllGroupPrjId(String year);
}