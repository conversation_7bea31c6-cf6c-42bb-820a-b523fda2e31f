package com.hualu.highwaymaintenance.decision.ImportExcelUtils;

import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ExelFileZipLoop {
    private static final Map<String, byte[]> byteCache = new HashMap<String, byte[]>();
    private static final String pathPrefix = "D://";

    //添加要压缩的字节文件
    public static final void putNext(String key, byte[] value)
    {
        //验证参数是为合法
        if(StringUtils.isBlank(key) && value != null && value.length > 0)
            throw new NullPointerException("参数不能为空");

        byteCache.put(key, value);
    }
    public static void main(String[] args) {
        Double crack4Light =700.0;
        BigDecimal crackString = null;
        if (crack4Light > 0 && crack4Light < 10)
        {
            crackString = new BigDecimal(10*0.1);
        }else if ((crack4Light*0.1)<=100)
        {
            crackString = new BigDecimal(crack4Light*0.1);
        }else if ((crack4Light*0.1)>100)
        {
            crackString = new BigDecimal("100");
        }
        BigDecimal crackLeString = new BigDecimal("100").subtract(crackString).multiply(new BigDecimal("0.2"));
        System.out.println(crackLeString);
    }
    //将文件压缩并保存到本地
    public static final String loopZipSave(String fileName, String suffix) {
        if (StringUtils.isBlank(fileName))
            throw new NullPointerException("文件压缩保存参数为空");

        ZipOutputStream zos = null;
        String tempPath = pathPrefix + fileName +
                "." + suffix.replace(".", "");
        try {

            File file = new File(tempPath);

            FileOutputStream fileoutputstream = new FileOutputStream(file);
            zos = new ZipOutputStream(fileoutputstream);

            fileSave(zos, null);

            //文件保存完毕清空缓存
            byteCache.clear();
        }catch (IOException e)
        {
            e.printStackTrace();
        }finally
        {
          try {
               zos.flush();
               zos.closeEntry();
               zos.close();
          } catch (IOException e) {
               e.printStackTrace();
          }
        }

        return tempPath;
    }
    //抽取公共方法，可以保存到本地 也可以进行下载
    public static void fileSave(OutputStream zos, String fileName) throws IOException {

        if (zos == null)
        {
            throw new NullPointerException("zos参数为空");
        }

        if(zos instanceof ZipOutputStream)
        {
            ZipOutputStream zis = (ZipOutputStream)zos;
            for (Map.Entry<String, byte[]> maps : byteCache.entrySet())
            {
                zis.putNextEntry(new ZipEntry(maps.getKey()));
                zis.write(maps.getValue());
                zis.closeEntry();
            }
        }else
        {
            if (byteCache != null && byteCache.size() > 0)
            {
                for (Map.Entry<String, byte[]> maps : byteCache.entrySet())
                {
                   zos.write(maps.getValue());
                }
            }else
            {
                File file = new File(fileName);
                FileInputStream stream = new FileInputStream(file);
                byte[] bytes = new byte[1024];
                int i = 0;
                while ((i = stream.read(bytes)) != -1)
                {
                    zos.write(bytes, 0 , i);
                }
                stream.close();
            }
        }
    }

    public static final void deleteFile(String fileName){
        try {
            File file = new File(fileName);
            boolean delete = file.delete();
            if (!delete)
            {
                throw new FileNotFoundException("文件删除失败");
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }
}
