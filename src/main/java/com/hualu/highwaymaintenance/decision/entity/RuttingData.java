package com.hualu.highwaymaintenance.decision.entity;

import java.math.BigDecimal;

/**
 *
 * @Title: 车辙
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午9:58:09
 */
public class RuttingData implements java.io.Serializable,RangeData {

    private static final long serialVersionUID = -4191003891755257390L;

    private String ruttingId; // 车辙ID
    private String prjId; // 检测项目ID
    private String rpIntrvlId; // 营运路段区间ID
    private Double startStake; // 起点桩号
    private Double endStake; // 止点桩号
    private String lane; // 车道
    private String pavementType; // 路面类型
    private Double leftRd; // RD左原始值
    private Double rightRd; // RD右原始值
    private BigDecimal leftRdM; // RD左修正值--
    private BigDecimal rightRdM; // RD右修正值--
    private String remark; // 备注
    private String lineId; // 路线ID
    private String lineCode; // 路线编码
    private String laneDirection; // 路线方向
    private Double rdi; // 评分
    private String rdiTcCode; // 评分等级
    private String structIntrvlUcode; // 单元编码
    private String rampId; // 匝道ID
    private String rampName; // 匝道name
    private String unitMargeId; //评定单元id
    private String rlStartStake;
    private String rlEndStake;
    private String lineName;
    private Integer year;//年份
    private Double rdm;
    private String routeCode;
    private String routeversion;
    private String hunMargeId;
    private Double startStakeUnit;

    public String getRuttingId() {
        return ruttingId;
    }
    public void setRuttingId(String ruttingId) {
        this.ruttingId = ruttingId;
    }
    public String getPrjId() {
        return prjId;
    }
    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }
    public String getRpIntrvlId() {
        return rpIntrvlId;
    }
    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }
    @Override
    public Double getStartStake() {
        return startStake;
    }
    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }
    @Override
    public Double getEndStake() {
        return endStake;
    }
    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }
    public String getLane() {
        return lane;
    }
    public void setLane(String lane) {
        this.lane = lane;
    }
    public String getPavementType() {
        return pavementType;
    }
    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }
    public Double getLeftRd() {
        return leftRd;
    }
    public void setLeftRd(Double leftRd) {
        this.leftRd = leftRd;
    }
    public Double getRightRd() {
        return rightRd;
    }
    public void setRightRd(Double rightRd) {
        this.rightRd = rightRd;
    }
    public BigDecimal getLeftRdM() {
        return leftRdM;
    }
    public void setLeftRdM(BigDecimal leftRdM) {
        this.leftRdM = leftRdM;
    }
    public BigDecimal getRightRdM() {
        return rightRdM;
    }
    public void setRightRdM(BigDecimal rightRdM) {
        this.rightRdM = rightRdM;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getLineId() {
        return lineId;
    }
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
    public String getLineCode() {
        return lineCode;
    }
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
    public String getLaneDirection() {
        return laneDirection;
    }
    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }
    public Double getRdi() {
        return rdi;
    }
    public void setRdi(Double rdi) {
        this.rdi = rdi;
    }
    public String getRdiTcCode() {
        return rdiTcCode;
    }
    public void setRdiTcCode(String rdiTcCode) {
        this.rdiTcCode = rdiTcCode;
    }
    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }
    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }
    public String getRampId() {
        return rampId;
    }
    public void setRampId(String rampId) {
        this.rampId = rampId;
    }
    public String getRampName() {
        return rampName;
    }
    public void setRampName(String rampName) {
        this.rampName = rampName;
    }
    public String getRlStartStake() {
        return rlStartStake;
    }
    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }
    public String getRlEndStake() {
        return rlEndStake;
    }
    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }
    public String getLineName() {
        return lineName;
    }
    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
    @Override
    public String getUnitMargeId() {
        return unitMargeId;
    }
    @Override
    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }
    public Double getRdm() {
        return rdm;
    }

    public void setRdm(Double rdm) {
        this.rdm = rdm;
    }
    @Override
    public String toString() {
        return "RuttingData [ruttingId=" + ruttingId + ", prjId=" + prjId + ", rpIntrvlId=" + rpIntrvlId
                + ", startStake=" + startStake + ", endStake=" + endStake + ", lane=" + lane + ", pavementType="
                + pavementType + ", leftRd=" + leftRd + ", rightRd=" + rightRd + ", leftRdM=" + leftRdM + ", rightRdM="
                + rightRdM + ", remark=" + remark + ", lineId=" + lineId + ", lineCode=" + lineCode + ", laneDirection="
                + laneDirection + ", rdi=" + rdi + ", rdiTcCode=" + rdiTcCode + ", structIntrvlUcode="
                + structIntrvlUcode + ", rampId=" + rampId + ", rampName=" + rampName + ", unitMargeId=" + unitMargeId
                + ", rlStartStake=" + rlStartStake + ", rlEndStake=" + rlEndStake + ", lineName=" + lineName + ", year="
                + year + "]";
    }

    @Override
    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }

    public String getHunMargeId() {
        return hunMargeId;
    }

    public void setHunMargeId(String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }

    public Double getStartStakeUnit() {
        return startStakeUnit;
    }

    public void setStartStakeUnit(Double startStakeUnit) {
        this.startStakeUnit = startStakeUnit;
    }
}