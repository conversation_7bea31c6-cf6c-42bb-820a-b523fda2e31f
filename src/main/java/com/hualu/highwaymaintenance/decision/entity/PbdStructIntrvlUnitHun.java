package com.hualu.highwaymaintenance.decision.entity;
import java.util.Objects;

/**
 * @Author: hewei
 * @Date: 2020/6/10 22:12
 * @Description:
 * @Version 1.0
 **/
public class PbdStructIntrvlUnitHun implements java.io.Serializable,Cloneable {

    private static final long serialVersionUID = 6932688977823690229L;

    private String structIntrvlUid;			//结构区间单元ID

    private String structIntrvlUcode;		//结构区间单元编码

    private String structIntrvlId;			//结构区间ID

    private String rpIntrvlId;				//营运路段区间ID

    private String techGrade;				//公路技术等级

    private String pavementType;			//路面类型

    private String pavementStructId; 	  	//路面结构ID

    private Double designLd;             	//设计弯沉

    private Double effectWidth;				//有效路面宽度

    private Integer year;					//年度

    private Double startStake;				//起点桩号

    private Double endStake;				//止点桩号

    private double length;					//长度（米）

    private Double startX;					//起点坐标X

    private Double startY;					//起点坐标Y

    private Double endX;					//止点坐标X

    private Double endY;					//止点坐标Y

    private String usered;					//是否正在使用的评定单元1，历史评定单元 0，正在使用的评定单元

    private String hunId;					//百米单元ID

    private String hunMargeId;				//百米评定单元合并ID

    private String structIntrvlCode;		//结构区间编码		---

    private String rlStartStake;

    private String rlEndStake;

    private String dstrctCode;				//所属行政区域

    private String laneType;				//车道类型

    private String pavementStruct;			//路面类型（导出模板）

    private String lineCode;				//路线编码（导出模板）

    private String lineName;

    private String lineDirectName;			//方向

    private Integer index ;					//弯沉的测点标记

    private String lane;					//车道

    private String intrvlMargeId;

    private String unitMargeId;

    private Integer index_length;

    private String routeCode;

    private String routeVersion;

    private String measureName;

    private String strutName;

    private String remark;

    public String getStructIntrvlUid() {
        return this.structIntrvlUid;
    }

    public void setStructIntrvlUid(final String structIntrvlUid) {
        this.structIntrvlUid = structIntrvlUid;
    }

    public String getStructIntrvlUcode() {
        return this.structIntrvlUcode;
    }

    public void setStructIntrvlUcode(final String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }

    public String getStructIntrvlId() {
        return this.structIntrvlId;
    }

    public void setStructIntrvlId(final String structIntrvlId) {
        this.structIntrvlId = structIntrvlId;
    }

    public String getRpIntrvlId() {
        return this.rpIntrvlId;
    }

    public void setRpIntrvlId(final String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public String getTechGrade() {
        return this.techGrade;
    }

    public void setTechGrade(final String techGrade) {
        this.techGrade = techGrade;
    }

    public String getPavementType() {
        return this.pavementType;
    }

    public void setPavementType(final String pavementType) {
        this.pavementType = pavementType;
    }

    public String getPavementStructId() {
        return this.pavementStructId;
    }

    public void setPavementStructId(final String pavementStructId) {
        this.pavementStructId = pavementStructId;
    }

    public Double getDesignLd() {
        return this.designLd;
    }

    public void setDesignLd(final Double designLd) {
        this.designLd = designLd;
    }

    public Double getEffectWidth() {
        return this.effectWidth;
    }

    public void setEffectWidth(final Double effectWidth) {
        this.effectWidth = effectWidth;
    }

    public Integer getYear() {
        return this.year;
    }

    public void setYear(final Integer year) {
        this.year = year;
    }

    public Double getStartStake() {
        return this.startStake;
    }

    public void setStartStake(final Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return this.endStake;
    }

    public void setEndStake(final Double endStake) {
        this.endStake = endStake;
    }

    public double getLength() {
        return this.length;
    }

    public void setLength(final double length) {
        this.length = length;
    }

    public Double getStartX() {
        return this.startX;
    }

    public void setStartX(final Double startX) {
        this.startX = startX;
    }

    public Double getStartY() {
        return this.startY;
    }

    public void setStartY(final Double startY) {
        this.startY = startY;
    }

    public Double getEndX() {
        return this.endX;
    }

    public void setEndX(final Double endX) {
        this.endX = endX;
    }

    public Double getEndY() {
        return this.endY;
    }

    public void setEndY(final Double endY) {
        this.endY = endY;
    }

    public String getUsered() {
        return this.usered;
    }

    public void setUsered(final String usered) {
        this.usered = usered;
    }

    public String getHunId() {
        return this.hunId;
    }

    public void setHunId(final String hunId) {
        this.hunId = hunId;
    }

    public String getHunMargeId() {
        return this.hunMargeId;
    }

    public void setHunMargeId(final String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }

    public String getStructIntrvlCode() {
        return this.structIntrvlCode;
    }

    public void setStructIntrvlCode(final String structIntrvlCode) {
        this.structIntrvlCode = structIntrvlCode;
    }

    public String getRlStartStake() {
        return this.rlStartStake;
    }

    public void setRlStartStake(final String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }

    public String getRlEndStake() {
        return this.rlEndStake;
    }

    public void setRlEndStake(final String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }

    public String getDstrctCode() {
        return this.dstrctCode;
    }

    public void setDstrctCode(final String dstrctCode) {
        this.dstrctCode = dstrctCode;
    }

    public String getLaneType() {
        return this.laneType;
    }

    public void setLaneType(final String laneType) {
        this.laneType = laneType;
    }

    public String getPavementStruct() {
        return this.pavementStruct;
    }

    public void setPavementStruct(final String pavementStruct) {
        this.pavementStruct = pavementStruct;
    }

    public String getLineCode() {
        return this.lineCode;
    }

    public void setLineCode(final String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLineName() {
        return this.lineName;
    }

    public void setLineName(final String lineName) {
        this.lineName = lineName;
    }

    public String getLineDirectName() {
        return this.lineDirectName;
    }

    public void setLineDirectName(final String lineDirectName) {
        this.lineDirectName = lineDirectName;
    }

    public Integer getIndex() {
        return this.index;
    }

    public void setIndex(final Integer index) {
        this.index = index;
    }

    public String getLane() {
        return this.lane;
    }

    public void setLane(final String lane) {
        this.lane = lane;
    }

    public String getIntrvlMargeId() {
        return this.intrvlMargeId;
    }

    public void setIntrvlMargeId(final String intrvlMargeId) {
        this.intrvlMargeId = intrvlMargeId;
    }

    public String getUnitMargeId() {
        return this.unitMargeId;
    }

    public void setUnitMargeId(final String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }

    public Integer getIndex_length() {
        return this.index_length;
    }

    public void setIndex_length(final Integer index_length) {
        this.index_length = index_length;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteVersion() {
        return routeVersion;
    }

    public void setRouteVersion(String routeVersion) {
        this.routeVersion = routeVersion;
    }

    public String getMeasureName() {
        return measureName;
    }

    public void setMeasureName(String measureName) {
        this.measureName = measureName;
    }

    public String getStrutName() {
        return strutName;
    }

    public void setStrutName(String strutName) {
        this.strutName = strutName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "PbdStructIntrvlUnitHun{" +
                "structIntrvlUid='" + structIntrvlUid + '\'' +
                ", structIntrvlUcode='" + structIntrvlUcode + '\'' +
                ", structIntrvlId='" + structIntrvlId + '\'' +
                ", rpIntrvlId='" + rpIntrvlId + '\'' +
                ", techGrade='" + techGrade + '\'' +
                ", pavementType='" + pavementType + '\'' +
                ", pavementStructId='" + pavementStructId + '\'' +
                ", designLd=" + designLd +
                ", effectWidth=" + effectWidth +
                ", year=" + year +
                ", startStake=" + startStake +
                ", endStake=" + endStake +
                ", length=" + length +
                ", startX=" + startX +
                ", startY=" + startY +
                ", endX=" + endX +
                ", endY=" + endY +
                ", usered='" + usered + '\'' +
                ", hunId='" + hunId + '\'' +
                ", hunMargeId='" + hunMargeId + '\'' +
                ", structIntrvlCode='" + structIntrvlCode + '\'' +
                ", rlStartStake='" + rlStartStake + '\'' +
                ", rlEndStake='" + rlEndStake + '\'' +
                ", dstrctCode='" + dstrctCode + '\'' +
                ", laneType='" + laneType + '\'' +
                ", pavementStruct='" + pavementStruct + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", lineName='" + lineName + '\'' +
                ", lineDirectName='" + lineDirectName + '\'' +
                ", index=" + index +
                ", lane='" + lane + '\'' +
                ", intrvlMargeId='" + intrvlMargeId + '\'' +
                ", unitMargeId='" + unitMargeId + '\'' +
                '}';
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) return true;
        if (!(o instanceof PbdStructIntrvlUnitHun)) return false;
        final PbdStructIntrvlUnitHun that = (PbdStructIntrvlUnitHun) o;
        return Objects.equals(this.getStructIntrvlUid(), that.getStructIntrvlUid()) &&
                Objects.equals(this.getStructIntrvlUcode(), that.getStructIntrvlUcode()) &&
                Objects.equals(this.getStructIntrvlId(), that.getStructIntrvlId()) &&
                Objects.equals(this.getRpIntrvlId(), that.getRpIntrvlId()) &&
                Objects.equals(this.getTechGrade(), that.getTechGrade()) &&
                Objects.equals(this.getPavementType(), that.getPavementType()) &&
                Objects.equals(this.getPavementStructId(), that.getPavementStructId()) &&
                Objects.equals(this.getDesignLd(), that.getDesignLd()) &&
                Objects.equals(this.getEffectWidth(), that.getEffectWidth()) &&
                Objects.equals(this.getYear(), that.getYear()) &&
                Objects.equals(this.getStartStake(), that.getStartStake()) &&
                Objects.equals(this.getEndStake(), that.getEndStake()) &&
                Objects.equals(this.getLength(), that.getLength()) &&
                Objects.equals(this.getStartX(), that.getStartX()) &&
                Objects.equals(this.getStartY(), that.getStartY()) &&
                Objects.equals(this.getEndX(), that.getEndX()) &&
                Objects.equals(this.getEndY(), that.getEndY()) &&
                Objects.equals(this.getUsered(), that.getUsered()) &&
                Objects.equals(this.getHunId(), that.getHunId()) &&
                Objects.equals(this.getHunMargeId(), that.getHunMargeId()) &&
                Objects.equals(this.getStructIntrvlCode(), that.getStructIntrvlCode()) &&
                Objects.equals(this.getRlStartStake(), that.getRlStartStake()) &&
                Objects.equals(this.getRlEndStake(), that.getRlEndStake()) &&
                Objects.equals(this.getDstrctCode(), that.getDstrctCode()) &&
                Objects.equals(this.getLaneType(), that.getLaneType()) &&
                Objects.equals(this.getPavementStruct(), that.getPavementStruct()) &&
                Objects.equals(this.getLineCode(), that.getLineCode()) &&
                Objects.equals(this.getLineName(), that.getLineName()) &&
                Objects.equals(this.getLineDirectName(), that.getLineDirectName()) &&
                Objects.equals(this.getIndex(), that.getIndex()) &&
                Objects.equals(this.getLane(), that.getLane()) &&
                Objects.equals(this.getIntrvlMargeId(), that.getIntrvlMargeId()) &&
                Objects.equals(this.getUnitMargeId(), that.getUnitMargeId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.getStructIntrvlUid(), this.getStructIntrvlUcode(), this.getStructIntrvlId(), this.getRpIntrvlId(), this.getTechGrade(), this.getPavementType(), this.getPavementStructId(), this.getDesignLd(), this.getEffectWidth(), this.getYear(), this.getStartStake(), this.getEndStake(), this.getLength(), this.getStartX(), this.getStartY(), this.getEndX(), this.getEndY(), this.getUsered(), this.getHunId(), this.getHunMargeId(), this.getStructIntrvlCode(), this.getRlStartStake(), this.getRlEndStake(), this.getDstrctCode(), this.getLaneType(), this.getPavementStruct(), this.getLineCode(), this.getLineName(), this.getLineDirectName(), this.getIndex(), this.getLane(), this.getIntrvlMargeId(), this.getUnitMargeId());
    }
}
