package com.hualu.highwaymaintenance.decision.entity;
/**
 *
 * @Title: 排水
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午11:36:52
 */
public class DrainageData implements java.io.Serializable {

    private static final long serialVersionUID = -8916772350106623486L;

    private String drainDataId;
    private String prjId;
    private String rpIntrvlId;
    private Double stake;
    private String pavementType;
    private String drainDssType;
    private Double dssL;
    private Double dssW;
    private Double dssD;
    private Double dssA;
    private Double dssV;
    private Double dssN;
    private String drainFacilityType;
    private Double designStake;
    private String drainPosition;
    private String drainBuildDate;
    private String paveDamageType;
    private String slopePosition;
    private String highSteering;
    private String fillStatus;
    private String remark;
    private String fileId;
    private String lineId;
    private String lineCode; // 路线编码
    private String laneDirection; // 导入EXCEL表格——路线方向变量
    private String lineDirect;
    private String lineName; // 路线名称
    private String rlStake; // 路线名称
    private String rampId; // 匝道ID
    private String rampName; // 匝道name
    private String unitMargeId;//评定单元id
    private Integer year;//年份
    private String routeCode;
    private String routeversion;
    private Double sci;
    private Double weight;
    private Double length;
    private String lane;

    public String getDrainDataId() {
        return drainDataId;
    }
    public void setDrainDataId(String drainDataId) {
        this.drainDataId = drainDataId;
    }
    public String getPrjId() {
        return prjId;
    }
    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }
    public String getRpIntrvlId() {
        return rpIntrvlId;
    }
    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }
    public Double getStake() {
        return stake;
    }
    public void setStake(Double stake) {
        this.stake = stake;
    }
    public String getPavementType() {
        return pavementType;
    }
    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }
    public String getDrainDssType() {
        return drainDssType;
    }
    public void setDrainDssType(String drainDssType) {
        this.drainDssType = drainDssType;
    }
    public Double getDssL() {
        return dssL;
    }
    public void setDssL(Double dssL) {
        this.dssL = dssL;
    }
    public Double getDssW() {
        return dssW;
    }
    public void setDssW(Double dssW) {
        this.dssW = dssW;
    }
    public Double getDssD() {
        return dssD;
    }
    public void setDssD(Double dssD) {
        this.dssD = dssD;
    }
    public Double getDssA() {
        return dssA;
    }
    public void setDssA(Double dssA) {
        this.dssA = dssA;
    }
    public Double getDssV() {
        return dssV;
    }
    public void setDssV(Double dssV) {
        this.dssV = dssV;
    }
    public Double getDssN() {
        return dssN;
    }
    public void setDssN(Double dssN) {
        this.dssN = dssN;
    }
    public String getDrainFacilityType() {
        return drainFacilityType;
    }
    public void setDrainFacilityType(String drainFacilityType) {
        this.drainFacilityType = drainFacilityType;
    }
    public Double getDesignStake() {
        return designStake;
    }
    public void setDesignStake(Double designStake) {
        this.designStake = designStake;
    }
    public String getDrainPosition() {
        return drainPosition;
    }
    public void setDrainPosition(String drainPosition) {
        this.drainPosition = drainPosition;
    }
    public String getDrainBuildDate() {
        return drainBuildDate;
    }
    public void setDrainBuildDate(String drainBuildDate) {
        this.drainBuildDate = drainBuildDate;
    }
    public String getPaveDamageType() {
        return paveDamageType;
    }
    public void setPaveDamageType(String paveDamageType) {
        this.paveDamageType = paveDamageType;
    }
    public String getSlopePosition() {
        return slopePosition;
    }
    public void setSlopePosition(String slopePosition) {
        this.slopePosition = slopePosition;
    }
    public String getHighSteering() {
        return highSteering;
    }
    public void setHighSteering(String highSteering) {
        this.highSteering = highSteering;
    }
    public String getFillStatus() {
        return fillStatus;
    }
    public void setFillStatus(String fillStatus) {
        this.fillStatus = fillStatus;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getFileId() {
        return fileId;
    }
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }
    public String getLineId() {
        return lineId;
    }
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
    public String getLineCode() {
        return lineCode;
    }
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
    public String getLaneDirection() {
        return laneDirection;
    }
    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }
    public String getLineDirect() {
        return lineDirect;
    }
    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }
    public String getLineName() {
        return lineName;
    }
    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
    public String getRlStake() {
        return rlStake;
    }
    public void setRlStake(String rlStake) {
        this.rlStake = rlStake;
    }
    public String getRampId() {
        return rampId;
    }
    public void setRampId(String rampId) {
        this.rampId = rampId;
    }
    public String getRampName() {
        return rampName;
    }
    public void setRampName(String rampName) {
        this.rampName = rampName;
    }
    public String getUnitMargeId() {
        return unitMargeId;
    }
    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }

    public Double getSci() {
        return sci;
    }

    public void setSci(Double sci) {
        this.sci = sci;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }
    @Override
    public String toString() {
        return "DrainageData [drainDataId=" + drainDataId + ", prjId=" + prjId + ", rpIntrvlId=" + rpIntrvlId
                + ", stake=" + stake + ", pavementType=" + pavementType + ", drainDssType=" + drainDssType + ", dssL="
                + dssL + ", dssW=" + dssW + ", dssD=" + dssD + ", dssA=" + dssA + ", dssV=" + dssV + ", dssN=" + dssN
                + ", drainFacilityType=" + drainFacilityType + ", designStake=" + designStake + ", drainPosition="
                + drainPosition + ", drainBuildDate=" + drainBuildDate + ", paveDamageType=" + paveDamageType
                + ", slopePosition=" + slopePosition + ", highSteering=" + highSteering + ", fillStatus=" + fillStatus
                + ", remark=" + remark + ", fileId=" + fileId + ", lineId=" + lineId + ", lineCode=" + lineCode
                + ", laneDirection=" + laneDirection + ", lineDirect=" + lineDirect + ", lineName=" + lineName
                + ", rlStake=" + rlStake + ", rampId=" + rampId + ", rampName=" + rampName + ", unitMargeId="
                + unitMargeId + ", year=" + year + "]";
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }
}