package com.hualu.highwaymaintenance.decision.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @Title: 抗滑
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午9:55:25
 */
public class AntiSlipData implements Serializable,RangeData {

    private static final long serialVersionUID = 4763981554879860155L;

    private String antiSlipId; // 抗滑数据ID
    private String prjId; // 检测项目ID
    private String rpIntrvlId; // 营运路段区间ID
    private Double startStake; // 起点桩号
    private Double endStake; // 止点桩号
    private String lane; // 车道
    private String pavementType; // 路面类型
    private Double paveTemp; // 路表温度
    private Double testSpeed; // 测试速度
    private Double sfcS; // SFC原始值
    private Double sfcM; // SFC修正值---修正值
    private String remark; // 备注
    private String lineId; // 路线ID
    private String lineCode; // 路线编码
    private String laneDirection; // 路线方向
    private Double sri; // 评分
    private String sriTcCode; // 评分等级
    private String structIntrvlUcode; // 单元编码
    private Date prjStartDate; // 项目创建时间
    private String rlStartStake;
    private String rlEndStake;
    private String lineName;
    private String rampId; // 匝道ID
    private String rampName; // 匝道Name
    private String unitMargeId; //评定单元id
    private String hunMargeId;
    private Integer year; //年份
    private String routeCode;
    private String routeversion;

    public String getAntiSlipId() {
        return antiSlipId;
    }
    public void setAntiSlipId(String antiSlipId) {
        this.antiSlipId = antiSlipId;
    }
    public String getPrjId() {
        return prjId;
    }
    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }
    public String getRpIntrvlId() {
        return rpIntrvlId;
    }
    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }
    @Override
    public Double getStartStake() {
        return startStake;
    }
    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }
    @Override
    public Double getEndStake() {
        return endStake;
    }
    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }
    public String getLane() {
        return lane;
    }
    public void setLane(String lane) {
        this.lane = lane;
    }
    public String getPavementType() {
        return pavementType;
    }
    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }
    public Double getPaveTemp() {
        return paveTemp;
    }
    public void setPaveTemp(Double paveTemp) {
        this.paveTemp = paveTemp;
    }
    public Double getTestSpeed() {
        return testSpeed;
    }
    public void setTestSpeed(Double testSpeed) {
        this.testSpeed = testSpeed;
    }
    public Double getSfcS() {
        return sfcS;
    }
    public void setSfcS(Double sfcS) {
        this.sfcS = sfcS;
    }
    public Double getSfcM() {
        return sfcM;
    }
    public void setSfcM(Double sfcM) {
        this.sfcM = sfcM;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getLineId() {
        return lineId;
    }
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
    public String getLineCode() {
        return lineCode;
    }
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
    public String getLaneDirection() {
        return laneDirection;
    }
    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }
    public Double getSri() {
        return sri;
    }
    public void setSri(Double sri) {
        this.sri = sri;
    }
    public String getSriTcCode() {
        return sriTcCode;
    }
    public void setSriTcCode(String sriTcCode) {
        this.sriTcCode = sriTcCode;
    }
    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }
    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }
    public Date getPrjStartDate() {
        return prjStartDate;
    }
    public void setPrjStartDate(Date prjStartDate) {
        this.prjStartDate = prjStartDate;
    }
    public String getRlStartStake() {
        return rlStartStake;
    }
    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }
    public String getRlEndStake() {
        return rlEndStake;
    }
    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }
    public String getLineName() {
        return lineName;
    }
    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
    public String getRampId() {
        return rampId;
    }
    public void setRampId(String rampId) {
        this.rampId = rampId;
    }
    public String getRampName() {
        return rampName;
    }
    public void setRampName(String rampName) {
        this.rampName = rampName;
    }

    @Override
    public String getUnitMargeId() {
        return unitMargeId;
    }
    @Override
    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }



    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public String getHunMargeId() {
        return hunMargeId;
    }

    public void setHunMargeId(String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }

    @Override
    public String toString() {
        return "AntiSlipData{" +
                "antiSlipId='" + antiSlipId + '\'' +
                ", prjId='" + prjId + '\'' +
                ", rpIntrvlId='" + rpIntrvlId + '\'' +
                ", startStake=" + startStake +
                ", endStake=" + endStake +
                ", lane='" + lane + '\'' +
                ", pavementType='" + pavementType + '\'' +
                ", paveTemp=" + paveTemp +
                ", testSpeed=" + testSpeed +
                ", sfcS=" + sfcS +
                ", sfcM=" + sfcM +
                ", remark='" + remark + '\'' +
                ", lineId='" + lineId + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", laneDirection='" + laneDirection + '\'' +
                ", sri=" + sri +
                ", sriTcCode='" + sriTcCode + '\'' +
                ", structIntrvlUcode='" + structIntrvlUcode + '\'' +
                ", prjStartDate=" + prjStartDate +
                ", rlStartStake='" + rlStartStake + '\'' +
                ", rlEndStake='" + rlEndStake + '\'' +
                ", lineName='" + lineName + '\'' +
                ", rampId='" + rampId + '\'' +
                ", rampName='" + rampName + '\'' +
                ", unitMargeId='" + unitMargeId + '\'' +
                ", hunMargeId='" + hunMargeId + '\'' +
                ", year=" + year +
                ", routeCode='" + routeCode + '\'' +
                ", routeversion='" + routeversion + '\'' +
                '}';
    }
}