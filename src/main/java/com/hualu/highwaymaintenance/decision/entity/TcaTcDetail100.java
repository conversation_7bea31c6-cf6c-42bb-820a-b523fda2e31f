package com.hualu.highwaymaintenance.decision.entity;
import java.io.Serializable;
import java.util.Date;

public class TcaTcDetail100 implements Serializable {

    private String tcDetailId; //
    private String prjId; //
    private String rpIntrvlId; // 营运路段区间ID
    private String structIntrvlUid; // 结构区间单元
    private Integer length;
    private Double startStake;
    private Double endStake;
    private String techGrade;
    private String orgId;
    private String lane;
    private Double pqi;
    private Double pciFront;
    private Double pciBehind;
    private Double rqi;
    private Double rdi;
    private Double sri;
    private Double pssi;
    private Double pwi;
    private Double pbi;
    private String pqiTcCode;
    private String pciFrontTcCode;
    private String pciBehindTcCode;
    private String rqiTcCode;
    private String rdiTcCode;
    private String sriTcCode;
    private String pssiTcCode;
    private String pbiTcCode;
    private String pwiTcCode;
    private Date createTime;

    private String structIntrvlUcode;
    private String unitMargeId;
    private String hunMargeId;
    private String lineId;

    private String rlStartStake;
    private String rlEndStake;
    private String lineName;
    private String mainlineName;

    private String longness;
    private String pavementType;
    // 拼接后技术指标
    private String rqiStr;
    private String pciStr;
    private String rdiStr;
    private String sriStr;
    private String laneStr;

    private Double drFront;

    private Double drBehind;

    private Double iri;

    private Double rd;

    private Double wr;

    private Double sfc;

    private Double ssr;

    private Integer year;

    private Double pr;

    private String tcs;

    private Double lcd;

    private String sturtName;

    public TcaTcDetail100(String prjId, String hunMargeId, Integer year) {
        this.prjId = prjId;
        this.hunMargeId = hunMargeId;
        this.year = year;
    }

    public Double getPr() {
        return pr;
    }

    public void setPr(Double pr) {
        this.pr = pr;
    }

    public String getTcs() {
        return tcs;
    }

    public void setTcs(String tcs) {
        this.tcs = tcs;
    }

    public TcaTcDetail100() {
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getTcDetailId() {
        return tcDetailId;
    }

    public void setTcDetailId(String tcDetailId) {
        this.tcDetailId = tcDetailId;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }

    public String getRpIntrvlId() {
        return rpIntrvlId;
    }

    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public String getStructIntrvlUid() {
        return structIntrvlUid;
    }

    public void setStructIntrvlUid(String structIntrvlUid) {
        this.structIntrvlUid = structIntrvlUid;
    }

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Double getStartStake() {
        return startStake;
    }

    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return endStake;
    }

    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }

    public Double getPqi() {
        return pqi;
    }

    public void setPqi(Double pqi) {
        this.pqi = pqi;
    }

    public Double getRqi() {
        return rqi;
    }

    public void setRqi(Double rqi) {
        this.rqi = rqi;
    }

    public Double getRdi() {
        return rdi;
    }

    public void setRdi(Double rdi) {
        this.rdi = rdi;
    }

    public Double getSri() {
        return sri;
    }

    public void setSri(Double sri) {
        this.sri = sri;
    }

    public Double getPssi() {
        return pssi;
    }

    public void setPssi(Double pssi) {
        this.pssi = pssi;
    }

    public String getPqiTcCode() {
        return pqiTcCode;
    }

    public void setPqiTcCode(String pqiTcCode) {
        this.pqiTcCode = pqiTcCode;
    }

    public String getRqiTcCode() {
        return rqiTcCode;
    }

    public void setRqiTcCode(String rqiTcCode) {
        this.rqiTcCode = rqiTcCode;
    }

    public String getRdiTcCode() {
        return rdiTcCode;
    }

    public void setRdiTcCode(String rdiTcCode) {
        this.rdiTcCode = rdiTcCode;
    }

    public String getSriTcCode() {
        return sriTcCode;
    }

    public void setSriTcCode(String sriTcCode) {
        this.sriTcCode = sriTcCode;
    }

    public String getPssiTcCode() {
        return pssiTcCode;
    }

    public void setPssiTcCode(String pssiTcCode) {
        this.pssiTcCode = pssiTcCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }

    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getRlStartStake() {
        return rlStartStake;
    }

    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }

    public String getRlEndStake() {
        return rlEndStake;
    }

    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getMainlineName() {
        return mainlineName;
    }

    public void setMainlineName(String mainlineName) {
        this.mainlineName = mainlineName;
    }

    public String getLongness() {
        return longness;
    }

    public void setLongness(String longness) {
        this.longness = longness;
    }

    public String getPavementType() {
        return pavementType;
    }

    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }

    public String getRqiStr() {
        return rqiStr;
    }

    public void setRqiStr(String rqiStr) {
        this.rqiStr = rqiStr;
    }

    public String getPciStr() {
        return pciStr;
    }

    public void setPciStr(String pciStr) {
        this.pciStr = pciStr;
    }

    public String getRdiStr() {
        return rdiStr;
    }

    public void setRdiStr(String rdiStr) {
        this.rdiStr = rdiStr;
    }

    public String getSriStr() {
        return sriStr;
    }

    public void setSriStr(String sriStr) {
        this.sriStr = sriStr;
    }

    public String getLaneStr() {
        return laneStr;
    }

    public void setLaneStr(String laneStr) {
        this.laneStr = laneStr;
    }

    public Double getIri() {
        return iri;
    }

    public void setIri(Double iri) {
        this.iri = iri;
    }

    public Double getRd() {
        return rd;
    }

    public void setRd(Double rd) {
        this.rd = rd;
    }

    public Double getWr() {
        return wr;
    }

    public void setWr(Double wr) {
        this.wr = wr;
    }

    public Double getSfc() {
        return sfc;
    }

    public void setSfc(Double sfc) {
        this.sfc = sfc;
    }

    public Double getSsr() {
        return ssr;
    }

    public void setSsr(Double ssr) {
        this.ssr = ssr;
    }


    public String getUnitMargeId() {
        return unitMargeId;
    }

    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }

    public String getHunMargeId() {
        return hunMargeId;
    }

    public void setHunMargeId(String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }

    public Double getPwi() {
        return pwi;
    }

    public void setPwi(Double pwi) {
        this.pwi = pwi;
    }

    public Double getPbi() {
        return pbi;
    }

    public void setPbi(Double pbi) {
        this.pbi = pbi;
    }

    public String getPwiTcCode() {
        return pwiTcCode;
    }

    public void setPwiTcCode(String pwiTcCode) {
        this.pwiTcCode = pwiTcCode;
    }

    public String getPbiTcCode() {
        return pbiTcCode;
    }

    public void setPbiTcCode(String pbiTcCode) {
        this.pbiTcCode = pbiTcCode;
    }

    public Double getPciFront() {
        return pciFront;
    }

    public void setPciFront(Double pciFront) {
        this.pciFront = pciFront;
    }

    public Double getPciBehind() {
        return pciBehind;
    }

    public void setPciBehind(Double pciBehind) {
        this.pciBehind = pciBehind;
    }

    public String getPciFrontTcCode() {
        return pciFrontTcCode;
    }

    public void setPciFrontTcCode(String pciFrontTcCode) {
        this.pciFrontTcCode = pciFrontTcCode;
    }

    public String getPciBehindTcCode() {
        return pciBehindTcCode;
    }

    public void setPciBehindTcCode(String pciBehindTcCode) {
        this.pciBehindTcCode = pciBehindTcCode;
    }

    public Double getDrFront() {
        return drFront;
    }

    public void setDrFront(Double drFront) {
        this.drFront = drFront;
    }

    public Double getDrBehind() {
        return drBehind;
    }

    public void setDrBehind(Double drBehind) {
        this.drBehind = drBehind;
    }

    public Double getLcd() {
        return lcd;
    }

    public void setLcd(Double lcd) {
        this.lcd = lcd;
    }

    public String getSturtName() {
        return sturtName;
    }

    public void setSturtName(String sturtName) {
        this.sturtName = sturtName;
    }

    public String getTechGrade() {
        return techGrade;
    }

    public void setTechGrade(String techGrade) {
        this.techGrade = techGrade;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}
