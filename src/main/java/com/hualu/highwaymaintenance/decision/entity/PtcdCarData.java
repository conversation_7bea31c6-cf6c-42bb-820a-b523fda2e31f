package com.hualu.highwaymaintenance.decision.entity;

import java.io.Serializable;
import java.util.Arrays;

/**
 *
 * @Title: 跳车
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午9:55:36
 */
public class PtcdCarData implements Serializable {

    private static final long serialVersionUID = 834165083899525189L;
    // 跳车指数数据ID
    private String pbiId;
    // 检测项目ID
    private String prjId;
    // 营运路段物理区间ID
    private String rpIntrvlId;
    // 起点桩号
    private Double startStake;
    // 止点桩号
    private Double endStake;
    // 车道类型
    private String lane;
    // 路面类型
    private String pavementType;
    // 备注
    private String remark;
    // 匝道ID
    private String rampId;
    // 第几程度的路面跳车
    private String pb;
    // 单位扣分
    private Double pbAi;
    // 跳车类型
    private Double pbiType;
    // 跳车类型总数
    private Double pbiCount;
    private Double length;
    // 路面纵断面高差(cm)
    private Double pbiHight; //最大跳车

    private Double pbHightRight; //左跳车
    private Double pbHightLeft; //右跳车
    // 评定单元id
    private String unitMargeId;//评定单元id
    private String hunMargeId;
    private Integer year;//年份
    private String rlStartStake;
    private String rlEndStake;
    private String lineId; // 路线ID
    private String lineCode; // 路线编码
    private String lineName; // 路线编码
    private String laneDirection; // 路线方向
    private Double PBI; // 评分
    private String PBITcCode; // 评分等级
    private String structIntrvlUcode; // 单元编码
    private String rampName; // 匝道name

    private String routeCode;

    private String routeversion;

    public String getPbiId() {
        return pbiId;
    }

    public void setPbiId(String pbiId) {
        this.pbiId = pbiId;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }

    public String getRpIntrvlId() {
        return rpIntrvlId;
    }

    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public Double getStartStake() {
        return startStake;
    }

    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return endStake;
    }

    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }

    public String getPavementType() {
        return pavementType;
    }

    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRampId() {
        return rampId;
    }

    public void setRampId(String rampId) {
        this.rampId = rampId;
    }

    public String getPb() {
        return pb;
    }

    public void setPb(String pb) {
        this.pb = pb;
    }

    public Double getPbAi() {
        return pbAi;
    }

    public void setPbAi(Double pbAi) {
        this.pbAi = pbAi;
    }

    public Double getPbiType() {
        return pbiType;
    }

    public void setPbiType(Double pbiType) {
        this.pbiType = pbiType;
    }

    public Double getPbiCount() {
        return pbiCount;
    }

    public void setPbiCount(Double pbiCount) {
        this.pbiCount = pbiCount;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Double getPbiHight() {
        return pbiHight;
    }

    public void setPbiHight(Double pbiHight) {
        this.pbiHight = pbiHight;
    }

    public String getRlStartStake() {
        return rlStartStake;
    }

    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }

    public String getRlEndStake() {
        return rlEndStake;
    }

    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLaneDirection() {
        return laneDirection;
    }

    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }

    public Double getPBI() {
        return PBI;
    }

    public void setPBI(Double pBI) {
        PBI = pBI;
    }

    public String getPBITcCode() {
        return PBITcCode;
    }

    public void setPBITcCode(String pBITcCode) {
        PBITcCode = pBITcCode;
    }

    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }

    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }

    public String getRampName() {
        return rampName;
    }

    public void setRampName(String rampName) {
        this.rampName = rampName;
    }

    public String getUnitMargeId() {
        return unitMargeId;
    }

    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }


    public String getHunMargeId() {
        return hunMargeId;
    }

    public void setHunMargeId(String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }

    public Double getPbHightRight() {
        return pbHightRight;
    }

    public void setPbHightRight(Double pbHightRight) {
        this.pbHightRight = pbHightRight;
    }

    public Double getPbHightLeft() {
        return pbHightLeft;
    }

    public void setPbHightLeft(Double pbHightLeft) {
        this.pbHightLeft = pbHightLeft;
    }

    @Override
    public String toString() {
        return "PtcdCarData [pbiId=" + pbiId + ", prjId=" + prjId + ", rpIntrvlId=" + rpIntrvlId + ", startStake="
                + startStake + ", endStake=" + endStake + ", lane=" + lane + ", pavementType=" + pavementType
                + ", remark=" + remark + ", rampId=" + rampId + ", pb=" + pb + ", pbAi=" + pbAi + ", pbiType=" + pbiType
                + ", pbiCount=" + pbiCount + ", length=" + length + ", pbiHight=" + pbiHight + ", unitMargeId="
                + unitMargeId + ", year=" + year + ", rlStartStake=" + rlStartStake + ", rlEndStake=" + rlEndStake
                + ", lineId=" + lineId + ", lineCode=" + lineCode + ", lineName=" + lineName + ", laneDirection="
                + laneDirection + ", PBI=" + PBI + ", PBITcCode=" + PBITcCode + ", structIntrvlUcode="
                + structIntrvlUcode + ", rampName=" + rampName + "]";
    }
}
