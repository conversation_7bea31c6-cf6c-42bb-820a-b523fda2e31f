package com.hualu.highwaymaintenance.decision.entity;
/**
 *
 * @Title: 弯沉
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午11:34:40
 */
public class DeflectionData implements java.io.Serializable {

    private static final long serialVersionUID = 3854646392278101180L;

    private String deflectionId; // 弯沉ID
    private String prjId; // 检测项目ID
    private String rpIntrvlId; // 营运路段区间ID
    private Double stake; // 桩号
    private String lane; // 车道
    private String pavementType; // 路面类型
    private Double paveTemp; // 路表温度
    private Double backmanS; // 原贝克曼梁测值
    private Double backmanM; // 修正后的贝克曼梁测值---修正值
    private Double standard;
    private String remark; // 备注
    private String testMethod; // 测试方法
    private String hunMargeId;
    private String lineId;
    private String lineCode; // 路线编码
    private String laneDirection; // 导入EXCEL表格——路线方向变量
    private String lineDirect;
    private Double pssi; // 评分
    private String pssiTcCode; // 评分等级
    private String structIntrvlUcode; // 单元编码
    private String rampId; // 匝道ID
    private String rampName; // 匝道name
    private String rlStake;
    private String lineName;
    private String unitMargeId;//评定单元id
    private Integer year;//年份
    private Double ssr;
    private String routeCode;
    private String routeversion;

    public String getDeflectionId() {
        return deflectionId;
    }
    public void setDeflectionId(String deflectionId) {
        this.deflectionId = deflectionId;
    }
    public String getPrjId() {
        return prjId;
    }
    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }
    public String getRpIntrvlId() {
        return rpIntrvlId;
    }
    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }
    public Double getStake() {
        return stake;
    }
    public void setStake(Double stake) {
        this.stake = stake;
    }
    public String getLane() {
        return lane;
    }
    public void setLane(String lane) {
        this.lane = lane;
    }
    public String getPavementType() {
        return pavementType;
    }
    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }
    public Double getPaveTemp() {
        return paveTemp;
    }
    public void setPaveTemp(Double paveTemp) {
        this.paveTemp = paveTemp;
    }
    public Double getBackmanS() {
        return backmanS;
    }
    public void setBackmanS(Double backmanS) {
        this.backmanS = backmanS;
    }
    public Double getBackmanM() {
        return backmanM;
    }
    public void setBackmanM(Double backmanM) {
        this.backmanM = backmanM;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getTestMethod() {
        return testMethod;
    }
    public void setTestMethod(String testMethod) {
        this.testMethod = testMethod;
    }
    public String getLineId() {
        return lineId;
    }
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
    public String getLineCode() {
        return lineCode;
    }
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
    public String getLaneDirection() {
        return laneDirection;
    }
    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }
    public String getLineDirect() {
        return lineDirect;
    }
    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }
    public Double getPssi() {
        return pssi;
    }
    public void setPssi(Double pssi) {
        this.pssi = pssi;
    }
    public String getPssiTcCode() {
        return pssiTcCode;
    }
    public void setPssiTcCode(String pssiTcCode) {
        this.pssiTcCode = pssiTcCode;
    }
    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }
    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }
    public String getRampId() {
        return rampId;
    }
    public void setRampId(String rampId) {
        this.rampId = rampId;
    }
    public String getRampName() {
        return rampName;
    }
    public void setRampName(String rampName) {
        this.rampName = rampName;
    }
    public String getRlStake() {
        return rlStake;
    }
    public void setRlStake(String rlStake) {
        this.rlStake = rlStake;
    }
    public String getLineName() {
        return lineName;
    }
    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
    public String getUnitMargeId() {
        return unitMargeId;
    }
    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }

    public Double getSsr() {
        return ssr;
    }

    public void setSsr(Double ssr) {
        this.ssr = ssr;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }

    public String getHunMargeId() {
        return hunMargeId;
    }

    public void setHunMargeId(String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }

    public Double getStandard() {
        return standard;
    }

    public void setStandard(Double standard) {
        this.standard = standard;
    }

    @Override
    public String toString() {
        return "DeflectionData{" +
                "deflectionId='" + deflectionId + '\'' +
                ", prjId='" + prjId + '\'' +
                ", rpIntrvlId='" + rpIntrvlId + '\'' +
                ", stake=" + stake +
                ", lane='" + lane + '\'' +
                ", pavementType='" + pavementType + '\'' +
                ", paveTemp=" + paveTemp +
                ", backmanS=" + backmanS +
                ", backmanM=" + backmanM +
                ", standard='" + standard + '\'' +
                ", remark='" + remark + '\'' +
                ", testMethod='" + testMethod + '\'' +
                ", hunMargeId='" + hunMargeId + '\'' +
                ", lineId='" + lineId + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", laneDirection='" + laneDirection + '\'' +
                ", lineDirect='" + lineDirect + '\'' +
                ", pssi=" + pssi +
                ", pssiTcCode='" + pssiTcCode + '\'' +
                ", structIntrvlUcode='" + structIntrvlUcode + '\'' +
                ", rampId='" + rampId + '\'' +
                ", rampName='" + rampName + '\'' +
                ", rlStake='" + rlStake + '\'' +
                ", lineName='" + lineName + '\'' +
                ", unitMargeId='" + unitMargeId + '\'' +
                ", year=" + year +
                ", ssr=" + ssr +
                ", routeCode='" + routeCode + '\'' +
                ", routeversion='" + routeversion + '\'' +
                '}';
    }
}