package com.hualu.highwaymaintenance.decision.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.math.BigDecimal;

/**
 * 重点处理路段
 */
public class PbdMainPointPavement {

    private String id;
    @Excel(name = "路线编码")
    private String lineCode;
    @Excel(name = "起点桩号")
    private BigDecimal startStake;
    @Excel(name = "止点桩号")
    private BigDecimal endStake;
    @Excel(name = "路段类型")
    private String typeName;
    @Excel(name = "管养单位")
    private String orgId;
    @Excel(name = "备注")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public BigDecimal getStartStake() {
        return startStake;
    }

    public void setStartStake(BigDecimal startStake) {
        this.startStake = startStake;
    }

    public BigDecimal getEndStake() {
        return endStake;
    }

    public void setEndStake(BigDecimal endStake) {
        this.endStake = endStake;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    @Override
    public String toString() {
        return "PbdMainPointPavement{" +
                "id='" + id + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", startStake=" + startStake +
                ", endStake=" + endStake +
                ", typeName='" + typeName + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
