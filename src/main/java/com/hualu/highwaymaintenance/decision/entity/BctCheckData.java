package com.hualu.highwaymaintenance.decision.entity;
/**
 *
 * @Title: 桥隧
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午11:47:02
 */
public class BctCheckData implements java.io.Serializable {

    private static final long serialVersionUID = 3068269455980674147L;

    private String brgClvtTnnlId;
    private String prjId;
    private String rpIntrvlId;
    private Double stake;
    private String facilityCat;
    private String structId;
    private String tcCode;
    private String structAllName;
    private String lineId;
    private String lineCode;//路线编码
    private String laneDirection;//路线方向
    private String lineDirect;
    private Double rlStartStake;
    private String rlStake;
    private String rampId;//匝道ID
    private String rampName;//匝道Name
    private String unitMargeId;//评定单元id
    private Integer year;//年份
    private String lv1;
    private String lv2;
    private String lv3;
    private String lv4;
    private String lv5;
    private String lv6;
    private String lv7;
    private String lv8;
    private String lv9;
    private String lv10;
    private String lv11;
    private String lv12;
    private String lv13;
    private String lv14;
    private String lv15;
    private String routeCode;
    private String routeversion;
    private String StructCount;
    private String length;

    public String getBrgClvtTnnlId() {
        return brgClvtTnnlId;
    }
    public void setBrgClvtTnnlId(String brgClvtTnnlId) {
        this.brgClvtTnnlId = brgClvtTnnlId;
    }
    public String getPrjId() {
        return prjId;
    }
    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }
    public String getRpIntrvlId() {
        return rpIntrvlId;
    }
    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }
    public Double getStake() {
        return stake;
    }
    public void setStake(Double stake) {
        this.stake = stake;
    }
    public String getFacilityCat() {
        return facilityCat;
    }
    public void setFacilityCat(String facilityCat) {
        this.facilityCat = facilityCat;
    }
    public String getStructId() {
        return structId;
    }
    public void setStructId(String structId) {
        this.structId = structId;
    }
    public String getTcCode() {
        return tcCode;
    }
    public void setTcCode(String tcCode) {
        this.tcCode = tcCode;
    }
    public String getStructAllName() {
        return structAllName;
    }
    public void setStructAllName(String structAllName) {
        this.structAllName = structAllName;
    }
    public String getLineId() {
        return lineId;
    }
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
    public String getLineCode() {
        return lineCode;
    }
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
    public String getLaneDirection() {
        return laneDirection;
    }
    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }
    public String getLineDirect() {
        return lineDirect;
    }
    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }
    public Double getRlStartStake() {
        return rlStartStake;
    }
    public void setRlStartStake(Double rlStartStake) {
        this.rlStartStake = rlStartStake;
    }
    public String getRlStake() {
        return rlStake;
    }
    public void setRlStake(String rlStake) {
        this.rlStake = rlStake;
    }
    public String getRampId() {
        return rampId;
    }
    public void setRampId(String rampId) {
        this.rampId = rampId;
    }
    public String getRampName() {
        return rampName;
    }
    public void setRampName(String rampName) {
        this.rampName = rampName;
    }

    public String getUnitMargeId() {
        return unitMargeId;
    }
    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }
    public String getLv1() {
        return lv1;
    }
    public void setLv1(String lv1) {
        this.lv1 = lv1;
    }
    public String getLv2() {
        return lv2;
    }
    public void setLv2(String lv2) {
        this.lv2 = lv2;
    }
    public String getLv3() {
        return lv3;
    }
    public void setLv3(String lv3) {
        this.lv3 = lv3;
    }
    public String getLv4() {
        return lv4;
    }
    public void setLv4(String lv4) {
        this.lv4 = lv4;
    }
    public String getLv5() {
        return lv5;
    }
    public void setLv5(String lv5) {
        this.lv5 = lv5;
    }
    public String getLv6() {
        return lv6;
    }
    public void setLv6(String lv6) {
        this.lv6 = lv6;
    }
    public String getLv7() {
        return lv7;
    }
    public void setLv7(String lv7) {
        this.lv7 = lv7;
    }
    public String getLv8() {
        return lv8;
    }
    public void setLv8(String lv8) {
        this.lv8 = lv8;
    }
    public String getLv9() {
        return lv9;
    }
    public void setLv9(String lv9) {
        this.lv9 = lv9;
    }
    public String getLv10() {
        return lv10;
    }
    public void setLv10(String lv10) {
        this.lv10 = lv10;
    }
    public String getLv11() {
        return lv11;
    }
    public void setLv11(String lv11) {
        this.lv11 = lv11;
    }
    public String getLv12() {
        return lv12;
    }
    public void setLv12(String lv12) {
        this.lv12 = lv12;
    }
    public String getLv13() {
        return lv13;
    }
    public void setLv13(String lv13) {
        this.lv13 = lv13;
    }
    public String getLv14() {
        return lv14;
    }
    public void setLv14(String lv14) {
        this.lv14 = lv14;
    }
    public String getLv15() {
        return lv15;
    }
    public void setLv15(String lv15) {
        this.lv15 = lv15;
    }
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }
    @Override
    public String toString() {
        return "BctCheckData [brgClvtTnnlId=" + brgClvtTnnlId + ", prjId=" + prjId + ", rpIntrvlId=" + rpIntrvlId
                + ", stake=" + stake + ", facilityCat=" + facilityCat + ", structId=" + structId + ", tcCode=" + tcCode
                + ", structAllName=" + structAllName + ", lineId=" + lineId + ", lineCode=" + lineCode
                + ", laneDirection=" + laneDirection + ", lineDirect=" + lineDirect + ", rlStartStake=" + rlStartStake
                + ", rlStake=" + rlStake + ", rampId=" + rampId + ", rampName=" + rampName + ", unitMargeId="
                + unitMargeId + ", year=" + year + ", lv1=" + lv1 + ", lv2=" + lv2 + ", lv3=" + lv3 + ", lv4=" + lv4
                + ", lv5=" + lv5 + ", lv6=" + lv6 + ", lv7=" + lv7 + ", lv8=" + lv8 + ", lv9=" + lv9 + ", lv10=" + lv10
                + ", lv11=" + lv11 + ", lv12=" + lv12 + ", lv13=" + lv13 + ", lv14=" + lv14 + ", lv15=" + lv15 + "]";
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }

    public String getStructCount() {
        return StructCount;
    }

    public void setStructCount(String structCount) {
        StructCount = structCount;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }
}