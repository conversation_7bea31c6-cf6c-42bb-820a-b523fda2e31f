package com.hualu.highwaymaintenance.decision.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

public class PbdStreamUnit implements Serializable, Cloneable, PbdStructParent {
    private static final long serialVersionUID = 7326070812510173753L;

    private String structIntrvlUid;			//结构区间单元ID

    private String structIntrvlUcode;		//结构区间单元编码

    private String structIntrvlId;			//结构区间ID

    private String rpIntrvlId;				//营运路段区间ID

    private String org_unit_id;

    private String techGrade;				//公路技术等级

    private String pavementType;			//路面类型

    private String pavementStructId; 	  	//路面结构ID

    private Double designLd;             	//设计弯沉

    private Double effectWidth;				//有效路面宽度

    private Integer year;					//年度
    //@Excel(name = "Start")
    private Double startStake;				//起点桩号
    //@Excel(name = "End")
    private Double endStake;				//止点桩号
    //@Excel(name = "单元长度")
    private Long length;					//长度（米）

    private Double startX;					//起点坐标X

    private Double startY;					//起点坐标Y

    private Double endX;					//止点坐标X

    private Double endY;					//止点坐标Y

    private String usered;					//是否正在使用的评定单元1，历史评定单元 0，正在使用的评定单元

    private String structIntrvlCode;		//结构区间编码		---
    @Excel(name = "起点桩号")
    private String rlStartStake;
    @Excel(name = "终点桩号")
    private String rlEndStake;

    private String dstrctCode;				//所属行政区域

    private String laneType;				//车道类型

    private String pavementStruct;			//路面类型（导出模板）
    @Excel(name = "路线编码")
    private String lineCode;				//路线编码（导出模板）
    @Excel(name = "桥隧构造物")
    private String strutName;

    private String lineName;

    private String lineId;

    private String lineDirectName;			//方向

    private String lineDirect;

    private Integer index ;					//弯沉的测点标记

    private String lane;					//车道

    private String intrvlMargeId;

    private String unitMargeId;

    private String prjOrgCode;//资产所属单位
    @Excel(name = "三级单位")
    private String oprtOrgCode;//管养单位

    private Integer version; //版本号

    private String intrvlId;//外键

    private Date create_date_unit;

    private Integer type;

    private String routeCode;

    private String routeVersion;

    private String remark;
    @Excel(name = "路面养护历史")
    private String measureName;

    private String mergeName;

    private String typeName;
    @Excel(name = "养护措施名称")
    private String measureNameAll;
    @Excel(name = "PCI")
    private BigDecimal pci;
    @Excel(name = "RDI")
    private BigDecimal RDI;
    @Excel(name = "RQI")
    private BigDecimal RQI;
    @Excel(name = "SRI")
    private BigDecimal SRI;
    @Excel(name = "PBI")
    private BigDecimal PBI;
    @Excel(name = "PSSI")
    private BigDecimal PSSI;
    @Excel(name = "PR")
    private BigDecimal PR;
    @Excel(name = "TCS")
    private BigDecimal TCS;
    @Excel(name = "LCD")
    private BigDecimal LCD;

    public PbdStreamUnit(Double startStake, Double endStake) {
        this.startStake = startStake;
        this.endStake = endStake;
    }

    public PbdStreamUnit() {
    }

    public PbdStreamUnit(Double startStake) {
        this.startStake = startStake;
    }

    public String getStructIntrvlUid() {
        return structIntrvlUid;
    }

    public void setStructIntrvlUid(String structIntrvlUid) {
        this.structIntrvlUid = structIntrvlUid;
    }

    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }

    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }

    public String getStructIntrvlId() {
        return structIntrvlId;
    }

    public void setStructIntrvlId(String structIntrvlId) {
        this.structIntrvlId = structIntrvlId;
    }

    public String getRpIntrvlId() {
        return rpIntrvlId;
    }

    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public String getOrg_unit_id() {
        return org_unit_id;
    }

    public void setOrg_unit_id(String org_unit_id) {
        this.org_unit_id = org_unit_id;
    }

    public String getTechGrade() {
        return techGrade;
    }

    public void setTechGrade(String techGrade) {
        this.techGrade = techGrade;
    }

    public String getPavementType() {
        return pavementType;
    }

    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }

    public String getPavementStructId() {
        return pavementStructId;
    }

    public void setPavementStructId(String pavementStructId) {
        this.pavementStructId = pavementStructId;
    }

    public Double getDesignLd() {
        return designLd;
    }

    public void setDesignLd(Double designLd) {
        this.designLd = designLd;
    }

    public Double getEffectWidth() {
        return effectWidth;
    }

    public void setEffectWidth(Double effectWidth) {
        this.effectWidth = effectWidth;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Double getStartStake() {
        return startStake;
    }

    @Override
    public String getHunMargeId() {
        return null;
    }

    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return endStake;
    }

    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }

    public Long getLength() {
        return length;
    }

    public void setLength(Long length) {
        this.length = length;
    }

    public Double getStartX() {
        return startX;
    }

    public void setStartX(Double startX) {
        this.startX = startX;
    }

    public Double getStartY() {
        return startY;
    }

    public void setStartY(Double startY) {
        this.startY = startY;
    }

    public Double getEndX() {
        return endX;
    }

    public void setEndX(Double endX) {
        this.endX = endX;
    }

    public Double getEndY() {
        return endY;
    }

    public void setEndY(Double endY) {
        this.endY = endY;
    }

    public String getUsered() {
        return usered;
    }

    public void setUsered(String usered) {
        this.usered = usered;
    }

    public String getStructIntrvlCode() {
        return structIntrvlCode;
    }

    public void setStructIntrvlCode(String structIntrvlCode) {
        this.structIntrvlCode = structIntrvlCode;
    }

    public String getRlStartStake() {
        return rlStartStake;
    }

    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }

    public String getRlEndStake() {
        return rlEndStake;
    }

    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }

    public String getDstrctCode() {
        return dstrctCode;
    }

    public void setDstrctCode(String dstrctCode) {
        this.dstrctCode = dstrctCode;
    }

    public String getLaneType() {
        return laneType;
    }

    public void setLaneType(String laneType) {
        this.laneType = laneType;
    }

    public String getPavementStruct() {
        return pavementStruct;
    }

    public void setPavementStruct(String pavementStruct) {
        this.pavementStruct = pavementStruct;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineDirectName() {
        return lineDirectName;
    }

    public void setLineDirectName(String lineDirectName) {
        this.lineDirectName = lineDirectName;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }

    public String getIntrvlMargeId() {
        return intrvlMargeId;
    }

    public void setIntrvlMargeId(String intrvlMargeId) {
        this.intrvlMargeId = intrvlMargeId;
    }

    public String getUnitMargeId() {
        return unitMargeId;
    }

    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }

    public String getPrjOrgCode() {
        return prjOrgCode;
    }

    public void setPrjOrgCode(String prjOrgCode) {
        this.prjOrgCode = prjOrgCode;
    }

    public String getOprtOrgCode() {
        return oprtOrgCode;
    }

    public void setOprtOrgCode(String oprtOrgCode) {
        this.oprtOrgCode = oprtOrgCode;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getIntrvlId() {
        return intrvlId;
    }

    public void setIntrvlId(String intrvlId) {
        this.intrvlId = intrvlId;
    }

    public Date getCreate_date_unit() {
        return create_date_unit;
    }

    public void setCreate_date_unit(Date create_date_unit) {
        this.create_date_unit = create_date_unit;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteVersion() {
        return routeVersion;
    }

    public void setRouteVersion(String routeVersion) {
        this.routeVersion = routeVersion;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStrutName() {
        return strutName;
    }

    public void setStrutName(String strutName) {
        this.strutName = strutName;
    }

    public String getMeasureName() {
        return measureName;
    }

    public void setMeasureName(String measureName) {
        this.measureName = measureName;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        PbdStreamUnit o = null;
        try{
            o = (PbdStreamUnit)super.clone();
        }catch(CloneNotSupportedException e){
            e.printStackTrace();
        }

        return o;
    }

    public String getMergeName() {
        return mergeName;
    }

    public void setMergeName(String mergeName) {
        this.mergeName = mergeName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public BigDecimal getPci() {
        return pci;
    }

    public void setPci(BigDecimal pci) {
        this.pci = pci;
    }

    public BigDecimal getRDI() {
        return RDI;
    }

    public void setRDI(BigDecimal RDI) {
        this.RDI = RDI;
    }

    public BigDecimal getRQI() {
        return RQI;
    }

    public void setRQI(BigDecimal RQI) {
        this.RQI = RQI;
    }

    public BigDecimal getSRI() {
        return SRI;
    }

    public void setSRI(BigDecimal SRI) {
        this.SRI = SRI;
    }

    public BigDecimal getPBI() {
        return PBI;
    }

    public void setPBI(BigDecimal PBI) {
        this.PBI = PBI;
    }

    public BigDecimal getPSSI() {
        return PSSI;
    }

    public void setPSSI(BigDecimal PSSI) {
        this.PSSI = PSSI;
    }

    public BigDecimal getPR() {
        return PR;
    }

    public void setPR(BigDecimal PR) {
        this.PR = PR;
    }

    public BigDecimal getTCS() {
        return TCS;
    }

    public void setTCS(BigDecimal TCS) {
        this.TCS = TCS;
    }

    public BigDecimal getLCD() {
        return LCD;
    }

    public void setLCD(BigDecimal LCD) {
        this.LCD = LCD;
    }

    @Override
    public String toString() {
        return "PbdStreamUnit{" +
                "structIntrvlUid='" + structIntrvlUid + '\'' +
                ", structIntrvlUcode='" + structIntrvlUcode + '\'' +
                ", structIntrvlId='" + structIntrvlId + '\'' +
                ", rpIntrvlId='" + rpIntrvlId + '\'' +
                ", org_unit_id='" + org_unit_id + '\'' +
                ", techGrade='" + techGrade + '\'' +
                ", pavementType='" + pavementType + '\'' +
                ", pavementStructId='" + pavementStructId + '\'' +
                ", designLd=" + designLd +
                ", effectWidth=" + effectWidth +
                ", year=" + year +
                ", startStake=" + startStake +
                ", endStake=" + endStake +
                ", length=" + length +
                ", startX=" + startX +
                ", startY=" + startY +
                ", endX=" + endX +
                ", endY=" + endY +
                ", usered='" + usered + '\'' +
                ", structIntrvlCode='" + structIntrvlCode + '\'' +
                ", rlStartStake='" + rlStartStake + '\'' +
                ", rlEndStake='" + rlEndStake + '\'' +
                ", dstrctCode='" + dstrctCode + '\'' +
                ", laneType='" + laneType + '\'' +
                ", pavementStruct='" + pavementStruct + '\'' +
                ", lineCode='" + lineCode + '\'' +
                ", strutName='" + strutName + '\'' +
                ", lineName='" + lineName + '\'' +
                ", lineId='" + lineId + '\'' +
                ", lineDirectName='" + lineDirectName + '\'' +
                ", lineDirect='" + lineDirect + '\'' +
                ", index=" + index +
                ", lane='" + lane + '\'' +
                ", intrvlMargeId='" + intrvlMargeId + '\'' +
                ", unitMargeId='" + unitMargeId + '\'' +
                ", prjOrgCode='" + prjOrgCode + '\'' +
                ", oprtOrgCode='" + oprtOrgCode + '\'' +
                ", version=" + version +
                ", intrvlId='" + intrvlId + '\'' +
                ", create_date_unit=" + create_date_unit +
                ", type=" + type +
                ", routeCode='" + routeCode + '\'' +
                ", routeVersion='" + routeVersion + '\'' +
                ", remark='" + remark + '\'' +
                ", measureName='" + measureName + '\'' +
                ", mergeName='" + mergeName + '\'' +
                ", typeName='" + typeName + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PbdStreamUnit unit = (PbdStreamUnit) o;
        return Objects.equals(structIntrvlUid, unit.structIntrvlUid) &&
                Objects.equals(structIntrvlUcode, unit.structIntrvlUcode) &&
                Objects.equals(structIntrvlId, unit.structIntrvlId) &&
                Objects.equals(rpIntrvlId, unit.rpIntrvlId) &&
                Objects.equals(org_unit_id, unit.org_unit_id) &&
                Objects.equals(techGrade, unit.techGrade) &&
                Objects.equals(pavementType, unit.pavementType) &&
                Objects.equals(pavementStructId, unit.pavementStructId) &&
                Objects.equals(designLd, unit.designLd) &&
                Objects.equals(effectWidth, unit.effectWidth) &&
                Objects.equals(year, unit.year) &&
                Objects.equals(startStake, unit.startStake) &&
                Objects.equals(endStake, unit.endStake) &&
                Objects.equals(length, unit.length) &&
                Objects.equals(startX, unit.startX) &&
                Objects.equals(startY, unit.startY) &&
                Objects.equals(endX, unit.endX) &&
                Objects.equals(endY, unit.endY) &&
                Objects.equals(usered, unit.usered) &&
                Objects.equals(structIntrvlCode, unit.structIntrvlCode) &&
                Objects.equals(rlStartStake, unit.rlStartStake) &&
                Objects.equals(rlEndStake, unit.rlEndStake) &&
                Objects.equals(dstrctCode, unit.dstrctCode) &&
                Objects.equals(laneType, unit.laneType) &&
                Objects.equals(pavementStruct, unit.pavementStruct) &&
                Objects.equals(lineCode, unit.lineCode) &&
                Objects.equals(strutName, unit.strutName) &&
                Objects.equals(lineName, unit.lineName) &&
                Objects.equals(lineId, unit.lineId) &&
                Objects.equals(lineDirectName, unit.lineDirectName) &&
                Objects.equals(lineDirect, unit.lineDirect) &&
                Objects.equals(index, unit.index) &&
                Objects.equals(lane, unit.lane) &&
                Objects.equals(intrvlMargeId, unit.intrvlMargeId) &&
                Objects.equals(unitMargeId, unit.unitMargeId) &&
                Objects.equals(prjOrgCode, unit.prjOrgCode) &&
                Objects.equals(oprtOrgCode, unit.oprtOrgCode) &&
                Objects.equals(version, unit.version) &&
                Objects.equals(intrvlId, unit.intrvlId) &&
                Objects.equals(create_date_unit, unit.create_date_unit) &&
                Objects.equals(type, unit.type) &&
                Objects.equals(routeCode, unit.routeCode) &&
                Objects.equals(routeVersion, unit.routeVersion) &&
                Objects.equals(remark, unit.remark) &&
                Objects.equals(measureName, unit.measureName) &&
                Objects.equals(mergeName, unit.mergeName) &&
                Objects.equals(typeName, unit.typeName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(structIntrvlUid, structIntrvlUcode, structIntrvlId, rpIntrvlId, org_unit_id, techGrade, pavementType, pavementStructId, designLd, effectWidth, year, startStake, endStake, length, startX, startY, endX, endY, usered, structIntrvlCode, rlStartStake, rlEndStake, dstrctCode, laneType, pavementStruct, lineCode, strutName, lineName, lineId, lineDirectName, lineDirect, index, lane, intrvlMargeId, unitMargeId, prjOrgCode, oprtOrgCode, version, intrvlId, create_date_unit, type, routeCode, routeVersion, remark, measureName, mergeName, typeName);
    }

    public String getMeasureNameAll() {
        return measureNameAll;
    }

    public void setMeasureNameAll(String measureNameAll) {
        this.measureNameAll = measureNameAll;
    }
}
