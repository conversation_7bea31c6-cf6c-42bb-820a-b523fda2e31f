package com.hualu.highwaymaintenance.decision.entity;

public abstract class PavementIriRutParent {
    public Double startStake;

    public Double endStake;

    public String routeCode;

    public Double getStartStake() {
        return startStake;
    }

    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return endStake;
    }

    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }
}
