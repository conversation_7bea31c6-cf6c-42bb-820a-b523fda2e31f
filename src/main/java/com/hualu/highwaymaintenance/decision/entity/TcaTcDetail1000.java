package com.hualu.highwaymaintenance.decision.entity;

import java.io.Serializable;
import java.util.Objects;

/**
 * 评定结果实体类
 */
public class TcaTcDetail1000 implements Serializable {

    private String tcDetailId;
    private String prjId;
    private String rpIntrvlId;
    private String structIntrvlUid;

    private String orgId;

    private String orgName;

    private String sturtName;

    private Double PAVEMENTTHK;

    private Double EFFECTWIDTH;

    private Double CALWIDTH;

    private Double startStake;

    private Double endStake;

    private String lane;

    private Double pqi;

    private Double sci;

    private Double bci;

    private Double tci;

    private Double pciFront;

    private Double pciBehind;

    private Double rdi;

    private Double rqi;

    private Double sri;

    private Double pssi;

    private Double mqi;
    private Double pbi;
    private Double pwi;

    private Double pr;

    private String tcs;

    private Double lcd;

    private String pqiTcCode;
    private String sciTcCode;
    private String bciTcCode;
    private String tciTcCode;
    private String pciFrontTcCode;
    private String pciBehindTcCode;
    private String rqiTcCode;
    private String rdiTcCode;
    private String sriTcCode;
    private String pssiTcCode;
    private String mqiTcCode;
    private String pbiTcCode;
    private String pwiTcCode;

    private Double drFront;

    private Double drBehind;

    private Double iri;

    private Double rd;

    private Double wr;

    private Double sfc;

    private Double ssr;

    private String pqir;
    private String scir;
    private String bcir;
    private String tcir;
    private String pcir;
    private String rqir;
    private String rdir;
    private String srir;
    private String pssir;
    private String mqir;
    private String pbir;
    private String pwir;

    private String drr;

    private String irir;

    private String rdr;

    private String wrr;

    private String sfcr;

    private String ssrr;

    private String structIntrvlUcode;

    private String lineId;

    private String rlStartStake;

    private String rlEndStake;

    private String lineName;

    private String lineDirect;

    private String facility_cat;

    private String mainlineId;

    private String longness;

    private String pavementType;

    private String techGrade; //公路等级

    private String length;

    private Double lengthSum;

    private String remark;

    private Integer that;

    private String unitMargeId;

    private Double indexType;

    private String year;

    private String parentId;

    private Integer aadt;

    public TcaTcDetail1000(String prjId) {
        this.prjId = prjId;
    }

    public TcaTcDetail1000(String prjId, String unitMargeId, String year) {
        this.prjId = prjId;
        this.unitMargeId = unitMargeId;
        this.year = year;
    }

    public TcaTcDetail1000() {
    }

    public String getTcDetailId() {
        return tcDetailId;
    }

    public void setTcDetailId(String tcDetailId) {
        this.tcDetailId = tcDetailId;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }

    public String getRpIntrvlId() {
        return rpIntrvlId;
    }

    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public String getStructIntrvlUid() {
        return structIntrvlUid;
    }

    public void setStructIntrvlUid(String structIntrvlUid) {
        this.structIntrvlUid = structIntrvlUid;
    }

    public Double getStartStake() {
        return startStake;
    }

    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }

    public Double getEndStake() {
        return endStake;
    }

    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }

    public String getLane() {
        return lane;
    }

    public void setLane(String lane) {
        this.lane = lane;
    }

    public Double getPqi() {
        return pqi;
    }

    public void setPqi(Double pqi) {
        this.pqi = pqi;
    }

    public Double getSci() {
        return sci;
    }

    public void setSci(Double sci) {
        this.sci = sci;
    }

    public Double getBci() {
        return bci;
    }

    public void setBci(Double bci) {
        this.bci = bci;
    }

    public Double getTci() {
        return tci;
    }

    public void setTci(Double tci) {
        this.tci = tci;
    }

    public Double getRqi() {
        return rqi;
    }

    public void setRqi(Double rqi) {
        this.rqi = rqi;
    }

    public Double getRdi() {
        return rdi;
    }

    public void setRdi(Double rdi) {
        this.rdi = rdi;
    }

    public Double getSri() {
        return sri;
    }

    public void setSri(Double sri) {
        this.sri = sri;
    }

    public Double getPssi() {
        return pssi;
    }

    public void setPssi(Double pssi) {
        this.pssi = pssi;
    }

    public Double getMqi() {
        return mqi;
    }

    public void setMqi(Double mqi) {
        this.mqi = mqi;
    }

    public Double getPbi() {
        return pbi;
    }

    public void setPbi(Double pbi) {
        this.pbi = pbi;
    }

    public Double getPwi() {
        return pwi;
    }

    public void setPwi(Double pwi) {
        this.pwi = pwi;
    }

    public String getPqiTcCode() {
        return pqiTcCode;
    }

    public void setPqiTcCode(String pqiTcCode) {
        this.pqiTcCode = pqiTcCode;
    }

    public String getSciTcCode() {
        return sciTcCode;
    }

    public void setSciTcCode(String sciTcCode) {
        this.sciTcCode = sciTcCode;
    }

    public String getBciTcCode() {
        return bciTcCode;
    }

    public void setBciTcCode(String bciTcCode) {
        this.bciTcCode = bciTcCode;
    }

    public String getTciTcCode() {
        return tciTcCode;
    }

    public void setTciTcCode(String tciTcCode) {
        this.tciTcCode = tciTcCode;
    }

    public String getRqiTcCode() {
        return rqiTcCode;
    }

    public void setRqiTcCode(String rqiTcCode) {
        this.rqiTcCode = rqiTcCode;
    }

    public String getRdiTcCode() {
        return rdiTcCode;
    }

    public void setRdiTcCode(String rdiTcCode) {
        this.rdiTcCode = rdiTcCode;
    }

    public String getSriTcCode() {
        return sriTcCode;
    }

    public void setSriTcCode(String sriTcCode) {
        this.sriTcCode = sriTcCode;
    }

    public String getPssiTcCode() {
        return pssiTcCode;
    }

    public void setPssiTcCode(String pssiTcCode) {
        this.pssiTcCode = pssiTcCode;
    }

    public String getMqiTcCode() {
        return mqiTcCode;
    }

    public void setMqiTcCode(String mqiTcCode) {
        this.mqiTcCode = mqiTcCode;
    }

    public String getPbiTcCode() {
        return pbiTcCode;
    }

    public void setPbiTcCode(String pbiTcCode) {
        this.pbiTcCode = pbiTcCode;
    }

    public String getPwiTcCode() {
        return pwiTcCode;
    }

    public void setPwiTcCode(String pwiTcCode) {
        this.pwiTcCode = pwiTcCode;
    }

    public Double getIri() {
        return iri;
    }

    public void setIri(Double iri) {
        this.iri = iri;
    }

    public Double getRd() {
        return rd;
    }

    public void setRd(Double rd) {
        this.rd = rd;
    }

    public Double getWr() {
        return wr;
    }

    public void setWr(Double wr) {
        this.wr = wr;
    }

    public Double getSfc() {
        return sfc;
    }

    public void setSfc(Double sfc) {
        this.sfc = sfc;
    }

    public Double getSsr() {
        return ssr;
    }

    public void setSsr(Double ssr) {
        this.ssr = ssr;
    }

    public String getPqir() {
        return pqir;
    }

    public void setPqir(String pqir) {
        this.pqir = pqir;
    }

    public String getScir() {
        return scir;
    }

    public void setScir(String scir) {
        this.scir = scir;
    }

    public String getBcir() {
        return bcir;
    }

    public void setBcir(String bcir) {
        this.bcir = bcir;
    }

    public String getTcir() {
        return tcir;
    }

    public void setTcir(String tcir) {
        this.tcir = tcir;
    }

    public String getPcir() {
        return pcir;
    }

    public void setPcir(String pcir) {
        this.pcir = pcir;
    }

    public String getRqir() {
        return rqir;
    }

    public void setRqir(String rqir) {
        this.rqir = rqir;
    }

    public String getRdir() {
        return rdir;
    }

    public void setRdir(String rdir) {
        this.rdir = rdir;
    }

    public String getSrir() {
        return srir;
    }

    public void setSrir(String srir) {
        this.srir = srir;
    }

    public String getPssir() {
        return pssir;
    }

    public void setPssir(String pssir) {
        this.pssir = pssir;
    }

    public String getMqir() {
        return mqir;
    }

    public void setMqir(String mqir) {
        this.mqir = mqir;
    }

    public String getPbir() {
        return pbir;
    }

    public void setPbir(String pbir) {
        this.pbir = pbir;
    }

    public String getPwir() {
        return pwir;
    }

    public void setPwir(String pwir) {
        this.pwir = pwir;
    }

    public String getDrr() {
        return drr;
    }

    public void setDrr(String drr) {
        this.drr = drr;
    }

    public String getIrir() {
        return irir;
    }

    public void setIrir(String irir) {
        this.irir = irir;
    }

    public String getRdr() {
        return rdr;
    }

    public void setRdr(String rdr) {
        this.rdr = rdr;
    }

    public String getWrr() {
        return wrr;
    }

    public void setWrr(String wrr) {
        this.wrr = wrr;
    }

    public String getSfcr() {
        return sfcr;
    }

    public void setSfcr(String sfcr) {
        this.sfcr = sfcr;
    }

    public String getSsrr() {
        return ssrr;
    }

    public void setSsrr(String ssrr) {
        this.ssrr = ssrr;
    }

    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }

    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getRlStartStake() {
        return rlStartStake;
    }

    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }

    public String getRlEndStake() {
        return rlEndStake;
    }

    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public String getFacility_cat() {
        return facility_cat;
    }

    public void setFacility_cat(String facility_cat) {
        this.facility_cat = facility_cat;
    }

    public String getMainlineId() {
        return mainlineId;
    }

    public void setMainlineId(String mainlineId) {
        this.mainlineId = mainlineId;
    }

    public String getLongness() {
        return longness;
    }

    public void setLongness(String longness) {
        this.longness = longness;
    }

    public String getPavementType() {
        return pavementType;
    }

    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }

    public String getTechGrade() {
        return techGrade;
    }

    public void setTechGrade(String techGrade) {
        this.techGrade = techGrade;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getThat() {
        return that;
    }

    public void setThat(Integer that) {
        this.that = that;
    }

    public String getUnitMargeId() {
        return unitMargeId;
    }

    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }

    public Double getIndexType() {
        return indexType;
    }

    public void setIndexType(Double indexType) {
        this.indexType = indexType;
    }

    public Double getLengthSum() {
        return lengthSum;
    }

    public void setLengthSum(Double lengthSum) {
        this.lengthSum = lengthSum;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Double getPciFront() {
        return pciFront;
    }

    public void setPciFront(Double pciFront) {
        this.pciFront = pciFront;
    }

    public Double getPciBehind() {
        return pciBehind;
    }

    public void setPciBehind(Double pciBehind) {
        this.pciBehind = pciBehind;
    }

    public Double getDrFront() {
        return drFront;
    }

    public void setDrFront(Double drFront) {
        this.drFront = drFront;
    }

    public Double getDrBehind() {
        return drBehind;
    }

    public void setDrBehind(Double drBehind) {
        this.drBehind = drBehind;
    }

    public String getPciFrontTcCode() {
        return pciFrontTcCode;
    }

    public void setPciFrontTcCode(String pciFrontTcCode) {
        this.pciFrontTcCode = pciFrontTcCode;
    }

    public String getPciBehindTcCode() {
        return pciBehindTcCode;
    }

    public void setPciBehindTcCode(String pciBehindTcCode) {
        this.pciBehindTcCode = pciBehindTcCode;
    }

    @Override
    public String toString() {
        return "TcaTcDetail1000{" +
                "tcDetailId='" + tcDetailId + '\'' +
                ", prjId='" + prjId + '\'' +
                ", rpIntrvlId='" + rpIntrvlId + '\'' +
                ", structIntrvlUid='" + structIntrvlUid + '\'' +
                ", startStake=" + startStake +
                ", endStake=" + endStake +
                ", lane='" + lane + '\'' +
                ", pqi=" + pqi +
                ", sci=" + sci +
                ", bci=" + bci +
                ", tci=" + tci +
                ", pciFront=" + pciFront +
                ", pciBehind=" + pciBehind +
                ", rdi=" + rdi +
                ", rqi=" + rqi +
                ", sri=" + sri +
                ", pssi=" + pssi +
                ", mqi=" + mqi +
                ", pbi=" + pbi +
                ", pwi=" + pwi +
                ", pqiTcCode='" + pqiTcCode + '\'' +
                ", sciTcCode='" + sciTcCode + '\'' +
                ", bciTcCode='" + bciTcCode + '\'' +
                ", tciTcCode='" + tciTcCode + '\'' +
                ", pciFrontTcCode='" + pciFrontTcCode + '\'' +
                ", pciBehindTcCode='" + pciBehindTcCode + '\'' +
                ", rqiTcCode='" + rqiTcCode + '\'' +
                ", rdiTcCode='" + rdiTcCode + '\'' +
                ", sriTcCode='" + sriTcCode + '\'' +
                ", pssiTcCode='" + pssiTcCode + '\'' +
                ", mqiTcCode='" + mqiTcCode + '\'' +
                ", pbiTcCode='" + pbiTcCode + '\'' +
                ", pwiTcCode='" + pwiTcCode + '\'' +
                ", drFront=" + drFront +
                ", drBehind=" + drBehind +
                ", iri=" + iri +
                ", rd=" + rd +
                ", wr=" + wr +
                ", sfc=" + sfc +
                ", ssr=" + ssr +
                ", pqir='" + pqir + '\'' +
                ", scir='" + scir + '\'' +
                ", bcir='" + bcir + '\'' +
                ", tcir='" + tcir + '\'' +
                ", pcir='" + pcir + '\'' +
                ", rqir='" + rqir + '\'' +
                ", rdir='" + rdir + '\'' +
                ", srir='" + srir + '\'' +
                ", pssir='" + pssir + '\'' +
                ", mqir='" + mqir + '\'' +
                ", pbir='" + pbir + '\'' +
                ", pwir='" + pwir + '\'' +
                ", drr='" + drr + '\'' +
                ", irir='" + irir + '\'' +
                ", rdr='" + rdr + '\'' +
                ", wrr='" + wrr + '\'' +
                ", sfcr='" + sfcr + '\'' +
                ", ssrr='" + ssrr + '\'' +
                ", structIntrvlUcode='" + structIntrvlUcode + '\'' +
                ", lineId='" + lineId + '\'' +
                ", rlStartStake='" + rlStartStake + '\'' +
                ", rlEndStake='" + rlEndStake + '\'' +
                ", lineName='" + lineName + '\'' +
                ", lineDirect='" + lineDirect + '\'' +
                ", facility_cat='" + facility_cat + '\'' +
                ", mainlineId='" + mainlineId + '\'' +
                ", longness='" + longness + '\'' +
                ", pavementType='" + pavementType + '\'' +
                ", techGrade='" + techGrade + '\'' +
                ", length='" + length + '\'' +
                ", lengthSum=" + lengthSum +
                ", remark='" + remark + '\'' +
                ", that=" + that +
                ", unitMargeId='" + unitMargeId + '\'' +
                ", indexType=" + indexType +
                ", orgId='" + orgId + '\'' +
                ", orgName='" + orgName + '\'' +
                ", year='" + year + '\'' +
                ", parentId='" + parentId + '\'' +
                '}';
    }

    public String getSturtName() {
        return sturtName;
    }

    public void setSturtName(String sturtName) {
        this.sturtName = sturtName;
    }

    public double getPAVEMENTTHK() {
        return PAVEMENTTHK;
    }

    public void setPAVEMENTTHK(double PAVEMENTTHK) {
        this.PAVEMENTTHK = PAVEMENTTHK;
    }

    public void setPAVEMENTTHK(Double PAVEMENTTHK) {
        this.PAVEMENTTHK = PAVEMENTTHK;
    }

    public Double getEFFECTWIDTH() {
        return EFFECTWIDTH;
    }

    public void setEFFECTWIDTH(Double EFFECTWIDTH) {
        this.EFFECTWIDTH = EFFECTWIDTH;
    }

    public Double getCALWIDTH() {
        return CALWIDTH;
    }

    public void setCALWIDTH(Double CALWIDTH) {
        this.CALWIDTH = CALWIDTH;
    }

    public Integer getAadt() {
        return aadt;
    }

    public void setAadt(Integer aadt) {
        this.aadt = aadt;
    }

    public Double getPr() {
        return pr;
    }

    public void setPr(Double pr) {
        this.pr = pr;
    }

    public String getTcs() {
        return tcs;
    }

    public void setTcs(String tcs) {
        this.tcs = tcs;
    }

    public Double getLcd() {
        return lcd;
    }

    public void setLcd(Double lcd) {
        this.lcd = lcd;
    }
}
