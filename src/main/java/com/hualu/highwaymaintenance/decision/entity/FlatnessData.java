package com.hualu.highwaymaintenance.decision.entity;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 *
 * @Title: 平整度
 * @Description:
 * <AUTHOR>
 * @date 2020年7月1日 上午11:31:07
 */
public class FlatnessData implements java.io.Serializable, RangeData {

    private static final long serialVersionUID = -6086627078466120647L;

    private String flatnessId; // 平整度ID
    private String prjId; // 检测项目ID
    private String rpIntrvlId; // 营运路段区间ID
    private Double startStake; // 起点桩号
    private Double endStake; // 止点桩号
    @ExcelProperty(value = "车道", index = 1)
    private String lane; // 车道类型
    private String pavementType; // 路面类型
    private Double leftIri; // IRI左原始值
    @ExcelProperty(value = "右IRI", index = 5)
    private Double rightIri; // IRI右原始值
    @ExcelProperty(value = "左IRI", index = 4)
    private Double leftIriM; // IRI左修正值
    private Double rightIriM; // IRI右修正值
    private String remark; // 备注
    private String lineId; // 路线ID
    @ExcelProperty(value = "路线编码", index = 0)
    private String lineCode; // 路线编码
    private String lineName; // 路线编码
    private String laneDirection; // 路线方向
    private Double rqi; // 评分
    private String rqiTcCode; // 评分等级
    private String structIntrvlUcode; // 单元编码
    private String rampId; // 匝道ID
    private String rampName; // 匝道name
    @ExcelProperty(value = "开始桩号", index = 2)
    private String rlStartStake;
    @ExcelProperty(value = "结束桩号", index = 3)
    private String rlEndStake;
    private String unitMargeId;//评定单元id
    private String hunMargeId;
    private Integer year;//年份
    private Double iriM;
    private String routeCode;
    private String routeversion;

    public String getFlatnessId() {
        return flatnessId;
    }
    public void setFlatnessId(String flatnessId) {
        this.flatnessId = flatnessId;
    }
    public String getPrjId() {
        return prjId;
    }
    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }
    public String getRpIntrvlId() {
        return rpIntrvlId;
    }
    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }
    public Double getStartStake() {
        return startStake;
    }
    public void setStartStake(Double startStake) {
        this.startStake = startStake;
    }
    public Double getEndStake() {
        return endStake;
    }
    public void setEndStake(Double endStake) {
        this.endStake = endStake;
    }
    public String getLane() {
        return lane;
    }
    public void setLane(String lane) {
        this.lane = lane;
    }
    public String getPavementType() {
        return pavementType;
    }
    public void setPavementType(String pavementType) {
        this.pavementType = pavementType;
    }
    public Double getLeftIri() {
        return leftIri;
    }
    public void setLeftIri(Double leftIri) {
        this.leftIri = leftIri;
    }
    public Double getRightIri() {
        return rightIri;
    }
    public void setRightIri(Double rightIri) {
        this.rightIri = rightIri;
    }
    public Double getLeftIriM() {
        return leftIriM;
    }
    public void setLeftIriM(Double leftIriM) {
        this.leftIriM = leftIriM;
    }
    public Double getRightIriM() {
        return rightIriM;
    }
    public void setRightIriM(Double rightIriM) {
        this.rightIriM = rightIriM;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getLineId() {
        return lineId;
    }
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
    public String getLineCode() {
        return lineCode;
    }
    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
    public String getLineName() {
        return lineName;
    }
    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
    public String getLaneDirection() {
        return laneDirection;
    }
    public void setLaneDirection(String laneDirection) {
        this.laneDirection = laneDirection;
    }
    public Double getRqi() {
        return rqi;
    }
    public void setRqi(Double rqi) {
        this.rqi = rqi;
    }
    public String getRqiTcCode() {
        return rqiTcCode;
    }
    public void setRqiTcCode(String rqiTcCode) {
        this.rqiTcCode = rqiTcCode;
    }
    public String getStructIntrvlUcode() {
        return structIntrvlUcode;
    }
    public void setStructIntrvlUcode(String structIntrvlUcode) {
        this.structIntrvlUcode = structIntrvlUcode;
    }
    public String getRampId() {
        return rampId;
    }
    public void setRampId(String rampId) {
        this.rampId = rampId;
    }
    public String getRampName() {
        return rampName;
    }
    public void setRampName(String rampName) {
        this.rampName = rampName;
    }
    public String getRlStartStake() {
        return rlStartStake;
    }
    public void setRlStartStake(String rlStartStake) {
        this.rlStartStake = rlStartStake;
    }
    public String getRlEndStake() {
        return rlEndStake;
    }
    public void setRlEndStake(String rlEndStake) {
        this.rlEndStake = rlEndStake;
    }
    public String getUnitMargeId() {
        return unitMargeId;
    }
    public void setUnitMargeId(String unitMargeId) {
        this.unitMargeId = unitMargeId;
    }
    public Integer getYear() {
        return year;
    }
    public void setYear(Integer year) {
        this.year = year;
    }

    public Double getIriM() {
        return iriM;
    }

    public void setIriM(Double iriM) {
        this.iriM = iriM;
    }

    @Override
    public String toString() {
        return "FlatnessData [flatnessId=" + flatnessId + ", prjId=" + prjId + ", rpIntrvlId=" + rpIntrvlId
                + ", startStake=" + startStake + ", endStake=" + endStake + ", lane=" + lane + ", pavementType="
                + pavementType + ", leftIri=" + leftIri + ", rightIri=" + rightIri + ", leftIriM=" + leftIriM
                + ", rightIriM=" + rightIriM + ", remark=" + remark + ", lineId=" + lineId + ", lineCode=" + lineCode
                + ", lineName=" + lineName + ", laneDirection=" + laneDirection + ", rqi=" + rqi + ", rqiTcCode="
                + rqiTcCode + ", structIntrvlUcode=" + structIntrvlUcode + ", rampId=" + rampId + ", rampName="
                + rampName + ", rlStartStake=" + rlStartStake + ", rlEndStake=" + rlEndStake + ", unitMargeId="
                + unitMargeId + ", year=" + year + "]";
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteversion() {
        return routeversion;
    }

    public void setRouteversion(String routeversion) {
        this.routeversion = routeversion;
    }

    public String getHunMargeId() {
        return hunMargeId;
    }

    public void setHunMargeId(String hunMargeId) {
        this.hunMargeId = hunMargeId;
    }
}