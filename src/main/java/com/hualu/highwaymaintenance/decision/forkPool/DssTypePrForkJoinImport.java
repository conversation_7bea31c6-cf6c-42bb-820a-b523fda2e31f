package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail100;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DssTypePrForkJoinImport extends RecursiveTask<List<DssInfo>> {

    private PbdStreamMapper baseMapper;

    private List<DssInfo> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public DssTypePrForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public DssTypePrForkJoinImport(List<DssInfo> list) {
        this.list = list;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected List<DssInfo> compute() {
        List<DssInfo> dssTypePrList = this.baseMapper.getDssTypePrList(orgId, Integer.valueOf(year), type);
        this.list = dssTypePrList;
        if (this.units != null && this.units.size() > 0)
        {
            setUnitsComputer();
        }
        if (this.huns != null && this.huns.size() > 0 && this.type == 1)
        {
            setHunsComputer();
        }
        return null;
    }

    private void setUnitsComputer()
    {
        //计算PCI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        List<DssInfo> list = this.list;
        Map<String, List<DssInfo>> collect1 = list.stream().collect(Collectors.groupingBy(v -> v.getUnitMargeId()));
        collect1.entrySet().stream().forEach(vs ->
        {
            List<DssInfo> value = vs.getValue();
            if (this.type == 0)
            {
                Map<String, TcaTcDetail100> Huncollect = detail100s.stream().collect(Collectors
                        .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));
                value.stream().forEach(v ->
                {
                    BigDecimal dr = v.getDr();
                    BigDecimal hundred = new BigDecimal("100");
                    BigDecimal divide = hundred.multiply(dr).divide(v.getHunLength().multiply(new BigDecimal("3.75")), 2, BigDecimal.ROUND_HALF_UP);
                    if (Huncollect.containsKey(v.getUnitMargeId()))
                    {
                        TcaTcDetail100 tcaTcDetail100 = Huncollect.get(v.getUnitMargeId());
                        tcaTcDetail100.setPr(divide.doubleValue());
                        if (this.year != null && tcaTcDetail100.getYear() != null)
                        {
                            tcaTcDetail100.setYear(this.year);
                        }
                    }
                });
            }
            double sum = value.stream().mapToDouble(v -> v.getDr().doubleValue()).sum();
            if (sum > 0)
            {
                System.out.println(sum);
            }
            BigDecimal dr = new BigDecimal(Double.toString(sum));
            BigDecimal hundred = new BigDecimal("100");
            BigDecimal divide = hundred.multiply(dr).divide(value.stream().findFirst().get().getLength().multiply(new BigDecimal("3.75")), 2, BigDecimal.ROUND_HALF_UP);
            if (collect.containsKey(vs.getKey()))
            {
                TcaTcDetail1000 tcaTcDetail1000 = collect.get(vs.getKey());
                tcaTcDetail1000.setPr(divide.doubleValue());
                if (this.year != null && StringUtils.isBlank(tcaTcDetail1000.getYear()))
                {
                    tcaTcDetail1000.setYear(this.year.toString());
                }
            }
        });
    }
    private void setHunsComputer()
    {

    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private String orgId;

        private String lineCode;

        private String prjId;

        private int type;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setType(int type) {
            this.type = type;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public DssTypePrForkJoinImport build()
        {
            DssTypePrForkJoinImport dssTypeForkJoinImport = new DssTypePrForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.type == 1)
            {
                dssTypeForkJoinImport.setType(this.type);
            }

            if (this.detail100s != null)
            {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }
             return dssTypeForkJoinImport;
        }
    }
}
