package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class RuttingInfoForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<RuttingData> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public RuttingInfoForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.list = list;
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public RuttingInfoForkJoinImport(List<RuttingData> list) {
        this.list = list;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    protected void compute() {
        //计算RDI
        List<RuttingData> ruttingData = this.baseMapper.getRdiUnitData(orgId, Integer.valueOf(year), this.lineCode);
        if (this.type == 0)
        {
            listRutGroupUnitRange(ruttingData, this.huns);
        }else
        {
            listRutGroupUnitRange(ruttingData, this.units);
        }
        this.list = ruttingData;
        setHunsComputer(ruttingData);
    }

    private void setUnitsComputer(List<RuttingData> list)
    {
        //计算RQI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        List<RuttingData> dataNewList = new ArrayList<RuttingData>();
        Map<String, List<RuttingData>> rutt = list.stream().filter(v -> !StringUtils.isBlank(v.getUnitMargeId()))
                .collect(Collectors.groupingBy(RuttingData::getUnitMargeId));
        rutt.entrySet().stream().forEach(v ->
        {
            RuttingData ruttingData = new RuttingData();
            List<RuttingData> value = v.getValue();
            BigDecimal iriAvg = new BigDecimal(Double.toString(value.stream().map(vv -> Math.max(vv.getLeftRdM().doubleValue()
                    , vv.getRightRdM().doubleValue())).mapToDouble(vv -> vv.doubleValue()).average().orElse(0)));
            ruttingData.setRdm(iriAvg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            RuttingData tempNess = value.stream().findFirst().get();
            ruttingData.setUnitMargeId(tempNess.getUnitMargeId());
            dataNewList.add(ruttingData);
        });

        dataNewList.stream().forEach(rdiHun ->
        {
            double avgIri = rdiHun.getRdm();
            BigDecimal decimalUnit = null;
            BigDecimal addUnit = null;
            BigDecimal rdmUnit = null;
            BigDecimal multiplyUnit = null;
            BigDecimal rdiCountUnit = null;
            if (avgIri <= 10) {
                decimalUnit = new BigDecimal("1");
                addUnit = new BigDecimal("100");
                rdmUnit = new BigDecimal(String.valueOf(avgIri));
                multiplyUnit = decimalUnit.multiply(rdmUnit);
                rdiCountUnit = addUnit.subtract(multiplyUnit).setScale(2, BigDecimal.ROUND_HALF_UP);
            } else if (avgIri > 10 && avgIri <= 40) {
                decimalUnit = new BigDecimal("3");
                addUnit = new BigDecimal("90");
                rdmUnit = new BigDecimal(String.valueOf(avgIri));
                BigDecimal subtract = rdmUnit.subtract(new BigDecimal("10"));
                multiplyUnit = decimalUnit.multiply(subtract);
                rdiCountUnit = addUnit.subtract(multiplyUnit).setScale(2, BigDecimal.ROUND_HALF_UP);
                ;
            } else if (avgIri > 40) {
                rdiCountUnit = new BigDecimal("0");
            }
            if (collect.containsKey(rdiHun.getUnitMargeId())) {
                TcaTcDetail1000 detail1000 = collect.get(rdiHun.getUnitMargeId());
                detail1000.setRdi(rdiCountUnit.doubleValue());
                detail1000.setRd(avgIri);
                if (this.year != null && StringUtils.isBlank(detail1000.getYear()))
                {
                    detail1000.setYear(this.year.toString());
                }
            } else if (!collect.containsKey(rdiHun.getUnitMargeId())) {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setRdi(rdiCountUnit.doubleValue());
                tcaTcDetail1000.setUnitMargeId(rdiHun.getUnitMargeId());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setRd(avgIri);
                detail1000s.add(tcaTcDetail1000);
            }
        });

    }

    private void setHunsComputer(List<RuttingData> list)
    {
        Map<String, TcaTcDetail1000> cacheUnits = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        Map<String, List<RuttingData>> collect = null;
        if (this.type == 0)
        {
            collect = list.stream().filter(v -> !StringUtils.isBlank(v.getHunMargeId())).collect(Collectors.groupingBy(v -> v.getUnitMargeId()));
        }else if(this.type == 1)
        {
            collect = list.stream().filter(v -> !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors.groupingBy(v -> v.getUnitMargeId()));
        }
        collect.entrySet().stream().forEach(vs ->
        {
            List<RuttingData> value = vs.getValue();
            List<Double> avg = new ArrayList<>();
            if (this.type == 0)
            {
                Map<String, TcaTcDetail100> cache = detail100s.stream().collect(Collectors
                        .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));
                Map<String, List<RuttingData>> collect1 = value.stream().collect(Collectors.groupingBy(v -> v.getHunMargeId()));
                collect1.entrySet().stream().forEach(huns ->
                {
                    double asDouble = huns.getValue().stream().mapToDouble(v -> Math.max(v.getLeftRdM().doubleValue(), v.getRightRdM().doubleValue())).average().getAsDouble();
                    avg.add(asDouble);
                    BigDecimal decimal = null;
                    BigDecimal add = null;
                    BigDecimal rdm = new BigDecimal(Double.toString(asDouble)).setScale(3, BigDecimal.ROUND_HALF_UP);;
                    BigDecimal multiply = null;
                    BigDecimal rdiCount = null;
                    if (rdm.doubleValue() <= 10) {
                        decimal = new BigDecimal("1");
                        add = new BigDecimal("100");
                        multiply = decimal.multiply(rdm);
                        rdiCount = add.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                        ;
                    } else if (rdm.doubleValue() > 10 && rdm.doubleValue() <= 40) {
                        decimal = new BigDecimal("3");
                        add = new BigDecimal("90");
                        BigDecimal subtract = rdm.subtract(new BigDecimal("10"));
                        multiply = decimal.multiply(subtract);
                        rdiCount = add.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                        ;
                    } else if (rdm.doubleValue() > 40) {
                        rdiCount = new BigDecimal("0");
                    }
                    if (cache.containsKey(huns.getKey())) {
                        TcaTcDetail100 tcaTcDetail100 = cache.get(huns.getKey());
                        tcaTcDetail100.setRdi(rdiCount.doubleValue());
                        tcaTcDetail100.setRd(rdm.doubleValue());
                    }
                });
            }

            double asDouble = 0;
            if (this.type == 0)
            {
                asDouble = avg.stream().mapToDouble(v -> v.doubleValue()).average().getAsDouble();
            }else if (this.type == 1)
            {
                asDouble = value.stream().mapToDouble(v -> Math.max(v.getLeftRdM().doubleValue(), v.getRightRdM().doubleValue())).average().orElse(0);
            }
            BigDecimal rdm = new BigDecimal(Double.toString(asDouble));
            BigDecimal decimal = null;
            BigDecimal add = null;
            BigDecimal multiply = null;
            BigDecimal rdiCount = null;
            if (rdm.doubleValue() <= 10) {
                decimal = new BigDecimal("1");
                add = new BigDecimal("100");
                multiply = decimal.multiply(rdm);
                rdiCount = add.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                ;
            } else if (rdm.doubleValue() > 10 && rdm.doubleValue() <= 40) {
                decimal = new BigDecimal("3");
                add = new BigDecimal("90");
                BigDecimal subtract = rdm.subtract(new BigDecimal("10"));
                multiply = decimal.multiply(subtract);
                rdiCount = add.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                ;
            } else if (rdm.doubleValue() > 40) {
                rdiCount = new BigDecimal("0");
            }
            if (cacheUnits.containsKey(vs.getKey())) {
                TcaTcDetail1000 tcaTcDetail100 = cacheUnits.get(vs.getKey());
                tcaTcDetail100.setRdi(rdiCount.doubleValue());
                tcaTcDetail100.setRd(rdm.doubleValue());
            }
        });
    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public RuttingInfoForkJoinImport build()
        {
            RuttingInfoForkJoinImport dssTypeForkJoinImport = new RuttingInfoForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0)
            {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

             return dssTypeForkJoinImport;
        }
    }

    private <T extends RangeData> void listRutGroupUnitNewRange(List<T> list, List<PbdStreamUnit> unis)
    {
        Map<String, List<PbdStreamUnit>> collect = unis.stream().collect(Collectors.groupingBy(PbdStreamUnit::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode())).collect(Collectors.groupingBy(RangeData::getRouteCode));

        collect.entrySet().stream().forEach(v ->
        {
            List<RangeData> ruttingData = checkRange.get(v.getKey());
            List<PbdStreamUnit> value = v.getValue();
            // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
            Collections.sort(value, Comparator.comparing(PbdStreamUnit::getStartStake));
            ruttingData.stream().forEach(ness ->
            {
                Double startStake = ness.getStartStake();
                int index = Collections.binarySearch(value, new PbdStreamUnit(startStake), Comparator.comparing(PbdStreamUnit::getStartStake));
                if (index < 0)
                {
                    index = -index - 1;
                }
                if (index >= 0 && !StringUtils.isBlank(value.get(index).getUnitMargeId())) {
                    ness.setUnitMargeId(value.get(index).getUnitMargeId());
                }
            });
        });
    }

    private void listRutGroupUnitRange(List<? extends RangeData> list, List<? extends PbdStructParent> unis) {
        Map<String, List<PbdStructParent>> collect = unis.stream().filter(v -> !StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(PbdStructParent::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !org.apache.commons.lang3.StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(RangeData::getRouteCode));

        // 创建CompletableFuture列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (Map.Entry<String, List<PbdStructParent>> entry : collect.entrySet()) {
            String routeCode = entry.getKey();
            List<RangeData> ruttingData = checkRange.get(routeCode);
            List<PbdStructParent> value = entry.getValue();
            futures.add(CompletableFuture.runAsync(() -> {
                // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
                Collections.sort(value, Comparator.comparing(PbdStructParent::getStartStake));
                for (int i = 0; i < ruttingData.size(); i++) {
                    RangeData ness = ruttingData.get(i);
                    Double startStake = ness.getStartStake();
                    PbdStructParent unit = value.stream().filter(dss ->
                            isSubMilestoneInRange(dss, startStake)).findFirst().orElse(new PbdStructIntrvlHun());
                    if (unit != null && !StringUtils.isBlank(unit.getUnitMargeId())) {
                        ness.setHunMargeId(unit.getHunMargeId());
                        ness.setUnitMargeId(unit.getUnitMargeId());
                    }
                }
            }));
        }

        // 等待所有CompletableFuture执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /*private <T extends RangeData> void listRutGroupUnitRange(List<T> list, List<? extends PbdStructParent> unis) {
        Map<String, List<PbdStructParent>> collect = unis.stream().collect(Collectors.groupingBy(PbdStructParent::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode())).collect(Collectors.groupingBy(RangeData::getRouteCode));

        collect.entrySet().stream().forEach(v ->
        {
            List<RangeData> ruttingData = checkRange.get(v.getKey());
            List<PbdStructParent> value = v.getValue();
            // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
            Collections.sort(value, Comparator.comparing(PbdStructParent::getStartStake));
            ruttingData.stream().forEach(ness ->
            {
                Double startStake = ness.getStartStake();
                PbdStructParent unit = value.stream().filter(dss -> isSubMilestoneInRange(dss, startStake)).findFirst().orElse(new PbdStructIntrvlHun());
                if (unit != null && !StringUtils.isBlank(unit.getUnitMargeId())) {
                    ness.setUnitMargeId(unit.getUnitMargeId());
                    ness.setHunMargeId(unit.getHunMargeId());
                }
            });
        });
    }*/

    //匹配算法
    private boolean isSubMilestoneInRange(PbdStructParent unit, Double startStake) {
        BigDecimal milestoneStart = new BigDecimal(Double.toString(startStake));
        BigDecimal rangeStart = new BigDecimal(Double.toString(unit.getStartStake()));
        BigDecimal rangeEnd = new BigDecimal(Double.toString(unit.getEndStake()));
        BigDecimal subtractEnd = milestoneStart.subtract(rangeEnd);
        BigDecimal rangeStartSubtract = milestoneStart.subtract(rangeStart);
        BigDecimal multiply = subtractEnd.multiply(rangeStartSubtract);
        if (multiply.doubleValue() < 0 || startStake.doubleValue() == rangeStart.doubleValue()) {
            return true;
        } else {
            return false;
        }
    }
}
