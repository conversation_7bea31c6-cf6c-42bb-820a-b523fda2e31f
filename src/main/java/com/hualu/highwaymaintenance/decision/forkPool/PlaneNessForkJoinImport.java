package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RecursiveAction;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PlaneNessForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<FlatnessData> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public PlaneNessForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public PlaneNessForkJoinImport(List<FlatnessData> list) {
        this.list = list;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        try {
            //查询平整度数据，并计算分数
            List<FlatnessData> flatessData = this.baseMapper.getFlatessData(orgId, Integer.valueOf(year));
            if (this.type == 0)
            {
                listRutGroupUnitRange(flatessData, huns);
            }else
            {
                listRutGroupUnitRange(flatessData, units);
            }

            if (this.units != null && this.units.size() > 0 && this.type == 1)
            {
                List<FlatnessData> data = groupMaxStatistics(flatessData.stream().filter(v -> !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors
                        .groupingBy(FlatnessData::getUnitMargeId)));
                setUnitsComputer(data);
            }
            if (this.huns != null && this.huns.size() > 0 && this.type == 0)
            {
                List<FlatnessData> data = groupMaxStatistics(flatessData.stream().filter(v -> !StringUtils.isBlank(v.getHunMargeId())).collect(Collectors
                        .groupingBy(FlatnessData::getHunMargeId)));
                setHunsComputer(data);
            }
        }catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void setUnitsComputer(List<FlatnessData> list)
    {
        //计算RQI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        list.stream().forEach(a ->
        {
            double avgIri = a.getIriM();
            double exp = new BigDecimal(String.valueOf(Math.exp(0.65 * avgIri))).setScale(3, BigDecimal.ROUND_UP).doubleValue();
            BigDecimal decimal = new BigDecimal("0.026");
            BigDecimal add = decimal.multiply(new BigDecimal(String.valueOf(exp))).add(new BigDecimal("1")).setScale(4, BigDecimal.ROUND_DOWN);
            BigDecimal hundred = new BigDecimal("100");
            double rqi = hundred.divide(add, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (collect.containsKey(a.getUnitMargeId())) {
                TcaTcDetail1000 detail1000 = collect.get(a.getUnitMargeId());
                detail1000.setRqi(rqi);
                detail1000.setIri(avgIri);
                if (this.year != null && StringUtils.isBlank(detail1000.getYear()))
                {
                    detail1000.setYear(this.year.toString());
                }
            } else if (!collect.containsKey(a.getUnitMargeId())) {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setRqi(rqi);
                tcaTcDetail1000.setUnitMargeId(a.getUnitMargeId());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setLane(a.getLane());
                tcaTcDetail1000.setIri(avgIri);
                detail1000s.add(tcaTcDetail1000);
            }

        });
    }

    private void setHunsComputer(List<FlatnessData> list)
    {
        Map<String, TcaTcDetail100> collect = detail100s.stream().collect(Collectors
                .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));

        list.stream().forEach(v ->
        {
            double exp = Math.exp(0.65 * v.getIriM());
            BigDecimal decimal = new BigDecimal("0.026");
            BigDecimal add = decimal.multiply(new BigDecimal(String.valueOf(exp))).add(new BigDecimal("1"));
            BigDecimal hundred = new BigDecimal("100");
            double rqi = hundred.divide(add, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (collect.containsKey(v.getHunMargeId())) {
                TcaTcDetail100 tcaTcDetail100 = collect.get(v.getHunMargeId());
                tcaTcDetail100.setRqi(rqi);
                tcaTcDetail100.setIri(v.getIriM());
            }
        });

        //计算RQI情况
        Map<String, TcaTcDetail1000> ten1000 = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        Map<String, List<FlatnessData>> collect1 = list.stream().filter(v -> !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors
                .groupingBy(FlatnessData::getUnitMargeId));
        collect1.entrySet().stream().forEach(v ->
        {
            List<FlatnessData> value = v.getValue();
            double avgIri = value.stream().mapToDouble(iriM -> iriM.getIriM()).average().getAsDouble();
            double exp = new BigDecimal(String.valueOf(Math.exp(0.65 * avgIri))).setScale(3, BigDecimal.ROUND_UP).doubleValue();
            BigDecimal decimal = new BigDecimal("0.026");
            BigDecimal add = decimal.multiply(new BigDecimal(String.valueOf(exp))).add(new BigDecimal("1")).setScale(4, BigDecimal.ROUND_DOWN);
            BigDecimal hundred = new BigDecimal("100");
            double rqi = hundred.divide(add, 2, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (ten1000.containsKey(v.getKey())) {
                TcaTcDetail1000 detail1000 = ten1000.get(v.getKey());
                detail1000.setRqi(rqi);
                detail1000.setIri(avgIri);
                if (this.year != null && StringUtils.isBlank(detail1000.getYear()))
                {
                    detail1000.setYear(this.year.toString());
                }
            } else if (!ten1000.containsKey(v.getKey())) {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setRqi(rqi);
                tcaTcDetail1000.setUnitMargeId(v.getKey());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setLane(value.stream().findFirst().get().getLane());
                tcaTcDetail1000.setIri(avgIri);
                detail1000s.add(tcaTcDetail1000);
            }
        });
    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public PlaneNessForkJoinImport build()
        {
            PlaneNessForkJoinImport dssTypeForkJoinImport = new PlaneNessForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0)
            {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

             return dssTypeForkJoinImport;
        }
    }

    private <T extends RangeData> void listRutGroupUnitNewRange(List<T> list, List<PbdStreamUnit> unis)
    {
        Map<String, List<PbdStreamUnit>> collect = unis.stream().collect(Collectors.groupingBy(PbdStreamUnit::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode())).collect(Collectors.groupingBy(RangeData::getRouteCode));

        collect.entrySet().stream().forEach(v ->
        {
            List<RangeData> ruttingData = checkRange.get(v.getKey());
            List<PbdStreamUnit> value = v.getValue();
            // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
            List<Double> collect1 = value.stream().map(vs -> vs.getStartStake()).collect(Collectors.toList());
            Collections.sort(collect1);
            ruttingData.stream().forEach(ness ->
            {
                Double startStake = ness.getStartStake();
                int index = Collections.binarySearch(collect1, startStake);
                if (index < 0) {
                    index = -index - 1;
                }
                if (index >= 0 && !StringUtils.isBlank(value.get(index).getUnitMargeId())) {
                    ness.setUnitMargeId(value.get(index).getUnitMargeId());
                }
            });
        });
    }

    private <T extends RangeData> void listRutGroupUnitRange(List<T> list, List<? extends PbdStructParent> unis) {
        Map<String, List<PbdStructParent>> collect = unis.stream().filter(v -> !StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(PbdStructParent::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode())).collect(Collectors.groupingBy(RangeData::getRouteCode));

        // 创建CompletableFuture列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (Map.Entry<String, List<PbdStructParent>> entry : collect.entrySet()) {
            String routeCode = entry.getKey();
            List<RangeData> ruttingData = checkRange.get(routeCode);
            List<PbdStructParent> value = entry.getValue();
            futures.add(CompletableFuture.runAsync(() -> {
                // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
                Collections.sort(value, Comparator.comparing(PbdStructParent::getStartStake));
                ruttingData.forEach(ness -> {
                    Double startStake = ness.getStartStake();
                    PbdStructParent unit = value.stream().filter(dss -> isSubMilestoneInRange(dss, startStake)).findFirst().orElse(new PbdStructIntrvlHun());
                    if (unit != null && !StringUtils.isBlank(unit.getUnitMargeId())) {
                        ness.setHunMargeId(unit.getHunMargeId());
                        ness.setUnitMargeId(unit.getUnitMargeId());
                    }
                });
            }));
        }

        // 等待所有CompletableFuture执行完毕
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /*private <T extends RangeData> void listRutGroupUnitRange(List<T> list, List<? extends PbdStructParent> unis) {
        Map<String, List<PbdStructParent>> collect = unis.stream().filter(v -> !StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(PbdStructParent::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode())).collect(Collectors.groupingBy(RangeData::getRouteCode));

        collect.entrySet().stream().forEach(v ->
        {
            List<RangeData> ruttingData = checkRange.get(v.getKey());
            List<PbdStructParent> value = v.getValue();
            // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
            Collections.sort(value, Comparator.comparing(PbdStructParent::getStartStake));
            ruttingData.parallelStream().forEach(ness ->
            {
                Double startStake = ness.getStartStake();
                PbdStructParent unit = value.parallelStream().filter(dss -> isSubMilestoneInRange(dss, startStake)).findFirst().orElse(new PbdStructIntrvlHun());
                if (unit != null && !StringUtils.isBlank(unit.getUnitMargeId())) {
                    ness.setHunMargeId(unit.getHunMargeId());
                    ness.setUnitMargeId(unit.getUnitMargeId());
                }
            });
        });
    }*/

    //匹配算法
    private boolean isSubMilestoneInRange(PbdStructParent unit, Double startStake) {
        BigDecimal milestoneStart = new BigDecimal(Double.toString(startStake));
        BigDecimal rangeStart = new BigDecimal(Double.toString(unit.getStartStake()));
        BigDecimal rangeEnd = new BigDecimal(Double.toString(unit.getEndStake()));
        BigDecimal subtractEnd = milestoneStart.subtract(rangeEnd);
        BigDecimal rangeStartSubtract = milestoneStart.subtract(rangeStart);
        BigDecimal multiply = subtractEnd.multiply(rangeStartSubtract);
        if (multiply.doubleValue() < 0 || startStake.doubleValue() == rangeStart.doubleValue()) {
            return true;
        } else {
            return false;
        }
    }

    private List<FlatnessData> groupMaxStatistics(Map<String, List<FlatnessData>> mapsList) {
        List<FlatnessData> dataNewList = new ArrayList<FlatnessData>();
        mapsList.entrySet().stream().forEach(v ->
        {
            FlatnessData flatnessData = new FlatnessData();
            List<FlatnessData> value = v.getValue();
            BigDecimal iriAvg = new BigDecimal(Double.toString(value.stream().map(vv -> Math.max(vv.getLeftIriM().doubleValue()
                    , vv.getRightIriM().doubleValue())).mapToDouble(vv -> vv.doubleValue()).average().orElse(0)));
            flatnessData.setIriM(iriAvg.setScale(6, BigDecimal.ROUND_HALF_UP).doubleValue());
            FlatnessData tempNess = value.stream().findFirst().get();
            flatnessData.setUnitMargeId(tempNess.getUnitMargeId());
            flatnessData.setHunMargeId(tempNess.getHunMargeId());
            dataNewList.add(flatnessData);
        });

        return dataNewList;
    }
}
