package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.DrainageData;
import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.util.MapUtil;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DrainageDataForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<DrainageData> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public DrainageDataForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public DrainageDataForkJoinImport(List<DrainageData> list) {
        this.list = list;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        List<DrainageData> damageDate = this.baseMapper.getDamageDate(orgId, Integer.valueOf(year));
        this.list = damageDate;
        if (this.units != null && this.units.size() > 0)
        {

            setUnitsComputer();
        }
        if (this.huns != null && this.huns.size() > 0)
        {
            setHunsComputer();
        }
    }

    private void setUnitsComputer()
    {
        //计算SCI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        Map<String, List<DrainageData>> dataNewMap = this.list.stream().collect(Collectors.groupingBy(DrainageData::getUnitMargeId));

        dataNewMap.entrySet().stream().forEach(v ->
        {
            List<DrainageData> value = v.getValue();
            //sumList
            List<Double> sumList = new ArrayList<Double>();
            Map<String, List<DrainageData>> dssType = value.stream().collect(Collectors.groupingBy(DrainageData::getDrainDssType));
            //合并病害
            List<Double> groupSum = new ArrayList<Double>();
            Map<String, Double> stringDoubleMap = MapUtil.filterLJDssTypeWeight();
            dssType.entrySet().stream().forEach(a ->
            {
                stringDoubleMap.remove(a.getKey());
                List<DrainageData> drainageData = a.getValue();
                double sum = drainageData.stream().mapToDouble(s -> s.getSci()).sum();
                if (sum > 100) {
                    sum = 100;
                }
                BigDecimal decimal = new BigDecimal(String.valueOf(100 - sum));
                BigDecimal multiply = decimal.multiply(new BigDecimal(drainageData.get(0).getWeight()));
                sumList.add(multiply.doubleValue());
            });
            sumList.addAll(stringDoubleMap.entrySet().stream().map(entry
                    -> entry.getValue()).collect(Collectors.toList()));
            double typeSum = sumList.stream().mapToDouble(sum -> sum).sum();
            if (collect.containsKey(v.getKey())) {
                TcaTcDetail1000 tcaTcDetail1000 = collect.get(v.getKey());
                tcaTcDetail1000.setSci(typeSum);
                if (this.year != null && StringUtils.isBlank(tcaTcDetail1000.getYear()))
                {
                    tcaTcDetail1000.setYear(this.year.toString());
                }
            } else if (!collect.containsKey(v.getKey()))
            {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setSci(typeSum);
                tcaTcDetail1000.setUnitMargeId(v.getKey());
                tcaTcDetail1000.setYear(String.valueOf(value.get(0).getYear()));
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setLane(value.get(0).getLane());
                detail1000s.add(tcaTcDetail1000);
            }
        });
    }

    private void setHunsComputer()
    {

    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DrainageDataForkJoinImport build()
        {
            DrainageDataForkJoinImport dssTypeForkJoinImport = new DrainageDataForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

             return dssTypeForkJoinImport;
        }
    }
}
