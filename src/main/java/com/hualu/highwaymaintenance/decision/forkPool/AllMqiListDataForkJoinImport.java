package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail100;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.MapUtil;
import com.hualu.highwaymaintenance.util.StringUtil;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AllMqiListDataForkJoinImport extends RecursiveAction {

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private int type = 0;

    public AllMqiListDataForkJoinImport(List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        if (this.units != null && this.units.size() > 0) {

            setUnitsComputer();
        }
        if (this.huns != null && this.huns.size() > 0) {
            setHunsComputer();
        }
    }

    private void setUnitsComputer() {
        //计算TCI情况
        Map<String, TcaTcDetail1000> pqiCount = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        List<Double> sriCount = detail1000s.stream().map(TcaTcDetail1000::getSri).collect(Collectors.toList());
        try {
            pqiCount.entrySet().stream().forEach(a ->
            {
                TcaTcDetail1000 value = a.getValue();
                value.setPavementType("LQ");

                if (value.getPciFront() == null) {
                    value.setPciFront(100.0);
                    value.setDrFront(0.0);
                }
                if (value.getPciBehind() == null) {
                    value.setPciBehind(100.0);
                    value.setDrBehind(0.0);
                }
                Double pbis = value.getPbi();
                if (pbis == null) {
                    value.setPbi(100.0);
                    pbis = 100.0;
                }
                if(value.getRqi() == null)
                {
                    value.setIri(0.0);
                    value.setRqi(100.0);
                }
                if (value.getRdi() == null)
                {
                    value.setRd(0.0);
                    value.setRdi(100.0);
                }
                if (value.getSri() == null && sriCount.size() > 0)
                {
                    value.setSfc(0.0);
                    value.setSri(100.0);
                }

                value.setPciFrontTcCode(StringUtil.pciTcCode(value.getPciFront()));
                value.setPciBehindTcCode(StringUtil.pciTcCode(value.getPciBehind()));

                if ("SN".equals(value.getPavementType())) {
                    Double rqi = value.getRqi();
                    if (rqi != null) {
                        value.setRqiTcCode(StringUtil
                                .rqiTcCode(value.getRqi()));
                    }
                    if (rqi != null) {
                        value.setRqiTcCode(StringUtil
                                .rqiTcCode(value.getRqi()));
                    }
                } else {
                    Double rqi = value.getRqi();
                    if (rqi != null) {
                        value.setRqiTcCode(StringUtil
                                .rqiTcCode(value.getRqi()));
                    }
                }
                if (value.getPciFront() != null
                        && value.getRqi() != null
                        && value.getRdi() != null
                        && pbis != null
                        && (value.getSri() != null
                        || value.getPwi() != null)) {
                    BigDecimal pci = new BigDecimal(String.valueOf(value.getPciFront()));
                    BigDecimal rqi = new BigDecimal(String.valueOf(value.getRqi()));
                    BigDecimal rdi1 = new BigDecimal(String.valueOf(value.getRdi()));
                    BigDecimal pbi1 = new BigDecimal(String.valueOf(pbis));

                    int index = 0;
                    System.out.println(value);
                    Double pciWeight = StringUtil.getPqiWeight(value.getPavementType(), "pci");
                    BigDecimal pciMultiply = pci.multiply(new BigDecimal(pciWeight));
                    Double rqiWeight = StringUtil.getPqiWeight(value.getPavementType(), "rqi");
                    BigDecimal rqiMultiply = rqi.multiply(new BigDecimal(rqiWeight));
                    Double rdiWeight = StringUtil.getPqiWeight(value.getPavementType(), "rdi");
                    BigDecimal rdi1Multiply = rdi1.multiply(new BigDecimal(rdiWeight));
                    Double pbiWeight = StringUtil.getPqiWeight(value.getPavementType(), "pbi");
                    BigDecimal pbi1Multiply = pbi1.multiply(new BigDecimal(pbiWeight));
                    Double sriWeight = StringUtil.getPqiWeight(value.getPavementType(), "sri");

                    Double pwiWeight = StringUtil.getPqiWeight(value.getPavementType(), "pwi");

                    BigDecimal add = pciMultiply.add(rqiMultiply).add(rdi1Multiply).add(pbi1Multiply);
                    if (value.getSri() == null || value.getSri().doubleValue() == 0) {
                        BigDecimal pwi1 = new BigDecimal(String.valueOf(value.getPwi()));
                        BigDecimal pwi1Multiply = pwi1.multiply(new BigDecimal(pwiWeight));
                        add = add.add(pwi1Multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                        ;
                        value.setThat(0);
                    } else {
                        BigDecimal sri1 = new BigDecimal(String.valueOf(value.getSri()));
                        BigDecimal sri1Multiply = sri1.multiply(new BigDecimal(sriWeight));
                        add = add.add(sri1Multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                        ;
                        value.setThat(1);
                    }
                    value.setPqi(add.doubleValue());
                }
            });

            //计算百米的PQI
            if (this.type == 0)
            {
                Map<String, TcaTcDetail100> pqiHUns = detail100s.stream().collect(Collectors
                        .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));

                pqiHUns.entrySet().stream().forEach(a ->
                {
                    TcaTcDetail100 value = a.getValue();
                    value.setPavementType("LQ");


                    if (value.getTcs() == null) {
                        value.setTcs("NULL");
                    }
                    if (value.getPciFront() == null) {
                        value.setPciFront(100.0);
                        value.setDrFront(0.0);
                    }
                    if (value.getPciBehind() == null) {
                        value.setPciBehind(100.0);
                        value.setDrBehind(0.0);
                    }
                    Double pbis = value.getPbi();
                    if (pbis == null) {
                        value.setPbi(100.0);
                        pbis = 100.0;
                    }
                    if(value.getRqi() == null)
                    {
                        value.setIri(0.0);
                        value.setRqi(100.0);
                    }
                    if (value.getRdi() == null)
                    {
                        value.setRd(0.0);
                        value.setRdi(100.0);
                    }
                    if (value.getSri() == null && sriCount.size() > 0)
                    {
                        value.setSfc(0.0);
                        value.setSri(100.0);
                    }
                    if (value.getPssi() == null || value.getPssi().doubleValue() == 0)
                    {
                        value.setPssi(100.0);
                    }

                    value.setPciFrontTcCode(StringUtil.pciTcCode(value.getPciFront()));
                    value.setPciBehindTcCode(StringUtil.pciTcCode(value.getPciBehind()));

                    if ("SN".equals(value.getPavementType())) {
                        Double rqi = value.getRqi();
                        if (rqi != null) {
                            value.setRqiTcCode(StringUtil
                                    .rqiTcCode(value.getRqi()));
                        }
                        if (rqi != null) {
                            value.setRqiTcCode(StringUtil
                                    .rqiTcCode(value.getRqi()));
                        }
                    } else {
                        Double rqi = value.getRqi();
                        if (rqi != null) {
                            value.setRqiTcCode(StringUtil
                                    .rqiTcCode(value.getRqi()));
                        }
                    }
                    if (value.getPciFront() != null
                            && value.getRqi() != null
                            && value.getRdi() != null
                            && pbis != null
                            && (value.getSri() != null
                            || value.getPwi() != null)) {
                        BigDecimal pci = new BigDecimal(String.valueOf(value.getPciFront()));
                        BigDecimal rqi = new BigDecimal(String.valueOf(value.getRqi()));
                        BigDecimal rdi1 = new BigDecimal(String.valueOf(value.getRdi()));
                        BigDecimal pbi1 = new BigDecimal(String.valueOf(pbis));

                        int index = 0;
                        System.out.println(value);
                        Double pciWeight = StringUtil.getPqiWeight(value.getPavementType(), "pci");
                        BigDecimal pciMultiply = pci.multiply(new BigDecimal(pciWeight));
                        Double rqiWeight = StringUtil.getPqiWeight(value.getPavementType(), "rqi");
                        BigDecimal rqiMultiply = rqi.multiply(new BigDecimal(rqiWeight));
                        Double rdiWeight = StringUtil.getPqiWeight(value.getPavementType(), "rdi");
                        BigDecimal rdi1Multiply = rdi1.multiply(new BigDecimal(rdiWeight));
                        Double pbiWeight = StringUtil.getPqiWeight(value.getPavementType(), "pbi");
                        BigDecimal pbi1Multiply = pbi1.multiply(new BigDecimal(pbiWeight));
                        Double sriWeight = StringUtil.getPqiWeight(value.getPavementType(), "sri");

                        Double pwiWeight = StringUtil.getPqiWeight(value.getPavementType(), "pwi");

                        BigDecimal add = pciMultiply.add(rqiMultiply).add(rdi1Multiply).add(pbi1Multiply);
                        if (value.getSri() == null || value.getSri().doubleValue() == 0) {
                            BigDecimal pwi1 = new BigDecimal(String.valueOf(value.getPwi()));
                            BigDecimal pwi1Multiply = pwi1.multiply(new BigDecimal(pwiWeight));
                            add = add.add(pwi1Multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;
                        } else {
                            BigDecimal sri1 = new BigDecimal(String.valueOf(value.getSri()));
                            BigDecimal sri1Multiply = sri1.multiply(new BigDecimal(sriWeight));
                            add = add.add(sri1Multiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                            ;
                        }
                        value.setPqi(add.doubleValue());
                    }
                });
            }

            detail1000s.stream().filter(v -> v != null).forEach(detail1000 ->
            {
                Double sci = detail1000.getSci();
                if (sci == null) {
                    detail1000.setSci(100.0);
                    sci = 100.0;
                }
                if (detail1000.getTcs() == null) {
                    detail1000.setTcs("NULL");
                }
                Double bci = detail1000.getBci();
                if (bci == null) {
                    detail1000.setBci(100.0);
                    bci = 100.0;
                }
                Double tci = detail1000.getTci();
                if (tci == null) {
                    detail1000.setTci(100.0);
                    tci = 100.0;
                }
                if (detail1000.getPssi() == null || detail1000.getPssi().doubleValue() == 0)
                {
                    detail1000.setPssi(100.0);
                }
                if (detail1000.getPqi() != null) {
                    BigDecimal pqiMultiply =
                            new BigDecimal(String.valueOf(detail1000.getPqi()));
                    BigDecimal sciMultiply = new BigDecimal(String.valueOf(sci));
                    BigDecimal bciMultiply = new BigDecimal(String.valueOf(detail1000.getBci()));
                    BigDecimal tciMultiply = new BigDecimal(String.valueOf(tci));
                    BigDecimal pqiCountMultiply = pqiMultiply.multiply(new BigDecimal("0.7"));
                    BigDecimal sciCountMultiply = sciMultiply.multiply(new BigDecimal("0.08"));
                    BigDecimal bciCountMultiply = bciMultiply.multiply(new BigDecimal("0.12"));
                    BigDecimal tciCountMultiply = tciMultiply.multiply(new BigDecimal("0.10"));
                    BigDecimal add = pqiCountMultiply.add(sciCountMultiply).add(bciCountMultiply)
                            .add(tciCountMultiply).setScale(2, BigDecimal.ROUND_HALF_UP);
                    ;
                    detail1000.setMqi(add.doubleValue());
                    detail1000.setMqiTcCode(StringUtil.rdiTcCode(add.doubleValue()));
                }
                detail1000.setPciFrontTcCode(StringUtil.pciTcCode(detail1000.getPciFront()));
                if ("SN".equals(detail1000.getPavementType())) {
                    Double rqi = detail1000.getRqi();
                    if (rqi != null) {
                        detail1000.setRqiTcCode(StringUtil
                                .rqiTcCode(rqi));
                    }
                } else {
                    Double rqi = detail1000.getRqi();
                    if (rqi != null) {
                        detail1000.setRqiTcCode(StringUtil
                                .rqiLQTcCode(rqi));
                    }
                }
                if ("SN".equals(detail1000.getPavementType())) {
                    Double rdi1 = detail1000.getRdi();
                    if (rdi1 != null) {
                        detail1000.setRdiTcCode(StringUtil
                                .rdiTcCode(rdi1));
                    }else
                    {
                        detail1000.setRdi(new Double("100"));
                        detail1000.setRd(new Double(0));
                        detail1000.setRdiTcCode(StringUtil
                                .rdiTcCode(detail1000.getRdi()));
                    }
                } else {
                    Double rdi1 = detail1000.getRdi();
                    if (rdi1 != null) {
                        detail1000.setRdiTcCode(StringUtil
                                .rdiTcCode(rdi1));
                    }
                }
                detail1000.setPbiTcCode(StringUtil.rdiTcCode(detail1000.getPbi()));
                detail1000.setPwiTcCode(StringUtil.rdiTcCode(detail1000.getPwi()));
                detail1000.setSriTcCode(StringUtil.rdiTcCode(detail1000.getSri()));
                detail1000.setPssiTcCode(StringUtil.rdiTcCode(detail1000.getPssi()));
                detail1000.setSciTcCode(StringUtil.rdiTcCode(detail1000.getSci()));
                detail1000.setPqiTcCode(StringUtil.rdiTcCode(detail1000.getPqi()));
                detail1000.setBciTcCode(StringUtil.rdiTcCode(detail1000.getBci()));
                detail1000.setTciTcCode(StringUtil.rdiTcCode(detail1000.getTci()));
            });
        }catch (Exception e)
        {
            e.printStackTrace();
        }

    }

    private void setHunsComputer() {

    }

    public static class DssTypeInSideClazz {
        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public AllMqiListDataForkJoinImport build() {
            AllMqiListDataForkJoinImport dssTypeForkJoinImport = new AllMqiListDataForkJoinImport(detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0) {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0) {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0) {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

            return dssTypeForkJoinImport;
        }
    }
}
