package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PtcdCarDataForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<PtcdCarData> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public PtcdCarDataForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public PtcdCarDataForkJoinImport(List<PtcdCarData> list) {
        this.list = list;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        //处理PBI数据
        List<PtcdCarData> ptcdCarDataList = this.baseMapper.getPbiUnitData(orgId, Integer.valueOf(year));
        this.list = ptcdCarDataList;
        if (this.units != null && this.units.size() > 0)
        {
            setUnitsComputer();
        }
        if (this.huns != null && this.huns.size() > 0 && this.type == 0)
        {
            List<PtcdCarData> carData = this.baseMapper.getPbiHunsData(orgId, Integer.valueOf(year));
            setHunsComputer(carData);
        }
    }

    private void setUnitsComputer()
    {
        //计算PSSI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        List<PtcdCarData> dataNewList = this.list;

        dataNewList.stream().forEach(v ->
        {
            if (collect.containsKey(v.getUnitMargeId())) {
                TcaTcDetail1000 tcaTcDetail1000 = collect.get(v.getUnitMargeId());
                tcaTcDetail1000.setPbi(v.getPBI());
                if (this.year != null && tcaTcDetail1000.getYear() == null)
                {
                    tcaTcDetail1000.setYear(this.year.toString());
                }
            } else if (!collect.containsKey(v.getUnitMargeId())) {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setPbi(v.getPBI());
                tcaTcDetail1000.setUnitMargeId(v.getUnitMargeId());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setPavementType(v.getPavementType());
                tcaTcDetail1000.setLane(v.getLane());
                detail1000s.add(tcaTcDetail1000);
            }
        });
    }

    private void setHunsComputer(List<PtcdCarData> carData)
    {
        Map<String, TcaTcDetail100> collect = detail100s.stream().collect(Collectors
                .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));

        List<PtcdCarData> dataNewList = carData;

        dataNewList.stream().forEach(v ->
        {
            if (collect.containsKey(v.getUnitMargeId())) {
                TcaTcDetail100 tcaTcDetail100 = collect.get(v.getUnitMargeId());
                tcaTcDetail100.setPbi(v.getPBI());
                if (this.year != null && tcaTcDetail100.getYear() == null)
                {
                    tcaTcDetail100.setYear(this.year);
                }
            }
        });
    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public PtcdCarDataForkJoinImport build()
        {
            PtcdCarDataForkJoinImport dssTypeForkJoinImport = new PtcdCarDataForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0)
            {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

             return dssTypeForkJoinImport;
        }
    }
}
