package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail100;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DssTypeForkRevampJoinImport extends RecursiveTask<List<DssInfo>> {

    private PbdStreamMapper baseMapper;

    private List<DssInfo> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    private int delete = 0;

    public DssTypeForkRevampJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setDelete(int delete) {
        this.delete = delete;
    }

    public DssTypeForkRevampJoinImport(List<DssInfo> list) {
        this.list = list;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    @Override
    protected List<DssInfo> compute() {
        String usered = "21";
        if (this.delete == 1)
        {
            usered = "22";
        }

        //查询病害
        List<DssInfo> dssInfos = this.baseMapper.getDesionDssTypeList(orgId, "LMLQ", lineCode, year.toString(), usered);
        this.list = dssInfos;

        if (this.units != null && this.units.size() > 0 && this.delete == 1)
        {
            setUnitsComputer();
        }
        if (this.huns != null && this.huns.size() > 0 && this.delete == 0)
        {
            //查询百米病害数据情况
            List<DssInfo> dssInfosHun = this.baseMapper.getDssInfoHunLq(orgId, "LMLQ", year.toString());
            setHunsComputer(dssInfosHun);
        }
        return null;
    }

    private void setUnitsComputer()
    {
        //计算PCI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        sumPciZero(this.list, collect, detail1000s);
    }

    private void setHunsComputer(List<DssInfo> dssInfosHun)
    {
        Map<String, TcaTcDetail100> collect = detail100s.stream().collect(Collectors
                .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));
        sumPciCentury(dssInfosHun, collect, detail100s);
    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private List<DssInfo> list;

        private String orgId;

        private String lineCode;

        private String prjId;

        private int type;

        private int delete;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setType(int type) {
            this.type = type;
            return this;
        }

        public DssTypeInSideClazz setDelete(int delete) {
            this.delete = delete;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public DssTypeForkRevampJoinImport build()
        {
            DssTypeForkRevampJoinImport dssTypeForkJoinImport = new DssTypeForkRevampJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0)
            {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

            if (this.type == 1)
            {
                dssTypeForkJoinImport.setType(this.type);
            }
             return dssTypeForkJoinImport;
        }
    }

    public void sumPciCentury(List<DssInfo> dssInfos, Map<String, TcaTcDetail100> drUnit, List<TcaTcDetail100> detail100s)
    {
        Map<String, List<DssInfo>> collect2 = dssInfos.stream().filter(v
                -> !StringUtils.isBlank(v.getHunMargeId())).collect(Collectors.groupingBy(DssInfo::getHunMargeId));

        collect2.entrySet().stream().forEach(v ->
        {
            //根据病害类型分组
            List<DssInfo> value1 = v.getValue();
            Double v1 = getIndexArrays(value1);
            BigDecimal multiplyHundred = new BigDecimal("100");
            BigDecimal dr = multiplyHundred.multiply(new BigDecimal(String.valueOf(v1)))
                    .divide(value1.get(0).getLength().multiply(new BigDecimal("3.75")), 7, BigDecimal.ROUND_HALF_UP);
            TcaTcDetail100 tcaTcDetail100 = new TcaTcDetail100();
            BigDecimal hundred = new BigDecimal(String.valueOf("100"));
            BigDecimal power = new BigDecimal(Math.pow(dr.doubleValue(), 0.412)).setScale(15, BigDecimal.ROUND_HALF_UP);
            BigDecimal ten = new BigDecimal("15.00");
            BigDecimal multiply = ten.multiply(power);
            double pci = hundred.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            tcaTcDetail100.setPciBehind(pci);
            tcaTcDetail100.setDrBehind(pci);
            tcaTcDetail100.setHunMargeId(v.getKey());
            tcaTcDetail100.setTcDetailId(StringUtil.getUUID());
            tcaTcDetail100.setPrjId(value1.get(0).getRelTaskCode());
            tcaTcDetail100.setLane(value1.get(0).getLane());
            tcaTcDetail100.setPavementType(value1.get(0).getPavementType());
            System.out.println(value1.get(0).getStake() + ":" + pci);
            System.out.println(drUnit.containsKey(v.getKey()));
            if (!drUnit.containsKey(v.getKey())) {
                System.out.println(1);
                detail100s.add(tcaTcDetail100);
                drUnit.put(tcaTcDetail100.getUnitMargeId(), tcaTcDetail100);
            }else
            {
                System.out.println(2);
                TcaTcDetail100 tcaTcDetail10 = drUnit.get(v.getKey());
                tcaTcDetail10.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail10.setPrjId(value1.get(0).getRelTaskCode());
                tcaTcDetail10.setYear(value1.get(0).getYear());
                tcaTcDetail10.setLane(value1.get(0).getLane());
                tcaTcDetail10.setPavementType(value1.get(0).getPavementType());
                tcaTcDetail10.setPciBehind(pci);
                tcaTcDetail10.setDrBehind(dr.doubleValue());
                if (this.year != null && tcaTcDetail10 != null)
                {
                    tcaTcDetail10.setYear(this.year);
                }
            }
        });

        Map<String, List<DssInfo>> collectUnits = this.list.stream().filter(v
                -> !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors.groupingBy(DssInfo::getUnitMargeId));
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));
        collectUnits.entrySet().stream().forEach(v ->
        {
            List<DssInfo> value = v.getValue();
            Double v1 = getIndexArrays(value);
            BigDecimal multiplyHundred = new BigDecimal("100");
            BigDecimal dr = multiplyHundred.multiply(new BigDecimal(Double.toString(v1)))
                    .divide(value.get(0).getLength().multiply(new BigDecimal("3.75")), 6, BigDecimal.ROUND_HALF_UP);
            BigDecimal hundred = new BigDecimal("100");
            BigDecimal power = new BigDecimal(Math.pow(dr.doubleValue(), 0.412));
            BigDecimal ten = new BigDecimal("15.00");
            BigDecimal multiply = ten.multiply(power);
            double pci = hundred.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

            if (collect.containsKey(v.getKey()))
            {
                TcaTcDetail1000 tcaTcDetail1000 = collect.get(v.getKey());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setPrjId(value.get(0).getRelTaskCode());
                tcaTcDetail1000.setYear(String.valueOf(value.get(0).getYear()));
                tcaTcDetail1000.setLane(value.get(0).getLane());
                tcaTcDetail1000.setPavementType(value.get(0).getPavementType());
                tcaTcDetail1000.setPciBehind(pci);
                tcaTcDetail1000.setDrBehind(dr.doubleValue());
            }
        });
    }

    private Double getIndexArrays(List<DssInfo> value1)
    {
        Map<String, List<DssInfo>> collect = value1.stream().collect(Collectors.groupingBy(DssInfo::getDssType));
        List<DssInfo> dssInfos1 = collect.get("LMLQ-0001"); //龟裂
        double cheapLight = dssInfos1 == null ? 0 : dssInfos1.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double cheapCentre = dssInfos1 == null ? 0 : dssInfos1.stream().filter(vs -> "02".equals(StringUtil.codeDgree(StringUtil.codeDgree(vs.getDssDegree()))))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double cheapWeight = dssInfos1 == null ? 0 : dssInfos1.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack = collect.get("LMLQ-0002"); //块状裂缝
        double crackLight = crack == null ? 0 : crack.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crackCentre = crack == null ? 0 : crack.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack3 = collect.get("LMLQ-0003");//纵向裂缝
        double crack3Light = crack3 == null ? 0 : crack3.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack3Centre = crack3 == null ? 0 : crack3.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack4 = collect.get("LMLQ-0004");//横向裂缝
        double crack4Light = crack4 == null ? 0 : crack4.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack4Centre = crack4 == null ? 0 : crack4.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack5 = collect.get("LMLQ-0005");//坑槽
        double crack5Light = crack5 == null ? 0 : crack5.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack5Centre = crack5 == null ? 0 : crack5.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack6 = collect.get("LMLQ-0006");//松散
        double crack6Light = crack6 == null ? 0 : crack6.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack6Centre = crack6 == null ? 0 : crack6.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack7 = collect.get("LMLQ-0007");//沉陷
        double crack7Light = crack7 == null ? 0 : crack7.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack7Centre = crack7 == null ? 0 : crack7.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack8 = collect.get("LMLQ-0008");//车辙
        double crack8Light = crack8 == null ? 0 : crack8.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack8Centre = crack8 == null ? 0 : crack8.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> crack9 = collect.get("LMLQ-0009");//波浪拥包
        double crack9Light = crack9 == null ? 0 : crack9.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double crack9Centre = crack9 == null ? 0 : crack9.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> dssInfos2 = collect.get("LMLQ-0010");//泛油
        double crack10 = dssInfos2 == null ? 0 : dssInfos2.stream().map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> dssInfos3 = collect.get("LMLQ-0011");//块状修补
        double crack11 = dssInfos3 == null ? 0 : dssInfos3.stream().map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        List<DssInfo> dssInfos4 = collect.get("LMLQ-0012");//条状修补
        double crack12 = dssInfos4 == null ? 0 : dssInfos4.stream().map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
        double v1 = 0.1 * cheapLight + 0.1 * cheapCentre + 0.1 * cheapWeight + 0.1 * crackLight + 0.1 * crackCentre + 0.1 * crack3Light * 0.2 + 0.1 * crack3Centre * 0.2 + 0.1 * crack4Light * 0.2 + 0.1 * crack4Centre
                * 0.2 + 0.1 * crack5Light + 0.1 * crack5Centre +
                0.6 * crack6Light + 1 * crack6Centre + 0.6 * crack7Light + 1 * crack7Centre + 0.6 * crack8Light + 1 * crack8Centre + 0.6 * crack9Light + 1 * crack9Centre
                + 0.2 * crack10 + 0.1 * crack12 * 0.2 + 0.1 * crack11;

        return v1;
    }

    public final void sumPciZero(List<DssInfo> dssInfos, Map<String, TcaTcDetail1000> drUnit, List<TcaTcDetail1000> detail1000s)
    {
        Map<String, List<DssInfo>> collect2 = dssInfos.stream().collect(Collectors.groupingBy(DssInfo::getUnitMargeId));
        List<DssInfo> unitGroup = new ArrayList<DssInfo>();
        collect2.entrySet().stream().forEach(v ->
        {
            if ("536006f6-151f-4eda-b778-afd6bcfecbbb".equals(v.getKey()))
            {
                System.out.println(1);
            }
            //根据病害类型分组
            List<DssInfo> value1 = v.getValue();
            Map<String, List<DssInfo>> collect = value1.stream().collect(Collectors.groupingBy(DssInfo::getDssType));
            List<DssInfo> dssInfos1 = collect.get("LMLQ-0001");
            double cheapLight = dssInfos1 == null ? 0 : dssInfos1.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double cheapCentre = dssInfos1 == null ? 0 : dssInfos1.stream().filter(vs -> "02".equals(StringUtil.codeDgree(StringUtil.codeDgree(vs.getDssDegree()))))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double cheapWeight = dssInfos1 == null ? 0 : dssInfos1.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack = collect.get("LMLQ-0002");
            double crackLight = crack == null ? 0 : crack.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crackCentre = crack == null ? 0 : crack.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack3 = collect.get("LMLQ-0003");
            double crack3Light = crack3 == null ? 0 : crack3.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack3Centre = crack3 == null ? 0 : crack3.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack4 = collect.get("LMLQ-0004");
            double crack4Light = crack4 == null ? 0 : crack4.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack4Centre = crack4 == null ? 0 : crack4.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack5 = collect.get("LMLQ-0005");
            double crack5Light = crack5 == null ? 0 : crack5.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack5Centre = crack5 == null ? 0 : crack5.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack6 = collect.get("LMLQ-0006");
            double crack6Light = crack6 == null ? 0 : crack6.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack6Centre = crack6 == null ? 0 : crack6.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack7 = collect.get("LMLQ-0007");
            double crack7Light = crack7 == null ? 0 : crack7.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack7Centre = crack7 == null ? 0 : crack7.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack8 = collect.get("LMLQ-0008");
            double crack8Light = crack8 == null ? 0 : crack8.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack8Centre = crack8 == null ? 0 : crack8.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> crack9 = collect.get("LMLQ-0009");
            double crack9Light = crack9 == null ? 0 : crack9.stream().filter(vs -> "01".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double crack9Centre = crack9 == null ? 0 : crack9.stream().filter(vs -> "03".equals(StringUtil.codeDgree(vs.getDssDegree())))
                    .map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> dssInfos2 = collect.get("LMLQ-0010");
            double crack10 = dssInfos2 == null ? 0 : dssInfos2.stream().map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> dssInfos3 = collect.get("LMLQ-0011");
            double crack11 = dssInfos3 == null ? 0 : dssInfos3.stream().map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            List<DssInfo> dssInfos4 = collect.get("LMLQ-0012");
            double crack12 = dssInfos4 == null ? 0 : dssInfos4.stream().map(vs -> vs.getDr() == null ? 0 : vs.getDr().doubleValue()).mapToDouble(value -> value).sum();
            double v1 = 0.1 * cheapLight + 0.1 * cheapCentre + 0.1 * cheapWeight + 0.1 * crackLight + 0.1 * crackCentre + 0.1 * crack3Light * 0.2 + 0.1 * crack3Centre * 0.2 + 0.1 * crack4Light * 0.2 + 0.1 * crack4Centre
                    * 0.2 + 0.1 * crack5Light + 0.1 * crack5Centre +
                    0.6 * crack6Light + 1 * crack6Centre + 0.6 * crack7Light + 1 * crack7Centre + 0.6 * crack8Light + 1 * crack8Centre + 0.6 * crack9Light + 1 * crack9Centre
                    + 0.2 * crack10 + 0.1 * crack12 * 0.2 + 0.1 * crack11;
            BigDecimal multiplyHundred = new BigDecimal("100");
            BigDecimal dr = multiplyHundred.multiply(new BigDecimal(Double.toString(v1)))
                    .divide(value1.get(0).getLength().multiply(new BigDecimal("3.75")), 6, BigDecimal.ROUND_HALF_UP);
            BigDecimal hundred = new BigDecimal("100");
            BigDecimal power = new BigDecimal(Math.pow(dr.doubleValue(), 0.412));
            BigDecimal ten = new BigDecimal("15.00");
            BigDecimal multiply = ten.multiply(power);
            double pci = hundred.subtract(multiply).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            if (!drUnit.containsKey(v.getKey())) {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setPciBehind(pci);
                tcaTcDetail1000.setDrBehind(pci);
                tcaTcDetail1000.setUnitMargeId(v.getKey());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setPrjId(value1.get(0).getRelTaskCode());
                tcaTcDetail1000.setYear(String.valueOf(value1.get(0).getYear()));
                tcaTcDetail1000.setLane(value1.get(0).getLane());
                tcaTcDetail1000.setPavementType(value1.get(0).getPavementType());
                detail1000s.add(tcaTcDetail1000);
            }else
            {
                TcaTcDetail1000 tcaTcDetail1000 = drUnit.get(v.getKey());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setPrjId(value1.get(0).getRelTaskCode());
                tcaTcDetail1000.setYear(String.valueOf(value1.get(0).getYear()));
                tcaTcDetail1000.setLane(value1.get(0).getLane());
                tcaTcDetail1000.setPavementType(value1.get(0).getPavementType());
                tcaTcDetail1000.setPciBehind(pci);
                tcaTcDetail1000.setDrBehind(dr.doubleValue());
                if (this.year != null && StringUtils.isBlank(tcaTcDetail1000.getYear()))
                {
                    tcaTcDetail1000.setYear(this.year.toString());
                }
            }
        });
    }

    private static final String tcodeDegree(String degree)
    {
        return degree == null ? "01" : "重".equals(degree) ? "03" : "中".equals(degree) ? "02" : "轻".equals(degree) ? "01" : degree;
    }
}
