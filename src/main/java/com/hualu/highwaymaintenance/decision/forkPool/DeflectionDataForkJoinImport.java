package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementCallback;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class DeflectionDataForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<DeflectionData> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private JdbcTemplate jdbcTemplate;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public DeflectionDataForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public DeflectionDataForkJoinImport(List<DeflectionData> list) {
        this.list = list;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    protected void compute() {
        List<DeflectionData> deflectionDataList = getPssiListData(orgId, Integer.valueOf(year));
        this.list = deflectionDataList;
        if (this.units != null && this.units.size() > 0) {
            setUnitsComputer(deflectionDataList);
        }
    }


    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private void setUnitsComputer(List<DeflectionData> list) {
        //计算PSSI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().filter(distinctByKey(TcaTcDetail1000::getUnitMargeId)).collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));


        Map<String, List<DeflectionData>> collect1 = list.stream().collect(Collectors.groupingBy(v -> v.getUnitMargeId()));
        collect1.entrySet().forEach(v ->
        {
            List<DeflectionData> value = v.getValue();
            if (this.type == 0) {
                Map<String, TcaTcDetail100> hunCollect = detail100s.stream().collect(Collectors
                        .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));
                value.stream().forEach(unitSsr ->
                {
                    double asDouble = unitSsr.getSsr();
                    BigDecimal decimalUnit = new BigDecimal(100);
                    BigDecimal oneUnit = new BigDecimal("1");
                    BigDecimal twentyUnit = new BigDecimal("15.71");
                    BigDecimal expUnit = new BigDecimal(String.valueOf(Math.exp(-5.19 * asDouble)));

                    BigDecimal multiply = twentyUnit.multiply(expUnit).add(oneUnit);
                    BigDecimal divide = decimalUnit.divide(multiply, 2, BigDecimal.ROUND_HALF_UP);

                    if (hunCollect.containsKey(unitSsr.getHunMargeId())) {
                        TcaTcDetail100 detail100 = hunCollect.get(unitSsr.getHunMargeId());
                        detail100.setPssi(divide.doubleValue());
                        detail100.setSsr(new BigDecimal(asDouble).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                        if (this.year != null && detail100.getYear() != null) {
                            detail100.setYear(this.year);
                        }
                    }
                });
            }

            double v1 = value.stream().mapToDouble(ssr -> ssr.getSsr()).average().orElse(0);
            BigDecimal decimal = new BigDecimal(Double.toString(v1)).setScale(3, BigDecimal.ROUND_HALF_UP);

            double asDouble = decimal.doubleValue();
            BigDecimal decimalUnit = new BigDecimal(100);
            BigDecimal oneUnit = new BigDecimal("1");
            BigDecimal twentyUnit = new BigDecimal("15.71");
            BigDecimal expUnit = new BigDecimal(String.valueOf(Math.exp(-5.19 * asDouble)));

            BigDecimal multiply = twentyUnit.multiply(expUnit).add(oneUnit);
            BigDecimal divide = decimalUnit.divide(multiply, 2, BigDecimal.ROUND_HALF_UP);
            if (collect.containsKey(v.getKey())) {
                TcaTcDetail1000 detail1000 = collect.get(v.getKey());
                detail1000.setPssi(divide.doubleValue());
                detail1000.setSsr(new BigDecimal(asDouble).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                if (this.year != null && StringUtils.isBlank(detail1000.getYear())) {
                    detail1000.setYear(this.year.toString());
                }
            }

        });
        list.stream().forEach(v ->
        {
            double asDouble = v.getSsr();

        });
    }

    private void setHunsComputer(List<DeflectionData> deflectionDataList) {
    }

    public static class DssTypeInSideClazz {
        private JdbcTemplate jdbcTemplate;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private PbdStreamMapper baseMapper;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private List<DeflectionData> list;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setJdbcTemplate(JdbcTemplate jdbcTemplate) {
            this.jdbcTemplate = jdbcTemplate;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }

        public DeflectionDataForkJoinImport build() {
            DeflectionDataForkJoinImport dssTypeForkJoinImport = new DeflectionDataForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0) {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0) {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0) {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

            if (this.jdbcTemplate != null) {
                dssTypeForkJoinImport.setJdbcTemplate(jdbcTemplate);
            }

            return dssTypeForkJoinImport;
        }
    }

    private List<DeflectionData> getPssiListData(String orgId, Integer year) {
        String sql = "select d.prj_id as prjId,\n" +
                "                d.lane,\n" +
                "                u.ROUTECODE as routeCode,\n" +
                "        round(HUN.design_ld / (avg(d.backman_m) + 1.645 * STDDEV(d.backman_m)), 2) as ssr,\n" +
                "        u.unit_marge_id as unitMargeId,\n" +
                "                u.HUN_MARGE_ID as hunMargeId,\n" +
                "        max(u.year)                                                              as years,\n" +
                "        u.pavement_type as pavementType\n" +
                "        from ptcmsdb.PBD_STRUCT_INTRVL_HUN_DESION u\n" +
                "        join ptcmsdb.PTCD_DEFLECTION_DATA_DESION d\n" +
                "        on u.ROUTECODE=d.ROUTECODE\n" +
                "        JOIN PTCMSDB.PBD_STRUCT_INTRVL_UNIT_NEW HUN ON HUN.UNIT_MARGE_ID=U.UNIT_MARGE_ID\n" +
                "        JOIN GDGS.BASE_ROUTE_LOGIC L ON U.ROUTECODE=L.ROUTE_CODE\n" +
                "        where (d.STAKE = u.start_stake or (d.STAKE - u.start_stake) * (d.STAKE - u.end_stake) < 0)\n" +
                "        AND L.OPRT_ORG_CODE=? AND d.year=?\n" +
                "        and d.lane = u.lane and U.LANE IN ('A','B') AND U.USERED=22\n" +
                "        group by u.pavement_type,u.unit_marge_id,u.HUN_MARGE_ID, d.prj_id, u.ROUTECODE, d.lane, u.length,HUN.design_ld";
        List<DeflectionData> execute = this.jdbcTemplate.execute(sql, new PreparedStatementCallback<List<DeflectionData>>() {
            @Override
            public List<DeflectionData> doInPreparedStatement(PreparedStatement ps) throws SQLException, DataAccessException {
                ps.setString(1, orgId);
                ps.setInt(2, year);
                ResultSet resultSet = ps.executeQuery();
                List<DeflectionData> deflectionDataList = new ArrayList<DeflectionData>();
                while (resultSet.next()) {
                    DeflectionData deflectionData = new DeflectionData();
                    String prjId = resultSet.getString("prjId");
                    String lane = resultSet.getString("lane");
                    String routeCode = resultSet.getString("routeCode");
                    double ssr = resultSet.getDouble("ssr");
                    String unitMargeId = resultSet.getString("unitMargeId");
                    String hunMargeId = resultSet.getString("hunMargeId");
                    String pavementType = resultSet.getString("pavementType");
                    deflectionData.setSsr(ssr);
                    deflectionData.setPrjId(prjId);
                    deflectionData.setRouteCode(routeCode);
                    deflectionData.setLane(lane);
                    deflectionData.setUnitMargeId(unitMargeId);
                    deflectionData.setHunMargeId(hunMargeId);
                    deflectionData.setPavementType(pavementType);
                    deflectionDataList.add(deflectionData);
                }
                return deflectionDataList;
            }
        });
        return execute;
    }
}
