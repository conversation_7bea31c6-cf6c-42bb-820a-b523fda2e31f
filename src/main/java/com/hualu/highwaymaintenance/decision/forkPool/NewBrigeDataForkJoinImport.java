package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.util.StringUtil;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class NewBrigeDataForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<Map<String, String>> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public NewBrigeDataForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public NewBrigeDataForkJoinImport(List<Map<String, String>> list) {
        this.list = list;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        try {
            List<Map<String, String>> newBrigeBci = this.baseMapper.getNewBrigeBci(Integer.valueOf(year));
            this.list = newBrigeBci;
            if (this.units != null && this.units.size() > 0)
            {

                setUnitsComputer();
            }
            if (this.huns != null && this.huns.size() > 0)
            {
                setHunsComputer();
            }
        }catch (Exception e)
        {

        }
    }

    private void setUnitsComputer()
    {
        //计算BCI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        List<Map<String, String>> newBrigeBci = this.list;

        Map<String, List<Map<String, String>>> line_direct = newBrigeBci.stream().collect(Collectors.groupingBy(vv -> vv.get("ROUTE_CODE")));
        line_direct.entrySet().stream().forEach(brige ->
        {
            List<Map<String, String>> value1 = brige.getValue();
            Map<String, List<Map<String, String>>> type1 = value1.stream().collect(Collectors.groupingBy(v -> v.get("UNIT_MARGE_ID")));
            type1.entrySet().stream().forEach(v ->
            {
                Map<String, String> maps = new HashMap<String, String>();
                final Double[] score = {new Double(0.0)};
                String type2 = null;
                List<Map<String, String>> value = v.getValue();
                value.stream().forEach(vs ->
                {
                    int code2 = Integer.valueOf(vs.get("CODE2"));
                    int code3 = Integer.valueOf(vs.get("CODE3"));
                    int code4 = Integer.valueOf(vs.get("CODE4"));
                    int code5 = Integer.valueOf(vs.get("CODE5"));
                    int i = ((code2 * 10) + (code3 * 40) + (code4 * 70) + (code5 * 100));
                    BigDecimal startStake = new BigDecimal(vs.get("START_STAKE"));
                    BigDecimal endStake = new BigDecimal(vs.get("END_STAKE"));
                    BigDecimal multiply = startStake.subtract(endStake).abs().multiply(new BigDecimal("1000"));
                    BigDecimal complate = new BigDecimal(i * 1000);
                    BigDecimal divide = complate.divide(multiply, 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal hundred = new BigDecimal("100");
                    BigDecimal subtract = hundred.subtract(divide).setScale(2, BigDecimal.ROUND_HALF_UP);
                    double v1 = subtract.doubleValue() < 0 ? 0 : subtract.doubleValue();
                    maps.put("type", type2);
                    Double oldscore = score[0];
                    if (v1 > oldscore.doubleValue())
                    {
                        score[0] = v1;
                    }
                });
                if (collect.containsKey(v.getKey())) {
                    TcaTcDetail1000 tcaTcDetail1000 = collect.get(v.getKey());
                    tcaTcDetail1000.setBci(score[0].doubleValue());
                    tcaTcDetail1000.setStructIntrvlUid(maps.get("type"));
                    if (this.year != null && tcaTcDetail1000.getYear() == null)
                    {
                        tcaTcDetail1000.setYear(this.year.toString());
                    }
                }
            });

        });


    }

    private void setHunsComputer()
    {

    }

    public static class DssTypeInSideClazz
    {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public NewBrigeDataForkJoinImport build()
        {
            NewBrigeDataForkJoinImport dssTypeForkJoinImport = new NewBrigeDataForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

             return dssTypeForkJoinImport;
        }
    }
}
