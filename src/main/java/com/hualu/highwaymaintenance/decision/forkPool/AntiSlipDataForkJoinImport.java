package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.*;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class AntiSlipDataForkJoinImport extends RecursiveAction {

    private List<AntiSlipData> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private List<TcaTcDetail100> detail100s;

    private PbdStreamMapper baseMapper;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public AntiSlipDataForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public AntiSlipDataForkJoinImport(List<AntiSlipData> list) {
        this.list = list;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setDetail100s(List<TcaTcDetail100> detail100s) {
        this.detail100s = detail100s;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        //SRI
        List<AntiSlipData> sriUnitData = this.baseMapper.getSriUnitData(orgId, Integer.valueOf(year));
        if (sriUnitData == null || sriUnitData.size() == 0) {
            sriUnitData = this.baseMapper.getSriUnitData(orgId, Integer.valueOf(year) - 1);
        }
        if (this.type == 0)
        {
           // listRutGroupUnitRange(sriUnitData, this.huns);
            listRutGroupUnitBinaryRange(sriUnitData, this.huns);
        }else
        {
            listRutGroupUnitRange(sriUnitData, this.units);
        }
        this.list = sriUnitData;

        setHunsComputer(sriUnitData);
    }

    private void setUnitsComputer(List<AntiSlipData> list)
    {
        //计算SRI情况
        Map<String, TcaTcDetail1000> collect = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        List<AntiSlipData> dataNewList = new ArrayList<AntiSlipData>();
        Map<String, List<AntiSlipData>> rutt = list.stream().filter(v -> !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors.groupingBy(AntiSlipData::getUnitMargeId));
        rutt.entrySet().stream().forEach(v ->
        {
            AntiSlipData AntiSlipData = new AntiSlipData();
            List<com.hualu.highwaymaintenance.decision.entity.AntiSlipData> value = v.getValue();
            BigDecimal iriAvg = new BigDecimal(Double.toString(value.stream().map(vv
                    -> vv.getSfcM().doubleValue()).mapToDouble(vv -> vv.doubleValue()).average().orElse(0)));
            AntiSlipData.setSfcM(iriAvg.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            com.hualu.highwaymaintenance.decision.entity.AntiSlipData tempNess = value.stream().findFirst().get();
            AntiSlipData.setUnitMargeId(tempNess.getUnitMargeId());
            dataNewList.add(AntiSlipData);
        });

        dataNewList.stream().forEach(v ->
        {
            double asDouble = v.getSfcM();
            BigDecimal decimalUnit = new BigDecimal(100 - 35);
            BigDecimal oneUnit = new BigDecimal("1");
            BigDecimal twentyUnit = new BigDecimal("28.6");
            BigDecimal expUnit = new BigDecimal(String.valueOf(Math.exp(-0.105 * asDouble)));

            BigDecimal multiplyUnit = twentyUnit.multiply(expUnit).add(oneUnit);
            BigDecimal add = decimalUnit.divide(multiplyUnit, 2, BigDecimal.ROUND_HALF_UP)
                    .add(new BigDecimal("35")).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (collect.containsKey(v.getUnitMargeId())) {
                TcaTcDetail1000 detail1000 = collect.get(v.getUnitMargeId());
                detail1000.setSri(add.doubleValue());
                detail1000.setSfc(asDouble);
                if (this.year != null && StringUtils.isBlank(detail1000.getYear()))
                {
                    detail1000.setYear(this.year.toString());
                }
            } else if (!collect.containsKey(v.getUnitMargeId())) {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setSri(add.doubleValue());
                tcaTcDetail1000.setUnitMargeId(v.getUnitMargeId());
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setSfc(asDouble);
                detail1000s.add(tcaTcDetail1000);
            }
        });
    }

    private void setHunsComputer(List<AntiSlipData> list)
    {
        Map<String, TcaTcDetail1000> cacheUnits = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        Map<String, List<AntiSlipData>> collect = null;
        if (this.type == 0)
        {
            collect = list.stream().filter(v -> !StringUtils.isBlank(v.getHunMargeId())
                    && !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors.groupingBy(v -> v.getUnitMargeId()));
        }else if(this.type == 1)
        {
            collect = list.stream().filter(v -> !StringUtils.isBlank(v.getUnitMargeId())).collect(Collectors.groupingBy(v -> v.getUnitMargeId()));
        }
        collect.entrySet().stream().forEach(v ->
        {
            List<AntiSlipData> value = v.getValue();
            List<Double> lists = new ArrayList<>();
            if (this.type == 0)
            {
                Map<String, TcaTcDetail100> cache = detail100s.stream().collect(Collectors
                        .toMap(TcaTcDetail100::getHunMargeId, Function.identity()));
                Map<String, List<AntiSlipData>> collect1 = value.stream().collect(Collectors.groupingBy(huns -> huns.getHunMargeId()));
                collect1.entrySet().stream().forEach(tcDetail ->
                {
                    List<AntiSlipData> value1 = tcDetail.getValue();
                    double sfcM = value1.stream().mapToDouble(sfc -> sfc.getSfcM()).average().getAsDouble();
                    lists.add(sfcM);

                    BigDecimal decimal = new BigDecimal(100 - 35);
                    BigDecimal one = new BigDecimal("1");
                    BigDecimal twenty = new BigDecimal("28.6");
                    BigDecimal exp = new BigDecimal(String.valueOf(Math.exp(-0.105 * sfcM)));

                    BigDecimal multiply = twenty.multiply(exp).setScale(4, BigDecimal.ROUND_HALF_UP).add(one);
                    BigDecimal add = decimal.divide(multiply, 2, BigDecimal.ROUND_HALF_UP).add(
                            new BigDecimal("35")).setScale(2, BigDecimal.ROUND_HALF_UP);
                    if (cache.containsKey(tcDetail.getKey())) {
                        TcaTcDetail100 tcaTcDetail100 = cache.get(tcDetail.getKey());
                        tcaTcDetail100.setSri(add.doubleValue());
                        tcaTcDetail100.setSfc(sfcM);
                    }
                });
            }
            double sfcM = 0;
            if (this.type == 0)
            {
                sfcM = lists.stream().mapToDouble(sfc -> sfc.doubleValue()).average().getAsDouble();
            }else if (this.type == 1)
            {
                sfcM = value.stream().mapToDouble(sfc -> sfc.getSfcM()).average().orElse(0);
            }
            BigDecimal decimal = new BigDecimal(100 - 35);
            BigDecimal one = new BigDecimal("1");
            BigDecimal twenty = new BigDecimal("28.6");
            BigDecimal exp = new BigDecimal(String.valueOf(Math.exp(-0.105 * sfcM)));

            BigDecimal multiply = twenty.multiply(exp).setScale(4, BigDecimal.ROUND_HALF_UP).add(one);
            BigDecimal add = decimal.divide(multiply, 2, BigDecimal.ROUND_HALF_UP).add(
                    new BigDecimal("35")).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (cacheUnits.containsKey(v.getKey())) {
                TcaTcDetail1000 tcaTcDetail100 = cacheUnits.get(v.getKey());
                tcaTcDetail100.setSri(add.doubleValue());
                tcaTcDetail100.setSfc(sfcM);
            }
        });
    }

    public static class DssTypeInSideClazz
    {
        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<TcaTcDetail100> detail100s;

        private PbdStreamMapper baseMapper;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public DssTypeInSideClazz setDetail100s(List<TcaTcDetail100> detail100s) {
            this.detail100s = detail100s;
            return this;
        }


        public AntiSlipDataForkJoinImport build()
        {
            AntiSlipDataForkJoinImport dssTypeForkJoinImport = new AntiSlipDataForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0)
            {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0)
            {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            if (this.detail100s != null && this.detail100s.size() > 0)
            {
                dssTypeForkJoinImport.setDetail100s(this.detail100s);
            }

             return dssTypeForkJoinImport;
        }
    }


    private <T extends RangeData> void listRutGroupUnitRange(List<T> list, List<? extends PbdStructParent> unis) {
        Map<String, List<PbdStructParent>> collect = new ConcurrentHashMap(unis.parallelStream()
                .collect(Collectors.groupingBy(PbdStructParent::getRouteCode)));
        Map<String, List<T>> checkRange = list.parallelStream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(RangeData::getRouteCode));

        collect.entrySet().parallelStream().forEach(v ->
        {
            List<T> ruttingData = checkRange.get(v.getKey());
            if (ruttingData != null)
            {
                List<PbdStructParent> value = v.getValue();
                // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
                Collections.sort(value, Comparator.comparing(PbdStructParent::getStartStake));
                System.out.println(ruttingData);
                ruttingData.parallelStream().forEach(ness ->
                {
                    Double startStake = ness.getStartStake();
                    int idx = Collections.binarySearch(value, new PbdStructIntrvlHun(startStake), Comparator.comparing(vv -> vv.getStartStake()));
                    if (idx < 0) {
                        idx = -idx - 2;
                    }
                    try {
                        if (idx < value.size() && isInRange(value.get(idx), startStake)) {
                            ness.setUnitMargeId(value.get(idx).getUnitMargeId());
                            ness.setHunMargeId(value.get(idx).getHunMargeId());
                        }
                    }catch (ArrayIndexOutOfBoundsException e)
                    {
                        e.printStackTrace();
                    }
                });
            }
        });
    }

    private boolean isInRange(PbdStructParent unit, Double startStake) {
        BigDecimal milestoneStart = BigDecimal.valueOf(startStake);
        BigDecimal rangeStart = BigDecimal.valueOf(unit.getStartStake());
        BigDecimal rangeEnd = BigDecimal.valueOf(unit.getEndStake());
        return milestoneStart.compareTo(rangeStart) >= 0 && milestoneStart.compareTo(rangeEnd) <= 0;
    }

    private <T extends RangeData> void listRutGroupUnitBinaryRange(List<T> list, List<? extends PbdStructParent> unis) {
        Map<String, List<PbdStructParent>> collect = unis.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(PbdStructParent::getRouteCode));

        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode()))
                .collect(Collectors.groupingBy(RangeData::getRouteCode));

        // 创建CompletableFuture列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        try {
            for (Map.Entry<String, List<PbdStructParent>> entry : collect.entrySet()) {
                String routeCode = entry.getKey();
                List<RangeData> ruttingData = checkRange.get(routeCode);
                List<PbdStructParent> value = entry.getValue();

                futures.add(CompletableFuture.runAsync(() -> {
                    // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
                    Collections.sort(value, Comparator.comparing(PbdStructParent::getStartStake));
                    ruttingData.forEach(ness -> {
                        Double startStake = ness.getStartStake();
                        int index = binarySearch(value, startStake);
                        if (index >= 0) {
                            PbdStructParent unit = value.get(index);
                            if (unit != null && !StringUtils.isBlank(unit.getUnitMargeId())) {
                                ness.setHunMargeId(unit.getHunMargeId());
                                ness.setUnitMargeId(unit.getUnitMargeId());
                            }
                        }
                    });
                }));
                // 等待所有CompletableFuture执行完毕
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            }
        }catch (Exception e)
        {

        }
    }

    // 使用二分法查找目标桩号所在的位置
    private int binarySearch(List<PbdStructParent> list, Double target) {
        int low = 0;
        int high = list.size() - 1;

        while (low <= high) {
            int mid = low + (high - low) / 2;
            PbdStructParent current = list.get(mid);
            Double currentStartStake = current.getStartStake();

            if (currentStartStake.equals(target)) {
                return mid;
            } else if (currentStartStake < target) {
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }

        // 返回最接近目标桩号的位置
        return high;
    }




    /*private <T extends RangeData> void listRutGroupUnitRange(List<T> list, List<PbdStreamUnit> unis) {
        Map<String, List<PbdStreamUnit>> collect = unis.stream().collect(Collectors.groupingBy(PbdStreamUnit::getRouteCode));
        Map<String, List<RangeData>> checkRange = list.stream()
                .filter(v -> !StringUtils.isBlank(v.getRouteCode())).collect(Collectors.groupingBy(RangeData::getRouteCode));

        collect.entrySet().stream().forEach(v ->
        {
            List<RangeData> ruttingData = checkRange.get(v.getKey());
            List<PbdStreamUnit> value = v.getValue();
            // 将PbdStreamUnit列表按照其里程范围的起点桩号从小到大排序
            Collections.sort(value, Comparator.comparing(PbdStreamUnit::getStartStake));
            ruttingData.stream().forEach(ness ->
            {
                Double startStake = ness.getStartStake();
                PbdStreamUnit unit = value.stream().filter(dss -> isSubMilestoneInRange(dss, startStake)).findFirst().orElse(new PbdStreamUnit());
                if (unit != null && !StringUtils.isBlank(unit.getUnitMargeId())) {
                    ness.setUnitMargeId(unit.getUnitMargeId());
                }
            });
        });
    }

    //匹配算法
    private boolean isSubMilestoneInRange(PbdStreamUnit unit, Double startStake) {
        BigDecimal milestoneStart = new BigDecimal(Double.toString(startStake));
        BigDecimal rangeStart = new BigDecimal(Double.toString(unit.getStartStake()));
        BigDecimal rangeEnd = new BigDecimal(Double.toString(unit.getEndStake()));
        BigDecimal subtractEnd = milestoneStart.subtract(rangeEnd);
        BigDecimal rangeStartSubtract = milestoneStart.subtract(rangeStart);
        BigDecimal multiply = subtractEnd.multiply(rangeStartSubtract);
        if (multiply.doubleValue() < 0 || startStake.doubleValue() == rangeStart.doubleValue()) {
            return true;
        } else {
            return false;
        }
    }*/
}
