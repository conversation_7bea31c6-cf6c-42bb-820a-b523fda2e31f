package com.hualu.highwaymaintenance.decision.forkPool;

import com.hualu.highwaymaintenance.decision.entity.PbdStreamUnit;
import com.hualu.highwaymaintenance.decision.entity.PbdStructIntrvlHun;
import com.hualu.highwaymaintenance.decision.entity.TcaTcDetail1000;
import com.hualu.highwaymaintenance.decision.mapper.PbdStreamMapper;
import com.hualu.highwaymaintenance.module.task.domain.DssInfo;
import com.hualu.highwaymaintenance.util.MapUtil;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RecursiveAction;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TciTypeListDataForkJoinImport extends RecursiveAction {

    private PbdStreamMapper baseMapper;

    private List<DssInfo> list;

    private List<PbdStreamUnit> units;

    private List<PbdStructIntrvlHun> huns;

    private List<TcaTcDetail1000> detail1000s;

    private String orgId;

    private String lineCode;

    private String prjId;

    private Integer year;

    private int type = 0;

    public TciTypeListDataForkJoinImport(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
        this.baseMapper = baseMapper;
        this.detail1000s = detail1000s;
        this.orgId = orgId;
        this.lineCode = lineCode;
        this.prjId = prjId;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public TciTypeListDataForkJoinImport(List<DssInfo> list) {
        this.list = list;
    }

    public void setUnits(List<PbdStreamUnit> units) {
        this.units = units;
    }

    public void setHuns(List<PbdStructIntrvlHun> huns) {
        this.huns = huns;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    protected void compute() {
        List<DssInfo> tciData = this.baseMapper.getTciData(orgId, Integer.valueOf(year), this.type);
        this.list = tciData;
        if (this.units != null && this.units.size() > 0) {
            setUnitsComputer();
        }
    }

    private void setUnitsComputer() {
        //计算TCI情况
        Map<String, TcaTcDetail1000> cache = detail1000s.stream().collect(Collectors
                .toMap(TcaTcDetail1000::getUnitMargeId, Function.identity()));

        List<DssInfo> dssInfos = this.list;
        Map<String, List<DssInfo>> collect2 = dssInfos.stream().collect(Collectors.groupingBy(DssInfo::getUnitMargeId));
        collect2.entrySet().stream().forEach(v ->
        {
            //根据病害类型分组
            List<DssInfo> value1 = v.getValue();
            value1.stream().forEach(vv -> {
                String drainDssType = vv.getDssType();
                vv.setDssType(MapUtil.filterJADssTypeGroup(drainDssType));

            });
            Map<String, List<DssInfo>> collect = value1.stream().collect(Collectors.groupingBy(DssInfo::getDssType));
            double cheapLight = getDrSum(collect.get("JA-0001"), "01");
            double cheapWeight = getDrSum(collect.get("JA-0001"), "03");
            double crackLight = getDrSum(collect.get("JA-0002"), "01");
            double crack3Light = getDrSum(collect.get("JA-0003"), "01");
            double crack4Light = getDrSum(collect.get("JA-0004"), "01");
            double crack5Dou = getDrSum(collect.get("JA-0005"), null);

            BigDecimal decimal = null;
            if ((cheapLight * 10 + cheapWeight * 30) <= 100) {
                decimal = new BigDecimal(cheapLight * 10 + cheapWeight * 30);
            } else {
                decimal = new BigDecimal("100");
            }
            BigDecimal crackLe = null;
            if ((crackLight * 20) <= 100) {
                crackLe = new BigDecimal(crackLight * 20);
            } else {
                crackLe = new BigDecimal("100");
            }
            BigDecimal crackSign = null;
            if ((crack3Light * 20) <= 100) {
                crackSign = new BigDecimal(crack3Light * 20);
            } else {
                crackSign = new BigDecimal("100");
            }
            BigDecimal crackString = null;
            if (crack4Light > 0 && crack4Light < 10) {
                crackString = new BigDecimal(10 * 0.1);
            } else if ((crack4Light * 0.1) <= 100) {
                crackString = new BigDecimal(crack4Light * 0.1);
            } else if ((crack4Light * 0.1) > 100) {
                crackString = new BigDecimal("100");
            }
            BigDecimal crackGreed = null;
            if (crack5Dou > 0 && crack5Dou < 10) {
                crackGreed = new BigDecimal(10 * 0.1);
            } else if ((crack5Dou * 0.1) <= 100) {
                crackGreed = new BigDecimal(crack5Dou * 0.1);
            } else if ((crack5Dou * 0.1) > 100) {
                crackGreed = new BigDecimal("100");
            }
            BigDecimal multiply1 = new BigDecimal("100").subtract(decimal).multiply(new BigDecimal("0.25"));
            BigDecimal crackLe1 = new BigDecimal("100").subtract(crackLe).multiply(new BigDecimal("0.1"));
            BigDecimal crackLeSign = new BigDecimal("100").subtract(crackSign).multiply(new BigDecimal("0.25"));
            BigDecimal crackLeString = new BigDecimal("100").subtract(crackString).multiply(new BigDecimal("0.2"));
            BigDecimal crackLeGreed = new BigDecimal("100").subtract(crackGreed).multiply(new BigDecimal("0.2"));
            BigDecimal tci = multiply1.add(crackLe1).add(crackLeSign).add(crackLeString).add(crackLeGreed);

            if (cache.containsKey(v.getKey())) {
                TcaTcDetail1000 tcaTcDetail1000 = cache.get(v.getKey());
                tcaTcDetail1000.setTci(tci.doubleValue());
                if (this.year != null && StringUtils.isBlank(tcaTcDetail1000.getYear()))
                {
                    tcaTcDetail1000.setYear(this.year.toString());
                }
            } else {
                TcaTcDetail1000 tcaTcDetail1000 = new TcaTcDetail1000();
                tcaTcDetail1000.setTci(tci.doubleValue());
                tcaTcDetail1000.setUnitMargeId(v.getKey());
                tcaTcDetail1000.setYear(String.valueOf(value1.get(0).getYear()));
                tcaTcDetail1000.setTcDetailId(StringUtil.getUUID());
                tcaTcDetail1000.setPavementType(value1.get(0).getPavementType());
                tcaTcDetail1000.setPrjId(value1.get(0).getRelTaskCode());
                tcaTcDetail1000.setLane(value1.get(0).getLane());
                detail1000s.add(tcaTcDetail1000);
            }
        });
    }

    private double getDrSum(List<DssInfo> dssInfos, String degreeCode) {
        if (dssInfos == null) {
            return 0.0;
        }
        return dssInfos.stream()
                .filter(vs -> degreeCode == null || degreeCode.equals(MapUtil.tcodeDegree(MapUtil.tcodeDegree(vs.getDssDegree()))))
                .mapToDouble(vs -> vs.getDr() == null ? 0.0 : vs.getDr().doubleValue())
                .sum();
    }

    private void setHunsComputer() {

    }

    public static class DssTypeInSideClazz {
        private PbdStreamMapper baseMapper;

        private List<PbdStreamUnit> units;

        private List<PbdStructIntrvlHun> huns;

        private List<TcaTcDetail1000> detail1000s;

        private List<DssInfo> list;

        private String orgId;

        private String lineCode;

        private String prjId;

        public DssTypeInSideClazz(PbdStreamMapper baseMapper, List<TcaTcDetail1000> detail1000s, String orgId, String lineCode, String prjId) {
            this.baseMapper = baseMapper;
            this.detail1000s = detail1000s;
            this.orgId = orgId;
            this.lineCode = lineCode;
            this.prjId = prjId;
        }

        public DssTypeInSideClazz setUnits(List<PbdStreamUnit> units) {
            this.units = units;
            return this;
        }

        public DssTypeInSideClazz setHuns(List<PbdStructIntrvlHun> huns) {
            this.huns = huns;
            return this;
        }

        public TciTypeListDataForkJoinImport build() {
            TciTypeListDataForkJoinImport dssTypeForkJoinImport = new TciTypeListDataForkJoinImport(baseMapper, detail1000s, orgId, lineCode, prjId);
            if (this.units != null && this.units.size() > 0) {
                dssTypeForkJoinImport.setUnits(this.units);
            }

            if (this.huns != null && this.huns.size() > 0) {
                dssTypeForkJoinImport.setHuns(this.huns);
            }

            return dssTypeForkJoinImport;
        }
    }
}
