package com.hualu.highwaymaintenance;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hualu.highwaymaintenance.common.exception.BusinessException;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.util.JwtUtil;
import com.hualu.highwaymaintenance.util.SystemConstant;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.AsyncHandlerInterceptor;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @description:
 * @author: JDS
 * @time: 2022/10/20
 */
public class AppInterceptor implements AsyncHandlerInterceptor {

  @Override
  public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response,
      @NotNull Object handler) {
    String token = request.getHeader("Authorization");
    boolean isApp = "1".equals(request.getHeader("AppClient"));
    if (!isApp && (request.getRequestURI().contains("swagger") ||
        request.getRequestURI().contains("api-docs") ||
        request.getRequestURI().contains("decision") ||
        request.getRequestURI().contains("highwayWeb") ||
        request.getRequestURI().contains("highwayApp"))) {
      return true;
    }

    if (StringUtils.isBlank(token)) {
      //给app端使用这个头表示授权失败，跳转登录页
      response.addHeader("user_not_auth", "0");
      throw new BusinessException("用户授权认证没有通过!客户端请求参数中无token信息",
          RestResult.OAUTH_FAIL);
    } else {
      Map<String, Object> tokenMap = JwtUtil.unsign(token, Map.class);
      if (null != tokenMap && !tokenMap.isEmpty() &&
          tokenMap.get(SystemConstant.USER_CODE) != null) {
        return true;
      } else {
        //给app端使用这个头表示授权失败，跳转登录页
        response.addHeader("user_not_auth", "0");
        throw new BusinessException("用户授权认证没有通过!客户端请求参数token信息无效",
            RestResult.OAUTH_FAIL);
      }
    }
  }
}
