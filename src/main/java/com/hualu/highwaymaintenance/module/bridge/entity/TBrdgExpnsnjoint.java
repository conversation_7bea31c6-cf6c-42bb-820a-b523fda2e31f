package com.hualu.highwaymaintenance.module.bridge.entity;


import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.type.JdbcType;


import java.util.Date;

/**
 * 全新版本伸缩缝
 */
@ApiModel(value = "全新版本伸缩缝")
@TableName(value = "BCTCMSDB.T_BRDG_EXPNSNJOINT")
public class TBrdgExpnsnjoint {

  @TableField(value = "BRIDGE_NAME")
  @ExcelProperty(value = "桥梁名称")
  private String bridgeName;
  @TableField(exist =false)
  @ExcelProperty(value = "路线类型")
  private String lineType;

  public String getLineType() {
    return lineType;
  }

  public void setLineType(String lineType) {
    this.lineType = lineType;
  }

  @TableField(value = "PRODUCER")
  @ExcelProperty(value = "生产产家")
  private String producer;
  @TableField(value = "IMAGE_ID")
  @ExcelProperty(value = "imageId")
  private String imageId;
  @TableField(value = "SSF_GAP")
  @ExcelProperty(value = "伸缩缝缝宽")
  private String ssfGap;

  public String getSsfGap() {
    return ssfGap;
  }

  public void setSsfGap(String ssfGap) {
    this.ssfGap = ssfGap;
  }

  public String getImageId() {
    return imageId;
  }

  public void setImageId(String imageId) {
    this.imageId = imageId;
  }

  public String getProducer() {
    return producer;
  }

  public void setProducer(String producer) {
    this.producer = producer;
  }

  @TableField(value = "LINE_CODE")
  @ExcelProperty(value = "路线编号")
  private String lineCode;
  public String getBridgeName() {
    return bridgeName;
  }

  public void setBridgeName(String bridgeName) {
    this.bridgeName = bridgeName;
  }

  /**
   * 伸缩缝方向
   */
  @TableField(exist = false)
  @ExcelProperty(value = "伸缩缝方向")
  @ExcelSelected(source = {"横向","纵向"})
  private String ssfDirectStr;
  @TableField(exist = false)
  @ExcelProperty(value = "伸缩缝类型")
  private String ssfTypeStr;

  public String getSsfDirectStr() {
    return ssfDirectStr;
  }

  public void setSsfDirectStr(String ssfDirectStr) {
    this.ssfDirectStr = ssfDirectStr;
  }

  public String getSsfTypeStr() {
    return ssfTypeStr;
  }

  public void setSsfTypeStr(String ssfTypeStr) {
    this.ssfTypeStr = ssfTypeStr;
  }

  public String getLineCode() {
    return lineCode;
  }

  public void setLineCode(String lineCode) {
    this.lineCode = lineCode;
  }

  @TableField(value = "ID")
  @ExcelProperty(value = "id")
  @ApiModelProperty(value = "")
  private String id;

  /**
   * 伸缩缝类型
   */
  @TableField(value = "SSF_TYPE")
  @ApiModelProperty(value = "伸缩缝类型")
  @ExcelProperty(value = "伸缩缝类型")
  private String ssfType;

  /**
   * 伸缩缝方向
   */
  @TableField(value = "SSF_DIRECT")
  @ApiModelProperty(value = "伸缩缝方向")
  @ExcelProperty(value = "伸缩缝方向")
  private String ssfDirect;

  /**
   * 伸缩缝位置
   */
  @TableField(value = "SSF_LOCATION")
  @ExcelProperty(value = "伸缩缝位置")
  @ApiModelProperty(value = "伸缩缝位置")
  private String ssfLocation;

  /**
   * 伸缩缝长度
   */
  @TableField(value = "SSF_LENGTH",jdbcType = JdbcType.FLOAT,updateStrategy = FieldStrategy.IGNORED)
  @ExcelProperty(value = "伸缩缝长度")
  @ApiModelProperty(value = "伸缩缝长度")
  private Float ssfLength;

  /**
   * 伸缩缝两侧梁长
   */
  @TableField(value = "SSF_LENGTH_BOTH_SIDE")
  @ExcelProperty(value = "伸缩缝两侧梁长")
  @ApiModelProperty(value = "伸缩缝两侧梁长")
  private String ssfLengthBothSide;

  /**
   * 所属桥梁
   */
  @TableField(value = "BRDG_ID")
  @ExcelProperty(value = "bridgeId")
  @ApiModelProperty(value = "所属桥梁")
  private String brdgId;

  @TableField(value = "VALID_FLAG")
  @ApiModelProperty(value = "")
  private int validFlag;

  @TableField(value = "CREATE_USER")
  @ApiModelProperty(value = "")
  private String createUser;

  @TableField(value = "CREATE_DATE")
  @ApiModelProperty(value = "")
  private Date createDate;

  @TableField(value = "UPDATE_USER")
  @ApiModelProperty(value = "")
  private String updateUser;

  @TableField(value = "UPDATE_DATE")
  @ApiModelProperty(value = "")
  private Date updateDate;

  @TableField(value = "DEL_USER")
  @ApiModelProperty(value = "")
  private String delUser;

  @TableField(value = "DEL_DATE")
  @ApiModelProperty(value = "")
  private Date delDate;

  public static final String COL_ID = "ID";

  public static final String COL_SSF_TYPE = "SSF_TYPE";

  public static final String COL_SSF_DIRECT = "SSF_DIRECT";

  public static final String COL_SSF_LOCATION = "SSF_LOCATION";

  public static final String COL_SSF_LENGTH = "SSF_LENGTH";

  public static final String COL_SSF_LENGTH_BOTH_SIDE = "SSF_LENGTH_BOTH_SIDE";

  public static final String COL_BRDG_ID = "BRDG_ID";

  public static final String COL_VALID_FLAG = "VALID_FLAG";

  public static final String COL_CREATE_USER = "CREATE_USER";

  public static final String COL_CREATE_DATE = "CREATE_DATE";

  public static final String COL_UPDATE_USER = "UPDATE_USER";

  public static final String COL_UPDATE_DATE = "UPDATE_DATE";

  public static final String COL_DEL_USER = "DEL_USER";

  public static final String COL_DEL_DATE = "DEL_DATE";
  public static final String PRODUCER = "PRODUCER";

  /**
   * @return ID
   */
  public String getId() {
    return id;
  }

  /**
   * @param id
   */
  public void setId(String id) {
    this.id = id;
  }

  /**
   * 获取伸缩缝类型
   *
   * @return SSF_TYPE - 伸缩缝类型
   */
  public String getSsfType() {
    return ssfType;
  }

  /**
   * 设置伸缩缝类型
   *
   * @param ssfType 伸缩缝类型
   */
  public void setSsfType(String ssfType) {
    this.ssfType = ssfType;
  }

  /**
   * 获取伸缩缝方向
   *
   * @return SSF_DIRECT - 伸缩缝方向
   */
  public String getSsfDirect() {
    return ssfDirect;
  }

  /**
   * 设置伸缩缝方向
   *
   * @param ssfDirect 伸缩缝方向
   */
  public void setSsfDirect(String ssfDirect) {
    this.ssfDirect = ssfDirect;
  }

  /**
   * 获取伸缩缝位置
   *
   * @return SSF_LOCATION - 伸缩缝位置
   */
  public String getSsfLocation() {
    return ssfLocation;
  }

  /**
   * 设置伸缩缝位置
   *
   * @param ssfLocation 伸缩缝位置
   */
  public void setSsfLocation(String ssfLocation) {
    this.ssfLocation = ssfLocation;
  }

  /**
   * 获取伸缩缝长度
   *
   * @return SSF_LENGTH - 伸缩缝长度
   */
  public Float getSsfLength() {
    return ssfLength;
  }

  /**
   * 设置伸缩缝长度
   *
   * @param ssfLength 伸缩缝长度
   */
  public void setSsfLength(Float ssfLength) {
    this.ssfLength = ssfLength;
  }

  /**
   * 获取伸缩缝两侧梁长
   *
   *
   * @return SSF_LENGTH_BOTH_SIDE - 伸缩缝两侧梁长
   */
  public String getSsfLengthBothSide() {
    return ssfLengthBothSide;
  }

  /**
   * 设置伸缩缝两侧梁长
   *
   * @param ssfLengthBothSide 伸缩缝两侧梁长
   */
  public void setSsfLengthBothSide(String ssfLengthBothSide) {
    this.ssfLengthBothSide = ssfLengthBothSide;
  }

  /**
   * 获取所属桥梁
   *
   * @return BRDG_ID - 所属桥梁
   */
  public String getBrdgId() {
    return brdgId;
  }

  /**
   * 设置所属桥梁
   *
   * @param brdgId 所属桥梁
   */
  public void setBrdgId(String brdgId) {
    this.brdgId = brdgId;
  }

  /**
   * @return VALID_FLAG
   */
  public int getValidFlag() {
    return validFlag;
  }

  /**
   * @param validFlag
   */
  public void setValidFlag(int validFlag) {
    this.validFlag = validFlag;
  }

  /**
   * @return CREATE_USER
   */
  public String getCreateUser() {
    return createUser;
  }

  /**
   * @param createUser
   */
  public void setCreateUser(String createUser) {
    this.createUser = createUser;
  }

  /**
   * @return CREATE_DATE
   */
  public Date getCreateDate() {
    return createDate;
  }

  /**
   * @param createDate
   */
  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  /**
   * @return UPDATE_USER
   */
  public String getUpdateUser() {
    return updateUser;
  }

  /**
   * @param updateUser
   */
  public void setUpdateUser(String updateUser) {
    this.updateUser = updateUser;
  }

  /**
   * @return UPDATE_DATE
   */
  public Date getUpdateDate() {
    return updateDate;
  }

  /**
   * @param updateDate
   */
  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  /**
   * @return DEL_USER
   */
  public String getDelUser() {
    return delUser;
  }

  /**
   * @param delUser
   */
  public void setDelUser(String delUser) {
    this.delUser = delUser;
  }

  /**
   * @return DEL_DATE
   */
  public Date getDelDate() {
    return delDate;
  }

  /**
   * @param delDate
   */
  public void setDelDate(Date delDate) {
    this.delDate = delDate;
  }
}