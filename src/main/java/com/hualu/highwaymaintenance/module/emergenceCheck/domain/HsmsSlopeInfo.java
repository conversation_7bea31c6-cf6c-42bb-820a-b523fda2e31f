package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 边坡基础信息表
    */
@ApiModel(description="边坡基础信息表")
@TableName(value = "HSMSDB.HSMS_SLOPE_INFO")
public class HsmsSlopeInfo {
    /**
    * 边坡主键
    */
    @ApiModelProperty(value="边坡主键")
    private String slopeId;

    /**
    * 边坡类型
    */
    @ApiModelProperty(value="边坡类型")
    private String slopeType;

    /**
    * 边坡编码
    */
    @ApiModelProperty(value="边坡编码")
    private String slopeCode;

    /**
    * 边坡名称
    */
    @ApiModelProperty(value="边坡名称")
    private String slopeName;

    /**
    * 养护等级
    */
    @ApiModelProperty(value="养护等级")
    private String maintainSeries;

    /**
    * 是否重点
    */
    @ApiModelProperty(value="是否重点")
    private String isImportant;

    /**
    * 边坡位置
    */
    @ApiModelProperty(value="边坡位置")
    private String slopePosition;

    /**
    * 边坡坡率
    */
    @ApiModelProperty(value="边坡坡率")
    private String slopeRate;

    /**
    * 边坡坡级
    */
    @ApiModelProperty(value="边坡坡级")
    private Long slopeLevel;

    /**
    * 边坡长度
    */
    @ApiModelProperty(value="边坡长度")
    private BigDecimal slopeLength;

    /**
    * 路线ID
    */
    @ApiModelProperty(value="路线ID")
    private String lineId;

    /**
    * 路线方向
    */
    @ApiModelProperty(value="路线方向")
    private String lineDirection;

    /**
    * 起点桩号
    */
    @ApiModelProperty(value="起点桩号")
    private BigDecimal startStakeOld;

    /**
    * 终点桩号
    */
    @ApiModelProperty(value="终点桩号")
    private BigDecimal endStakeOld;

    /**
    * 主要支档加固形式
    */
    @ApiModelProperty(value="主要支档加固形式")
    private String slopeRetainingAndReinforce;

    /**
    * 竣工时间
    */
    @ApiModelProperty(value="竣工时间")
    private Date completionTimeDate;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String remark;

    /**
    * 创建人
    */
    @ApiModelProperty(value="创建人")
    private String createUserId;

    /**
    * 修改人
    */
    @ApiModelProperty(value="修改人")
    private String updateUserId;

    /**
    * 创建时间
    */
    @ApiModelProperty(value="创建时间")
    private Date createTime;

    /**
    * 修改时间
    */
    @ApiModelProperty(value="修改时间")
    private Date updateTime;

    /**
    * 是否删除
    */
    @ApiModelProperty(value="是否删除")
    private String isDeleted;

    /**
    * X坐标
    */
    @ApiModelProperty(value="X坐标")
    private BigDecimal gisX;

    /**
    * Y坐标
    */
    @ApiModelProperty(value="Y坐标")
    private BigDecimal gisY;

    /**
    * 百度X坐标
    */
    @ApiModelProperty(value="百度X坐标")
    private BigDecimal bdGisX;

    /**
    * 百度Y坐标
    */
    @ApiModelProperty(value="百度Y坐标")
    private BigDecimal bdGisY;

    /**
    * 管养公司
    */
    @ApiModelProperty(value="管养公司")
    private String optOrgId;

    /**
    * 营运公司
    */
    @ApiModelProperty(value="营运公司")
    private String prjOrgId;

    /**
    * 是否主线（0是，1不是）
    */
    @ApiModelProperty(value="是否主线（0是，1不是）")
    private String isMainLine;

    /**
    * 营运区间ID
    */
    @ApiModelProperty(value="营运区间ID")
    private String rpIntrvlId;

    /**
    * 中心桩号
    */
    @ApiModelProperty(value="中心桩号")
    private BigDecimal cntrStakeNumOld;

    /**
    * 评定日期
    */
    @ApiModelProperty(value="评定日期")
    private Date evaluateDate;

    /**
    * 评定单位
    */
    @ApiModelProperty(value="评定单位")
    private String evaluationUnit;

    /**
    * 检测项目ID
    */
    @ApiModelProperty(value="检测项目ID")
    private String prjId;

    /**
    * 边坡评定等级
    */
    @ApiModelProperty(value="边坡评定等级")
    private String slopeTcGrade;

    /**
    * 距离该物理路段区间起始点的便宜量
    */
    @ApiModelProperty(value="距离该物理路段区间起始点的便宜量")
    private BigDecimal startOffsetOld;

    /**
    * 匝道路线ID
    */
    @ApiModelProperty(value="匝道路线ID")
    private String mainRampLineId;

    /**
    * 主线桩号对应的主线路段区间ID
    */
    @ApiModelProperty(value="主线桩号对应的主线路段区间ID")
    private String threadRpid;

    /**
    * 起点x坐标
    */
    @ApiModelProperty(value="起点x坐标")
    private BigDecimal gisSx;

    /**
    * 起点y坐标
    */
    @ApiModelProperty(value="起点y坐标")
    private BigDecimal gisSy;

    /**
    * 止点x坐标
    */
    @ApiModelProperty(value="止点x坐标")
    private BigDecimal gisEx;

    /**
    * 止点y坐标
    */
    @ApiModelProperty(value="止点y坐标")
    private BigDecimal gisEy;

    /**
    * 起点营运区间id
    */
    @ApiModelProperty(value="起点营运区间id")
    private String startRpIntrvlId;

    /**
    * 止点营运区间id
    */
    @ApiModelProperty(value="止点营运区间id")
    private String endRpIntrvlId;

    /**
    * 是否有检修道（1有，2无）
    */
    @ApiModelProperty(value="是否有检修道（1有，2无）")
    private String roadway;

    /**
    * 是否定检边坡（1是，0否）
    */
    @ApiModelProperty(value="是否定检边坡（1是，0否）")
    private String isSpecialSlope;

    /**
    * 设计起点桩号
    */
    @ApiModelProperty(value="设计起点桩号")
    private BigDecimal designStartStake;

    /**
    * 设计终点桩号
    */
    @ApiModelProperty(value="设计终点桩号")
    private BigDecimal designEndStake;

    /**
    * 重点监测坡
    */
    @ApiModelProperty(value="重点监测坡")
    private String redSlope;

    /**
    * 路线编码
    */
    @ApiModelProperty(value="路线编码")
    private String lineCode;

    /**
    * 物理中心桩号值
    */
    @ApiModelProperty(value="物理中心桩号值")
    private BigDecimal physicsCntrStake;

    /**
    * 逻辑中心桩号值
    */
    @ApiModelProperty(value="逻辑中心桩号值")
    private BigDecimal logicCntrStake;

    /**
    * 物理路段编码
    */
    @ApiModelProperty(value="物理路段编码")
    private String routeCode;

    /**
    * 路线版本
    */
    @ApiModelProperty(value="路线版本")
    private String routeVersion;

    /**
    * 逻辑匝道中心桩号
    */
    @ApiModelProperty(value="逻辑匝道中心桩号")
    private BigDecimal logicRampCntrStake;

    /**
    * 逻辑终点桩号
    */
    @ApiModelProperty(value="逻辑终点桩号")
    private BigDecimal logicEndStake;

    /**
    * 逻辑起点桩号
    */
    @ApiModelProperty(value="逻辑起点桩号")
    private BigDecimal logicStartStake;

    /**
    * 起点桩号
    */
    @ApiModelProperty(value="起点桩号")
    private BigDecimal startStake;

    /**
    * 止点桩号
    */
    @ApiModelProperty(value="止点桩号")
    private BigDecimal endStake;

    /**
    * 中心桩号
    */
    @ApiModelProperty(value="中心桩号")
    private BigDecimal cntrStakeNum;

    /**
    * 距离该物理路段区间起始点的便宜量
    */
    @ApiModelProperty(value="距离该物理路段区间起始点的便宜量")
    private BigDecimal startOffset;

    /**
    * 逻辑起点桩号(文本)
    */
    @ApiModelProperty(value="逻辑起点桩号(文本)")
    private String logicEndStrstake;

    /**
    * 逻辑止点桩号(文本)
    */
    @ApiModelProperty(value="逻辑止点桩号(文本)")
    private String logicStartStrstake;

    /**
    * 中心桩号(文本)
    */
    @ApiModelProperty(value="中心桩号(文本)")
    private String logicCntrStrstake;

    /**
    * 设计起点桩号(文本)
    */
    @ApiModelProperty(value="设计起点桩号(文本)")
    private String designStartStrstake;

    /**
    * 设计终点桩号(文本)
    */
    @ApiModelProperty(value="设计终点桩号(文本)")
    private String designEndStrstake;

    /**
    * 行政区划
    */
    @ApiModelProperty(value="行政区划")
    private String place;

    /**
    * 备用字段
    */
    @ApiModelProperty(value="备用字段")
    private String attr1;

    @ApiModelProperty(value="")
    private String completionTime;

    /**
    * 边坡状态，0营运，1移交
    */
    @ApiModelProperty(value="边坡状态，0营运，1移交")
    private String status;

    /**
    * 采集的纬度
    */
    @ApiModelProperty(value="采集的纬度")
    private BigDecimal colStartLatitude;

    /**
    * 采集的经度
    */
    @ApiModelProperty(value="采集的经度")
    private BigDecimal colStartLongitude;

    /**
    * 采集的日期
    */
    @ApiModelProperty(value="采集的日期")
    private Date colStartDate;

    /**
    * 采集人
    */
    @ApiModelProperty(value="采集人")
    private String colStartPerson;

    @ApiModelProperty(value="")
    private BigDecimal colEndLatitude;

    @ApiModelProperty(value="")
    private BigDecimal colEndLongitude;

    @ApiModelProperty(value="")
    private Date colEndDate;

    @ApiModelProperty(value="")
    private String colEndPerson;

    @ApiModelProperty(value="")
    private BigDecimal colStartAltitude;

    @ApiModelProperty(value="")
    private BigDecimal colEndAltitude;

    /**
    * 是否在省交通集团高填陡坡路堤应急排查整治工作台账中（1是其他否）
    */
    @ApiModelProperty(value="是否在省交通集团高填陡坡路堤应急排查整治工作台账中（1是其他否）")
    private String inEmergencyInvest;

    /**
    * 典型断面类型
    */
    @ApiModelProperty(value="典型断面类型")
    private String typicalSectionType;

    public String getSlopeId() {
        return slopeId;
    }

    public void setSlopeId(String slopeId) {
        this.slopeId = slopeId;
    }

    public String getSlopeType() {
        return slopeType;
    }

    public void setSlopeType(String slopeType) {
        this.slopeType = slopeType;
    }

    public String getSlopeCode() {
        return slopeCode;
    }

    public void setSlopeCode(String slopeCode) {
        this.slopeCode = slopeCode;
    }

    public String getSlopeName() {
        return slopeName;
    }

    public void setSlopeName(String slopeName) {
        this.slopeName = slopeName;
    }

    public String getMaintainSeries() {
        return maintainSeries;
    }

    public void setMaintainSeries(String maintainSeries) {
        this.maintainSeries = maintainSeries;
    }

    public String getIsImportant() {
        return isImportant;
    }

    public void setIsImportant(String isImportant) {
        this.isImportant = isImportant;
    }

    public String getSlopePosition() {
        return slopePosition;
    }

    public void setSlopePosition(String slopePosition) {
        this.slopePosition = slopePosition;
    }

    public String getSlopeRate() {
        return slopeRate;
    }

    public void setSlopeRate(String slopeRate) {
        this.slopeRate = slopeRate;
    }

    public Long getSlopeLevel() {
        return slopeLevel;
    }

    public void setSlopeLevel(Long slopeLevel) {
        this.slopeLevel = slopeLevel;
    }

    public BigDecimal getSlopeLength() {
        return slopeLength;
    }

    public void setSlopeLength(BigDecimal slopeLength) {
        this.slopeLength = slopeLength;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineDirection() {
        return lineDirection;
    }

    public void setLineDirection(String lineDirection) {
        this.lineDirection = lineDirection;
    }

    public BigDecimal getStartStakeOld() {
        return startStakeOld;
    }

    public void setStartStakeOld(BigDecimal startStakeOld) {
        this.startStakeOld = startStakeOld;
    }

    public BigDecimal getEndStakeOld() {
        return endStakeOld;
    }

    public void setEndStakeOld(BigDecimal endStakeOld) {
        this.endStakeOld = endStakeOld;
    }

    public String getSlopeRetainingAndReinforce() {
        return slopeRetainingAndReinforce;
    }

    public void setSlopeRetainingAndReinforce(String slopeRetainingAndReinforce) {
        this.slopeRetainingAndReinforce = slopeRetainingAndReinforce;
    }

    public Date getCompletionTimeDate() {
        return completionTimeDate;
    }

    public void setCompletionTimeDate(Date completionTimeDate) {
        this.completionTimeDate = completionTimeDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public BigDecimal getGisX() {
        return gisX;
    }

    public void setGisX(BigDecimal gisX) {
        this.gisX = gisX;
    }

    public BigDecimal getGisY() {
        return gisY;
    }

    public void setGisY(BigDecimal gisY) {
        this.gisY = gisY;
    }

    public BigDecimal getBdGisX() {
        return bdGisX;
    }

    public void setBdGisX(BigDecimal bdGisX) {
        this.bdGisX = bdGisX;
    }

    public BigDecimal getBdGisY() {
        return bdGisY;
    }

    public void setBdGisY(BigDecimal bdGisY) {
        this.bdGisY = bdGisY;
    }

    public String getOptOrgId() {
        return optOrgId;
    }

    public void setOptOrgId(String optOrgId) {
        this.optOrgId = optOrgId;
    }

    public String getPrjOrgId() {
        return prjOrgId;
    }

    public void setPrjOrgId(String prjOrgId) {
        this.prjOrgId = prjOrgId;
    }

    public String getIsMainLine() {
        return isMainLine;
    }

    public void setIsMainLine(String isMainLine) {
        this.isMainLine = isMainLine;
    }

    public String getRpIntrvlId() {
        return rpIntrvlId;
    }

    public void setRpIntrvlId(String rpIntrvlId) {
        this.rpIntrvlId = rpIntrvlId;
    }

    public BigDecimal getCntrStakeNumOld() {
        return cntrStakeNumOld;
    }

    public void setCntrStakeNumOld(BigDecimal cntrStakeNumOld) {
        this.cntrStakeNumOld = cntrStakeNumOld;
    }

    public Date getEvaluateDate() {
        return evaluateDate;
    }

    public void setEvaluateDate(Date evaluateDate) {
        this.evaluateDate = evaluateDate;
    }

    public String getEvaluationUnit() {
        return evaluationUnit;
    }

    public void setEvaluationUnit(String evaluationUnit) {
        this.evaluationUnit = evaluationUnit;
    }

    public String getPrjId() {
        return prjId;
    }

    public void setPrjId(String prjId) {
        this.prjId = prjId;
    }

    public String getSlopeTcGrade() {
        return slopeTcGrade;
    }

    public void setSlopeTcGrade(String slopeTcGrade) {
        this.slopeTcGrade = slopeTcGrade;
    }

    public BigDecimal getStartOffsetOld() {
        return startOffsetOld;
    }

    public void setStartOffsetOld(BigDecimal startOffsetOld) {
        this.startOffsetOld = startOffsetOld;
    }

    public String getMainRampLineId() {
        return mainRampLineId;
    }

    public void setMainRampLineId(String mainRampLineId) {
        this.mainRampLineId = mainRampLineId;
    }

    public String getThreadRpid() {
        return threadRpid;
    }

    public void setThreadRpid(String threadRpid) {
        this.threadRpid = threadRpid;
    }

    public BigDecimal getGisSx() {
        return gisSx;
    }

    public void setGisSx(BigDecimal gisSx) {
        this.gisSx = gisSx;
    }

    public BigDecimal getGisSy() {
        return gisSy;
    }

    public void setGisSy(BigDecimal gisSy) {
        this.gisSy = gisSy;
    }

    public BigDecimal getGisEx() {
        return gisEx;
    }

    public void setGisEx(BigDecimal gisEx) {
        this.gisEx = gisEx;
    }

    public BigDecimal getGisEy() {
        return gisEy;
    }

    public void setGisEy(BigDecimal gisEy) {
        this.gisEy = gisEy;
    }

    public String getStartRpIntrvlId() {
        return startRpIntrvlId;
    }

    public void setStartRpIntrvlId(String startRpIntrvlId) {
        this.startRpIntrvlId = startRpIntrvlId;
    }

    public String getEndRpIntrvlId() {
        return endRpIntrvlId;
    }

    public void setEndRpIntrvlId(String endRpIntrvlId) {
        this.endRpIntrvlId = endRpIntrvlId;
    }

    public String getRoadway() {
        return roadway;
    }

    public void setRoadway(String roadway) {
        this.roadway = roadway;
    }

    public String getIsSpecialSlope() {
        return isSpecialSlope;
    }

    public void setIsSpecialSlope(String isSpecialSlope) {
        this.isSpecialSlope = isSpecialSlope;
    }

    public BigDecimal getDesignStartStake() {
        return designStartStake;
    }

    public void setDesignStartStake(BigDecimal designStartStake) {
        this.designStartStake = designStartStake;
    }

    public BigDecimal getDesignEndStake() {
        return designEndStake;
    }

    public void setDesignEndStake(BigDecimal designEndStake) {
        this.designEndStake = designEndStake;
    }

    public String getRedSlope() {
        return redSlope;
    }

    public void setRedSlope(String redSlope) {
        this.redSlope = redSlope;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public BigDecimal getPhysicsCntrStake() {
        return physicsCntrStake;
    }

    public void setPhysicsCntrStake(BigDecimal physicsCntrStake) {
        this.physicsCntrStake = physicsCntrStake;
    }

    public BigDecimal getLogicCntrStake() {
        return logicCntrStake;
    }

    public void setLogicCntrStake(BigDecimal logicCntrStake) {
        this.logicCntrStake = logicCntrStake;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getRouteVersion() {
        return routeVersion;
    }

    public void setRouteVersion(String routeVersion) {
        this.routeVersion = routeVersion;
    }

    public BigDecimal getLogicRampCntrStake() {
        return logicRampCntrStake;
    }

    public void setLogicRampCntrStake(BigDecimal logicRampCntrStake) {
        this.logicRampCntrStake = logicRampCntrStake;
    }

    public BigDecimal getLogicEndStake() {
        return logicEndStake;
    }

    public void setLogicEndStake(BigDecimal logicEndStake) {
        this.logicEndStake = logicEndStake;
    }

    public BigDecimal getLogicStartStake() {
        return logicStartStake;
    }

    public void setLogicStartStake(BigDecimal logicStartStake) {
        this.logicStartStake = logicStartStake;
    }

    public BigDecimal getStartStake() {
        return startStake;
    }

    public void setStartStake(BigDecimal startStake) {
        this.startStake = startStake;
    }

    public BigDecimal getEndStake() {
        return endStake;
    }

    public void setEndStake(BigDecimal endStake) {
        this.endStake = endStake;
    }

    public BigDecimal getCntrStakeNum() {
        return cntrStakeNum;
    }

    public void setCntrStakeNum(BigDecimal cntrStakeNum) {
        this.cntrStakeNum = cntrStakeNum;
    }

    public BigDecimal getStartOffset() {
        return startOffset;
    }

    public void setStartOffset(BigDecimal startOffset) {
        this.startOffset = startOffset;
    }

    public String getLogicEndStrstake() {
        return logicEndStrstake;
    }

    public void setLogicEndStrstake(String logicEndStrstake) {
        this.logicEndStrstake = logicEndStrstake;
    }

    public String getLogicStartStrstake() {
        return logicStartStrstake;
    }

    public void setLogicStartStrstake(String logicStartStrstake) {
        this.logicStartStrstake = logicStartStrstake;
    }

    public String getLogicCntrStrstake() {
        return logicCntrStrstake;
    }

    public void setLogicCntrStrstake(String logicCntrStrstake) {
        this.logicCntrStrstake = logicCntrStrstake;
    }

    public String getDesignStartStrstake() {
        return designStartStrstake;
    }

    public void setDesignStartStrstake(String designStartStrstake) {
        this.designStartStrstake = designStartStrstake;
    }

    public String getDesignEndStrstake() {
        return designEndStrstake;
    }

    public void setDesignEndStrstake(String designEndStrstake) {
        this.designEndStrstake = designEndStrstake;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getAttr1() {
        return attr1;
    }

    public void setAttr1(String attr1) {
        this.attr1 = attr1;
    }

    public String getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(String completionTime) {
        this.completionTime = completionTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getColStartLatitude() {
        return colStartLatitude;
    }

    public void setColStartLatitude(BigDecimal colStartLatitude) {
        this.colStartLatitude = colStartLatitude;
    }

    public BigDecimal getColStartLongitude() {
        return colStartLongitude;
    }

    public void setColStartLongitude(BigDecimal colStartLongitude) {
        this.colStartLongitude = colStartLongitude;
    }

    public Date getColStartDate() {
        return colStartDate;
    }

    public void setColStartDate(Date colStartDate) {
        this.colStartDate = colStartDate;
    }

    public String getColStartPerson() {
        return colStartPerson;
    }

    public void setColStartPerson(String colStartPerson) {
        this.colStartPerson = colStartPerson;
    }

    public BigDecimal getColEndLatitude() {
        return colEndLatitude;
    }

    public void setColEndLatitude(BigDecimal colEndLatitude) {
        this.colEndLatitude = colEndLatitude;
    }

    public BigDecimal getColEndLongitude() {
        return colEndLongitude;
    }

    public void setColEndLongitude(BigDecimal colEndLongitude) {
        this.colEndLongitude = colEndLongitude;
    }

    public Date getColEndDate() {
        return colEndDate;
    }

    public void setColEndDate(Date colEndDate) {
        this.colEndDate = colEndDate;
    }

    public String getColEndPerson() {
        return colEndPerson;
    }

    public void setColEndPerson(String colEndPerson) {
        this.colEndPerson = colEndPerson;
    }

    public BigDecimal getColStartAltitude() {
        return colStartAltitude;
    }

    public void setColStartAltitude(BigDecimal colStartAltitude) {
        this.colStartAltitude = colStartAltitude;
    }

    public BigDecimal getColEndAltitude() {
        return colEndAltitude;
    }

    public void setColEndAltitude(BigDecimal colEndAltitude) {
        this.colEndAltitude = colEndAltitude;
    }

    public String getInEmergencyInvest() {
        return inEmergencyInvest;
    }

    public void setInEmergencyInvest(String inEmergencyInvest) {
        this.inEmergencyInvest = inEmergencyInvest;
    }

    public String getTypicalSectionType() {
        return typicalSectionType;
    }

    public void setTypicalSectionType(String typicalSectionType) {
        this.typicalSectionType = typicalSectionType;
    }
}