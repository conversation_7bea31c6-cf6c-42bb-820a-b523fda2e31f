package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsRectificationLedger;
import org.apache.ibatis.annotations.Param;

public interface HsmsRectificationLedgerMapper extends BaseMapper<HsmsRectificationLedger> {
    int deleteByPrimaryKey(String dateId);

    int insert(HsmsRectificationLedger record);

    int insertSelective(HsmsRectificationLedger record);

    HsmsRectificationLedger selectByPrimaryKey(String dateId);

    int updateByPrimaryKeySelective(HsmsRectificationLedger record);

    int updateByPrimaryKey(HsmsRectificationLedger record);

    IPage<HsmsRectificationLedger> loadRectificationLedgerPage(@Param("page") IPage<HsmsRectificationLedger> page, @Param("orgId") String orgId, @Param("riskType") String riskType, @Param("reportDate") String reportDate, @Param("isComplete") String isComplete, @Param("lineCode") String lineCode, @Param("lineDirect") String lineDirect, @Param("startStake") Double startStake, @Param("endStake") Double endStake);
}