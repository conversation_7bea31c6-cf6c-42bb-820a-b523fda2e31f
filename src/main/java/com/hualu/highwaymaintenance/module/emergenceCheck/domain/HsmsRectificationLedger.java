package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;

@ApiModel(description="HSMS_RECTIFICATION_LEDGER")
@TableName(schema = "HSMSDB", value = "HSMS_RECTIFICATION_LEDGER")
public class HsmsRectificationLedger {
    @ApiModelProperty(value="")
    private String dateId;

    /**
    * 路段名称
    */
    @ApiModelProperty(value="路段名称")
    private String pathName;

    /**
    * 占道情况
    */
    @ApiModelProperty(value="占道情况")
    private String roadOccupationSituation;

    /**
    * 桩号位置
    */
    @ApiModelProperty(value="桩号位置")
    private String stakePosition;

    /**
    * 路段类型
    */
    @ApiModelProperty(value="路段类型")
    private String roadSectionType;

    /**
    * 排查时间
    */
    @ApiModelProperty(value="排查时间")
    private String inspectionTime;

    /**
    * 病害隐患类型
    */
    @ApiModelProperty(value="病害隐患类型")
    private String diseaseHazardsTypes;

    /**
    * 现场病害情况
    */
    @ApiModelProperty(value="现场病害情况")
    private String currentStatusOfDamage;

    /**
    * 采取措施
    */
    @ApiModelProperty(value="采取措施")
    private String takeMeasures;

    /**
    * 预计完成加固处治时间
    */
    @ApiModelProperty(value="预计完成加固处治时间")
    private String expectedCompletion;

    /**
    * 预估处置费用
    */
    @ApiModelProperty(value="预估处置费用")
    private String estimatedDisposalCosts;

    /**
    * 设计单位
    */
    @ApiModelProperty(value="设计单位")
    private String designUnits;

    /**
    * 施工单位
    */
    @ApiModelProperty(value="施工单位")
    private String construtionUnit;

    /**
    * 备注
    */
    @ApiModelProperty(value="备注")
    private String notes;

    /**
    * 病害类型（A类或B类）
    */
    @ApiModelProperty(value="病害类型（A类或B类）")
    private String type;

    @ApiModelProperty(value="")
    private String isDeleted;

    @ApiModelProperty(value="")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createTime;

    @ApiModelProperty(value="")
    private String createUser;

    @ApiModelProperty(value="")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date updateTime;

    @ApiModelProperty(value="")
    private String updateUser;

    @ApiModelProperty(value="")
    private String routeCode;

    @ApiModelProperty(value="")
    private String orgCode;

    @ApiModelProperty(value="")
    private String startStake;

    @ApiModelProperty(value="")
    private String endStake;

    @ApiModelProperty(value="")
    private String lng;

    @ApiModelProperty(value="")
    private String lat;

    /**
    * 路线编码
    */
    @ApiModelProperty(value="路线编码")
    private String lineCode;

    /**
    * 路线方向
    */
    @ApiModelProperty(value="路线方向")
    private String lineDirect;

    /**
    * 二级单位
    */
    @ApiModelProperty(value="二级单位")
    private String secondOrgCode;

    /**
    * 边坡id，复用为各列id
    */
    @ApiModelProperty(value="边坡id，复用为各列id")
    private String slopeId;

    @TableField(exist = false)
    private String orgName;

    @TableField(exist = false)
    private String slopeName;

    @TableField(exist = false)
    private String rampNameVo;

    @TableField(exist = false)
    private String slopeTypeName;

    @TableField(exist = false)
    private String slopeSectionName;

    /**
    * 上报日期
    */
    @ApiModelProperty(value="上报日期")
    private String completeDate;

    /**
    * 是否完成
    */
    @ApiModelProperty(value="是否完成")
    private String isFinish;

    /**
    * 所属市县区
    */
    @ApiModelProperty(value="所属市县区")
    private String city;

    /**
    * 预计恢复时间
    */
    @ApiModelProperty(value="预计恢复时间")
    private String estimatedRecoveryTime;

    /**
    * 现场照片(多连接，逗号分隔)（弃用）
    */
    @ApiModelProperty(value="现场照片(多连接，逗号分隔)（弃用）")
    private String livePictures;

    /**
    * 路方联系人及电话
    */
    @ApiModelProperty(value="路方联系人及电话")
    private String roadContactInformation;

    /**
    * 属地联系人及电话
    */
    @ApiModelProperty(value="属地联系人及电话")
    private String territoryContactInformation;

    /**
    * 隐患发生时间
    */
    @ApiModelProperty(value="隐患发生时间")
    private String happenDate;

    /**
    * 边坡照片(多张，通过逗号分隔)（弃用）
    */
    @ApiModelProperty(value="边坡照片(多张，通过逗号分隔)（弃用）")
    private String slopePictures;

    /**
    * 边坡类型
    */
    @ApiModelProperty(value="边坡类型")
    private String slopeType;

    @ApiModelProperty(value="")
    private String height;

    /**
    * 中心桩号
    */
    @ApiModelProperty(value="中心桩号")
    private String cntrStake;

    /**
    * 灾害隐患类型位置
    */
    @ApiModelProperty(value="灾害隐患类型位置")
    private String disasterTypeSite;

    /**
    * 路线方向描述
    */
    @ApiModelProperty(value="路线方向描述")
    private String lineDirectDesc;

    /**
    * 总体开工时间
    */
    @ApiModelProperty(value="总体开工时间")
    private String workStartTime;

    /**
    * 总体处治进度
    */
    @ApiModelProperty(value="总体处治进度")
    private String workSchedule;

    /**
    * 结构物正面照（弃用）
    */
    @ApiModelProperty(value="结构物正面照（弃用）")
    private String frontalPhoto;

    /**
    * 结构物全貌照（弃用）
    */
    @ApiModelProperty(value="结构物全貌照（弃用）")
    private String fullPhoto;

    /**
    * 结构物俯视照（弃用）
    */
    @ApiModelProperty(value="结构物俯视照（弃用）")
    private String topPhoto;

    /**
    * 结构物测点布设图（弃用）
    */
    @ApiModelProperty(value="结构物测点布设图（弃用）")
    private String deploymentPhoto;

    /**
    * 结构物断面图（弃用）
    */
    @ApiModelProperty(value="结构物断面图（弃用）")
    private String sectionPhoto;

    /**
    * 细部照片（弃用）
    */
    @ApiModelProperty(value="细部照片（弃用）")
    private String detailPhoto;

    /**
    * 设计起点桩号
    */
    @ApiModelProperty(value="设计起点桩号")
    private String designStartStake;

    /**
    * 设计止点桩号
    */
    @ApiModelProperty(value="设计止点桩号")
    private String designEndStake;

    /**
    * 设计中心桩号
    */
    @ApiModelProperty(value="设计中心桩号")
    private String designCntrStake;

    /**
    * 典型断面类型
    */
    @ApiModelProperty(value="典型断面类型")
    private String typicalSectionType;

    /**
    * 工程地质
    */
    @ApiModelProperty(value="工程地质")
    private String engineGeology;

    /**
    * 水文地质
    */
    @ApiModelProperty(value="水文地质")
    private String hydrogeology;

    /**
    * 坡率1：1.25
    */
    @ApiModelProperty(value="坡率1：1.25")
    private String slopeRate;

    /**
    * 监理单位
    */
    @ApiModelProperty(value="监理单位")
    private String supervisionUnit;

    /**
     * 结构物起点桩号
     */
    @TableField(value = "STRUCT_START_STAKE")
    private String structStartStake;

    /**
     * 结构物终点桩号
     */
    @TableField(value = "STRUCT_END_STAKE")
    private String structEndStake;

    /**
     * 结构物终点桩号
     */
    @TableField(value = "STRUCT_CNTR_STAKE")
    private String structCntrStake;

    /**
     * 结构物设计起点桩号
     */
    @TableField(value = "STRUCT_DES_START_STAKE")
    private String structDesStartStake;

    /**
     * 结构物设计终点桩号
     */
    @TableField(value = "STRUCT_DES_END_STAKE")
    private String structDesEndStake;

    /**
     * 结构物设计终点桩号
     */
    @TableField(value = "STRUCT_DES_CNTR_STAKE")
    private String structDesCntrStake;

    /**
     * 匝道名称
     */
    @TableField(value = "RAMP_NAME")
    private String rampName;

    /**
     * 匝道与主线相连点位置的桩号
     */
    @TableField(value = "RAMP_LINK_STAKE")
    private String rampLinkStake;

    /**
     * 匝道编号（A-H）
     */
    @TableField(value = "RAMP_REF")
    private String rampRef;

    /**
     * 边坡在匝道上的位置 0.25
     */
    @TableField(value = "SLOPE_RAMP_SITE")
    private String slopeRampSite;

    /**
     * 边坡在匝道的左侧还是右侧
     */
    @TableField(value = "SLOPE_RAMP_SIDE")
    private String slopeRampSide;

    @TableField(exist = false)
    private List<HsmsRectificationLedgerFile> ledgerFileList;

    public List<HsmsRectificationLedgerFile> getLedgerFileList() {
        return ledgerFileList;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSlopeName() {
        return slopeName;
    }

    public void setSlopeName(String slopeName) {
        this.slopeName = slopeName;
    }

    public String getRampNameVo() {
        return rampNameVo;
    }

    public void setRampNameVo(String rampNameVo) {
        this.rampNameVo = rampNameVo;
    }

    public String getSlopeTypeName() {
        return slopeTypeName;
    }

    public void setSlopeTypeName(String slopeTypeName) {
        this.slopeTypeName = slopeTypeName;
    }

    public String getSlopeSectionName() {
        return slopeSectionName;
    }

    public void setSlopeSectionName(String slopeSectionName) {
        this.slopeSectionName = slopeSectionName;
    }

    public void setLedgerFileList(List<HsmsRectificationLedgerFile> ledgerFileList) {
        this.ledgerFileList = ledgerFileList;
    }

    public String getStructStartStake() {
        return structStartStake;
    }

    public void setStructStartStake(String structStartStake) {
        this.structStartStake = structStartStake;
    }

    public String getStructEndStake() {
        return structEndStake;
    }

    public void setStructEndStake(String structEndStake) {
        this.structEndStake = structEndStake;
    }

    public String getStructCntrStake() {
        return structCntrStake;
    }

    public void setStructCntrStake(String structCntrStake) {
        this.structCntrStake = structCntrStake;
    }

    public String getStructDesStartStake() {
        return structDesStartStake;
    }

    public void setStructDesStartStake(String structDesStartStake) {
        this.structDesStartStake = structDesStartStake;
    }

    public String getStructDesEndStake() {
        return structDesEndStake;
    }

    public void setStructDesEndStake(String structDesEndStake) {
        this.structDesEndStake = structDesEndStake;
    }

    public String getStructDesCntrStake() {
        return structDesCntrStake;
    }

    public void setStructDesCntrStake(String structDesCntrStake) {
        this.structDesCntrStake = structDesCntrStake;
    }

    public String getRampName() {
        return rampName;
    }

    public void setRampName(String rampName) {
        this.rampName = rampName;
    }

    public String getRampLinkStake() {
        return rampLinkStake;
    }

    public void setRampLinkStake(String rampLinkStake) {
        this.rampLinkStake = rampLinkStake;
    }

    public String getRampRef() {
        return rampRef;
    }

    public void setRampRef(String rampRef) {
        this.rampRef = rampRef;
    }

    public String getSlopeRampSite() {
        return slopeRampSite;
    }

    public void setSlopeRampSite(String slopeRampSite) {
        this.slopeRampSite = slopeRampSite;
    }

    public String getSlopeRampSide() {
        return slopeRampSide;
    }

    public void setSlopeRampSide(String slopeRampSide) {
        this.slopeRampSide = slopeRampSide;
    }

    public String getDateId() {
        return dateId;
    }

    public void setDateId(String dateId) {
        this.dateId = dateId;
    }

    public String getPathName() {
        return pathName;
    }

    public void setPathName(String pathName) {
        this.pathName = pathName;
    }

    public String getRoadOccupationSituation() {
        return roadOccupationSituation;
    }

    public void setRoadOccupationSituation(String roadOccupationSituation) {
        this.roadOccupationSituation = roadOccupationSituation;
    }

    public String getStakePosition() {
        return stakePosition;
    }

    public void setStakePosition(String stakePosition) {
        this.stakePosition = stakePosition;
    }

    public String getRoadSectionType() {
        return roadSectionType;
    }

    public void setRoadSectionType(String roadSectionType) {
        this.roadSectionType = roadSectionType;
    }

    public String getInspectionTime() {
        return inspectionTime;
    }

    public void setInspectionTime(String inspectionTime) {
        this.inspectionTime = inspectionTime;
    }

    public String getDiseaseHazardsTypes() {
        return diseaseHazardsTypes;
    }

    public void setDiseaseHazardsTypes(String diseaseHazardsTypes) {
        this.diseaseHazardsTypes = diseaseHazardsTypes;
    }

    public String getCurrentStatusOfDamage() {
        return currentStatusOfDamage;
    }

    public void setCurrentStatusOfDamage(String currentStatusOfDamage) {
        this.currentStatusOfDamage = currentStatusOfDamage;
    }

    public String getTakeMeasures() {
        return takeMeasures;
    }

    public void setTakeMeasures(String takeMeasures) {
        this.takeMeasures = takeMeasures;
    }

    public String getExpectedCompletion() {
        return expectedCompletion;
    }

    public void setExpectedCompletion(String expectedCompletion) {
        this.expectedCompletion = expectedCompletion;
    }

    public String getEstimatedDisposalCosts() {
        return estimatedDisposalCosts;
    }

    public void setEstimatedDisposalCosts(String estimatedDisposalCosts) {
        this.estimatedDisposalCosts = estimatedDisposalCosts;
    }

    public String getDesignUnits() {
        return designUnits;
    }

    public void setDesignUnits(String designUnits) {
        this.designUnits = designUnits;
    }

    public String getConstrutionUnit() {
        return construtionUnit;
    }

    public void setConstrutionUnit(String construtionUnit) {
        this.construtionUnit = construtionUnit;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getRouteCode() {
        return routeCode;
    }

    public void setRouteCode(String routeCode) {
        this.routeCode = routeCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getStartStake() {
        return startStake;
    }

    public void setStartStake(String startStake) {
        this.startStake = startStake;
    }

    public String getEndStake() {
        return endStake;
    }

    public void setEndStake(String endStake) {
        this.endStake = endStake;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(String lineDirect) {
        this.lineDirect = lineDirect;
    }

    public String getSecondOrgCode() {
        return secondOrgCode;
    }

    public void setSecondOrgCode(String secondOrgCode) {
        this.secondOrgCode = secondOrgCode;
    }

    public String getSlopeId() {
        return slopeId;
    }

    public void setSlopeId(String slopeId) {
        this.slopeId = slopeId;
    }

    public String getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(String completeDate) {
        this.completeDate = completeDate;
    }

    public String getIsFinish() {
        return isFinish;
    }

    public void setIsFinish(String isFinish) {
        this.isFinish = isFinish;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getEstimatedRecoveryTime() {
        return estimatedRecoveryTime;
    }

    public void setEstimatedRecoveryTime(String estimatedRecoveryTime) {
        this.estimatedRecoveryTime = estimatedRecoveryTime;
    }

    public String getLivePictures() {
        return livePictures;
    }

    public void setLivePictures(String livePictures) {
        this.livePictures = livePictures;
    }

    public String getRoadContactInformation() {
        return roadContactInformation;
    }

    public void setRoadContactInformation(String roadContactInformation) {
        this.roadContactInformation = roadContactInformation;
    }

    public String getTerritoryContactInformation() {
        return territoryContactInformation;
    }

    public void setTerritoryContactInformation(String territoryContactInformation) {
        this.territoryContactInformation = territoryContactInformation;
    }

    public String getHappenDate() {
        return happenDate;
    }

    public void setHappenDate(String happenDate) {
        this.happenDate = happenDate;
    }

    public String getSlopePictures() {
        return slopePictures;
    }

    public void setSlopePictures(String slopePictures) {
        this.slopePictures = slopePictures;
    }

    public String getSlopeType() {
        return slopeType;
    }

    public void setSlopeType(String slopeType) {
        this.slopeType = slopeType;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getCntrStake() {
        return cntrStake;
    }

    public void setCntrStake(String cntrStake) {
        this.cntrStake = cntrStake;
    }

    public String getDisasterTypeSite() {
        return disasterTypeSite;
    }

    public void setDisasterTypeSite(String disasterTypeSite) {
        this.disasterTypeSite = disasterTypeSite;
    }

    public String getLineDirectDesc() {
        return lineDirectDesc;
    }

    public void setLineDirectDesc(String lineDirectDesc) {
        this.lineDirectDesc = lineDirectDesc;
    }

    public String getWorkStartTime() {
        return workStartTime;
    }

    public void setWorkStartTime(String workStartTime) {
        this.workStartTime = workStartTime;
    }

    public String getWorkSchedule() {
        return workSchedule;
    }

    public void setWorkSchedule(String workSchedule) {
        this.workSchedule = workSchedule;
    }

    public String getFrontalPhoto() {
        return frontalPhoto;
    }

    public void setFrontalPhoto(String frontalPhoto) {
        this.frontalPhoto = frontalPhoto;
    }

    public String getFullPhoto() {
        return fullPhoto;
    }

    public void setFullPhoto(String fullPhoto) {
        this.fullPhoto = fullPhoto;
    }

    public String getTopPhoto() {
        return topPhoto;
    }

    public void setTopPhoto(String topPhoto) {
        this.topPhoto = topPhoto;
    }

    public String getDeploymentPhoto() {
        return deploymentPhoto;
    }

    public void setDeploymentPhoto(String deploymentPhoto) {
        this.deploymentPhoto = deploymentPhoto;
    }

    public String getSectionPhoto() {
        return sectionPhoto;
    }

    public void setSectionPhoto(String sectionPhoto) {
        this.sectionPhoto = sectionPhoto;
    }

    public String getDetailPhoto() {
        return detailPhoto;
    }

    public void setDetailPhoto(String detailPhoto) {
        this.detailPhoto = detailPhoto;
    }

    public String getDesignStartStake() {
        return designStartStake;
    }

    public void setDesignStartStake(String designStartStake) {
        this.designStartStake = designStartStake;
    }

    public String getDesignEndStake() {
        return designEndStake;
    }

    public void setDesignEndStake(String designEndStake) {
        this.designEndStake = designEndStake;
    }

    public String getDesignCntrStake() {
        return designCntrStake;
    }

    public void setDesignCntrStake(String designCntrStake) {
        this.designCntrStake = designCntrStake;
    }

    public String getTypicalSectionType() {
        return typicalSectionType;
    }

    public void setTypicalSectionType(String typicalSectionType) {
        this.typicalSectionType = typicalSectionType;
    }

    public String getEngineGeology() {
        return engineGeology;
    }

    public void setEngineGeology(String engineGeology) {
        this.engineGeology = engineGeology;
    }

    public String getHydrogeology() {
        return hydrogeology;
    }

    public void setHydrogeology(String hydrogeology) {
        this.hydrogeology = hydrogeology;
    }

    public String getSlopeRate() {
        return slopeRate;
    }

    public void setSlopeRate(String slopeRate) {
        this.slopeRate = slopeRate;
    }

    public String getSupervisionUnit() {
        return supervisionUnit;
    }

    public void setSupervisionUnit(String supervisionUnit) {
        this.supervisionUnit = supervisionUnit;
    }
}