package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsSlopeInfo;

public interface HsmsSlopeInfoMapper {
    int deleteByPrimaryKey(String slopeId);

    int insert(HsmsSlopeInfo record);

    int insertSelective(HsmsSlopeInfo record);

    HsmsSlopeInfo selectByPrimaryKey(String slopeId);

    int updateByPrimaryKeySelective(HsmsSlopeInfo record);

    int updateByPrimaryKey(HsmsSlopeInfo record);
}