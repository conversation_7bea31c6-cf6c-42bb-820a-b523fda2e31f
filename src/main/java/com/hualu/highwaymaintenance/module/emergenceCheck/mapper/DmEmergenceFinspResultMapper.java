package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.DmEmergenceFinspResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DmEmergenceFinspResultMapper extends BaseMapper<DmEmergenceFinspResult> {
    int deleteByPrimaryKey(String finspResId);

    int insert(DmEmergenceFinspResult record);

    int insertSelective(DmEmergenceFinspResult record);

    DmEmergenceFinspResult selectByPrimaryKey(String finspResId);

    int updateByPrimaryKeySelective(DmEmergenceFinspResult record);

    int updateByPrimaryKey(DmEmergenceFinspResult record);

    List<DmEmergenceFinspResult> loadAllItemById(@Param("finspId") String finspId);
}