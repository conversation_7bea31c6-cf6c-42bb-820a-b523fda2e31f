package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.DmEmergencyFinsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DmEmergencyFinspMapper extends BaseMapper<DmEmergencyFinsp> {
  int deleteByPrimaryKey(String finspId);

  int insert(DmEmergencyFinsp record);

  int insertSelective(DmEmergencyFinsp record);

  DmEmergencyFinsp selectByPrimaryKey(String finspId);

  int updateByPrimaryKeySelective(DmEmergencyFinsp record);

  int updateByPrimaryKey(DmEmergencyFinsp record);

  void insertResult(@Param("finspId") String finspId, @Param("createUserId") String createUserId);

  IPage<DmEmergencyFinsp> loadDmEmergencyPage(@Param("page") IPage<DmEmergencyFinsp> page, @Param("orgId") String orgId, @Param("slopeName") String slopeName, @Param("inspDate") String inspDate);

  DmEmergencyFinsp findLastDmFinspByOrgId(@Param("orgId") String orgId);

  List<String> loadAllPhotosByFinspId(@Param("finspId") String finspId);
}