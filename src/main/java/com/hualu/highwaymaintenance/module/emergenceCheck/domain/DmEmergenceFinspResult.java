package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
    * 高填陡坡路堤边坡现场检查原始记录表明细
    */
@ApiModel(description="高填陡坡路堤边坡现场检查原始记录表明细")
@TableName(value = "HSMSDB.dm_emergence_finsp_result")
public class DmEmergenceFinspResult {
    /**
    * 明细id
    */
    @ApiModelProperty(value="明细id")
    private String finspResId;

    /**
    * 主单id
    */
    @ApiModelProperty(value="主单id")
    private String finspId;

    /**
    * 检查项（关联字典）
    */
    @ApiModelProperty(value="检查项（关联字典）")
    private String finspItemId;

    /**
    * 是否检查
    */
    @ApiModelProperty(value="是否检查")
    private String inspResult;

    /**
    * 情况描述
    */
    @ApiModelProperty(value="情况描述")
    private String issueDesc;
    /**
     * 创建用户ID
     */
    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUserId;

    @TableField(exist = false)
    private List<DmEmergencyResultPhoto> photoList;
    @TableField(exist = false)
    private String itemName;
    @TableField(exist = false)
    private String itemCat;
    @TableField(exist = false)
    private int photoNum;

    public int getPhotoNum() {
        return photoNum;
    }

    public void setPhotoNum(int photoNum) {
        this.photoNum = photoNum;
    }

    //有问题的答案选项
    @TableField(exist = false)
    private String  answerVal;

    public String getAnswerVal() {
        return answerVal;
    }

    public void setAnswerVal(String answerVal) {
        this.answerVal = answerVal;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemCat() {
        return itemCat;
    }

    public void setItemCat(String itemCat) {
        this.itemCat = itemCat;
    }

    public List<DmEmergencyResultPhoto> getPhotoList() {
        return photoList;
    }

    public void setPhotoList(List<DmEmergencyResultPhoto> photoList) {
        this.photoList = photoList;
    }

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 逻辑删除标志
     */
    @ApiModelProperty(value="逻辑删除标志")
    private Integer validFlag;

    public Integer getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(Integer validFlag) {
        this.validFlag = validFlag;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getFinspResId() {
        return finspResId;
    }

    public void setFinspResId(String finspResId) {
        this.finspResId = finspResId;
    }

    public String getFinspId() {
        return finspId;
    }

    public void setFinspId(String finspId) {
        this.finspId = finspId;
    }

    public String getFinspItemId() {
        return finspItemId;
    }

    public void setFinspItemId(String finspItemId) {
        this.finspItemId = finspItemId;
    }

    public String getInspResult() {
        return inspResult;
    }

    public void setInspResult(String inspResult) {
        this.inspResult = inspResult;
    }

    public String getIssueDesc() {
        return issueDesc;
    }

    public void setIssueDesc(String issueDesc) {
        this.issueDesc = issueDesc;
    }
}