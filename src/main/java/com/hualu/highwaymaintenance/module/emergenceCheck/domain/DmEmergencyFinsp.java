package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 * 边坡应急排查台账
 */
@ApiModel(description = "边坡应急排查台账")
@TableName(value = "HSMSDB.DM_EMERGENCY_FINSP")
public class DmEmergencyFinsp {
  /**
   * 边坡应急单id
   */
  @ApiModelProperty(value = "边坡应急单id")
  private String finspId;

  /**
   * 管养单位
   */
  @ApiModelProperty(value = "管养单位")
  private String mntOrgId;

  /**
   * 检查单位
   */
  @ApiModelProperty(value = "检查单位")
  private String inspOrg;

  /**
   * 检查日期
   */
  @ApiModelProperty(value = "检查日期")
  private String inspDate;

  /**
   * 路线编码
   */
  @ApiModelProperty(value = "路线编码")
  private String lineCode;

  /**
   * 路线名称
   */
  @ApiModelProperty(value = "路线名称")
  private String lineName;

  /**
   * 边坡ID
   */
  @ApiModelProperty(value = "边坡ID")
  private String slopeId;
  @TableField(exist = false)
  private String slopeName;
  @TableField(exist = false)
  private int careItemNum;
  @TableField(exist = false)
  private int photoNum;

  public int getCareItemNum() {
    return careItemNum;
  }

  public void setCareItemNum(int careItemNum) {
    this.careItemNum = careItemNum;
  }

  public int getPhotoNum() {
    return photoNum;
  }

  public void setPhotoNum(int photoNum) {
    this.photoNum = photoNum;
  }

  private String delUserId;
  private Date delTime;
  private Integer validFlag;

  public Integer getValidFlag() {
    return validFlag;
  }

  public void setValidFlag(Integer validFlag) {
    this.validFlag = validFlag;
  }

  public Date getDelTime() {
    return delTime;
  }

  public void setDelTime(Date delTime) {
    this.delTime = delTime;
  }

  public String getDelUserId() {
    return delUserId;
  }

  public void setDelUserId(String delUserId) {
    this.delUserId = delUserId;
  }

  public String getSlopeName() {
    return slopeName;
  }

  public void setSlopeName(String slopeName) {
    this.slopeName = slopeName;
  }

  /**
   * 创建用户ID
   */
  @ApiModelProperty(value = "创建用户ID")
  private String createUserId;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  /**
   * 更新用户
   */
  @ApiModelProperty(value = "更新用户")
  private String updateUserId;

  /**
   * 更新时间
   */
  @ApiModelProperty(value = "更新时间")
  private Date updateTime;

  /**
   * 检查人
   */
  @ApiModelProperty(value = "检查人")
  private String inspPerson;

  /**
   * 记录人
   */
  @ApiModelProperty(value = "记录人")
  private String recordPerson;

  /**
   * 负责人
   */
  @ApiModelProperty(value = "负责人")
  private String chargePerson;

  public String getFinspId() {
    return finspId;
  }

  public void setFinspId(String finspId) {
    this.finspId = finspId;
  }

  public String getMntOrgId() {
    return mntOrgId;
  }

  public void setMntOrgId(String mntOrgId) {
    this.mntOrgId = mntOrgId;
  }

  public String getInspOrg() {
    return inspOrg;
  }

  public void setInspOrg(String inspOrg) {
    this.inspOrg = inspOrg;
  }

  public String getInspDate() {
    return inspDate;
  }

  public void setInspDate(String inspDate) {
    this.inspDate = inspDate;
  }

  public String getLineCode() {
    return lineCode;
  }

  public void setLineCode(String lineCode) {
    this.lineCode = lineCode;
  }

  public String getLineName() {
    return lineName;
  }

  public void setLineName(String lineName) {
    this.lineName = lineName;
  }

  public String getSlopeId() {
    return slopeId;
  }

  public void setSlopeId(String slopeId) {
    this.slopeId = slopeId;
  }

  public String getCreateUserId() {
    return createUserId;
  }

  public void setCreateUserId(String createUserId) {
    this.createUserId = createUserId;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public String getUpdateUserId() {
    return updateUserId;
  }

  public void setUpdateUserId(String updateUserId) {
    this.updateUserId = updateUserId;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getInspPerson() {
    return inspPerson;
  }

  public void setInspPerson(String inspPerson) {
    this.inspPerson = inspPerson;
  }

  public String getRecordPerson() {
    return recordPerson;
  }

  public void setRecordPerson(String recordPerson) {
    this.recordPerson = recordPerson;
  }

  public String getChargePerson() {
    return chargePerson;
  }

  public void setChargePerson(String chargePerson) {
    this.chargePerson = chargePerson;
  }
}