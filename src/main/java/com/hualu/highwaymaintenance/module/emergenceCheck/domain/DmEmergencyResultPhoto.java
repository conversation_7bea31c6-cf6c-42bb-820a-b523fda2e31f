package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
    * 检查结果照片
    */
@ApiModel(description="检查结果照片")
@TableName(value = "HSMSDB.DM_EMERGENCY_RESULT_PHOTO")
public class DmEmergencyResultPhoto {
    @ApiModelProperty(value="")
    private String photoId;

    /**
    * 明细id
    */
    @ApiModelProperty(value="明细id")
    private String resultId;

    /**
    * 文件id
    */
    @ApiModelProperty(value="文件id")
    private String fileId;

    /**
     * 逻辑删除标志
     */
    @ApiModelProperty(value="逻辑删除标志")
    private Integer validFlag;

    /**
     * 创建用户ID
     */
    @ApiModelProperty(value = "创建用户ID")
    private String createUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新用户
     */
    @ApiModelProperty(value = "更新用户")
    private String updateUserId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getValidFlag() {
        return validFlag;
    }

    public void setValidFlag(Integer validFlag) {
        this.validFlag = validFlag;
    }

    public String getPhotoId() {
        return photoId;
    }

    public void setPhotoId(String photoId) {
        this.photoId = photoId;
    }

    public String getResultId() {
        return resultId;
    }

    public void setResultId(String resultId) {
        this.resultId = resultId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }
}