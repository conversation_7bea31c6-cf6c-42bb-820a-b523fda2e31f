package com.hualu.highwaymaintenance.module.emergenceCheck.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.common.vo.TokenMsg;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsRectificationLedger;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsRectificationLedgerFile;
import com.hualu.highwaymaintenance.module.maintainbase.service.DiseaseSituationService;
import com.hualu.highwaymaintenance.module.maintainbase.service.HsmsRectificationLedgerFileService;
import com.hualu.highwaymaintenance.module.maintainbase.service.HsmsRectificationLedgerService;
import com.hualu.highwaymaintenance.util.TokenUtils;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description:边坡紧急排查检查单
 * @author: JDS
 * @time: 2024/06/05
 */
@RestController
@RequestMapping("/rectificationLedger")
public class RectificationLedgerController {

  @Resource
  private HsmsRectificationLedgerService rectificationLedgerService;
  @Resource
  private DiseaseSituationService diseaseSituationService;
  @Resource
  private HsmsRectificationLedgerFileService fileService;
  @Resource
  private ObjectMapper objectMapper;

  /**
   * 查询隐患排查单列表
   *
   * @param reportDate （检查日期之前）
   * @param isComplete （加固是否完成）
   */
  @GetMapping("/loadRectificationLedgerPage")
  @ApiOperation(nickname = "loadRectificationLedgerPage", value = "查询隐患排查单列表")
  public RestResult<List<HsmsRectificationLedger>> loadRectificationLedgerPage(
      @RequestParam(required = false, defaultValue = "1") int pageIndex,
      @RequestParam(required = false, defaultValue = "20") int pageSize,
      @RequestParam(required = false) String riskType,
      @RequestParam(required = false) String reportDate,
      @RequestParam(required = false) String isComplete,
      @RequestParam(required = false) String lineCode,
      @RequestParam(required = false) String lineDirect,
      @RequestParam(required = false) Double startStake,
      @RequestParam(required = false) Double endStake
  ) {
    String orgId = "N000053";
    try {
      TokenMsg tokenMap = TokenUtils.getTokenMap();
      orgId = tokenMap.getOrgId();
    } catch (Exception e) {
      e.printStackTrace();
    }
    IPage<HsmsRectificationLedger> page = new Page<>(pageIndex, pageSize);
    IPage<HsmsRectificationLedger> p = rectificationLedgerService.loadRectificationLedgerPage(
        page,
        orgId,
        riskType,
        reportDate,
        isComplete,lineCode,lineDirect,startStake,endStake
    );
    return RestResult.success(p.getRecords(), p.getTotal(), p.getCurrent(), p.getSize());
  }

  /**
   * 查询隐患排查主单信息
   */
  @ApiOperation(nickname = "loadRectificationLedger", value = "查询隐患排查主单信息")
  @GetMapping("/loadRectificationLedger")
  public RestResult<HsmsRectificationLedger> loadRectificationLedger(
      String ledgerId
  ) {
    return RestResult.success(rectificationLedgerService.loadRectificationLedger(ledgerId),
        "查询隐患排查主单信息完毕");
  }

  /**
   * 查询隐患排查病害信息
   */
  @ApiOperation(nickname = "loadDiseaseSituationByLedgerId", value = "查询隐患排查病害信息")
  @GetMapping("/loadDiseaseSituationByLedgerId")
  public RestResult<List<DiseaseSituation>> loadDiseaseSituationByLedgerId(
      String ledgerId
  ) {
    return RestResult.success(diseaseSituationService.loadDiseaseSituationByLedgerId(ledgerId),
        "查询隐患排查病害信息完毕");
  }

  /**
   * 新增边坡照片
   */
  @ApiOperation(nickname = "addLedgerPhoto", value = "新增边坡照片")
  @PostMapping("/addLedgerPhoto")
  public RestResult<List<HsmsRectificationLedgerFile>> addLedgerPhoto(
      @RequestParam("ledgerId") String ledgerId,
      @RequestParam("fileType") String fileType,
      @RequestParam(value = "files", required = false) MultipartFile[] files
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }
    return RestResult.success(fileService.addLedgerPhoto(ledgerId, files, userCode, fileType),
        "新增边坡照片成功");
  }

  @ApiOperation(nickname = "removeLedgerPhoto", value = "删除边坡照片")
  @GetMapping("/removeLedgerPhoto")
  public RestResult<String> removeLedgerPhoto(
      String photoId
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }
    fileService.removeLedgerPhoto(photoId, userCode);
    return RestResult.success("删除边坡照片成功");
  }

  /**
   * 修改病害
   */
  @ApiOperation(nickname = "modifyDisease", value = "修改病害")
  @PostMapping("/modifyDisease")
  public RestResult<DiseaseSituation> modifyDisease(
      @RequestBody DiseaseSituation data
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }

    return RestResult.success(diseaseSituationService.modifyDisease(data, userCode),
        "修改病害成功");
  }

  /**
   * 修改病害
   */
  @ApiOperation(nickname = "removeDisease", value = "删除病害")
  @PostMapping("/removeDisease")
  public RestResult<String> modifyDisease(
      String diseaseId
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }
    diseaseSituationService.deleteByPrimaryKey(diseaseId);
    return RestResult.success("删除病害成功");
  }

  /**
   * 新增病害
   */
  @ApiOperation(nickname = "addDisease", value = "新增病害")
  @PostMapping("/addDisease")
  public RestResult<DiseaseSituation> addDisease(
      @RequestParam(value = "data", required = false) String data,
      @RequestParam(value = "filesBefore", required = false) MultipartFile[] filesBefore,
      @RequestParam(value = "filesIn", required = false) MultipartFile[] filesIn,
      @RequestParam(value = "filesAfter", required = false) MultipartFile[] filesAfter,
      @RequestParam(value = "dssWhole", required = false) MultipartFile[] dssWhole
  ) throws JsonProcessingException {
    String userCode = "123";
    DiseaseSituation diseaseSituation = objectMapper.readValue(data, DiseaseSituation.class);
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }

    return RestResult.success(
        diseaseSituationService.addDisease(
            diseaseSituation,
            userCode,
            filesBefore,
            filesIn,
            filesAfter,
            dssWhole
        ),
        "新增病害成功"
    );
  }

  /**
   * 新增病害照片
   */
  @ApiOperation(nickname = "addDiseasePhoto", value = "新增病害照片")
  @PostMapping("/addDiseasePhoto")
  public RestResult<String> addDiseasePhoto(
      @RequestParam("dssId") String dssId,
      @RequestParam("fileType") String fileType,
      @RequestParam(value = "files", required = false) MultipartFile[] files
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }
    return RestResult.success(
        diseaseSituationService.addDiseasePhoto(dssId, files, userCode, fileType),
        "新增病害照片成功");
  }

  @ApiOperation(nickname = "removeDiseasePhoto", value = "删除病害照片")
  @GetMapping("/removeDiseasePhoto")
  public RestResult<String> removeDiseasePhoto(
      String dssId, String photoId, String fileType
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }
    diseaseSituationService.removeDiseasePhoto(photoId, dssId, userCode, fileType);
    return RestResult.success("删除病害照片成功");
  }

  @ApiOperation(nickname = "updateProjectProgress", value = "修改工程进度")
  @GetMapping("/updateProjectProgress")
  public RestResult<String> updateProjectProgress(
      String ledgerId, String isFinish, String projectProgress
  ) {
    String userCode = "123";
    try {
      TokenMsg tokenMsg = TokenUtils.getTokenMap();
      userCode = tokenMsg.getUserCode();
    } catch (Exception e) {
      e.printStackTrace();
    }
    rectificationLedgerService.updateProjectProgress(ledgerId, isFinish, projectProgress);
    return RestResult.success("修改成功");
  }
}
