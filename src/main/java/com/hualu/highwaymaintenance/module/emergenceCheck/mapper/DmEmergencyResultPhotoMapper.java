package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.DmEmergencyResultPhoto;

public interface DmEmergencyResultPhotoMapper extends BaseMapper<DmEmergencyResultPhoto> {
    int insert(DmEmergencyResultPhoto record);

    int insertSelective(DmEmergencyResultPhoto record);

    DmEmergencyResultPhoto selectByPrimaryKey(String photoId);

    void updateByPrimaryKey(DmEmergencyResultPhoto dmEmergencyResultPhoto);
}