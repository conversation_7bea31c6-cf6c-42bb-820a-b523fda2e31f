package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;

import java.util.Date;

/**
 * 现场病害情况(照片相关)
 */
@ApiModel(description="现场病害情况(照片相关)")
@TableName(value = "HSMSDB.DISEASE_SITUATION")
public class DiseaseSituation {
    @TableId("DATA_ID")
    private String dataId;

    /**
     * 现场病害情况
     */
    @TableField("DISEASE_SITUATION")
    private String diseaseSituation;

    /**
     * 采取措施
     */
    @TableField("TAKE_STEPS")
    private String takeSteps;

    /**
     * 处置前 细部
     */
    @TableField("TREATMENT_BEFORE")
    private String treatmentBefore;

    /**
     * 处置前 主要
     */
    @TableField("TREATMENT_BEFORE_MAIN")
    private String treatmentBeforeMain;



    /**
     * 处置中
     */
    @TableField("TREATMENT_IN")
    private String treatmentIn;

    /**
     * 处置后
     */
    @TableField("TREATMENT_AFTER")
    private String treatmentAfter;

    @TableField("RECTIFICATION_LEDGER_ID")
    private String rectificationLedgerId;

    @TableField("IS_DELETE")
    private String isDelete;

    /**
     * DISEASE_EXTRA_DESC
     */
    @TableField("DISEASE_EXTRA_DESC")
    private String diseaseExtraDesc;

    /**
     * 工程进度描述
     */
    @TableField("PROJECT_PROGRESS_DESC")
    private String projectProgressDesc;

    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    public String getTreatmentBeforeMain() {
        return treatmentBeforeMain;
    }

    public void setTreatmentBeforeMain(String treatmentBeforeMain) {
        this.treatmentBeforeMain = treatmentBeforeMain;
    }

    public String getProjectProgressDesc() {
        return projectProgressDesc;
    }

    public void setProjectProgressDesc(String projectProgressDesc) {
        this.projectProgressDesc = projectProgressDesc;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getDiseaseSituation() {
        return diseaseSituation;
    }

    public void setDiseaseSituation(String diseaseSituation) {
        this.diseaseSituation = diseaseSituation;
    }

    public String getTakeSteps() {
        return takeSteps;
    }

    public void setTakeSteps(String takeSteps) {
        this.takeSteps = takeSteps;
    }

    public String getTreatmentBefore() {
        return treatmentBefore;
    }

    public void setTreatmentBefore(String treatmentBefore) {
        this.treatmentBefore = treatmentBefore;
    }

    public String getTreatmentIn() {
        return treatmentIn;
    }

    public void setTreatmentIn(String treatmentIn) {
        this.treatmentIn = treatmentIn;
    }

    public String getTreatmentAfter() {
        return treatmentAfter;
    }

    public void setTreatmentAfter(String treatmentAfter) {
        this.treatmentAfter = treatmentAfter;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public String getRectificationLedgerId() {
        return rectificationLedgerId;
    }

    public void setRectificationLedgerId(String rectificationLedgerId) {
        this.rectificationLedgerId = rectificationLedgerId;
    }

    public String getDiseaseExtraDesc() {
        return diseaseExtraDesc;
    }

    public void setDiseaseExtraDesc(String diseaseExtraDesc) {
        this.diseaseExtraDesc = diseaseExtraDesc;
    }
}