package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.DiseaseSituation;

public interface DiseaseSituationMapper extends BaseMapper<DiseaseSituation> {
    int deleteByPrimaryKey(String dataId);

    int insert(DiseaseSituation record);

    int insertSelective(DiseaseSituation record);

    DiseaseSituation selectByPrimaryKey(String dataId);

    int updateByPrimaryKeySelective(DiseaseSituation record);

    int updateByPrimaryKey(DiseaseSituation record);
}