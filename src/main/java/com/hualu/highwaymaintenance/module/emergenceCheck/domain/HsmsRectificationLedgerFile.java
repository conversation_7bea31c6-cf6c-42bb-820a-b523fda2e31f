package com.hualu.highwaymaintenance.module.emergenceCheck.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
    * 文件表
    */
@ApiModel(description="文件表")
@TableName(value = "HSMSDB.HSMS_RECTIFICATION_LEDGER_FILE")
public class HsmsRectificationLedgerFile {
    @ApiModelProperty(value="")
    private String dataId;

    /**
    * 文件地址
    */
    @ApiModelProperty(value="文件地址")
    private String filePath;

    /**
    * 文件名
    */
    @ApiModelProperty(value="文件名")
    private String fileName;

    /**
    * 文件大小
    */
    @ApiModelProperty(value="文件大小")
    private String fileSize;

    /**
    * 文件描述
    */
    @ApiModelProperty(value="文件描述")
    private String fileDescribe;

    /**
    * 删除否
    */
    @ApiModelProperty(value="删除否")
    private String isDelete;

    /**
    * 文件类型（业务类型）1、结构物全貌正面照；2、结构物立面照；
3、结构物俯视照；4、总体图、5、典型断面图；6、工点设计图(立面图)；
7、最近次定检报告；8、监测报告；9、测点布设图；10、最近次养护维修；
11、应急处治施工图纸；12、完工后立面照;13、处治资料,14.监测资料上传

    */
    @ApiModelProperty(value="文件类型（业务类型）1、结构物全貌正面照；2、结构物立面照；,3、结构物俯视照；4、总体图、5、典型断面图；6、工点设计图(立面图)；,7、最近次定检报告；8、监测报告；9、测点布设图；10、最近次养护维修；,11、应急处治施工图纸；12、完工后立面照;13、处治资料,14.监测资料上传,")
    private String fileType;

    /**
    * 上传时间
    */
    @ApiModelProperty(value="上传时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
    * 对应数据项id
    */
    @ApiModelProperty(value="对应数据项id")
    private String rectificationLedgerId;

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileDescribe() {
        return fileDescribe;
    }

    public void setFileDescribe(String fileDescribe) {
        this.fileDescribe = fileDescribe;
    }

    public String getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRectificationLedgerId() {
        return rectificationLedgerId;
    }

    public void setRectificationLedgerId(String rectificationLedgerId) {
        this.rectificationLedgerId = rectificationLedgerId;
    }
}