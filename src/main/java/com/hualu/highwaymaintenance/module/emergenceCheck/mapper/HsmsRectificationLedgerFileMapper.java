package com.hualu.highwaymaintenance.module.emergenceCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.highwaymaintenance.module.emergenceCheck.domain.HsmsRectificationLedgerFile;

public interface HsmsRectificationLedgerFileMapper extends BaseMapper<HsmsRectificationLedgerFile> {
    int deleteByPrimaryKey(String dataId);

    int insert(HsmsRectificationLedgerFile record);

    int insertSelective(HsmsRectificationLedgerFile record);

    HsmsRectificationLedgerFile selectByPrimaryKey(String dataId);

    int updateByPrimaryKeySelective(HsmsRectificationLedgerFile record);

    int updateByPrimaryKey(HsmsRectificationLedgerFile record);
}