package com.hualu.highwaymaintenance.module.clearObstacles.mapper;

import com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesSendUserDto;
import com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesStatDto;
import com.hualu.highwaymaintenance.module.clearObstacles.entity.ClearObstacles;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CLEAR_OBSTACLES(清障)】的数据库操作Mapper
* @createDate 2024-08-26 10:36:17
* @Entity com.hualu.highwaymaintenance.module.clearObstacles.entity.ClearObstacles
*/
public interface ClearObstaclesMapper extends BaseMapper<ClearObstacles> {

    List<ClearObstaclesSendUserDto> selectSendUsers(@Param("idList") List<String> idList);

    List<ClearObstaclesStatDto> getClearObstaclesStat(@Param("orgCodes") List<String> orgCodes,@Param("month") String month);
}




