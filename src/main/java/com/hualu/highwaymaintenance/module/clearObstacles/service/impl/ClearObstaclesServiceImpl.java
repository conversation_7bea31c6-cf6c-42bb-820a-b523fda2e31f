package com.hualu.highwaymaintenance.module.clearObstacles.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.highwaymaintenance.common.exception.BusinessException;
import com.hualu.highwaymaintenance.common.vo.TokenMsg;
import com.hualu.highwaymaintenance.module.accpt.domain.DmTaskAccpt;
import com.hualu.highwaymaintenance.module.accpt.service.DmTaskAccptDetailService;
import com.hualu.highwaymaintenance.module.accpt.service.DmTaskAccptService;
import com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesSendUserDto;
import com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesStatDto;
import com.hualu.highwaymaintenance.module.clearObstacles.entity.ClearObstacles;
import com.hualu.highwaymaintenance.module.clearObstacles.service.ClearObstaclesService;
import com.hualu.highwaymaintenance.module.clearObstacles.mapper.ClearObstaclesMapper;
import com.hualu.highwaymaintenance.module.file.service.DssImageService;
import com.hualu.highwaymaintenance.module.file.service.MongoService;
import com.hualu.highwaymaintenance.module.mpItem.entity.MpcMpitemPrice;
import com.hualu.highwaymaintenance.module.mpItem.service.MpcMpitemPriceService;
import com.hualu.highwaymaintenance.module.user.domain.FwRightOrg;
import com.hualu.highwaymaintenance.module.user.service.FwRightOrgService;
import com.hualu.highwaymaintenance.util.StringUtil;
import com.hualu.highwaymaintenance.util.TokenUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【CLEAR_OBSTACLES(清障)】的数据库操作Service实现
* @createDate 2024-08-26 10:36:17
*/
@Service
public class ClearObstaclesServiceImpl extends ServiceImpl<ClearObstaclesMapper, ClearObstacles>
    implements ClearObstaclesService{

    @Resource
    private DmTaskAccptService dmTaskAccptService;

    @Resource
    private DmTaskAccptDetailService dmTaskAccptDetailService;

    @Resource
    private MpcMpitemPriceService mpcMpitemPriceService;

    @Resource
    private DssImageService dssImageService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    FwRightOrgService orgService;

    public static String fileReposity = "D:\\upload\\temp\\";

    @Transactional
    @Override
    public void saveOrUpdateClear(ClearObstacles clearObstacles, MultipartFile[] before, MultipartFile[] after) {
        TokenMsg tokenMap = TokenUtils.getTokenMap();
        String userCode = tokenMap.getUserCode();
        String id = clearObstacles.getId();
        int orgGrpFlag = tokenMap.getOrgGrpFlag();
        if(orgGrpFlag == 0 && (after == null || after.length == 0)){
            throw new BusinessException("须上传处理后照片！");
        }
        if(StringUtils.isBlank(id)){
            clearObstacles.setOrgCode(tokenMap.getOrgId());
            clearObstacles.setType(orgGrpFlag);
            clearObstacles.setCreateUser(userCode);
            clearObstacles.setCreateTime(new Date());
            clearObstacles.setId(StringUtil.getUUID());
            clearObstacles.setPerson(tokenMap.getUserName());
            setFileIds(clearObstacles,before,0,null,tokenMap);
            setFileIds(clearObstacles,after,1,null,tokenMap);
        }else{
            LambdaQueryWrapper<ClearObstacles> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ClearObstacles::getId,id);
            ClearObstacles obstacles = getOne(wrapper);
            if(obstacles == null){
                throw new BusinessException("此ID（"+id+"）的记录不存在！");
            }
            setFileIds(clearObstacles,before,0,obstacles.getBeforeImages(),tokenMap);
            setFileIds(clearObstacles,after,1,obstacles.getAfterImages(),tokenMap);
        }
        clearObstacles.setUpdateTime(new Date());
        clearObstacles.setUpdateUser(userCode);
        clearObstacles.setStatus(0);
        saveOrUpdate(clearObstacles);
    }

    private void setFileIds(ClearObstacles clearObstacles,MultipartFile[] files,int type,String originaImages,TokenMsg tokenMap){
        if(files != null && files.length > 0) {
            originaImages = StringUtils.isBlank(originaImages) ? "" : (originaImages + ",");
            int length = files.length;
            File repository = new File(fileReposity);
            if (!repository.exists()) {
                repository.mkdirs();
            }
            DateFormat dfDate = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = new Date();
            for(int i = 0; i < length; i ++){
                MultipartFile file = files[i];
                String fileName = tokenMap.getOrgId() + tokenMap.getUserCode() + dfDate.format(date) + file.getOriginalFilename();
                File destFile = new File(fileReposity + fileName);
                try {
                    file.transferTo(destFile);
                    String fileId = mongoService.uploadFileForTg(destFile);
                    destFile.delete();
                    originaImages += fileId + ((i < length - 1) ? ",":"");
                } catch (Exception e) {
                    throw new BusinessException("图片解析失败");
                }
            }
            if(type == 0){
                clearObstacles.setBeforeImages(originaImages);
            }else{
                clearObstacles.setAfterImages(originaImages);
            }
        }
    }

    @Transactional
    @Override
    public void sendAccpet() {
        TokenMsg tokenMap = TokenUtils.getTokenMap();
        DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate now = LocalDate.now();
        LambdaQueryWrapper<ClearObstacles> wrapper = new LambdaQueryWrapper<>();
        String orgId = tokenMap.getOrgId();
        wrapper.eq(ClearObstacles::getOrgCode, orgId);
        wrapper.eq(ClearObstacles::getStatus, 0);
        //wrapper.apply("to_char(create_time,'yyyy-MM-dd') = {0}", now.format(format));
        List<ClearObstacles> list = list(wrapper);
        if(null == list || list.size() == 0) {
            throw new BusinessException(now.format(format) + "当天没有待处理的清障记录！");
        }
        List<MpcMpitemPrice> contrAndPrice = mpcMpitemPriceService.queryContrAndPrice(orgId);
        if(null == contrAndPrice || contrAndPrice.size() == 0) {
            throw new BusinessException("没有对应的合同！");
        }
        MpcMpitemPrice mpcMpitemPrice = contrAndPrice.get(0);
        LambdaQueryWrapper<DmTaskAccpt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DmTaskAccpt::getIsClear,1);
        queryWrapper.eq(DmTaskAccpt::getStatus,0);
        queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') = {0}", now.format(format));
        DmTaskAccpt dmTaskAccpt = dmTaskAccptService.getOne(queryWrapper);
        if(null == dmTaskAccpt){
            dmTaskAccpt = dmTaskAccptService.createDmTaskAccptForClear(tokenMap);
        }
        dmTaskAccptDetailService.createDetailForClear(dmTaskAccpt.getMtaskAccptId(),mpcMpitemPrice,list);
        list.forEach(m->{
            String stake = StringUtil.getDisplayStake(m.getStake().doubleValue());
            m.setStatus(1);
            String beforeImages = m.getBeforeImages();
            if(StringUtils.isBlank(beforeImages)){
                throw new BusinessException("桩号为"+ stake +"的施工前图片为空！");
            }
            String afterImages = m.getAfterImages();
            if(StringUtils.isBlank(afterImages)){
                throw new BusinessException("桩号为"+ stake +"的施工后图片为空！");
            }
            dssImageService.insertImageByClear(m.getId(),beforeImages.split(","),0);
            dssImageService.insertImageByClear(m.getId(),afterImages.split(","),1);
            this.baseMapper.updateById(m);
        });
    }

    @Transactional
    @Override
    public void removeImage(String id,String fileId, String type) {
        LambdaQueryWrapper<ClearObstacles> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ClearObstacles::getId,id);
        ClearObstacles clearObstacles = getOne(wrapper);
        if(clearObstacles == null){
            throw new BusinessException("记录不存在！");
        }
        if(clearObstacles.getStatus() != 0){
            throw new BusinessException("已提交的记录不能删除图片！");
        }
        if("after".equals(type)){
            String afterImages = clearObstacles.getAfterImages();
            String[] images = afterImages.split(",");
            if(images != null && images.length > 0) {
                List<String> list = new ArrayList<>(Arrays.asList(images));
                list.remove(fileId);
                clearObstacles.setAfterImages(list.stream().map(String::valueOf).collect(Collectors.joining(",")));
                updateById(clearObstacles);
            }
        }else{
            String beforeImages = clearObstacles.getBeforeImages();
            String[] images = beforeImages.split(",");
            if(images != null && images.length > 0) {
                List<String> list = new ArrayList<>(Arrays.asList(images));
                list.remove(fileId);
                clearObstacles.setBeforeImages(list.stream().map(String::valueOf).collect(Collectors.joining(",")));
                updateById(clearObstacles);
            }
        }
    }

    @Override
    public void initSendUser(List<ClearObstacles> clearObstacles) {

        if (CollectionUtil.isEmpty(clearObstacles)) {
            return;
        }
        //获取清账ID
        List<String> idList = clearObstacles.stream().map(ClearObstacles::getId).collect(Collectors.toList());
        List<ClearObstaclesSendUserDto> dtoList = baseMapper.selectSendUsers(idList);
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, String> userMap = dtoList.stream().collect(Collectors.toMap(ClearObstaclesSendUserDto::getId, ClearObstaclesSendUserDto::getSendUserName));
        clearObstacles.forEach(item->{
            String sendUserName = userMap.get(item.getId());
            item.setSendUserName(sendUserName);
        });
    }

    @Override
    public List<ClearObstaclesStatDto> getClearObstaclesStat(String orgCode,String month) {
        List<FwRightOrg> fwRightOrgs = orgService.queryAllChilds(orgCode);
        List<String> orgCodes = fwRightOrgs.stream().map(FwRightOrg::getOrgCode).collect(Collectors.toList());
        List<ClearObstaclesStatDto> statDtos = baseMapper.getClearObstaclesStat(orgCodes,month);
        return statDtos;
    }
}




