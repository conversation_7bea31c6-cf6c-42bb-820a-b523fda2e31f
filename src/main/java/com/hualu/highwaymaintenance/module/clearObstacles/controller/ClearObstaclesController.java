package com.hualu.highwaymaintenance.module.clearObstacles.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hualu.highwaymaintenance.common.exception.BusinessException;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.common.vo.TokenMsg;
import com.hualu.highwaymaintenance.module.baseline.domain.BaseLine;
import com.hualu.highwaymaintenance.module.baseline.service.BaseLineService;
import com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesStatDto;
import com.hualu.highwaymaintenance.module.clearObstacles.entity.ClearObstacles;
import com.hualu.highwaymaintenance.module.clearObstacles.service.ClearObstaclesService;
import com.hualu.highwaymaintenance.module.datareport.service.impl.OrgServiceImpl;
import com.hualu.highwaymaintenance.module.platform.entity.BaseRouteLogic;
import com.hualu.highwaymaintenance.util.TokenUtils;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/clearObstacles")
@ApiOperation(nickname = "清障", value = "清障")
public class ClearObstaclesController {

    @Resource
    private ClearObstaclesService clearObstaclesService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private BaseLineService baseLineService;

    @Resource
    private OrgServiceImpl orgService;

    @GetMapping("/list")
    @ApiOperation(nickname = "获取列表", value = "获取列表")
    public RestResult<List<ClearObstacles>> list(String dateTime,Integer status,int pageIndex,int pageSize) {
        IPage<ClearObstacles> page = new Page<>(pageIndex, pageSize);
        LambdaQueryWrapper<ClearObstacles> queryWrapper = new LambdaQueryWrapper<>();
        TokenMsg tokenMap = TokenUtils.getTokenMap();
        String orgId = tokenMap.getOrgId();
        queryWrapper.eq(ClearObstacles::getOrgCode, orgId);
        queryWrapper.eq(null != status,ClearObstacles::getStatus, status);
        if(StringUtils.isBlank(dateTime)){
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate now = LocalDate.now();
            LocalDate last = now.minusMonths(1);
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') <= {0}", now.format(format));
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') >= {0}", last.format(format));
        }else{
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') = {0}",dateTime);
        }
        queryWrapper.orderByDesc(ClearObstacles::getUpdateTime);
        IPage<ClearObstacles> iPage = clearObstaclesService.page(page, queryWrapper);
        List<ClearObstacles> records = iPage.getRecords();
        if(records != null && !records.isEmpty()){
            List<String> lineCodes = records.stream().map(r -> r.getLineCode()).collect(Collectors.toList());
            LambdaQueryWrapper<BaseLine> baseLineQueryWrapper = new LambdaQueryWrapper<>();
            baseLineQueryWrapper.in(BaseLine::getLineCode,lineCodes);
            List<BaseLine> baseLines = baseLineService.list(baseLineQueryWrapper);
            records.forEach(r->{
                String lineCode = r.getLineCode();
                for(BaseLine line : baseLines){
                    String code = line.getLineCode();
                    if(StringUtils.isNotBlank(code) && code.equals(lineCode)){
                        r.setLineName(line.getLineAllname()+"("+code+")");
                        break;
                    }
                }
                Integer lineDirect = r.getLineDirect();
                r.setLineDirectName(1 == lineDirect ? "上行" : (2 == lineDirect ? "下行" : "全线"));
            });
            clearObstaclesService.initSendUser(records);
        }
      //List<ClearObstacles> resultList = records.stream()
      //        .sorted(Comparator.comparing(ClearObstacles::getAfterImages,Comparator.nullsFirst(String::compareTo))
      //        .thenComparing(ClearObstacles::getCreateTime).reversed())
      //        .collect(Collectors.toList());
      return RestResult.success(records, iPage.getTotal(), pageIndex, pageSize);
    }

    @GetMapping("/count")
    @ApiOperation(nickname = "获取数量", value = "获取数量")
    public RestResult<String> count(String dateTime, Integer status) {
        LambdaQueryWrapper<ClearObstacles> queryWrapper = new LambdaQueryWrapper<>();
        TokenMsg tokenMap = TokenUtils.getTokenMap();
        String orgId = tokenMap.getOrgId();
        queryWrapper.eq(ClearObstacles::getOrgCode, orgId);
        queryWrapper.eq(null != status, ClearObstacles::getStatus, status);
        if (StringUtils.isBlank(dateTime)) {
            DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate now = LocalDate.now();
            LocalDate last = now.minusMonths(1);
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') <= {0}", now.format(format));
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') >= {0}", last.format(format));
        } else {
            queryWrapper.apply("to_char(create_time,'yyyy-MM-dd') = {0}", dateTime);
        }
        long count = clearObstaclesService.count(queryWrapper);
        return RestResult.success(count + "", "");
    }


    @GetMapping("/init")
    @ApiOperation(nickname = "初始化", value = "初始化")
    public RestResult<ClearObstacles> init() {
        ClearObstacles clearObstacles = new ClearObstacles();
        TokenMsg tokenMap = TokenUtils.getTokenMap();
        String orgId = tokenMap.getOrgId();
        String userCode = tokenMap.getUserCode();
        clearObstacles.setLineDirect(1);
        int orgGrpFlag = tokenMap.getOrgGrpFlag();
        clearObstacles.setType(orgGrpFlag != 1 ? 0 : 1);
        clearObstacles.setLineDirectName("上行");
        List<BaseRouteLogic> route = orgService.getManageRoute(userCode);
        if(route == null || route.isEmpty()){
            throw new BusinessException("获取路线失败！");
        }
        BaseRouteLogic baseRouteLogic = route.get(0);
        String lineCode = baseRouteLogic.getLineCode();
        String lineId = baseRouteLogic.getLineId();
        clearObstacles.setLineCode(lineCode);
        clearObstacles.setLineId(lineId);
        BaseLine baseLine = baseLineService.getById(lineId);
        if(baseLine == null){
            throw new BusinessException("获取路线失败！");
        }
        clearObstacles.setLineName(baseLine.getLineAllname()+"("+lineCode+")");
        BaseRouteLogic stakeRange = orgService.getStakeRange(userCode, orgId, lineCode);
        if(stakeRange == null || stakeRange.getStartStake() == null || stakeRange.getEndStake() == null){
            throw new BusinessException("获取桩号范围失败！");
        }
        clearObstacles.setMinStake(stakeRange.getStartStake());
        clearObstacles.setMaxStake(stakeRange.getEndStake());
        return RestResult.success(clearObstacles, "获取成功");
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation(nickname = "保存或更新", value = "保存或更新")
    public RestResult<String> saveOrUpdate(String data,
                                           MultipartFile[] beforeFiles,
                                           MultipartFile[] afterFiles)
            throws JsonProcessingException {
        ClearObstacles clearObstacles = objectMapper.readValue(data, ClearObstacles.class);
        String id = clearObstacles.getId();
        if(StringUtils.isNotBlank(id)){
            LambdaQueryWrapper<ClearObstacles> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ClearObstacles::getId, id);
            ClearObstacles one = clearObstaclesService.getOne(queryWrapper);
            if(one == null){
                throw new BusinessException("此ID（"+id+"）的记录不存在！");
            }
            if(one.getStatus() != 0){
                throw new BusinessException("此记录已经提交，不可更改！");
            }
        }
        if(clearObstacles.getTimes() == null){
            throw new BusinessException("工程数量不能为空！");
        }
        if(StringUtils.isBlank(id) && (null == beforeFiles || beforeFiles.length < 1)){
            throw new BusinessException("施工前照片不能为空！");
        }
        clearObstaclesService.saveOrUpdateClear(clearObstacles, beforeFiles, afterFiles);
        return RestResult.success("操作成功");
    }

    @PostMapping("/removeImage")
    @ApiOperation(nickname = "removeImage",value ="删除照片" )
    public RestResult<String> removeImage(
            String id,String type,String fileId
    ){
        clearObstaclesService.removeImage(id,fileId,type);
        return RestResult.success("删除成功");
    }

    @ApiOperation(nickname = "removeById",value ="删除清障" )
    @GetMapping("removeById")
    public RestResult<String> removeById(String id){
        ClearObstacles dbEntity = clearObstaclesService.getById(id);
        if (dbEntity != null && dbEntity.getStatus() == 1){
            throw new BusinessException("已提交的数据无法删除！");
        }
        clearObstaclesService.removeById(id);
        return RestResult.success("删除成功");
    }

    @PostMapping("/sendAccpet")
    @ApiOperation(nickname = "提交验收", value = "提交验收")
    public RestResult<String> sendAccpet() {
        clearObstaclesService.sendAccpet();
        return RestResult.success("操作成功");
    }

    @ApiOperation(nickname = "按照月份、单位、统计推送个数", value = "按照月份、单位、统计推送个数")
    @GetMapping("/getClearObstaclesStat")
    public RestResult<List<ClearObstaclesStatDto>> getClearObstaclesStat(String orgCode,String month) {
        List<ClearObstaclesStatDto> statList = clearObstaclesService.getClearObstaclesStat(orgCode, month);
        return RestResult.success(statList);
    }
}