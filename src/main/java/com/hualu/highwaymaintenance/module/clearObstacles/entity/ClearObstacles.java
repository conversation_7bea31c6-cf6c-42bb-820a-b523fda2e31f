package com.hualu.highwaymaintenance.module.clearObstacles.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 清障
 * @TableName CLEAR_OBSTACLES
 */
@TableName(value ="MEMSDB.CLEAR_OBSTACLES")
public class ClearObstacles implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID")
    private String id;

    /**
     * 路线编码
     */
    @TableField(value = "LINE_CODE")
    private String lineCode;

    /**
     * 桩号
     */
    @TableField(value = "STAKE")
    private BigDecimal stake;

    /**
     * 工程数量
     */
    @TableField(value = "TIMES")
    private Integer times;

    /**
     * 施工前照片
     */
    @TableField(value = "BEFORE_IMAGES")
    private String beforeImages;

    /**
     * 施工后照片
     */
    @TableField(value = "AFTER_IMAGES")
    private String afterImages;

    /**
     * 病害位置描述
     */
    @TableField(value = "DESCRIPTION")
    private String description;

    /**
     * 组织机构
     */
    @TableField(value = "ORG_CODE")
    private String orgCode;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 创建用户
     */
    @TableField(value = "CREATE_USER")
    private String createUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 更新用户
     */
    @TableField(value = "UPDATE_USER")
    private String updateUser;

    /**
     * 状态
     */
    @TableField(value = "STATUS")
    private Integer status;

    /**
     * 巡查人
     */
    @TableField(value = "PERSON")
    private String person;

    /**
     * 巡查人
     */
    @TableField(value = "LINE_ID")
    private String lineId;

    /**
     * 巡查标识（养护单位0，其他1）
     */
    @TableField(value = "TYPE")
    private Integer type;

    /**
     * 路线方向
     */
    @TableField(value = "LINE_DIRECT")
    private Integer lineDirect;

    @TableField(exist = false)
    private String lineDirectName;

    @TableField(exist = false)
    private String lineName;

    @TableField(exist = false)
    private BigDecimal minStake;

    @TableField(exist = false)
    private BigDecimal maxStake;

    /**
     * 推送人
     */
    @TableField(exist = false)
    private String sendUserName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public String getSendUserName() {
        return sendUserName;
    }

    public void setSendUserName(String sendUserName) {
        this.sendUserName = sendUserName;
    }

    public String getLineDirectName() {
        return lineDirectName;
    }

    public void setLineDirectName(String lineDirectName) {
        this.lineDirectName = lineDirectName;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public BigDecimal getStake() {
        return stake;
    }

    public void setStake(BigDecimal stake) {
        this.stake = stake;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public String getBeforeImages() {
        return beforeImages;
    }

    public void setBeforeImages(String beforeImages) {
        this.beforeImages = beforeImages;
    }

    public String getAfterImages() {
        return afterImages;
    }

    public void setAfterImages(String afterImages) {
        this.afterImages = afterImages;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPerson() {
        return person;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public Integer getLineDirect() {
        return lineDirect;
    }

    public void setLineDirect(Integer lineDirect) {
        this.lineDirect = lineDirect;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public BigDecimal getMinStake() {
        return minStake;
    }

    public void setMinStake(BigDecimal minStake) {
        this.minStake = minStake;
    }

    public BigDecimal getMaxStake() {
        return maxStake;
    }

    public void setMaxStake(BigDecimal maxStake) {
        this.maxStake = maxStake;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}