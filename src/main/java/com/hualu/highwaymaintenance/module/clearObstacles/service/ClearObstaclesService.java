package com.hualu.highwaymaintenance.module.clearObstacles.service;

import com.hualu.highwaymaintenance.module.clearObstacles.dto.ClearObstaclesStatDto;
import com.hualu.highwaymaintenance.module.clearObstacles.entity.ClearObstacles;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CLEAR_OBSTACLES(清障)】的数据库操作Service
* @createDate 2024-08-26 10:36:17
*/
public interface ClearObstaclesService extends IService<ClearObstacles> {

    void saveOrUpdateClear(ClearObstacles clearObstacles, MultipartFile[] before,MultipartFile[] after);

    void sendAccpet();

    void removeImage(String id,String fileId, String type);

    /**
     * 初始化推送人
     * @param clearObstacles
     */
    void initSendUser(List<ClearObstacles> clearObstacles);

    List<ClearObstaclesStatDto> getClearObstaclesStat(String orgCode,String month);
}
