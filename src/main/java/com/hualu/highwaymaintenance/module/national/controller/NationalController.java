package com.hualu.highwaymaintenance.module.national.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.highwaymaintenance.common.exception.BusinessException;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.common.vo.TokenMsg;
import com.hualu.highwaymaintenance.module.dailycheck.service.DmDinspService;
import com.hualu.highwaymaintenance.module.dailycheck.vo.*;
import com.hualu.highwaymaintenance.module.national.entity.*;
import com.hualu.highwaymaintenance.module.national.service.*;
import com.hualu.highwaymaintenance.module.national.vo.MaintaincePlanVo;
import com.hualu.highwaymaintenance.module.user.domain.FwRightOrg;
import com.hualu.highwaymaintenance.util.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.ibatis.annotations.Param;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 国评控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-11 15:18
 */
@RestController
@CrossOrigin
@RequestMapping("/national")
public class NationalController {

  @Resource
  private NationalService nationalService;

  @Resource
  private PavementRepositoryNewService pavementRepositoryNewService;

  @Resource
  private BridgeRepositoryNewService bridgeRepositoryNewService;

  @Resource
  private TunnelRepositoryNewService tunnelRepositoryNewService;

  @Resource
  private PavementPlanImpService pavementPlanImpService;

  @Resource
  private BridgePlanImpService bridgePlanImpService;

  @Resource
  private TunnelPlanImpService tunnelPlanImpService;

  @Resource
  private PavementRepositoryImpService pavementRepositoryImpService;

  @Resource
  private BridgeRepositoryImpService bridgeRepositoryImpService;

  @Resource
  private TunnelRepositoryImpService tunnelRepositoryImpService;

  @Resource
  private NationalAnnexService nationalAnnexService;

  @Resource
  private BridgeSuggestService bridgeSuggestService;

  @Resource
  private PavementSuggestService pavementSuggestService;

  @Resource
  private TunnelSuggestService tunnelSuggestService;

  @Resource
  private BridgeSuggestOService bridgeSuggestOService;

  @Resource
  private PavementSuggestOService pavementSuggestOService;

  @Resource
  private TunnelSuggestOService tunnelSuggestOService;

  @Resource
  private AssessReportService assessReportService;

  @Resource
  private MaintaincePlanService maintaincePlanService;

  @Resource
  private RepositoryAdjustLogService repositoryAdjustLogService;

  @Resource
  private PavementSuggestTService pavementSuggestTService;

  @Resource
  private ExternalMatMeasureService externalMatMeasureService;

  @Resource
  private RepositoryPackageAnnexService repositoryPackageAnnexService;

  @Resource
  private DmDinspService dmDinspService;

  private static final String NATIONAL_ANNEX = "D:\\national_annex";

  private static final String NATIONAL_ASSESS = "D:\\national_assess";

  private static final String PACKAGE_ANNEX = "D:\\package_annex";

  @PostMapping("/exportTunnelRepository")
  @ApiOperation(nickname = "exportTunnelRepository", value = "导出隧道项目库")
  public void exportTunnelRepository(
      String secondCompanyOrg,
      String threeCompanyOrg,
      Integer startYear,
      Integer endYear,
      Integer target,
      String lineType,
      HttpServletResponse response
  ) throws Exception {
    List<TunnelRepositoryNew> resultList =
        nationalService.exportTunnelRepository(secondCompanyOrg, threeCompanyOrg, startYear,
            endYear,null,target,lineType);

    if(resultList == null || resultList.isEmpty()){
       throw new BusinessException("没有数据");
    }

    Map<BigDecimal, List<TunnelRepositoryNew>> yearMap =
        resultList.stream().collect(Collectors.groupingBy(TunnelRepositoryNew::getYear));

    String fileName =
        URLEncoder.encode("广东省国家高速公路隧道养护工程项目库.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
        yearMap.keySet()
            .stream()
            .map(t -> t.intValue() + "年")
            .sorted(String::compareTo)
            .collect(Collectors.toList());

    String s = dynamicTemplate(
        "templates/national/隧道项目库.xlsx",
        yearList
    );

    try (
        InputStream inputStream = Files.newInputStream(new File(s).toPath());
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
            .withTemplate(inputStream)
            .build()
    ) {
      for (Map.Entry<BigDecimal, List<TunnelRepositoryNew>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<TunnelRepositoryNew> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        int totalPreventCount =
            list.stream().mapToInt(t -> MathUtil.intValue(t.getPreventTunnelCount())).sum();
        int totalRepairCount =
            list.stream().mapToInt(t -> MathUtil.intValue(t.getRepairTunnelCount())).sum();
        double totalPreventMileage =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventMileage())).sum();
        double totalRepairMileage =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairMileage())).sum();
        double totalCivilPreventConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventCivilConst())).sum();
        double totalCivilRepairConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairCivilConst())).sum();
        double totalCivilConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilTotalConst())).sum();
        double totalEleConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getEleTotalConst())).sum();
        double totalConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalConst())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventCount", totalPreventCount);
        map.put("totalRepairCount", totalRepairCount);
        map.put("totalPreventMileage", totalPreventMileage);
        map.put("totalRepairMileage", totalRepairMileage);
        map.put("totalCivilPreventConst", totalCivilPreventConst);
        map.put("totalCivilRepairConst", totalCivilRepairConst);
        map.put("totalCivilConst", totalCivilConst);
        map.put("totalEleConst", totalEleConst);
        map.put("totalConst", totalConst);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @PostMapping("/exportPavementRepository")
  @ApiOperation(nickname = "exportPavementRepository", value = "导出路面项目库")
  public void exportPavementRepository(
      String secondCompanyOrg,
      String threeCompanyOrg,
      Integer startYear,
      Integer endYear,
      Integer target,
      String lineType,
      HttpServletResponse response
  ) throws Exception {
    List<PavementRepositoryNew> resultList =
        nationalService.exportPavementRepository(secondCompanyOrg, threeCompanyOrg, startYear,
            endYear,null,target,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<BigDecimal, List<PavementRepositoryNew>> yearMap =
        resultList.stream().collect(Collectors.groupingBy(PavementRepositoryNew::getYear));

    String fileName =
        URLEncoder.encode("广东省国家高速公路路面养护工程项目库.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
        yearMap.keySet()
            .stream()
            .map(t -> t.intValue() + "年")
            .sorted(String::compareTo)
            .collect(Collectors.toList());

    String s = dynamicTemplate(
        "templates/national/路面项目库.xlsx",
        yearList
    );

    try (
        InputStream inputStream = Files.newInputStream(new File(s).toPath());
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
            .withTemplate(inputStream)
            .build()
    ) {
      for (Map.Entry<BigDecimal, List<PavementRepositoryNew>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<PavementRepositoryNew> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        double totalPreventMileage =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventMileage())).sum();
        double totalRepairMileage =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairMileage())).sum();
        double totalPreventConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventFee())).sum();
        double totalRepairConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairFee())).sum();
        double totalConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventMileage", totalPreventMileage);
        map.put("totalRepairMileage", totalRepairMileage);
        map.put("totalPreventConst", totalPreventConst);
        map.put("totalRepairConst", totalRepairConst);
        map.put("totalConst", totalConst);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @PostMapping("/exportBridgeRepository")
  @ApiOperation(nickname = "exportBridgeRepository", value = "导出桥梁项目库")
  public void exportBridgeRepository(
      String secondCompanyOrg,
      String threeCompanyOrg,
      Integer startYear,
      Integer endYear,
      Integer target,
      String lineType,
      HttpServletResponse response
  ) throws Exception {
    List<BridgeRepositoryNew> resultList =
        nationalService.exportBridgeRepository(secondCompanyOrg, threeCompanyOrg, startYear,
            endYear,null,target,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<BigDecimal, List<BridgeRepositoryNew>> yearMap =
        resultList.stream().collect(Collectors.groupingBy(BridgeRepositoryNew::getYear));

    String fileName =
        URLEncoder.encode("广东省国家高速公路桥梁养护工程项目库.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
        yearMap.keySet()
            .stream()
            .map(t -> t.intValue() + "年")
            .sorted(String::compareTo)
            .collect(Collectors.toList());

    String s = dynamicTemplate(
        "templates/national/桥梁项目库.xlsx",
        yearList
    );

    try (
        InputStream inputStream = Files.newInputStream(new File(s).toPath());
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
            .withTemplate(inputStream)
            .build()
    ) {
      for (Map.Entry<BigDecimal, List<BridgeRepositoryNew>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<BridgeRepositoryNew> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        int totalPreventCount =
            list.stream().mapToInt(t -> MathUtil.intValue(t.getPreventQuantity())).sum();
        int totalRepairCount =
            list.stream().mapToInt(t -> MathUtil.intValue(t.getRepairQuantity())).sum();
        double totalPreventMileage =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventMileage())).sum();
        double totalRepairMileage =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairMileage())).sum();
        double totalPreventConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventFee())).sum();
        double totalRepairConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairFee())).sum();
        double totalConst =
            list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventCount", totalPreventCount);
        map.put("totalRepairCount", totalRepairCount);
        map.put("totalPreventMileage", totalPreventMileage);
        map.put("totalRepairMileage", totalRepairMileage);
        map.put("totalPreventConst", totalPreventConst);
        map.put("totalRepairConst", totalRepairConst);
        map.put("totalConst", totalConst);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  private String dynamicTemplate(String path, Collection<String> sheetNames) {
    File dir = new File("C:/template/");
    dir.mkdirs();
    String tempFilePath = dir.getAbsolutePath() + "/" + CommonUtil.uuid() + ".xlsx";
    try (
        FileOutputStream out = new FileOutputStream(tempFilePath);
        InputStream inputStream = new ClassPathResource(path).getInputStream();
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream)
    ) {
      for (String sheetName : sheetNames) {
        XSSFSheet sheet = workbook.getSheet(sheetName);
        if (sheet == null) {
          workbook.cloneSheet(0, sheetName);
        }
      }
      workbook.removeSheetAt(0);
      workbook.write(out);
    } catch (Exception e) {
      e.printStackTrace();
    }
    return tempFilePath;
  }

  /**
   * 导入桥梁库
   */
  @PostMapping("/importBridgeRepository")
  @ApiOperation(nickname = "importBridgeRepository", value = "导入桥梁库")
  public RestResult<List<String>> importBridgeRepository(
      MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importBridgeRepository(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
        : RestResult.error(errorList, "导入失败");
  }

  /**
   * 导入路面库
   */
  @PostMapping("/importPavementRepository")
  @ApiOperation(nickname = "importPavementRepository", value = "导入路面库")
  public RestResult<List<String>> importPavementRepository(
      MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importPavementRepository(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
        : RestResult.error(errorList, "导入失败");
  }

  /**
   * 导入隧道路
   */
  @PostMapping("/importTunnelRepository")
  @ApiOperation(nickname = "importTunnelRepository", value = "导入隧道路")
  public RestResult<List<String>> importTunnelRepository(
      MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importTunnelRepository(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
        : RestResult.error(errorList, "导入失败");
  }

//  @GetMapping("/queryRepositoryData")
  @RequestMapping("/queryRepositoryData")
  @ApiOperation(nickname = "queryRepositoryData", value = "查询项目库数据")
  public Object queryRepositoryData(String type,
                                    String secondCompanyOrg,
                                    String threeCompanyOrg,
                                    Integer startYear,
                                    Integer endYear,
                                    Integer current,
                                    Integer size,
                                    Integer target,String lineType){
    System.out.println(type);
    if(current == null) {
      current = 1;
    }
    if(size == null){
      size = 50;
    }
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    if("LM".equals(type)){
      Page<PavementRepositoryNew> page = new Page<>(current, size);
      IPage<PavementRepositoryNew> resultPage = pavementRepositoryNewService.getPavementRepositoryPage(secondCompanyOrg, threeCompanyOrg, startYear, endYear, orgId,target,lineType, page);
      return resultPage;
    }else if("QL".equals(type)){
      Page<BridgeRepositoryNew> page = new Page<>(current, size);
      IPage<BridgeRepositoryNew> resultPage = bridgeRepositoryNewService.getBridgeRepositoryPage(secondCompanyOrg, threeCompanyOrg, startYear, endYear, orgId,target,lineType, page);
      return resultPage;
    }else{
      Page<TunnelRepositoryNew> page = new Page<>(current, size);
      IPage<TunnelRepositoryNew> resultPage = tunnelRepositoryNewService.getTunnelRepositoryPage(secondCompanyOrg, threeCompanyOrg, startYear, endYear, orgId,target,lineType, page);
      return resultPage;
    }
  }

  @GetMapping("/queryRepositoryAdjust")
  @ApiOperation(nickname = "queryRepositoryAdjust", value = "查询项目库调整数据")
  public Object queryRepositoryAdjust(String type,String orgCode,String packgeId,Integer target){
    if("LM".equals(type)){
      List<PavementRepositoryNew> result = pavementRepositoryNewService.getPavementRepository(null, orgCode, null, null, null, null,target,null);
      result.stream().forEach(r -> r.setPackageId(packgeId));
      return result;
    }else if("QL".equals(type)){
      List<BridgeRepositoryNew> result = bridgeRepositoryNewService.getBridgeRepository(null, orgCode, null, null, null, null,target,null);
      result.stream().forEach(r -> r.setPackageId(packgeId));
      return result;
    }else{
      List<TunnelRepositoryNew> result = tunnelRepositoryNewService.getTunnelRepository(null, orgCode, null, null, null, null,target,null);
      result.stream().forEach(r -> r.setPackageId(packgeId));
      return result;
    }
  }

  @PostMapping("/saveLMRepositoryAdjust")
  @ApiOperation(nickname = "saveLMRepositoryAdjust", value = "保存路面项目库调整数据")
  public String saveLMRepositoryAdjust(@RequestBody List<PavementRepositoryNew> list){
    if(list == null || list.isEmpty()){
      return "no";
    }
    Map<String,String> keyName = new HashMap<>();
    keyName.put("preventMileage","预防养护工程量（km）");
    keyName.put("repairMileage","修复养护工程量（km）");
    keyName.put("preventFee","预防养护费用估算（万元）");
    keyName.put("repairFee","修复养护费用估算（万元）");
    keyName.put("totalFee","费用估算合计（万元）");
    keyName.put("stake","路段桩号");
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    List<String> orgs = list.stream().map(PavementRepositoryNew::getOprtOrgCode).collect(Collectors.toList());
    String orgCode = orgs.get(0);
    Date date = new Date();
    SimpleDateFormat format = new SimpleDateFormat("yyyy");
    List<PavementRepositoryNew> result = pavementRepositoryNewService.getPavementRepository(null, orgCode, null, null, null, null,Integer.parseInt(format.format(date)),null);
    List<RepositoryAdjustLog> logs = new ArrayList<>();
    for(PavementRepositoryNew re : list){
      String packageId = re.getPackageId();
      if(StringUtils.isBlank(packageId)){
        throw new BusinessException("包ID为空");
      }
      String content = re.getYear() + "年--" + re.getOprtOrgName() + "--" + re.getLineCode() + "--" + re.getLineName() + "--" + re.getStake();
      String oporat = re.getOporat();
      if("删除".equals(oporat) || "新增".equals(oporat)){
        RepositoryAdjustLog log = new RepositoryAdjustLog();
        log.setTime(date);
        log.setContent(content);
        log.setType(oporat);
        log.setRepositoryId(re.getId());
        log.setOrgId(orgId);
        log.setUserCode(tokenMsg.getUserCode());
        log.setPackageId(packageId);
        log.setId(StringUtil.getUUID());
        logs.add(log);
        continue;
      }
      for(PavementRepositoryNew r : result){
        if(re.getId().equals(r.getId())){
          Map<String, List<Object>> resultMap = CompareUtil.compareFields(r, re, new String[]{"packageId"});
          if(!resultMap.isEmpty()){
            Set<String> keys = resultMap.keySet();
            for(String key : keys){
              List<Object> objects = resultMap.get(key);
              RepositoryAdjustLog log = new RepositoryAdjustLog();
              log.setBeforeVal(objects.get(0).toString());
              log.setAfterVal(objects.get(1).toString());
              log.setTime(date);
              log.setPropertyName(keyName.get(key));
              log.setContent(content);
              log.setType("修改");
              log.setRepositoryId(r.getId());
              log.setOrgId(orgId);
              log.setUserCode(tokenMsg.getUserCode());
              log.setPackageId(packageId);
              log.setId(StringUtil.getUUID());
              logs.add(log);
            }
          }
        }
      }
    }
    repositoryAdjustLogService.saveBatch(logs);
    return "ok";
  }

  @PostMapping("/saveQLRepositoryAdjust")
  @ApiOperation(nickname = "saveQLRepositoryAdjust", value = "保存桥梁项目库调整数据")
  public String saveQLRepositoryAdjust(@RequestBody List<BridgeRepositoryNew> list){
    if(list == null || list.isEmpty()){
      return "no";
    }
    Map<String,String> keyName = new HashMap<>();
    keyName.put("preventMileage","预防养护工程量（延米）");
    keyName.put("repairMileage","修复养护工程量（延米）");
    keyName.put("preventQuantity","预防养护（座）");
    keyName.put("repairQuantity","修复养护（座）");
    keyName.put("preventFee","预防养护费用估算（万元）");
    keyName.put("repairFee","修复养护费用估算（万元）");
    keyName.put("totalFee","费用估算合计（万元）");
    keyName.put("stake","路段桩号");
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    List<String> orgs = list.stream().map(BridgeRepositoryNew::getOprtOrgCode).collect(Collectors.toList());
    String orgCode = orgs.get(0);
    Date date = new Date();
    SimpleDateFormat format = new SimpleDateFormat("yyyy");
    List<BridgeRepositoryNew> result = bridgeRepositoryNewService.getBridgeRepository(null, orgCode, null, null, null, null,Integer.parseInt(format.format(date)),null);
    List<RepositoryAdjustLog> logs = new ArrayList<>();
    for(BridgeRepositoryNew re : list){
      String packageId = re.getPackageId();
      if(StringUtils.isBlank(packageId)){
        throw new BusinessException("包ID为空");
      }
      String content = re.getYear() + "年--" + re.getOprtOrgName() + "--" + re.getLineCode() + "--" + re.getLineName() + "--" + re.getStake();
      String oporat = re.getOporat();
      if("删除".equals(oporat) || "新增".equals(oporat)){
        RepositoryAdjustLog log = new RepositoryAdjustLog();
        log.setTime(date);
        log.setContent(content);
        log.setType(oporat);
        log.setRepositoryId(re.getId());
        log.setOrgId(orgId);
        log.setUserCode(tokenMsg.getUserCode());
        log.setPackageId(packageId);
        log.setId(StringUtil.getUUID());
        logs.add(log);
        continue;
      }
      for(BridgeRepositoryNew r : result){
        if(re.getId().equals(r.getId())){
          Map<String, List<Object>> resultMap = CompareUtil.compareFields(r, re, new String[]{"packageId"});
          if(!resultMap.isEmpty()){
            Set<String> keys = resultMap.keySet();
            for(String key : keys){
              List<Object> objects = resultMap.get(key);
              RepositoryAdjustLog log = new RepositoryAdjustLog();
              log.setBeforeVal(objects.get(0).toString());
              log.setAfterVal(objects.get(1).toString());
              log.setTime(date);
              log.setPropertyName(keyName.get(key));
              log.setContent(content);
              log.setType("修改");
              log.setRepositoryId(r.getId());
              log.setOrgId(orgId);
              log.setUserCode(tokenMsg.getUserCode());
              log.setPackageId(packageId);
              log.setId(StringUtil.getUUID());
              logs.add(log);
            }
          }
        }
      }
    }
    repositoryAdjustLogService.saveBatch(logs);
    return "ok";
  }

  @PostMapping("/saveSDRepositoryAdjust")
  @ApiOperation(nickname = "saveSDRepositoryAdjust", value = "保存隧道项目库调整数据")
  public String saveSDRepositoryAdjust(@RequestBody List<TunnelRepositoryNew> list){
    if(list == null || list.isEmpty()){
      return "no";
    }
    Map<String,String> keyName = new HashMap<>();
    keyName.put("preventMileage","预防养护工程量（延米）");
    keyName.put("repairMileage","修复养护工程量（延米）");
    keyName.put("preventTunnelCount","预防养护（座）");
    keyName.put("repairTunnelCount","修复养护（座）");
    keyName.put("preventCivilConst","土建预防养护费用估算（万元）");
    keyName.put("repairCivilConst","土建修复养护费用估算（万元）");
    keyName.put("civilTotalConst","土建护费用估算（万元）");
    keyName.put("eleTotalConst","机电护费用估算（万元）");
    keyName.put("totalConst","费用估算合计（万元）");
    keyName.put("stake","路段桩号");
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    List<String> orgs = list.stream().map(TunnelRepositoryNew::getOprtOrgCode).collect(Collectors.toList());
    String orgCode = orgs.get(0);
    Date date = new Date();
    SimpleDateFormat format = new SimpleDateFormat("yyyy");
    List<TunnelRepositoryNew> result = tunnelRepositoryNewService.getTunnelRepository(null, orgCode, null, null, null, null,Integer.parseInt(format.format(date)),null);
    List<RepositoryAdjustLog> logs = new ArrayList<>();
    for(TunnelRepositoryNew re : list){
      String packageId = re.getPackageId();
      if(StringUtils.isBlank(packageId)){
        throw new BusinessException("包ID为空");
      }
      String content = re.getYear() + "年--" + re.getOprtOrgName() + "--" + re.getLineCode() + "--" + re.getLineName() + "--" + re.getStake();
      String oporat = re.getOporat();
      if("删除".equals(oporat) || "新增".equals(oporat)){
        RepositoryAdjustLog log = new RepositoryAdjustLog();
        log.setTime(date);
        log.setContent(content);
        log.setType(oporat);
        log.setRepositoryId(re.getId());
        log.setOrgId(orgId);
        log.setUserCode(tokenMsg.getUserCode());
        log.setPackageId(packageId);
        log.setId(StringUtil.getUUID());
        logs.add(log);
        continue;
      }
      for(TunnelRepositoryNew r : result){
        if(re.getId().equals(r.getId())){
          Map<String, List<Object>> resultMap = CompareUtil.compareFields(r, re, new String[]{"packageId"});
          if(!resultMap.isEmpty()){
            Set<String> keys = resultMap.keySet();
            for(String key : keys){
              List<Object> objects = resultMap.get(key);
              RepositoryAdjustLog log = new RepositoryAdjustLog();
              log.setBeforeVal(objects.get(0).toString());
              log.setAfterVal(objects.get(1).toString());
              log.setTime(date);
              log.setPropertyName(keyName.get(key));
              log.setContent(content);
              log.setType("修改");
              log.setRepositoryId(r.getId());
              log.setOrgId(orgId);
              log.setUserCode(tokenMsg.getUserCode());
              log.setPackageId(packageId);
              log.setId(StringUtil.getUUID());
              logs.add(log);
            }
          }
        }
      }
    }
    repositoryAdjustLogService.saveBatch(logs);
    return "ok";
  }

  @GetMapping("/queryRepositoryDesc")
  @ApiOperation(nickname = "queryRepositoryDesc", value = "查询项目库数据备注和统计")
  public Map<String,Object> queryRepositoryDesc(String type,
                                    String secondCompanyOrg,
                                    String threeCompanyOrg,
                                    Integer startYear,
                                    Integer endYear,String lineType,Integer target){
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    Map<String,Object> map = new HashMap<>();
    StringBuilder sb = new StringBuilder("备注：");
    if(StringUtils.isBlank(secondCompanyOrg) && StringUtils.isBlank(threeCompanyOrg)){
      sb.append("省交通集团");
    }
    if("LM".equals(type)){
      sb.append("路面养护工程项目库预防性养护里程");
      BigDecimal preventFeeAll = BigDecimal.ZERO;
      BigDecimal repairFeeAll = BigDecimal.ZERO;
      BigDecimal totalFeeAll = BigDecimal.ZERO;
      BigDecimal preventMileageAll = BigDecimal.ZERO;
      BigDecimal repairMileageAll = BigDecimal.ZERO;
      BigDecimal totalMileageAll = BigDecimal.ZERO;
      List<PavementRepositoryNew> repository = pavementRepositoryNewService.getPavementRepository(secondCompanyOrg, threeCompanyOrg, startYear, endYear, orgId,null,target,lineType);
      if(null != repository && !repository.isEmpty()){
        for (PavementRepositoryNew re : repository) {
          BigDecimal preventFee = re.getPreventFee();
          preventFeeAll = preventFeeAll.add(preventFee);
          BigDecimal repairFee = re.getRepairFee();
          repairFeeAll = repairFeeAll.add(repairFee);
          BigDecimal totalFee = re.getTotalFee();
          totalFeeAll = totalFeeAll.add(totalFee);
          BigDecimal preventMileage = re.getPreventMileage();
          preventMileageAll = preventMileageAll.add(preventMileage);
          BigDecimal repairMileage = re.getRepairMileage();
          repairMileageAll = repairMileageAll.add(repairMileage);
          totalMileageAll = totalMileageAll.add(repairMileage).add(preventMileage);
        }
      }
      sb.append(preventMileageAll)
              .append("km，预防性养护费用")
              .append(preventFeeAll)
              .append("万元；修复性养护里程")
              .append(repairMileageAll)
              .append("km，修复性养护费用")
              .append(repairFeeAll)
              .append("万元，合计养护里程")
              .append(totalMileageAll)
              .append("km，合计养护费用")
              .append(totalFeeAll)
              .append("万元。");
      map.put("preventMileageAll",preventMileageAll);
      map.put("repairMileageAll",repairMileageAll);
      map.put("projectTotal",repository.size());
      map.put("totalFeeAll",totalFeeAll);
    }else if("QL".equals(type)){
      sb.append("桥梁养护工程项目库预防性养护");
      BigDecimal preventQuantityAll = BigDecimal.ZERO;
      BigDecimal repairQuantityAll = BigDecimal.ZERO;
      BigDecimal totalQuantityAll = BigDecimal.ZERO;
      BigDecimal preventFeeAll = BigDecimal.ZERO;
      BigDecimal repairFeeAll = BigDecimal.ZERO;
      BigDecimal totalFeeAll = BigDecimal.ZERO;
      BigDecimal preventMileageAll = BigDecimal.ZERO;
      BigDecimal repairMileageAll = BigDecimal.ZERO;
      BigDecimal totalMileageAll = BigDecimal.ZERO;
      List<BridgeRepositoryNew> repository = bridgeRepositoryNewService.getBridgeRepository(secondCompanyOrg, threeCompanyOrg, startYear, endYear, orgId,null,target,lineType);
      if(null != repository && !repository.isEmpty()){
        for (BridgeRepositoryNew re : repository) {
          BigDecimal preventQuantity = re.getPreventQuantity();
          preventQuantityAll = preventQuantityAll.add(preventQuantity);
          BigDecimal repairQuantity = re.getRepairQuantity();
          repairQuantityAll = repairQuantityAll.add(repairQuantity);
          totalQuantityAll = totalQuantityAll.add(preventQuantity).add(repairQuantity);
          BigDecimal preventFee = re.getPreventFee();
          preventFeeAll = preventFeeAll.add(preventFee);
          BigDecimal repairFee = re.getRepairFee();
          repairFeeAll = repairFeeAll.add(repairFee);
          BigDecimal totalFee = re.getTotalFee();
          totalFeeAll = totalFeeAll.add(totalFee);
          BigDecimal preventMileage = re.getPreventMileage();
          preventMileageAll = preventMileageAll.add(preventMileage);
          BigDecimal repairMileage = re.getRepairMileage();
          repairMileageAll = repairMileageAll.add(repairMileage);
          totalMileageAll = totalMileageAll.add(repairMileage).add(preventMileage);
        }
      }
      sb.append(preventQuantityAll)
              .append("座，预防性养护工程量")
              .append(preventMileageAll)
              .append("延米；预防性养护费用")
              .append(preventFeeAll)
              .append("万元，修复性养护")
              .append(repairQuantityAll)
              .append("座，修复性养护工程量")
              .append(repairMileageAll)
              .append("延米，修复性养护费用")
              .append(repairFeeAll)
              .append("万元；合计养护")
              .append(totalQuantityAll)
              .append("座，合计养护工程量")
              .append(totalMileageAll)
              .append("延米，合计养护费用")
              .append(totalFeeAll)
              .append("万元。");
      map.put("preventCountTotal",preventQuantityAll);
      map.put("repairCountTotal",repairQuantityAll);
      map.put("projectTotal",repository.size());
      map.put("totalFeeAll",totalFeeAll);
    }else{
      sb.append("隧道养护工程项目库预防性养护");
      BigDecimal preventQuantityAll = BigDecimal.ZERO;
      BigDecimal repairQuantityAll = BigDecimal.ZERO;
      BigDecimal totalQuantityAll = BigDecimal.ZERO;
      BigDecimal civilPreventFeeAll = BigDecimal.ZERO;
      BigDecimal civilRepairFeeAll = BigDecimal.ZERO;
      BigDecimal civilTotalFeeAll = BigDecimal.ZERO;
      BigDecimal elelTotalFeeAll = BigDecimal.ZERO;
      BigDecimal totalFeeAll = BigDecimal.ZERO;
      BigDecimal preventMileageAll = BigDecimal.ZERO;
      BigDecimal repairMileageAll = BigDecimal.ZERO;
      BigDecimal totalMileageAll = BigDecimal.ZERO;
      List<TunnelRepositoryNew> repository = tunnelRepositoryNewService.getTunnelRepository(secondCompanyOrg, threeCompanyOrg, startYear, endYear, orgId, null,target,lineType);
      if(null != repository && !repository.isEmpty()){
        for (TunnelRepositoryNew re : repository) {
          BigDecimal preventQuantity = re.getPreventTunnelCount();
          preventQuantityAll = preventQuantityAll.add(preventQuantity);
          BigDecimal repairQuantity = re.getRepairTunnelCount();
          repairQuantityAll = repairQuantityAll.add(repairQuantity);
          totalQuantityAll = totalQuantityAll.add(preventQuantity).add(repairQuantity);
          BigDecimal civilPreventFee = re.getPreventCivilConst();
          civilPreventFeeAll = civilPreventFeeAll.add(civilPreventFee);
          BigDecimal civilRepairFee = re.getRepairCivilConst();
          civilRepairFeeAll = civilRepairFeeAll.add(civilRepairFee);
          civilTotalFeeAll = civilTotalFeeAll.add(civilPreventFee).add(civilRepairFee);
          BigDecimal eleTotalConst = re.getEleTotalConst();
          elelTotalFeeAll = elelTotalFeeAll.add(eleTotalConst);
          BigDecimal totalConst = re.getTotalConst();
          totalFeeAll = totalFeeAll.add(totalConst);
          BigDecimal preventMileage = re.getPreventMileage();
          preventMileageAll = preventMileageAll.add(preventMileage);
          BigDecimal repairMileage = re.getRepairMileage();
          repairMileageAll = repairMileageAll.add(repairMileage);
          totalMileageAll = totalMileageAll.add(repairMileage).add(preventMileage);
        }
      }
      sb.append(preventQuantityAll)
              .append("座，预防性养护工程量")
              .append(preventMileageAll)
              .append("延米；土建预防性养护费用")
              .append(civilPreventFeeAll)
              .append("万元，修复性养护")
              .append(repairQuantityAll)
              .append("座，修复性养护工程量")
              .append(repairMileageAll)
              .append("延米，土建修复性养护费用")
              .append(civilRepairFeeAll)
              .append("万元；合计养护")
              .append(totalQuantityAll)
              .append("座，合计养护工程量")
              .append(totalMileageAll)
              .append("延米，土建合计养护费用")
              .append(civilTotalFeeAll)
              .append("万元，机电养护费用")
              .append(elelTotalFeeAll)
              .append("万元，合计养护费用")
              .append(totalFeeAll)
              .append("万元。");
      map.put("preventCountTotal",preventQuantityAll);
      map.put("repairCountTotal",repairQuantityAll);
      map.put("projectTotal",repository.size());
      map.put("totalFeeAll",totalFeeAll);
    }
    map.put("remark",sb.toString());
    return map;
  }

  @GetMapping("/queryRepositoryPackagePage")
  @ApiOperation(nickname = "queryRepositoryPackagePage", value = "查询审核项目")
  public RestResult<IPage<RepositoryPackage>> queryRepositoryPackagePage(String orgCode,
                                                                String packageType,
                                                                Integer status,
                                                                Integer current,
                                                                Integer size){
    RestResult<IPage<RepositoryPackage>> result = RestResult.success("加载成功");
    if(current == null) {
      current = 1;
    }
    if(size == null){
      size = 50;
    }
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    Page<RepositoryPackage> page = new Page<>(current, size);
    IPage<RepositoryPackage> repositoryPackage = nationalService.queryRepositoryPackagePage(page, tokenMsg.getOrgId(), packageType,
            status, tokenMsg.getUserCode(), tokenMsg.getOrgLevel() + "");
    List<RepositoryPackage> records = repositoryPackage.getRecords();
    records.stream().filter(record -> StringUtils.isNotBlank(record.getAd())).forEach(record -> record.setFlowType("日志"));
    result.setData(repositoryPackage);
    return result;
  }

  @ApiOperation(nickname = "savePackage", value = "保存项目库包")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "packageName", value = "包名"),
          @ApiImplicitParam(name = "packageType", value = "包类型"),
          @ApiImplicitParam(name = "remark", value = "备注")})
  @PostMapping("/saveRepositoryPackage")
  public Object saveRepositoryPackage( @RequestParam(name = "packageName", required = true) String packageName,
                                      @RequestParam(name = "packageType", required = true) String packageType,
                                      @RequestParam("remark") String remark,
                                      @RequestParam(name = "files",required = false) MultipartFile[] files,
                                      @RequestParam("adjust") String adjust) {
    RestResult<IPage<RepositoryPackage>> result = RestResult.success("保存成功");
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    nationalService.savePackage(tokenMsg.getOrgId(), packageType, packageName, remark, tokenMsg.getUserCode(),adjust,files);
    return result;
  }

  @GetMapping("/queryPackageAnnex")
  @ApiOperation(nickname = "queryPackageAnnex", value = "查询包附件")
  public RestResult<List<RepositoryPackageAnnex>> queryPackageAnnex(String packageId){
    QueryWrapper<RepositoryPackageAnnex> queryWrapper = new QueryWrapper();
    queryWrapper.eq("package_id",packageId);
    List<RepositoryPackageAnnex> list = repositoryPackageAnnexService.list(queryWrapper);
    return RestResult.success(list);
  }

  @PostMapping("/deletePackageAnnex")
  @ApiOperation(nickname = "deletePackageAnnex", value = "删除包附件")
  public RestResult<String> deletePackageAnnex(String id) {
    if(StringUtils.isBlank(id)){
      throw new BusinessException("ID为空");
    }
    repositoryPackageAnnexService.removeById(id);
    return RestResult.success("删除成功");
  }

  @GetMapping("/downloadPackageAnnex")
  @ApiOperation(nickname = "downloadPackageAnnex", value = "下载包附件")
  public void downloadPackageAnnex(String id,HttpServletResponse rep)
          throws UnsupportedEncodingException {
    if(StringUtils.isBlank(id)){
      throw new BusinessException("ID为空");
    }
    RepositoryPackageAnnex annex = repositoryPackageAnnexService.getById(id);
    if(annex == null){
      throw new BusinessException("此文件不存在");
    }
    String fileName = annex.getFileName();
    File saveFile = new File(PACKAGE_ANNEX + annex.getFilePath());
    saveFile.getParentFile().mkdirs();
    rep.setContentType("application/octet-stream");
    String encodeFileName = URLEncoder.encode(fileName, "utf-8");
    rep.setHeader("Content-disposition", "attachment;filename=" + encodeFileName);
    try (
            BufferedInputStream in = new BufferedInputStream(Files.newInputStream(saveFile.toPath()));
            BufferedOutputStream out = new BufferedOutputStream(rep.getOutputStream())
    ) {
      int len;
      byte[] buffer = new byte[8096];
      while ((len = in.read(buffer)) != -1) {
        out.write(buffer, 0, len);
      }
    } catch (Exception e) {
      throw new BusinessException("下载错误");
    }
  }

  @ApiOperation(nickname = "deletePackage", value = "删除项目库包")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "ids", value = "id数组"),
          @ApiImplicitParam(name = "packageType", value = "包类型")})
  @PostMapping("/deletePackage")
  public Object deleteRepositoryPackage(String[] ids, String packageType) {
    nationalService.deletePackage(ids, packageType);
    RestResult<IPage<RepositoryPackage>> result = RestResult.success("删除成功");
    return result;
  }

  @ApiOperation(nickname = "loadUsers", value = "查找提交用户")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "flag", value = "抄送标志")})
  @RequestMapping(value = {"/loadUsers"}, method = {RequestMethod.GET})
  public Object loadUsers(String flag) {
    RestResult<List<Map>> result = RestResult.success("加载成功");
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    result.setData(nationalService.loadUsers(tokenMsg.getOrgId(),
            tokenMsg.getOrgLevel() + "", tokenMsg.getUserCode(), flag));
    return result;
  }

  @ApiOperation(nickname = "queryLogs", value = "包日志")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "packageId", value = "包ID")})
  @RequestMapping(value = {"/queryLogs"}, method = {RequestMethod.GET})
  public Object queryLogs(String packageId) {
    RestResult<Map<String,Object>> result = RestResult.success("加载成功");
    Map<String,Object> map = new HashMap<>();
    map.put("pack", nationalService.queryPackage(packageId));
    map.put("logs", nationalService.queryLogs(packageId));
    result.setData(map);
    return result;
  }

  @ApiOperation(nickname = "saveFlow", value = "保存流程")
  @RequestMapping(value = {"/saveFlow"}, method = {RequestMethod.POST})
  public Object saveFlow(@RequestBody RepositoryFlow flow) {
    nationalService.saveOrUpdateFlow(flow);
    RestResult<Object> result = RestResult.success("办理成功");
    return result;
  }

  @ApiOperation(nickname = "saveFlows", value = "批量保存流程（一级二级单位）")
  @RequestMapping(value = {"/saveFlows"}, method = {RequestMethod.POST})
  public Object saveFlows(RepositoryFlow flow) {
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    Integer orgLevel = tokenMsg.getOrgLevel();
    if(orgLevel != 2 && orgLevel != 1){
      return RestResult.error("当前用户不支持批量办理");
    }
    flow.setDealUserCode(tokenMsg.getUserCode());
    nationalService.saveOrUpdateFlows(flow);
    RestResult<Object> result = RestResult.success("办理成功");
    return result;
  }

  @PostMapping("/importBridgePlanImp")
  @ApiOperation(nickname = "importBridgePlanImp", value = "导入桥梁计划执行情况")
  public RestResult<List<String>> importBridgePlanImp(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importBridgePlanImp(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importPavementPlanImp")
  @ApiOperation(nickname = "importPavementPlanImp", value = "导入路面计划执行情况")
  public RestResult<List<String>> importPavementPlanImp(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importPavementPlanImp(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importTunnelPlanImp")
  @ApiOperation(nickname = "importTunnelPlanImp", value = "导入隧道计划执行情况")
  public RestResult<List<String>> importTunnelPlanImp(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importTunnelPlanImp(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importBridgeRepositoryImp")
  @ApiOperation(nickname = "importBridgeRepositoryImp", value = "导入桥梁项目库执行情况")
  public RestResult<List<String>> importBridgeRepositoryImp(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importBridgeRepositoryImp(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importPavementRepositoryImp")
  @ApiOperation(nickname = "importPavementRepositoryImp", value = "导入路面项目库执行情况")
  public RestResult<List<String>> importPavementRepositoryImp(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importPavementRepositoryImp(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importTunnelRepositoryImp")
  @ApiOperation(nickname = "importTunnelRepositoryImp", value = "导入隧道项目库执行情况")
  public RestResult<List<String>> importTunnelRepositoryImp(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importTunnelRepositoryImp(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @GetMapping("/queryImpData/{type}/{facilityCat}")
  @ApiOperation(nickname = "queryPlanImpData", value = "查询")
  public Object queryPlanImpData(@PathVariable String type,
                                    @PathVariable String facilityCat,
                                    Integer startYear,
                                    Integer endYear,
                                    Integer current,
                                    String orgCode,String lineType,
                                    Integer size){
    if(StringUtils.isBlank(type) || StringUtils.isBlank(facilityCat)){
      throw  new BusinessException("URL错误");
    }
    if(current == null) {
      current = 1;
    }
    if(size == null){
      size = 50;
    }
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }
    if("plan".equals(type)){
      if("LM".equals(facilityCat)){
        Page<PavementPlanImp> page = new Page<>(current, size);
        IPage<PavementPlanImp> resultPage = pavementPlanImpService.getPavementPlanImp(startYear, endYear, orgCode,lineType, page);
        return resultPage;
      }else if("QL".equals(facilityCat)){
        Page<BridgePlanImp> page = new Page<>(current, size);
        IPage<BridgePlanImp> resultPage = bridgePlanImpService.getBridgePlanImp(startYear, endYear, orgCode, lineType, page);
        return resultPage;
      }else{
        Page<TunnelPlanImp> page = new Page<>(current, size);
        IPage<TunnelPlanImp> resultPage = tunnelPlanImpService.getTunnelPlanImp(startYear, endYear, orgCode,lineType, page);
        return resultPage;
      }
    }else{
      if("LM".equals(facilityCat)){
        Page<PavementRepositoryImp> page = new Page<>(current, size);
        IPage<PavementRepositoryImp> resultPage = pavementRepositoryImpService.getPavementRepositoryImp(startYear, endYear, orgCode,lineType, page);
        return resultPage;
      }else if("QL".equals(facilityCat)){
        Page<BridgeRepositoryImp> page = new Page<>(current, size);
        IPage<BridgeRepositoryImp> resultPage = bridgeRepositoryImpService.getBridgeRepositoryImp(startYear, endYear, orgCode,lineType, page);
        return resultPage;
      }else{
        Page<TunnelRepositoryImp> page = new Page<>(current, size);
        IPage<TunnelRepositoryImp> resultPage = tunnelRepositoryImpService.getTunnelRepositoryImp(startYear, endYear, orgCode,lineType, page);
        return resultPage;
      }
    }
  }

  @GetMapping("/exportBridgePlanImpData")
  @ApiOperation(nickname = "exportBridgePlanImpData", value = "导出桥梁计划实施情况")
  public void exportBridgePlanImpData(
          Integer startYear,
          Integer endYear,
          String orgCode,String lineType,
          HttpServletResponse response
  ) throws Exception {
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }

    List<BridgePlanImp> resultList = bridgePlanImpService.getBridgePlanImp(startYear, endYear, orgCode,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<Integer, List<BridgePlanImp>> yearMap =
            resultList.stream().collect(Collectors.groupingBy(BridgePlanImp::getYear));

    String fileName =
            URLEncoder.encode(startYear+"年广东省交通集团国家高速桥梁养护工程计划完成情况.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
            yearMap.keySet()
                    .stream()
                    .map(t -> t.intValue() + "年")
                    .sorted(String::compareTo)
                    .collect(Collectors.toList());

    String s = dynamicTemplate(
            "templates/national/计划-执行情况-桥梁.xlsx",
            yearList
    );

    try (
            InputStream inputStream = Files.newInputStream(new File(s).toPath());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build()
    ) {
      for (Map.Entry<Integer, List<BridgePlanImp>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<BridgePlanImp> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        int totalPreventCount =
                list.stream().filter(t -> t.getPreventCount() != null).mapToInt(t -> t.getPreventCount()).sum();
        int totalRepairCount =
                list.stream().filter(t -> t.getRepairCount() != null).mapToInt(t -> t.getRepairCount()).sum();
        double totalPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventQuantity())).sum();
        double totalRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairQuantity())).sum();
        double totalPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventFee())).sum();
        double totalRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairFee())).sum();
        double totalTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();
        double totalFinishQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishQuantity())).sum();
        double totalFinishInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishInvest())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventCount",totalPreventCount);
        map.put("totalRepairCount",totalRepairCount);
        map.put("totalPreventQuantity", totalPreventQuantity);
        map.put("totalRepairQuantity", totalRepairQuantity);
        map.put("totalPreventFee", totalPreventFee);
        map.put("totalRepairFee", totalRepairFee);
        map.put("totalTotalFee", totalTotalFee);
        map.put("totalFinishQuantity", totalFinishQuantity);
        map.put("totalFinishInvest", totalFinishInvest);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @GetMapping("/exportTunnelPlanImpData")
  @ApiOperation(nickname = "exportTunnelPlanImpData", value = "导出隧道计划实施情况")
  public void exportTunnelPlanImpData(
          Integer startYear,
          Integer endYear,
          String orgCode,String lineType,
          HttpServletResponse response
  ) throws Exception {
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }

    List<TunnelPlanImp> resultList = tunnelPlanImpService.getTunnelPlanImp(startYear, endYear, orgCode,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<Integer, List<TunnelPlanImp>> yearMap =
            resultList.stream().collect(Collectors.groupingBy(TunnelPlanImp::getYear));

    String fileName =
            URLEncoder.encode(startYear+"年广东省交通集团国家高速隧道养护工程计划完成情况.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
            yearMap.keySet()
                    .stream()
                    .map(t -> t.intValue() + "年")
                    .sorted(String::compareTo)
                    .collect(Collectors.toList());

    String s = dynamicTemplate(
            "templates/national/计划-执行情况-隧道.xlsx",
            yearList
    );

    try (
            InputStream inputStream = Files.newInputStream(new File(s).toPath());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build()
    ) {
      for (Map.Entry<Integer, List<TunnelPlanImp>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<TunnelPlanImp> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        int totalPreventCount =
                list.stream().filter(t -> t.getPreventCount() != null).mapToInt(t -> t.getPreventCount()).sum();
        int totalRepairCount =
                list.stream().filter(t -> t.getRepairCount() != null).mapToInt(t -> t.getRepairCount()).sum();
        double totalPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventQuantity())).sum();
        double totalRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairQuantity())).sum();
        double totalCivilPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilPreventFee())).sum();
        double totalCivilRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilRepairFee())).sum();
        double totalCivilTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilTotalFee())).sum();
        double totalElecTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getElecTotalFee())).sum();
        double totalTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();
        double totalFinishQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishQuantity())).sum();
        double totalFinishInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishInvest())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventCount",totalPreventCount);
        map.put("totalRepairCount",totalRepairCount);
        map.put("totalPreventQuantity", totalPreventQuantity);
        map.put("totalRepairQuantity", totalRepairQuantity);
        map.put("totalCivilPreventFee", totalCivilPreventFee);
        map.put("totalCivilRepairFee", totalCivilRepairFee);
        map.put("totalCivilTotalFee", totalCivilTotalFee);
        map.put("totalElecTotalFee", totalElecTotalFee);
        map.put("totalTotalFee", totalTotalFee);
        map.put("totalFinishQuantity", totalFinishQuantity);
        map.put("totalFinishInvest", totalFinishInvest);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @GetMapping("/exportPavementPlanImpData")
  @ApiOperation(nickname = "exportPavementPlanImpData", value = "导出路面计划实施情况")
  public void exportPavementPlanImpData(
          Integer startYear,
          Integer endYear,
          String orgCode,String lineType,
          HttpServletResponse response
  ) throws Exception {
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }

    List<PavementPlanImp> resultList = pavementPlanImpService.getPavementPlanImp(startYear, endYear, orgCode,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<Integer, List<PavementPlanImp>> yearMap =
            resultList.stream().collect(Collectors.groupingBy(PavementPlanImp::getYear));

    String fileName =
            URLEncoder.encode(startYear+ "年广东省交通集团国家高速路面养护工程计划完成情况.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
            yearMap.keySet()
                    .stream()
                    .map(t -> t.intValue() + "年")
                    .sorted(String::compareTo)
                    .collect(Collectors.toList());

    String s = dynamicTemplate(
            "templates/national/计划-执行情况-路面.xlsx",
            yearList
    );

    try (
            InputStream inputStream = Files.newInputStream(new File(s).toPath());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build()
    ) {
      for (Map.Entry<Integer, List<PavementPlanImp>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<PavementPlanImp> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        double totalPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventQuantity())).sum();
        double totalRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairQuantity())).sum();
        double totalPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventFee())).sum();
        double totalRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairFee())).sum();
        double totalTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();
        double totalFinishQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishQuantity())).sum();
        double totalFinishInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishInvest())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventQuantity", totalPreventQuantity);
        map.put("totalRepairQuantity", totalRepairQuantity);
        map.put("totalPreventFee", totalPreventFee);
        map.put("totalRepairFee", totalRepairFee);
        map.put("totalTotalFee", totalTotalFee);
        map.put("totalFinishQuantity", totalFinishQuantity);
        map.put("totalFinishInvest", totalFinishInvest);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @GetMapping("/exportBridgeRepositoryImpData")
  @ApiOperation(nickname = "exportBridgeRepositoryImpData", value = "导出桥梁项目库实施情况")
  public void exportBridgeRepositoryImpData(
          Integer startYear,
          Integer endYear,
          String orgCode,String lineType,
          HttpServletResponse response
  ) throws Exception {
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }

    List<BridgeRepositoryImp> resultList = bridgeRepositoryImpService.getBridgeRepositoryImp(startYear, endYear, orgCode,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<Integer, List<BridgeRepositoryImp>> yearMap =
            resultList.stream().collect(Collectors.groupingBy(BridgeRepositoryImp::getYear));

    String fileName =
            URLEncoder.encode(startYear+"年度广东省交通集团国家高速养护工程跟计划执行-桥梁计划与项目库一致性情况.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
            yearMap.keySet()
                    .stream()
                    .map(t -> t.intValue() + "年")
                    .sorted(String::compareTo)
                    .collect(Collectors.toList());

    String s = dynamicTemplate(
            "templates/national/养护工程计划执行-桥梁计划与项目库一致性情况.xlsx",
            yearList
    );

    try (
            InputStream inputStream = Files.newInputStream(new File(s).toPath());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build()
    ) {
      for (Map.Entry<Integer, List<BridgeRepositoryImp>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<BridgeRepositoryImp> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        int totalPreventCount =
                list.stream().filter(t -> t.getPreventCount() != null).mapToInt(t -> t.getPreventCount()).sum();
        int totalRepairCount =
                list.stream().filter(t -> t.getRepairCount() != null).mapToInt(t -> t.getRepairCount()).sum();
        double totalPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventQuantity())).sum();
        double totalRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairQuantity())).sum();
        double totalPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventFee())).sum();
        double totalRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairFee())).sum();
        double totalTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();
        double totalPlanInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanInvest())).sum();
        double totalFinishQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishQuantity())).sum();
        double totalFinishInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishInvest())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventCount",totalPreventCount);
        map.put("totalRepairCount",totalRepairCount);
        map.put("totalPreventQuantity", totalPreventQuantity);
        map.put("totalRepairQuantity", totalRepairQuantity);
        map.put("totalPreventFee", totalPreventFee);
        map.put("totalRepairFee", totalRepairFee);
        map.put("totalTotalFee", totalTotalFee);
        map.put("totalPlanInvest", totalPlanInvest);
        map.put("totalFinishQuantity", totalFinishQuantity);
        map.put("totalFinishInvest", totalFinishInvest);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @GetMapping("/exportTunnelRepositoryImpData")
  @ApiOperation(nickname = "exportTunnelRepositoryImpData", value = "导出隧道项目库实施情况")
  public void exportTunnelRepositoryImpData(
          Integer startYear,
          Integer endYear,
          String orgCode,String lineType,
          HttpServletResponse response
  ) throws Exception {
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }

    List<TunnelRepositoryImp> resultList = tunnelRepositoryImpService.getTunnelRepositoryImp(startYear, endYear, orgCode,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<Integer, List<TunnelRepositoryImp>> yearMap =
            resultList.stream().collect(Collectors.groupingBy(TunnelRepositoryImp::getYear));

    String fileName =
            URLEncoder.encode(startYear+"年度广东省交通集团国家高速养护工程跟计划执行-隧道计划与项目库一致性情况.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
            yearMap.keySet()
                    .stream()
                    .map(t -> t.intValue() + "年")
                    .sorted(String::compareTo)
                    .collect(Collectors.toList());

    String s = dynamicTemplate(
            "templates/national/养护工程计划执行-隧道计划与项目库一致性情况.xlsx",
            yearList
    );

    try (
            InputStream inputStream = Files.newInputStream(new File(s).toPath());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build()
    ) {
      for (Map.Entry<Integer, List<TunnelRepositoryImp>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<TunnelRepositoryImp> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        int totalPreventCount =
                list.stream().filter(t -> t.getPreventCount() != null).mapToInt(t -> t.getPreventCount()).sum();
        int totalRepairCount =
                list.stream().filter(t -> t.getRepairCount() != null).mapToInt(t -> t.getRepairCount()).sum();
        double totalPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventQuantity())).sum();
        double totalRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairQuantity())).sum();
        double totalCivilPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilPreventFee())).sum();
        double totalCivilRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilRepairFee())).sum();
        double totalCivilTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getCivilTotalFee())).sum();
        double totalElecTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getElecTotalFee())).sum();
        double totalTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();
        double totalPlanInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanInvest())).sum();
        double totalFinishQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishQuantity())).sum();
        double totalFinishInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishInvest())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventCount",totalPreventCount);
        map.put("totalRepairCount",totalRepairCount);
        map.put("totalPreventQuantity", totalPreventQuantity);
        map.put("totalRepairQuantity", totalRepairQuantity);
        map.put("totalCivilPreventFee", totalCivilPreventFee);
        map.put("totalCivilRepairFee", totalCivilRepairFee);
        map.put("totalCivilTotalFee", totalCivilTotalFee);
        map.put("totalElecTotalFee", totalElecTotalFee);
        map.put("totalTotalFee", totalTotalFee);
        map.put("totalPlanInvest", totalPlanInvest);
        map.put("totalFinishQuantity", totalFinishQuantity);
        map.put("totalFinishInvest", totalFinishInvest);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @GetMapping("/exportPavementRepositoryImpData")
  @ApiOperation(nickname = "exportPavementRepositoryImpData", value = "导出路面项目库实施情况")
  public void exportPavementRepositoryImpData(
          Integer startYear,
          Integer endYear,
          String orgCode,String lineType,
          HttpServletResponse response
  ) throws Exception {
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }

    List<PavementRepositoryImp> resultList = pavementRepositoryImpService.getPavementRepositoryImp(startYear, endYear, orgCode,lineType);
    if(resultList == null || resultList.isEmpty()){
      throw new BusinessException("没有数据");
    }
    Map<Integer, List<PavementRepositoryImp>> yearMap =
            resultList.stream().collect(Collectors.groupingBy(PavementRepositoryImp::getYear));

    String fileName =
            URLEncoder.encode(startYear+"年度广东省交通集团国家高速养护工程跟计划执行-路面计划与项目库一致性情况.xlsx", "UTF-8");
    response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

    List<String> yearList =
            yearMap.keySet()
                    .stream()
                    .map(t -> t.intValue() + "年")
                    .sorted(String::compareTo)
                    .collect(Collectors.toList());

    String s = dynamicTemplate(
            "templates/national/养护工程计划执行-路面计划与项目库一致性情况.xlsx",
            yearList
    );

    try (
            InputStream inputStream = Files.newInputStream(new File(s).toPath());
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(inputStream)
                    .build()
    ) {
      for (Map.Entry<Integer, List<PavementRepositoryImp>> entry : yearMap.entrySet()) {
        String sheetName = entry.getKey() + "年";
        List<PavementRepositoryImp> list = entry.getValue();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        excelWriter.fill(list, fillConfig, writeSheet);

        //合计
        double totalPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventQuantity())).sum();
        double totalRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairQuantity())).sum();
        double totalPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPreventFee())).sum();
        double totalRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getRepairFee())).sum();
        double totalTotalFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getTotalFee())).sum();
        double totalPlanPreventQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanPreventQuantity())).sum();
        double totalPlanRepairQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanRepairQuantity())).sum();
        double totalPlanPreventFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanPreventFee())).sum();
        double totalPlanRepairFee =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanRepairFee())).sum();
        double totalPlanInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getPlanInvest())).sum();
        double totalFinishQuantity =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishQuantity())).sum();
        double totalFinishInvest =
                list.stream().mapToDouble(t -> MathUtil.doubleValue(t.getFinishInvest())).sum();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("totalPreventMileage", totalPreventQuantity);
        map.put("totalRepairMileage", totalRepairQuantity);
        map.put("totalPreventConst", totalPreventFee);
        map.put("totalRepairConst", totalRepairFee);
        map.put("totalConst", totalTotalFee);
        map.put("totalPlanPreventQuantity", totalPlanPreventQuantity);
        map.put("totalPlanRepairQuantity", totalPlanRepairQuantity);
        map.put("totalPlanPreventFee", totalPlanPreventFee);
        map.put("totalPlanRepairFee", totalPlanRepairFee);
        map.put("totalPlanInvest", totalPlanInvest);
        map.put("totalFinishQuantity", totalFinishQuantity);
        map.put("totalFinishInvest", totalFinishInvest);
        map.put("year", entry.getKey());
        excelWriter.fill(map, writeSheet);
      }
    }

    new File(s).delete();
  }

  @PostMapping("/uploadAnnex")
  @ApiOperation(nickname = "uploadAnnex", value = "上传佐证材料")
  public RestResult<List<NationalAnnex>> uploadAnnex(MultipartFile[] files, String type, String[] ids) {
    RestResult<List<NationalAnnex>> result = RestResult.success("上传成功");
    result.setData(nationalAnnexService.uploadAnnex(files, type, ids, NATIONAL_ANNEX));
    return result;
  }

  @PostMapping("/deleteAnnex")
  @ApiOperation(nickname = "deleteAnnex", value = "删除佐证材料")
  public RestResult<String> deleteAnnex(String id) {
    if(StringUtils.isBlank(id)){
      throw new BusinessException("ID为空");
    }
    nationalAnnexService.deleteAnnex(id);
    return RestResult.success("删除成功");
  }

  @GetMapping("/downloadAnnex")
  @ApiOperation(nickname = "downloadAnnex", value = "下载佐证材料")
  public void downloadAnnex(String id,HttpServletResponse rep)
          throws UnsupportedEncodingException {
    if(StringUtils.isBlank(id)){
      throw new BusinessException("ID为空");
    }
    NationalAnnex annex = nationalAnnexService.getById(id);
    if(annex == null){
      throw new BusinessException("此文件不存在");
    }
    String fileName = annex.getAnnexName();
    File saveFile = new File(NATIONAL_ANNEX + annex.getAnnexPath());
    saveFile.getParentFile().mkdirs();
    rep.setContentType("application/octet-stream");
    String encodeFileName = URLEncoder.encode(fileName, "utf-8");
    rep.setHeader("Content-disposition", "attachment;filename=" + encodeFileName);
    try (
            BufferedInputStream in = new BufferedInputStream(Files.newInputStream(saveFile.toPath()));
            BufferedOutputStream out = new BufferedOutputStream(rep.getOutputStream())
    ) {
      int len;
      byte[] buffer = new byte[8096];
      while ((len = in.read(buffer)) != -1) {
        out.write(buffer, 0, len);
      }
    } catch (Exception e) {
      throw new BusinessException("下载错误");
    }
  }

  @GetMapping("/queryAnnex")
  @ApiOperation(nickname = "queryAnnex", value = "查询佐证材料")
  public RestResult<List<NationalAnnex>> queryAnnex(String nationalId,String nationalSource){
    QueryWrapper<NationalAnnex> queryWrapper = new QueryWrapper();
    queryWrapper.eq("NATIONAL_ID",nationalId);
    queryWrapper.eq("NATIONAL_SOURCE",nationalSource);
    List<NationalAnnex> list = nationalAnnexService.list(queryWrapper);
    return RestResult.success(list);
  }

  @GetMapping("/queryImpDesc")
  @ApiOperation(nickname = "queryImpDesc", value = "查询项目库与计划实施情况、计划完成情况数据备注和统计")
  public Map<String,Object> queryImpDesc(String type,
                                         String facilityCat,
                                         Integer startYear,
                                         Integer endYear,String lineType,
                                         String orgCode){
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    Map<String,Object> map = new HashMap<>();
    StringBuilder sb = new StringBuilder("备注：");
    if(StringUtils.isBlank(orgCode)){
      orgCode = orgId;
      sb.append(tokenMsg.getOrgName());
    }
    sb.append(startYear).append("年度");
    if("plan".equals(type)){
      sb.append("国家高速");
      if("LM".equals(facilityCat)){
        sb.append("路面养护工程计划项目");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal finishInvestTotal = BigDecimal.ZERO;
        List<PavementPlanImp> pavementPlanImp = pavementPlanImpService.getPavementPlanImp(startYear, endYear, orgCode,lineType);
        if(null != pavementPlanImp && !pavementPlanImp.isEmpty()){
          for (PavementPlanImp re : pavementPlanImp) {
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal finishInvest = re.getFinishInvest();
            if(finishInvest != null){
              finishInvestTotal = finishInvestTotal.add(finishInvest);
            }
          }
        }
        int size = pavementPlanImp.size();
        sb.append(size)
                .append("项，计划费用")
                .append(totalFeeAll)
                .append("万元；实际已进场完成实施")
                .append(size)
                .append("项，完成投资")
                .append(finishInvestTotal)
                .append("万元，国家高速路面养护工程完成实际计划数量占比100%");
        map.put("totalFeeAll",totalFeeAll);
        map.put("finishInvestTotal",finishInvestTotal);
        map.put("planTotal",size);
        map.put("realTotal",size);
        map.put("rate",100);
      }else if("QL".equals(facilityCat)){
        sb.append("桥梁养护工程计划项目");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal finishInvestTotal = BigDecimal.ZERO;
        List<BridgePlanImp> bridgePlanImp = bridgePlanImpService.getBridgePlanImp(startYear, endYear, orgCode,lineType);
        if(null != bridgePlanImp && !bridgePlanImp.isEmpty()){
          for (BridgePlanImp re : bridgePlanImp) {
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal finishInvest = re.getFinishInvest();
            if(finishInvest != null){
              finishInvestTotal = finishInvestTotal.add(finishInvest);
            }
          }
        }
        int size = bridgePlanImp.size();
        sb.append(size)
                .append("项，计划费用")
                .append(totalFeeAll)
                .append("万元；实际已进场完成实施")
                .append(size)
                .append("项，完成投资")
                .append(finishInvestTotal)
                .append("万元，国家高速桥梁养护工程完成实际计划数量占比100%");
        map.put("totalFeeAll",totalFeeAll);
        map.put("finishInvestTotal",finishInvestTotal);
        map.put("planTotal",size);
        map.put("realTotal",size);
        map.put("rate",100);
      }else{
        sb.append("隧道养护工程计划项目");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal finishInvestTotal = BigDecimal.ZERO;
        List<TunnelPlanImp> tunnelPlanImp = tunnelPlanImpService.getTunnelPlanImp(startYear, endYear, orgCode,lineType);
        if(null != tunnelPlanImp && !tunnelPlanImp.isEmpty()){
          for (TunnelPlanImp re : tunnelPlanImp) {
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal finishInvest = re.getFinishInvest();
            if(finishInvest != null){
              finishInvestTotal = finishInvestTotal.add(finishInvest);
            }
          }
        }
        int size = tunnelPlanImp.size();
        sb.append(size)
                .append("项，计划费用")
                .append(totalFeeAll)
                .append("万元；实际已进场完成实施")
                .append(size)
                .append("项，完成投资")
                .append(finishInvestTotal)
                .append("万元，国家高速隧道养护工程完成实际计划数量占比100%");
        map.put("totalFeeAll",totalFeeAll);
        map.put("finishInvestTotal",finishInvestTotal);
        map.put("planTotal",size);
        map.put("realTotal",size);
        map.put("rate",100);
     }
    }else{
      if("LM".equals(facilityCat)){
        sb.append("路面养护工程项目库执行项目数量");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal planInvestAll = BigDecimal.ZERO;
        int planIndex = 0;
        List<PavementRepositoryImp> pavementRepositoryImp = pavementRepositoryImpService.getPavementRepositoryImp(startYear, endYear, orgCode,lineType);
        int size = pavementRepositoryImp.size();
        if(null != pavementRepositoryImp && !pavementRepositoryImp.isEmpty()){
          for (PavementRepositoryImp re : pavementRepositoryImp) {
            BigDecimal totalFee = re.getTotalFee();
            String remark = re.getRemark();
            if(totalFee != null && !"库外新增".equals(remark) && !"未入库".equals(remark)){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            if("库外新增".equals(remark) || "未入库".equals(remark)){
              size --;
            }
            BigDecimal planInvest = re.getPlanInvest();
            if(planInvest != null && planInvest.compareTo(BigDecimal.ZERO) > 0){
              planInvestAll = planInvestAll.add(planInvest);
              planIndex++;
            }
          }
        }
        BigDecimal totalRate = BigDecimal.valueOf(size * 100).divide(BigDecimal.valueOf(planIndex), 2, RoundingMode.HALF_UP);
        BigDecimal feeRate = totalFeeAll.multiply(BigDecimal.valueOf(100)).divide(planInvestAll,2,RoundingMode.HALF_UP);
        sb.append(size)
                .append("项，费用金额为")
                .append(totalFeeAll)
                .append("万元；")
                .append(startYear)
                .append("年度路面养护工程计划项目数量为")
                .append(planIndex)
                .append("项，费用金额为")
                .append(planInvestAll)
                .append("万元，按照养护工程项目数执行统计，匹配率=报部项目库项目完成数/省级计划项目下达数=")
                .append(totalRate)
                .append("%，按养护工程资金计划执行统计，匹配率=报部项目库项目资金完成数/省级计划项目资金下达数=")
                .append(feeRate)
                .append("%。");
        map.put("totalFeeAll",totalFeeAll);
        map.put("planInvestAll",planInvestAll);
        map.put("repositoryTotal",size);
        map.put("planTotal",planIndex);
        map.put("rate",totalRate);
      }else if("QL".equals(facilityCat)){
        sb.append("桥梁养护工程项目库执行项目数量");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal planInvestAll = BigDecimal.ZERO;
        int planIndex = 0;
        List<BridgeRepositoryImp> bridgeRepositoryImp = bridgeRepositoryImpService.getBridgeRepositoryImp(startYear, endYear, orgCode,lineType);
        int size = bridgeRepositoryImp.size();
        if(null != bridgeRepositoryImp && !bridgeRepositoryImp.isEmpty()){
          for (BridgeRepositoryImp re : bridgeRepositoryImp) {
            BigDecimal totalFee = re.getTotalFee();
            String remark = re.getRemark();
            if(totalFee != null && !"库外新增".equals(remark) && !"未入库".equals(remark)){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            if("库外新增".equals(remark) || "未入库".equals(remark)){
              size --;
            }
            BigDecimal planInvest = re.getPlanInvest();
            if(planInvest != null && planInvest.compareTo(BigDecimal.ZERO) > 0){
              planInvestAll = planInvestAll.add(planInvest);
              planIndex++;
            }
          }
        }
        BigDecimal totalRate = BigDecimal.valueOf(size * 100).divide(BigDecimal.valueOf(planIndex), 2, RoundingMode.HALF_UP);
        BigDecimal feeRate = totalFeeAll.multiply(BigDecimal.valueOf(100)).divide(planInvestAll,2,RoundingMode.HALF_UP);
        sb.append(size)
                .append("项，费用金额为")
                .append(totalFeeAll)
                .append("万元；")
                .append(startYear)
                .append("年度桥梁养护工程计划项目数量为")
                .append(planIndex)
                .append("项，费用金额为")
                .append(planInvestAll)
                .append("万元，按照养护工程项目数执行统计，匹配率=报部项目库项目完成数/省级计划项目下达数=")
                .append(totalRate)
                .append("%，按养护工程资金计划执行统计，匹配率=报部项目库项目资金完成数/省级计划项目资金下达数=")
                .append(feeRate)
                .append("%。");
        map.put("totalFeeAll",totalFeeAll);
        map.put("planInvestAll",planInvestAll);
        map.put("repositoryTotal",size);
        map.put("planTotal",planIndex);
        map.put("rate",totalRate);
      }else{
        sb.append("隧道养护工程项目库执行项目数量");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal planInvestAll = BigDecimal.ZERO;
        int planIndex = 0;
        List<TunnelRepositoryImp> tunnelRepositoryImp = tunnelRepositoryImpService.getTunnelRepositoryImp(startYear, endYear, orgCode,lineType);
        int size = tunnelRepositoryImp.size();
        if(null != tunnelRepositoryImp && !tunnelRepositoryImp.isEmpty()){
          for (TunnelRepositoryImp re : tunnelRepositoryImp) {
            BigDecimal totalFee = re.getTotalFee();
            String remark = re.getRemark();
            if(totalFee != null && !"库外新增".equals(remark) && !"未入库".equals(remark)){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            if("库外新增".equals(remark) || "未入库".equals(remark)){
              size --;
            }
            BigDecimal planInvest = re.getPlanInvest();
            if(planInvest != null && planInvest.compareTo(BigDecimal.ZERO) > 0){
              planInvestAll = planInvestAll.add(planInvest);
              planIndex++;
            }
          }
        }
        BigDecimal totalRate = BigDecimal.valueOf(size * 100).divide(BigDecimal.valueOf(planIndex), 2, RoundingMode.HALF_UP);
        BigDecimal feeRate = totalFeeAll.multiply(BigDecimal.valueOf(100)).divide(planInvestAll,2,RoundingMode.HALF_UP);
        sb.append(size)
                .append("项，费用金额为")
                .append(totalFeeAll)
                .append("万元；")
                .append(startYear)
                .append("年度隧道养护工程计划项目数量为")
                .append(planIndex)
                .append("项，费用金额为")
                .append(planInvestAll)
                .append("万元，按照养护工程项目数执行统计，匹配率=报部项目库项目完成数/省级计划项目下达数=")
                .append(totalRate)
                .append("%，按养护工程资金计划执行统计，匹配率=报部项目库项目资金完成数/省级计划项目资金下达数")
                .append(feeRate)
                .append("%。");
        map.put("totalFeeAll",totalFeeAll);
        map.put("planInvestAll",planInvestAll);
        map.put("repositoryTotal",size);
        map.put("planTotal",planIndex);
        map.put("rate",totalRate);
      }
    }
    map.put("remark",sb.toString());
    return map;
  }

  @PostMapping("/importBridgeSuggest")
  @ApiOperation(nickname = "importBridgeSuggest", value = "导入桥梁决策建议表")
  public RestResult<List<String>> importBridgeSuggest(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importBridgeSuggest(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importPavementSuggest")
  @ApiOperation(nickname = "importPavementSuggest", value = "导入路面决策建议表")
  public RestResult<List<String>> importPavementSuggest(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importPavementSuggest(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/importTunnelSuggest")
  @ApiOperation(nickname = "importTunnelSuggest", value = "导入隧道决策建议表")
  public RestResult<List<String>> importTunnelSuggest(
          MultipartFile excelFile
  ) {
    List<String> errorList = nationalService.importTunnelSuggest(excelFile);
    return errorList.isEmpty() ? RestResult.success("导入成功")
            : RestResult.error(errorList, "导入失败");
  }

  @PostMapping("/querySuggestData")
  @ApiOperation(nickname = "querySuggestData", value = "查询决策建议表数据")
  public Object querySuggestData(String facilityCat,
                                    String orgCode,
                                    Integer startYear,
                                    Integer endYear,
                                    Integer isDecision,
                                    String lineType,
                                    Integer current,
                                    Integer target,
                                    Integer size){
    if(current == null) {
      current = 1;
    }
    if(size == null){
      size = 50;
    }
    if(target == null){
      target = LocalDate.now().getYear();
    }
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    if(StringUtils.isBlank(orgCode)){
       orgCode = tokenMsg.getOrgId();
    }
    List<FwRightOrg> list = nationalService.queryOtherOrg();
    List<String> collect = list.stream().map(FwRightOrg::getOrgCode).collect(Collectors.toList());
    if(collect.contains(orgCode)){
      if("LM".equals(facilityCat)){
        Page<PavementSuggestO> page = new Page<>(current, size);
        IPage<PavementSuggestO> resultPage = pavementSuggestOService.getPavementSuggestO(startYear, endYear,
                orgCode,lineType,target,page);
        return resultPage;
      }else if("QL".equals(facilityCat)){
        Page<BridgeSuggestO> page = new Page<>(current, size);
        IPage<BridgeSuggestO> resultPage = bridgeSuggestOService.getBridgeSuggestO(startYear, endYear, orgCode,lineType,target, page);
        return resultPage;
      }else{
        Page<TunnelSuggestO> page = new Page<>(current, size);
        IPage<TunnelSuggestO> resultPage = tunnelSuggestOService.getTunnelSuggestO(startYear, endYear, orgCode,lineType,target, page);
        return resultPage;
      }
    }else{
      if("LM".equals(facilityCat)){
        Page<PavementSuggest> page = new Page<>(current, size);
        IPage<PavementSuggest> resultPage = pavementSuggestService.getPavementSuggest(startYear, endYear,
                orgCode,isDecision,lineType,target,page);
        return resultPage;
      }else if("QL".equals(facilityCat)){
        Page<BridgeSuggest> page = new Page<>(current, size);
        IPage<BridgeSuggest> resultPage = bridgeSuggestService.getBridgeSuggest(startYear, endYear, orgCode,lineType,target, page);
        return resultPage;
      }else{
        Page<TunnelSuggest> page = new Page<>(current, size);
        IPage<TunnelSuggest> resultPage = tunnelSuggestService.getTunnelSuggest(startYear, endYear, orgCode,lineType,target, page);
        return resultPage;
      }
    }
  }

  @GetMapping("/querySuggestDesc")
  @ApiOperation(nickname = "querySuggestDesc", value = "查询建议表统计")
  public Map<String,Object> querySuggestDesc(String facilityCat,
                                         Integer startYear,
                                         Integer endYear,
                                         String lineType,
                                         Integer isDecision,
                                         Integer target,
                                         String orgCode){
    TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
    String orgId = tokenMsg.getOrgId();
    Map<String,Object> map = new HashMap<>();
    StringBuilder sb = new StringBuilder("备注：");
    if(StringUtils.isBlank(orgCode)){
      orgCode = orgId;
      sb.append(tokenMsg.getOrgName());
    }
    List<FwRightOrg> list = nationalService.queryOtherOrg();
    List<String> collect = list.stream().map(FwRightOrg::getOrgCode).collect(Collectors.toList());
    if(collect.contains(orgCode)){
      if("LM".equals(facilityCat)){
        BigDecimal preventFeeAll = BigDecimal.ZERO;
        BigDecimal repairFeeAll = BigDecimal.ZERO;
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal preventQuantityAll = BigDecimal.ZERO;
        BigDecimal repairQuantityAll = BigDecimal.ZERO;
        List<PavementSuggestO> pavementSuggest = pavementSuggestOService.getPavementSuggestO(startYear, endYear, orgCode,lineType,target);
        if(null != pavementSuggest && !pavementSuggest.isEmpty()){
          for (PavementSuggestO re : pavementSuggest) {
            BigDecimal preventFee = re.getPreventFee();
            if(preventFee != null && preventFee.compareTo(BigDecimal.ZERO) > 0){
              preventFeeAll = preventFeeAll.add(preventFee);
            }
            BigDecimal repairFee = re.getRepairFee();
            if(repairFee != null && repairFee.compareTo(BigDecimal.ZERO) > 0){
              repairFeeAll = repairFeeAll.add(repairFee);
            }
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal preventQuantity = re.getPreventQuantity();
            if(preventQuantity != null){
              preventQuantityAll = preventQuantityAll.add(preventQuantity);
            }
            BigDecimal repairQuantity = re.getRepairQuantity();
            if(repairQuantity != null){
              repairQuantityAll = repairQuantityAll.add(repairQuantity);
            }
          }
        }
        map.put("projectTotal",pavementSuggest.size());
        map.put("totalFeeAll",totalFeeAll);
        map.put("preventQuantityAll",preventQuantityAll);
        map.put("repairQuantityAll",repairQuantityAll);
      }else if("QL".equals(facilityCat)){
        BigDecimal preventFeeAll = BigDecimal.ZERO;
        BigDecimal repairFeeAll = BigDecimal.ZERO;
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal preventQuantityAll = BigDecimal.ZERO;
        BigDecimal repairQuantityAll = BigDecimal.ZERO;
        Integer preventCountTotal = 0;
        Integer repairCountTotal = 0;
        List<BridgeSuggestO> bridgeSuggest = bridgeSuggestOService.getBridgeSuggestO(startYear, endYear, orgCode,lineType,target);
        if(null != bridgeSuggest && !bridgeSuggest.isEmpty()){
          for (BridgeSuggestO re : bridgeSuggest) {
            BigDecimal preventFee = re.getPreventFee();
            if(preventFee != null && preventFee.compareTo(BigDecimal.ZERO) > 0){
              preventFeeAll = preventFeeAll.add(preventFee);
            }
            BigDecimal repairFee = re.getRepairFee();
            if(repairFee != null && repairFee.compareTo(BigDecimal.ZERO) > 0){
              repairFeeAll = repairFeeAll.add(repairFee);
            }
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal preventQuantity = re.getPreventQuantity();
            if(preventQuantity != null){
              preventQuantityAll = preventQuantityAll.add(preventQuantity);
            }
            BigDecimal repairQuantity = re.getRepairQuantity();
            if(repairQuantity != null){
              repairQuantityAll = repairQuantityAll.add(repairQuantity);
            }
            Integer preventCount = re.getPreventCount();
            if(preventCount != null && preventCount > 0){
              preventCountTotal += preventCount;
            }
            Integer repairCount = re.getRepairCount();
            if(repairCount != null && repairCount > 0){
              repairCountTotal += repairCount;
            }
          }
        }
        map.put("preventCountTotal",preventCountTotal);
        map.put("repairCountTotal",repairCountTotal);
        map.put("totalFeeAll",totalFeeAll);
        map.put("projectTotal",bridgeSuggest.size());
      }else{
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal preventQuantityAll = BigDecimal.ZERO;
        BigDecimal repairQuantityAll = BigDecimal.ZERO;
        Integer preventCountTotal = 0;
        Integer repairCountTotal = 0;
        List<TunnelSuggestO> tunnelSuggest = tunnelSuggestOService.getTunnelSuggestO(startYear, endYear, orgCode,lineType,target);
        if(null != tunnelSuggest && !tunnelSuggest.isEmpty()){
          for (TunnelSuggestO re : tunnelSuggest) {
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal preventQuantity = re.getPreventQuantity();
            if(preventQuantity != null){
              preventQuantityAll = preventQuantityAll.add(preventQuantity);
            }
            BigDecimal repairQuantity = re.getRepairQuantity();
            if(repairQuantity != null){
              repairQuantityAll = repairQuantityAll.add(repairQuantity);
            }
            Integer preventCount = re.getPreventCount();
            if(preventCount != null && preventCount > 0){
              preventCountTotal += preventCount;
            }
            Integer repairCount = re.getRepairCount();
            if(repairCount != null && repairCount > 0){
              repairCountTotal += repairCount;
            }
          }
        }
        map.put("preventCountTotal",preventCountTotal);
        map.put("repairCountTotal",repairCountTotal);
        map.put("totalFeeAll",totalFeeAll);
        map.put("projectTotal",tunnelSuggest.size());
      }
    }else{
      if("LM".equals(facilityCat)){
        sb.append("建议国家高速公路路面预防养护项目工程量");
        BigDecimal preventFeeAll = BigDecimal.ZERO;
        BigDecimal repairFeeAll = BigDecimal.ZERO;
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal preventQuantityAll = BigDecimal.ZERO;
        BigDecimal repairQuantityAll = BigDecimal.ZERO;
        List<PavementSuggest> pavementSuggest = pavementSuggestService.getPavementSuggest(startYear, endYear, orgCode,isDecision,lineType,target);
        if(null != pavementSuggest && !pavementSuggest.isEmpty()){
          for (PavementSuggest re : pavementSuggest) {
            BigDecimal preventFee = re.getPreventFee();
            if(preventFee != null && preventFee.compareTo(BigDecimal.ZERO) > 0){
              preventFeeAll = preventFeeAll.add(preventFee);
            }
            BigDecimal repairFee = re.getRepairFee();
            if(repairFee != null && repairFee.compareTo(BigDecimal.ZERO) > 0){
              repairFeeAll = repairFeeAll.add(repairFee);
            }
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal preventQuantity = re.getPreventQuantity();
            if(preventQuantity != null){
              preventQuantityAll = preventQuantityAll.add(preventQuantity);
            }
            BigDecimal repairQuantity = re.getRepairQuantity();
            if(repairQuantity != null){
              repairQuantityAll = repairQuantityAll.add(repairQuantity);
            }
          }
        }
        sb.append(preventQuantityAll)
                .append("km，建议修复养护项目工程量")
                .append(repairQuantityAll)
                .append("km，合计建议养护工程")
                .append(preventQuantityAll.add(repairQuantityAll))
                .append("km，养护费用")
                .append(totalFeeAll)
                .append("万元，");
        map.put("projectTotal",pavementSuggest.size());
        map.put("totalFeeAll",totalFeeAll);
        map.put("preventQuantityAll",preventQuantityAll);
        map.put("repairQuantityAll",repairQuantityAll);
      }else if("QL".equals(facilityCat)){
        sb.append("建议国家高速公路桥梁预防养护项目工程量");
        BigDecimal preventFeeAll = BigDecimal.ZERO;
        BigDecimal repairFeeAll = BigDecimal.ZERO;
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal preventQuantityAll = BigDecimal.ZERO;
        BigDecimal repairQuantityAll = BigDecimal.ZERO;
        Integer preventCountTotal = 0;
        Integer repairCountTotal = 0;
        List<BridgeSuggest> bridgeSuggest = bridgeSuggestService.getBridgeSuggest(startYear, endYear, orgCode,lineType,target);
        if(null != bridgeSuggest && !bridgeSuggest.isEmpty()){
          for (BridgeSuggest re : bridgeSuggest) {
            BigDecimal preventFee = re.getPreventFee();
            if(preventFee != null && preventFee.compareTo(BigDecimal.ZERO) > 0){
              preventFeeAll = preventFeeAll.add(preventFee);
            }
            BigDecimal repairFee = re.getRepairFee();
            if(repairFee != null && repairFee.compareTo(BigDecimal.ZERO) > 0){
              repairFeeAll = repairFeeAll.add(repairFee);
            }
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal preventQuantity = re.getPreventQuantity();
            if(preventQuantity != null){
              preventQuantityAll = preventQuantityAll.add(preventQuantity);
            }
            BigDecimal repairQuantity = re.getRepairQuantity();
            if(repairQuantity != null){
              repairQuantityAll = repairQuantityAll.add(repairQuantity);
            }
            Integer preventCount = re.getPreventCount();
            if(preventCount != null && preventCount > 0){
              preventCountTotal += preventCount;
            }
            Integer repairCount = re.getRepairCount();
            if(repairCount != null && repairCount > 0){
              repairCountTotal += repairCount;
            }
          }
        }
        sb.append(preventQuantityAll)
                .append("延米，建议修复养护项目工程量")
                .append(repairQuantityAll)
                .append("延米，合计建议养护工程")
                .append(preventQuantityAll.add(repairQuantityAll))
                .append("延米，养护费用")
                .append(totalFeeAll)
                .append("万元，");
        map.put("preventCountTotal",preventCountTotal);
        map.put("repairCountTotal",repairCountTotal);
        map.put("totalFeeAll",totalFeeAll);
        map.put("projectTotal",bridgeSuggest.size());
      }else{
        sb.append("建议国家高速公路隧道预防养护项目工程量");
        BigDecimal totalFeeAll = BigDecimal.ZERO;
        BigDecimal preventQuantityAll = BigDecimal.ZERO;
        BigDecimal repairQuantityAll = BigDecimal.ZERO;
        Integer preventCountTotal = 0;
        Integer repairCountTotal = 0;
        List<TunnelSuggest> tunnelSuggest = tunnelSuggestService.getTunnelSuggest(startYear, endYear, orgCode,lineType,target);
        if(null != tunnelSuggest && !tunnelSuggest.isEmpty()){
          for (TunnelSuggest re : tunnelSuggest) {
            BigDecimal totalFee = re.getTotalFee();
            if(totalFee != null){
              totalFeeAll = totalFeeAll.add(totalFee);
            }
            BigDecimal preventQuantity = re.getPreventQuantity();
            if(preventQuantity != null){
              preventQuantityAll = preventQuantityAll.add(preventQuantity);
            }
            BigDecimal repairQuantity = re.getRepairQuantity();
            if(repairQuantity != null){
              repairQuantityAll = repairQuantityAll.add(repairQuantity);
            }
            Integer preventCount = re.getPreventCount();
            if(preventCount != null && preventCount > 0){
              preventCountTotal += preventCount;
            }
            Integer repairCount = re.getRepairCount();
            if(repairCount != null && repairCount > 0){
              repairCountTotal += repairCount;
            }
          }
        }
        sb.append(preventQuantityAll)
                .append("延米，建议修复养护项目工程量")
                .append(repairQuantityAll)
                .append("延米，合计建议养护工程")
                .append(preventQuantityAll.add(repairQuantityAll))
                .append("延米，养护费用")
                .append(totalFeeAll)
                .append("万元，");
        map.put("preventCountTotal",preventCountTotal);
        map.put("repairCountTotal",repairCountTotal);
        map.put("totalFeeAll",totalFeeAll);
        map.put("projectTotal",tunnelSuggest.size());
      }
    }

    map.put("remark",sb.toString());
    return map;
  }

  @PostMapping("/uploadAssess")
  @ApiOperation(nickname = "uploadAssess", value = "上传后评估报告")
  public RestResult<List<AssessReport>> uploadAssess(MultipartFile[] files,
                                                     Integer year,
                                                     String cat,
                                                     String remark,
                                                     Integer type) {
    RestResult<List<AssessReport>> result = RestResult.success("上传成功");
    result.setData(assessReportService.uploadAssess(files,NATIONAL_ASSESS,year,cat,remark,type));
    return result;
  }

  @PostMapping("/deleteAssess")
  @ApiOperation(nickname = "deleteAssess", value = "删除后评估报告")
  public RestResult<String> deleteAssess(String[] ids) {
    if(null == ids || ids.length == 0){
      throw new BusinessException("ID为空");
    }
    nationalService.deleteAssess(ids);
    return RestResult.success("删除成功");
  }

  @GetMapping("/downloadAssess")
  @ApiOperation(nickname = "downloadAssess", value = "下载后评估报告")
  public void downloadAssess(String[] ids,HttpServletResponse rep)
          throws IOException {
    if(null == ids || ids.length == 0){
      throw new BusinessException("ID为空");
    }
    List<AssessReport> list = nationalService.list(ids);
    if(list == null || list.isEmpty()){
      throw new BusinessException("此文件不存在");
    }
    if(list.size() == 1){
      AssessReport report = list.get(0);
      File saveFile = new File(NATIONAL_ASSESS + report.getFilePath());
      FileDownloadUtil.download(rep, saveFile, report.getFileName());
    }else{
      List<File> files = new ArrayList<>();
      for(AssessReport report : list){
        File saveFile = new File(NATIONAL_ASSESS + report.getFilePath());
        files.add(saveFile);
      }
      FileDownloadUtil.download(rep, files, "后评估报告.zip");
    }
  }

  @GetMapping("/queryAssess")
  @ApiOperation(nickname = "queryAssess", value = "查询后评估报告")
  public RestResult<IPage<AssessReport>> queryAssess(String orgCode,
                                                     Integer startYear,
                                                     Integer endYear,
                                                     Integer type,
                                                     Integer current,
                                                     Integer size){
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }
    if(current == null) {
      current = 1;
    }
    if(size == null){
      size = 50;
    }
    Page<AssessReport> page = new Page<>(current, size);
    IPage<AssessReport> list = assessReportService.queryList(orgCode, type, startYear, endYear, page);
    return RestResult.success(list);
  }

  @PostMapping("/queryMaintaincePlan")
  @ApiOperation(nickname = "queryMaintaincePlan", value = "查询计划汇总")
  public Object queryMaintaincePlan(String facilityCat,
                                    String orgCode,
                                    Integer year,
                                    Integer current,
                                    Integer size){
    if(current == null) {
      current = 1;
    }
    if(size == null){
      size = 50;
    }
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
    }
    Page<MaintaincePlanVo> page = new Page<>(current, size);
    IPage<MaintaincePlanVo> resultPage = null;
    if("LM".equals(facilityCat)){
      resultPage = maintaincePlanService.getPavementMaintainPlanPage(page, orgCode, year);
    }else if("QL".equals(facilityCat)){
      resultPage = maintaincePlanService.getBridgeMaintainPlanPage(page, orgCode, year);
    }else{
      resultPage = maintaincePlanService.getTunnelMaintainPlanPage(page, orgCode, year);
    }
    return resultPage;
  }

  @PostMapping("/queryMaintaincePlanDesc")
  @ApiOperation(nickname = "queryMaintaincePlanDesc", value = "查询计划汇总描述")
  public Object queryMaintaincePlanDesc(String facilityCat,
                                    String orgCode,
                                    Integer year){
    StringBuilder sb = new StringBuilder("备注：");
    if(StringUtils.isBlank(orgCode)){
      TokenMsg tokenMsg = TokenUtils.getTokenMapForNational();
      orgCode = tokenMsg.getOrgId();
      sb.append(tokenMsg.getOrgName());
    }
    List<MaintaincePlanVo> result = null;
    String call = "";
    if("LM".equals(facilityCat)){
      result = maintaincePlanService.getPavementMaintainPlanList(orgCode, year);
      call = "路面";
    }else if("QL".equals(facilityCat)){
      result = maintaincePlanService.getBridgeMaintainPlanList(orgCode, year);
      call = "桥梁";
    }else{
      result = maintaincePlanService.getTunnelMaintainPlanList(orgCode, year);
      call = "隧道";
    }
    BigDecimal suggestInvestTotal = BigDecimal.ZERO;
    BigDecimal suggestInvestTotalP = BigDecimal.ZERO;
    BigDecimal suggestInvestTotalR = BigDecimal.ZERO;
    BigDecimal realInvestTotal = BigDecimal.ZERO;
    BigDecimal realInvestTotalP = BigDecimal.ZERO;
    BigDecimal realInvestTotalR = BigDecimal.ZERO;
    int suggestCountP = 0;
    int suggestCountR= 0;
    int realCountP = 0;
    int realCountR = 0;
    int newCountP = 0;
    int newCountR = 0;
    int countSP = 0;
    int countSR = 0;
    int countP = 0;
    int countR = 0;

    int suggestCount= 0;
    int realCount = 0;
    Map<String,Object> map = new HashMap<>();
    if(result != null && !result.isEmpty()){
      for(MaintaincePlanVo mp : result){
        String isPreventPlanned = mp.getIsPreventPlanned();
        String isRepairPlanned = mp.getIsRepairPlanned();
        BigDecimal preventCost = mp.getPreventCost();
        if(preventCost != null && preventCost.compareTo(BigDecimal.ZERO) > 0){
          suggestInvestTotal = suggestInvestTotal.add(preventCost);
          suggestInvestTotalP = suggestInvestTotalP.add(preventCost);
        }
        BigDecimal civilPreventCost = mp.getCivilPreventCost();
        if(civilPreventCost != null && civilPreventCost.compareTo(BigDecimal.ZERO) > 0){
          suggestInvestTotal = suggestInvestTotal.add(civilPreventCost);
          suggestInvestTotalP = suggestInvestTotalP.add(civilPreventCost);
        }
        BigDecimal repairCost = mp.getRepairCost();
        if(repairCost != null && repairCost.compareTo(BigDecimal.ZERO) > 0){
          suggestInvestTotal = suggestInvestTotal.add(repairCost);
          suggestInvestTotalR = suggestInvestTotalR.add(repairCost);
        }
        BigDecimal civilRepairCost = mp.getCivilRepairCost();
        if(civilRepairCost != null && civilRepairCost.compareTo(BigDecimal.ZERO) > 0){
          suggestInvestTotal = suggestInvestTotal.add(civilRepairCost);
          suggestInvestTotalR = suggestInvestTotalR.add(civilRepairCost);
        }
        countSP += mp.getPreventSeats();
        countSR += mp.getRepairSeats();
        if("是".equals(isPreventPlanned) || "新增".equals(isPreventPlanned)){
          realCount ++;
          if("是".equals(isPreventPlanned)){
            suggestCount ++;
            suggestCountP ++;
          }
          realCountP ++;
          countP += mp.getPreventSeatsRel();
          BigDecimal preventCostRel = mp.getPreventCostRel();
          if(preventCostRel != null && preventCostRel.compareTo(BigDecimal.ZERO) > 0){
            realInvestTotal = realInvestTotal.add(preventCostRel);
            realInvestTotalP = realInvestTotalP.add(preventCostRel);
          }
          BigDecimal civilPreventCostRel = mp.getCivilPreventCostRel();
          if(civilPreventCostRel != null && civilPreventCostRel.compareTo(BigDecimal.ZERO) > 0){
            realInvestTotal = realInvestTotal.add(civilPreventCostRel);
            realInvestTotalP = realInvestTotalP.add(civilPreventCostRel);
          }
          if("新增".equals(isPreventPlanned)){
            newCountP ++;
          }
        }else if("否".equals(isPreventPlanned)){
          suggestCount ++;
          suggestCountP ++;
        }
        if("是".equals(isRepairPlanned) || "新增".equals(isRepairPlanned)){
          realCount ++;
          if("是".equals(isRepairPlanned)){
            suggestCountR ++;
            suggestCount ++;
          }
          realCountR ++;
          countR += mp.getRepairSeatsRel();
          BigDecimal repairCostRel = mp.getRepairCostRel();
          if(repairCostRel != null && repairCostRel.compareTo(BigDecimal.ZERO) > 0){
            realInvestTotal = realInvestTotal.add(repairCostRel);
            realInvestTotalR = realInvestTotalR.add(repairCostRel);
          }
          BigDecimal civilRepairCostRel = mp.getCivilRepairCostRel();
          if(civilRepairCostRel != null && civilRepairCostRel.compareTo(BigDecimal.ZERO) > 0){
            realInvestTotal = realInvestTotal.add(civilRepairCostRel);
            realInvestTotalR = realInvestTotalR.add(civilRepairCostRel);
          }
          if("新增".equals(isRepairPlanned)){
            newCountR ++;
          }
        }else if("否".equals(isRepairPlanned)){
          suggestCount ++;
          suggestCountR ++;
        }
      }
      BigDecimal rate = realInvestTotal.multiply(BigDecimal.valueOf(100)).divide(suggestInvestTotal, 2, RoundingMode.HALF_UP);
      sb.append(year).append("年");
      sb.append(call);
      sb.append("科学决策养护建议计划预防养护项目")
              .append(suggestCountP)
              .append("项，");
              if(countSP > 0){
                sb.append(countSP).append("座，");
              }
              sb.append("建议项目资金估算")
              .append(suggestInvestTotalP).append("万元；决策养护建议计划修复养护项目")
              .append(suggestCountR).append("项，");
              if(countSR > 0){
                sb.append(countSR).append("座，");
              }
              sb.append("建议项目资金估算")
              .append(suggestInvestTotalR)
              .append("万元；其中实际计划实施的")
              .append(call)
              .append("预防养护工程为")
              .append(realCountP)
              .append("项，");
              if(countP > 0){
                sb.append(countP).append("座，");
              }
              sb.append("计划实施资金")
              .append(realInvestTotalP)
              .append("万元");
              if(newCountP > 0){
                sb.append("(含新增").append(newCountP).append("项)");
              }
              sb.append("，实际计划实施的")
              .append(call)
              .append("修复养护工程为")
              .append(realCountR)
              .append("项，");
              if(countR > 0){
                sb.append(countR).append("座，");
              }
              sb.append("计划实施资金")
              .append(realInvestTotalR)
              .append("万元");
              if(newCountR > 0){
                sb.append("(含新增").append(newCountR).append("项)");
              }
              sb.append("。");
      map.put("rate",rate);
    }
    map.put("remark",sb.toString());
    map.put("realCount",realCount);
    map.put("suggestCount",suggestCount);
    map.put("suggestInvestTotal",suggestInvestTotal);
    map.put("realInvestTotal",realInvestTotal);
    return map;
  }

  @ApiOperation(nickname = "queryRepositoryAdjustLog", value = "调整日志")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "packageId", value = "包ID")})
  @RequestMapping(value = {"/queryRepositoryAdjustLog"}, method = {RequestMethod.GET})
  public Object queryRepositoryAdjustLog(String packageId,String facilityCat) {
    if("QL".equals(facilityCat)){
      return repositoryAdjustLogService.queryBridgeLog(packageId);
    }else if("SD".equals(facilityCat)){
      return repositoryAdjustLogService.queryTunnelLog(packageId);
    }else{
      return repositoryAdjustLogService.queryPavementLog(packageId);
    }
  }

  /**
   * 决策子系统的养护预估分析数据整理成国评建议表数据
   *
   * @return
   */
  @RequestMapping(value = {"/saveRequirementAnalysisToSuggestion"}, method = {RequestMethod.GET})
  @ApiOperation(nickname = "saveRequirementAnalysisToSuggestion", value = "决策子系统的养护预估分析数据整理成国评建议表数据")
  public RestResult<Boolean> saveRequirementAnalysisToSuggestion() {
    return RestResult.success(pavementSuggestTService.requirementAnalysisToSuggestion(), "成功");
  }

  /**
   * 国评获取外单位养护措施
   *
   * @return
   */
  @RequestMapping(value = {"/getExternalMeasure"}, method = {RequestMethod.GET, RequestMethod.POST})
  @ApiOperation(nickname = "getExternalMeasure", value = "国评获取外单位养护措施")
  public RestResult<List<ExternalMatMeasure>> getExternalMeasure(
      @RequestParam(value = "orgName", required = false) String orgName,
      @RequestParam(value = "year", required = false) Integer year,
      @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
      @RequestParam(value = "pageSize", required = false, defaultValue = "15") Integer pageSize
  ) {
    IPage<ExternalMatMeasure> p =
        externalMatMeasureService.getExternalMeasure(page, pageSize, orgName, year);
    return RestResult.success(p.getRecords(), p.getTotal(), page, pageSize);
  }

  /**
   * 国评外单位管养单位
   *
   * @return
   */
  @RequestMapping(value = {"/getExternalOrg"}, method = {RequestMethod.GET, RequestMethod.POST})
  @ApiOperation(nickname = "getExternalOrg", value = "国评外单位管养单位")
  public RestResult<List<String>> getExternalOrg() {
    List<String> orgList = externalMatMeasureService.getOrgList();
    return RestResult.success(orgList, "成功");
  }

  @ApiOperation(nickname = "querySlopeInspectLists",value ="查询边坡日常巡查" )
  @GetMapping("/querySlopeInspectLists")
  public RestResult<List<SlopeDmDinsp>> querySlopeInspectLists(
          String orgCode,String startDate,String endDate, Integer pageIndex, Integer pageSize
  ) {
    if(null == pageIndex){
      pageIndex = 1;
    }
    if(null == pageSize){
      pageSize = 50;
    }
    if(StringUtils.isBlank(orgCode)){
      orgCode = TokenUtils.getTokenMapForNational().getOrgId();
    }
    IPage<SlopeDmDinsp> page = new Page<>(pageIndex, pageSize);
    IPage<SlopeDmDinsp> iPage = dmDinspService.querySlopeInspectLists(page,orgCode,startDate,endDate);
    return RestResult.success(iPage.getRecords(),  iPage.getTotal(), iPage.getCurrent(), iPage.getSize());
  }

  @ApiOperation(nickname = "querySlopeInfoList",value ="查询日常巡查包含的边坡列表" )
  @GetMapping("/querySlopeInfoList")
  public RestResult<List<SlopeInfo>> querySlopeInfoList(
          String dinspId
  ) {
    List<SlopeInfo> slopeInfos = dmDinspService.querySlopeInfoList(dinspId);
    return RestResult.success(slopeInfos);
  }

  @ApiOperation(nickname = "querySlopeDmDinspRecord",value ="查询边坡日常巡查记录" )
  @GetMapping("/querySlopeDmDinspRecord")
  public RestResult<List<DmSlopeRef>> querySlopeDmDinspRecord(
          String dinspId
  ) {
    List<DmSlopeRef> records = dmDinspService.querySlopeDmDinspRecord(dinspId);
    return RestResult.success(records);
  }

  @ApiOperation(nickname = "querySlopeTotal",value ="查找边坡总表" )
  @GetMapping("/querySlopeTotal")
  public RestResult<List<SlopeTotalTable>> querySlopeTotal(
          String orgCode, String time
  ) {
    String year = "";
    if(StringUtils.isBlank(time)){
      time = DateTimeUtil.formatDate(new Date(),"yyyy-MM-dd");
    }
    year = time.substring(0,4);
    if(StringUtils.isBlank(orgCode)){
      orgCode = TokenUtils.getTokenMapForNational().getOrgId();
    }
    List<SlopeTotalTable> records = dmDinspService.queryTotalTable(orgCode, year, time);
    return RestResult.success(records);
  }

  @ApiOperation(nickname = "downloadAllAnnexs",value ="下载所有佐证材料" )
  @GetMapping("/downloadAllAnnexs")
  public RestResult<String> copyAnnexs(String orgCode,Integer year,String type,HttpServletResponse rep){
    nationalAnnexService.queryByOrgCode(orgCode,year,type,rep);
    return RestResult.success("OK");
  }

  @GetMapping("/downloadFile")
  public ResponseEntity<FileSystemResource> downloadFile(String filePath) {
    File file = new File(filePath);
    if (!file.exists()) {
      return ResponseEntity.notFound().build();
    }
      HttpHeaders headers = new HttpHeaders();
      headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"");

      return ResponseEntity.ok()
              .headers(headers)
              .contentType(MediaType.APPLICATION_OCTET_STREAM)
              .body(new FileSystemResource(file));
  }

  // ==================== 更新接口 ====================

  /**
   * 更新路面项目库执行情况
   */
  @PostMapping("/updatePavementRepositoryImp")
  @ApiOperation(nickname = "updatePavementRepositoryImp", value = "更新路面项目库执行情况")
  public RestResult<String> updatePavementRepositoryImp(@RequestBody PavementRepositoryImp pavementRepositoryImp) {
    boolean result = pavementRepositoryImpService.updatePavementRepositoryImp(pavementRepositoryImp);
    return result ? RestResult.success("更新成功") : RestResult.error("更新失败");
  }

  /**
   * 更新桥梁项目库执行情况
   */
  @PostMapping("/updateBridgeRepositoryImp")
  @ApiOperation(nickname = "updateBridgeRepositoryImp", value = "更新桥梁项目库执行情况")
  public RestResult<String> updateBridgeRepositoryImp(@RequestBody BridgeRepositoryImp bridgeRepositoryImp) {
    boolean result = bridgeRepositoryImpService.updateBridgeRepositoryImp(bridgeRepositoryImp);
    return result ? RestResult.success("更新成功") : RestResult.error("更新失败");
  }

  /**
   * 更新隧道项目库执行情况
   */
  @PostMapping("/updateTunnelRepositoryImp")
  @ApiOperation(nickname = "updateTunnelRepositoryImp", value = "更新隧道项目库执行情况")
  public RestResult<String> updateTunnelRepositoryImp(@RequestBody TunnelRepositoryImp tunnelRepositoryImp) {
    boolean result = tunnelRepositoryImpService.updateTunnelRepositoryImp(tunnelRepositoryImp);
    return result ? RestResult.success("更新成功") : RestResult.error("更新失败");
  }

  /**
   * 更新路面养护计划
   */
  @PostMapping("/updatePavementMaintainPlan")
  @ApiOperation(nickname = "updatePavementMaintainPlan", value = "更新路面养护计划")
  public RestResult<String> updatePavementMaintainPlan(@RequestBody MaintaincePlanVo maintaincePlanVo) {
    boolean result = maintaincePlanService.updatePavementMaintainPlan(maintaincePlanVo);
    return result ? RestResult.success("更新成功") : RestResult.error("更新失败");
  }

  /**
   * 更新桥梁养护计划
   */
  @PostMapping("/updateBridgeMaintainPlan")
  @ApiOperation(nickname = "updateBridgeMaintainPlan", value = "更新桥梁养护计划")
  public RestResult<String> updateBridgeMaintainPlan(@RequestBody MaintaincePlanVo maintaincePlanVo) {
    boolean result = maintaincePlanService.updateBridgeMaintainPlan(maintaincePlanVo);
    return result ? RestResult.success("更新成功") : RestResult.error("更新失败");
  }

  /**
   * 更新隧道养护计划
   */
  @PostMapping("/updateTunnelMaintainPlan")
  @ApiOperation(nickname = "updateTunnelMaintainPlan", value = "更新隧道养护计划")
  public RestResult<String> updateTunnelMaintainPlan(@RequestBody MaintaincePlanVo maintaincePlanVo) {
    boolean result = maintaincePlanService.updateTunnelMaintainPlan(maintaincePlanVo);
    return result ? RestResult.success("更新成功") : RestResult.error("更新失败");
  }

  /**
   * 新增路面项目库执行情况
   */
  @PostMapping("/addPavementRepositoryImp")
  @ApiOperation(nickname = "addPavementRepositoryImp", value = "新增路面项目库执行情况")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "orgCode", value = "组织编码", required = true, dataType = "String", paramType = "query"),
          @ApiImplicitParam(name = "year", value = "年份", required = false, dataType = "Integer", paramType = "query", defaultValue = "2025")
  })
  public RestResult<String> addPavementRepositoryImp(
          @RequestParam String orgCode,
          @RequestParam(defaultValue = "2025") Integer year,
          @RequestBody PavementRepositoryImp pavementRepositoryImp) {

    // 设置默认值
    pavementRepositoryImp.setOrgCode(orgCode);
    pavementRepositoryImp.setYear(year);
    pavementRepositoryImp.setTarget(year); // target值默认当前年份
    pavementRepositoryImp.setImportTime(new Date());

    // 生成ID
    if (pavementRepositoryImp.getId() == null || pavementRepositoryImp.getId().trim().isEmpty()) {
      pavementRepositoryImp.setId(UUID.randomUUID().toString());
    }

    boolean result = pavementRepositoryImpService.save(pavementRepositoryImp);
    return result ? RestResult.success("新增成功") : RestResult.error("新增失败");
  }

  /**
   * 新增桥梁项目库执行情况
   */
  @PostMapping("/addBridgeRepositoryImp")
  @ApiOperation(nickname = "addBridgeRepositoryImp", value = "新增桥梁项目库执行情况")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "orgCode", value = "组织编码", required = true, dataType = "String", paramType = "query"),
          @ApiImplicitParam(name = "year", value = "年份", required = false, dataType = "Integer", paramType = "query", defaultValue = "2025")
  })
  public RestResult<String> addBridgeRepositoryImp(
          @RequestParam String orgCode,
          @RequestParam(defaultValue = "2025") Integer year,
          @RequestBody BridgeRepositoryImp bridgeRepositoryImp) {

    // 设置默认值
    bridgeRepositoryImp.setOrgCode(orgCode);
    bridgeRepositoryImp.setYear(year);
    bridgeRepositoryImp.setTarget(year); // target值默认当前年份
    bridgeRepositoryImp.setImportTime(new Date());

    // 生成ID
    if (bridgeRepositoryImp.getId() == null || bridgeRepositoryImp.getId().trim().isEmpty()) {
      bridgeRepositoryImp.setId(UUID.randomUUID().toString());
    }

    boolean result = bridgeRepositoryImpService.save(bridgeRepositoryImp);
    return result ? RestResult.success("新增成功") : RestResult.error("新增失败");
  }

  /**
   * 新增隧道项目库执行情况
   */
  @PostMapping("/addTunnelRepositoryImp")
  @ApiOperation(nickname = "addTunnelRepositoryImp", value = "新增隧道项目库执行情况")
  @ApiImplicitParams({
          @ApiImplicitParam(name = "orgCode", value = "组织编码", required = true, dataType = "String", paramType = "query"),
          @ApiImplicitParam(name = "year", value = "年份", required = false, dataType = "Integer", paramType = "query", defaultValue = "2025")
  })
  public RestResult<String> addTunnelRepositoryImp(
          @RequestParam String orgCode,
          @RequestParam(defaultValue = "2025") Integer year,
          @RequestBody TunnelRepositoryImp tunnelRepositoryImp) {

    // 设置默认值
    tunnelRepositoryImp.setOrgCode(orgCode);
    tunnelRepositoryImp.setYear(year);
    tunnelRepositoryImp.setTarget(year); // target值默认当前年份
    tunnelRepositoryImp.setImportTime(new Date());

    // 生成ID
    if (tunnelRepositoryImp.getId() == null || tunnelRepositoryImp.getId().trim().isEmpty()) {
      tunnelRepositoryImp.setId(UUID.randomUUID().toString());
    }

    boolean result = tunnelRepositoryImpService.save(tunnelRepositoryImp);
    return result ? RestResult.success("新增成功") : RestResult.error("新增失败");
  }
}
