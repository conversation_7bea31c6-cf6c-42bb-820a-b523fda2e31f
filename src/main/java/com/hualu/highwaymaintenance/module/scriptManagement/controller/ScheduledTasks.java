package com.hualu.highwaymaintenance.module.scriptManagement.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapDataScriptConfig;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog;
import com.hualu.highwaymaintenance.module.scriptManagement.service.OnemapDataScriptConfigService;
import com.hualu.highwaymaintenance.module.scriptManagement.service.OnemapScriptExcuteLogService;
import com.hualu.highwaymaintenance.module.scriptManagement.service.impl.OnemapScriptExcuteLogServiceImpl;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Component
public class ScheduledTasks {

  @Resource
  private OnemapDataScriptConfigService scriptConfigService;
  @Resource
  private OnemapScriptExcuteLogService logService;
  @Scheduled(cron = "0 0 1 * * ?")
  public void executeTask() {
    LambdaQueryWrapper<OnemapDataScriptConfig> l  = new LambdaQueryWrapper<>();
    l.eq(OnemapDataScriptConfig::getIsEnable,1);
    List<OnemapDataScriptConfig> list = scriptConfigService.list(l);

    Calendar calendar = Calendar.getInstance();
    ZoneId zoneId = ZoneId.systemDefault();
    for(OnemapDataScriptConfig config:list){
      String id = config.getId();
      Date lastExcuteTime = config.getLastExcuteTime();
      Date nextExcuteTime = config.getNextExcuteTime();
      Date firstTime = config.getFirstTime();
      String excuteRate = config.getExcuteRate();
      if(firstTime==null&&(lastExcuteTime==null||nextExcuteTime.before(new Date()))||firstTime!=null&&firstTime.before(new Date())){
        config.setLastExcuteTime(new Date());
        lastExcuteTime = config.getLastExcuteTime();
        calendar.setTime(lastExcuteTime);
        if(excuteRate.equals("每日")){
          // 将日期加一天
          calendar.add(Calendar.DAY_OF_MONTH, 1);
          // 转换回Date对象（如果需要）
          Date newDate = calendar.getTime();
          config.setNextExcuteTime(newDate);
        }
        if(excuteRate.equals("每周")){
          calendar.add(Calendar.DAY_OF_MONTH, 7);
          // 转换回Date对象（如果需要）
          Date newDate = calendar.getTime();
          config.setNextExcuteTime(newDate);
        }
        LocalDate originalDate = LocalDate.of(lastExcuteTime.getYear(), lastExcuteTime.getMonth(), lastExcuteTime.getDay()); // 2023年3月31日
        // 加上一个月
        if(excuteRate.equals("每月")){
          LocalDate newDate = originalDate.plusMonths(1);
          config.setNextExcuteTime( Date.from(newDate.atStartOfDay(zoneId).toInstant()));
        }
        if(excuteRate.equals("每年")){
          LocalDate newDate = originalDate.plusMonths(12);
          config.setNextExcuteTime( Date.from(newDate.atStartOfDay(zoneId).toInstant()));
        }
        OnemapScriptExcuteLog log = new OnemapScriptExcuteLog();
        log.setExecutionTime(config.getLastExcuteTime());

        try{
          log.setId(StringUtil.getUUID());
          log.setScriptId(id);
          log.setExecutionStatus("成功");
          String dataScript = config.getDataScript();
          scriptConfigService.excuteBySql(dataScript);
          scriptConfigService.saveOrUpdate(config);
        }catch (Exception e){
          log.setExecutionStatus("失败");
          log.setErrorMessage(e.getMessage());
        }
        logService.save(log);
      }
    }
    // 你的任务逻辑
    System.out.println("执行任务");
  }
}
