package com.hualu.highwaymaintenance.module.scriptManagement.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog;
import com.hualu.highwaymaintenance.module.scriptManagement.mapper.OnemapScriptExcuteLogMapper;
import com.hualu.highwaymaintenance.module.scriptManagement.service.OnemapScriptExcuteLogService;

@Service
public class OnemapScriptExcuteLogServiceImpl extends ServiceImpl<OnemapScriptExcuteLogMapper, OnemapScriptExcuteLog> implements OnemapScriptExcuteLogService {


  @Resource
  private OnemapScriptExcuteLogMapper mapper;
  @Override
  public Page<OnemapScriptExcuteLog> getPage(Page<OnemapScriptExcuteLog> page1, String scriptId) {

    return mapper.getPage(page1,scriptId);
  }
}

