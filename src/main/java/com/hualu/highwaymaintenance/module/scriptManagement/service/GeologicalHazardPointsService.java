package com.hualu.highwaymaintenance.module.scriptManagement.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.highwaymaintenance.module.maintainbase.GeologicalHazardPoints;
public interface GeologicalHazardPointsService{


    int insert(GeologicalHazardPoints record);

    int insertSelective(GeologicalHazardPoints record);

  Page<GeologicalHazardPoints> getGeologicalHazardPointsPage(Page<GeologicalHazardPoints> page, String scaleLevel, String pointType, String pointName);
}
