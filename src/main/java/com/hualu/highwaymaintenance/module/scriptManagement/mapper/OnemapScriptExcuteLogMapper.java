package com.hualu.highwaymaintenance.module.scriptManagement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog;
import org.apache.ibatis.annotations.Param;

public interface OnemapScriptExcuteLogMapper extends BaseMapper<OnemapScriptExcuteLog> {

  Page<OnemapScriptExcuteLog> getPage(Page<OnemapScriptExcuteLog> page1, @Param("scriptId") String scriptId);
}