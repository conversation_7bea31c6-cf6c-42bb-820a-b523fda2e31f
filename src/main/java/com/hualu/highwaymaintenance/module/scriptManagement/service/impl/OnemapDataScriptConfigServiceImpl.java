package com.hualu.highwaymaintenance.module.scriptManagement.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.hualu.highwaymaintenance.module.scriptManagement.mapper.OnemapDataScriptConfigMapper;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapDataScriptConfig;
import com.hualu.highwaymaintenance.module.scriptManagement.service.OnemapDataScriptConfigService;

@Service
public class OnemapDataScriptConfigServiceImpl extends ServiceImpl<OnemapDataScriptConfigMapper, OnemapDataScriptConfig> implements OnemapDataScriptConfigService {

  @Resource
  private OnemapDataScriptConfigMapper mapper;

  @Override
  public void excuteBySql(String dataScript) {
    String[] split = dataScript.split(";");
    for (String s : split) {
      if (StringUtil.isNullOrEmpty(s)) {
        break;
      }
      mapper.excuteBySql(s);
    }
  }

  @Override
  public Page<OnemapDataScriptConfig> queryPage(Page<OnemapDataScriptConfig> page, String scriptName) {

    return mapper.queryPage(page, scriptName);
  }


}


