package com.hualu.highwaymaintenance.module.scriptManagement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapDataScriptConfig;
import org.apache.ibatis.annotations.Param;

public interface OnemapDataScriptConfigMapper extends BaseMapper<OnemapDataScriptConfig> {

  void excuteBySql(@Param("s") String s);

  Page<OnemapDataScriptConfig> queryPage(Page<OnemapDataScriptConfig> page, @Param("scriptName") String scriptName);
}