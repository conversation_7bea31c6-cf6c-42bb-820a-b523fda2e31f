package com.hualu.highwaymaintenance.module.scriptManagement.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
    * 脚本执行日志
    */
@TableName(value ="ONEMAP_SCRIPT_EXCUTE_LOG",schema = "GDGS")
@ApiModel(description="脚本执行日志")
public class OnemapScriptExcuteLog {
    /**
    * 主键
    */
    @TableId
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 脚本id
    */
    @ApiModelProperty(value="脚本id")
    private String scriptId;

    /**
    * 脚本执行的时间
    */
    @ApiModelProperty(value="脚本执行的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date executionTime;



    /**
    * 执行状态
    */
    @ApiModelProperty(value="执行状态")
    private String executionStatus;

    /**
    * 执行失败信息
    */
    @ApiModelProperty(value="执行失败信息")
    private String errorMessage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getScriptId() {
        return scriptId;
    }

    public void setScriptId(String scriptId) {
        this.scriptId = scriptId;
    }

    public Date getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Date executionTime) {
        this.executionTime = executionTime;
    }

    public String getExecutionStatus() {
        return executionStatus;
    }

    public void setExecutionStatus(String executionStatus) {
        this.executionStatus = executionStatus;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}