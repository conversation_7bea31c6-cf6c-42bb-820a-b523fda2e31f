package com.hualu.highwaymaintenance.module.scriptManagement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description="SSF_WS")
public class SsfWs {
    @ApiModelProperty(value="")
    private String bridgeId;

    @ApiModelProperty(value="")
    private String ssfType;

    @ApiModelProperty(value="")
    private String ssfLocation;

    @ApiModelProperty(value="")
    private String ssfLength;

    @ApiModelProperty(value="")
    private String ssfGap;

    @ApiModelProperty(value="")
    private String ssfProducer;

    public String getBridgeId() {
        return bridgeId;
    }

    public void setBridgeId(String bridgeId) {
        this.bridgeId = bridgeId;
    }

    public String getSsfType() {
        return ssfType;
    }

    public void setSsfType(String ssfType) {
        this.ssfType = ssfType;
    }

    public String getSsfLocation() {
        return ssfLocation;
    }

    public void setSsfLocation(String ssfLocation) {
        this.ssfLocation = ssfLocation;
    }

    public String getSsfLength() {
        return ssfLength;
    }

    public void setSsfLength(String ssfLength) {
        this.ssfLength = ssfLength;
    }

    public String getSsfGap() {
        return ssfGap;
    }

    public void setSsfGap(String ssfGap) {
        this.ssfGap = ssfGap;
    }

    public String getSsfProducer() {
        return ssfProducer;
    }

    public void setSsfProducer(String ssfProducer) {
        this.ssfProducer = ssfProducer;
    }
}