package com.hualu.highwaymaintenance.module.scriptManagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 数据抽取脚本配置表
    */
@TableName(value ="ONEMAP_DATA_SCRIPT_CONFIG",schema = "GDGS")
@ApiModel(description="数据抽取脚本配置表")
public class OnemapDataScriptConfig {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private String id;

    /**
    * 数据脚本
    */
    @ApiModelProperty(value="数据脚本")
    private String dataScript;

    /**
    * 脚本名称
    */
    @ApiModelProperty(value="脚本名称")
    private String scriptName;

    /**
    * 执行频率
    */
    @ApiModelProperty(value="执行频率")
    private String excuteRate;

    /**
    * 初次执行时间
    */
    @ApiModelProperty(value="初次执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastExcuteTime;

    /**
    * 下次执行时间
    */
    @ApiModelProperty(value="下次执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextExcuteTime;

    /**
    * 是否启用
    */
    @ApiModelProperty(value="是否启用")
    private BigDecimal isEnable;

    /**
    * 脚本创建时间
    */
    @ApiModelProperty(value="脚本创建时间")
    private Date createTime;

    /**
    * 脚本修改时间
    */
    @ApiModelProperty(value="脚本修改时间")
    private Date modifyTime;

    /**
    * 增量全量
    */
    @ApiModelProperty(value="增量全量")
    private String updateType;

    /**
    * 首次执行时间
    */
    @ApiModelProperty(value="首次执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date firstTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDataScript() {
        return dataScript;
    }

    public void setDataScript(String dataScript) {
        this.dataScript = dataScript;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getExcuteRate() {
        return excuteRate;
    }

    public void setExcuteRate(String excuteRate) {
        this.excuteRate = excuteRate;
    }

    public Date getLastExcuteTime() {
        return lastExcuteTime;
    }

    public void setLastExcuteTime(Date lastExcuteTime) {
        this.lastExcuteTime = lastExcuteTime;
    }

    public Date getNextExcuteTime() {
        return nextExcuteTime;
    }

    public void setNextExcuteTime(Date nextExcuteTime) {
        this.nextExcuteTime = nextExcuteTime;
    }

    public BigDecimal getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(BigDecimal isEnable) {
        this.isEnable = isEnable;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getUpdateType() {
        return updateType;
    }

    public void setUpdateType(String updateType) {
        this.updateType = updateType;
    }

    public Date getFirstTime() {
        return firstTime;
    }

    public void setFirstTime(Date firstTime) {
        this.firstTime = firstTime;
    }
}