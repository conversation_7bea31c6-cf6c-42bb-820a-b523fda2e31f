package com.hualu.highwaymaintenance.module.scriptManagement.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.module.bridge.entity.TBrdgExpnsnjoint;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapDataScriptConfig;
import com.hualu.highwaymaintenance.module.scriptManagement.entity.OnemapScriptExcuteLog;
import com.hualu.highwaymaintenance.module.scriptManagement.mapper.OnemapScriptExcuteLogMapper;
import com.hualu.highwaymaintenance.module.scriptManagement.service.OnemapDataScriptConfigService;
import com.hualu.highwaymaintenance.module.scriptManagement.service.OnemapScriptExcuteLogService;
import com.hualu.highwaymaintenance.util.StringUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
@RestController
@RequestMapping("/scriptConfigManage")
public class ScriptConfigManageController {
  @Resource
  private OnemapDataScriptConfigService scriptConfigService;
  @PostMapping("listScriptConfig")
  @ApiOperation(nickname = "列出脚本列表", value = "列出脚本列表")
  public RestResult<List<OnemapDataScriptConfig>> listScriptConfig(
      @RequestParam(required = false) String scriptName, int page, int pageSize) {
//    LambdaQueryWrapper<OnemapDataScriptConfig> l  = new LambdaQueryWrapper<>();
//    if(!StringUtil.isNullOrEmpty(scriptName)){
//      l.like(OnemapDataScriptConfig::getScriptName,scriptName);
//    }
    Page<OnemapDataScriptConfig> page1 = new Page<>(page,pageSize);
    page1 = scriptConfigService.queryPage(page1,scriptName);
    return RestResult.success(page1.getRecords(), page1.getTotal(), page1.getPages(),
        page1.getSize());
  }

  @PostMapping("/addScriptConfig")
  @ApiOperation(nickname = "新增或者更新脚本", value = "新增或者更新脚本")
  public RestResult<String> addScriptConfig(
      String scriptName,
      @RequestParam(required = false) String uid,
      String dataScript,String rate,String updateType,String firstTime
  ) throws ParseException {
    boolean successful;
    OnemapDataScriptConfig model = null;
    if (uid != null) {
      model = scriptConfigService.getById(uid);
    }
    if (model == null) {
      model = new OnemapDataScriptConfig();
    }
    model.setScriptName(scriptName);
    model.setDataScript(dataScript);
    model.setIsEnable(BigDecimal.valueOf(1));
    model.setExcuteRate(rate);
    model.setUpdateType(updateType);
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    model.setFirstTime(df.parse(firstTime));
    try {
      if (uid == null){
        model.setId(StringUtil.getUUID());
        model.setCreateTime(new Date());
      }else{
        model.setModifyTime(new Date());
      }
      scriptConfigService.saveOrUpdate(model);

      successful = true;
    } catch (Exception e) {
      successful = false;
      e.printStackTrace();
    }
    return successful ? RestResult.success(model.getId(), "新增或者更新脚本成功")
        : RestResult.error("新增或者更新脚本失败");
  }


  @PostMapping("/enableScriptConfig")
  @ApiOperation(nickname = "启用或禁用脚本", value = "启用或禁用脚本，1为启用，0为禁用")
  public RestResult<String> enableScriptConfig(
      String scriptId,
      int able
  ) {
    boolean successful;
    OnemapDataScriptConfig model = null;
    if (scriptId != null) {
      model = scriptConfigService.getById(scriptId);
    }
    model.setIsEnable(BigDecimal.valueOf(able));
    try {
      scriptConfigService.saveOrUpdate(model);
      successful = true;
    } catch (Exception e) {
      successful = false;
      e.printStackTrace();
    }
    return successful ? RestResult.success(model.getId(), "启用或禁用脚本成功")
        : RestResult.error("启用或禁用脚本失败");
  }
  @Resource
  private OnemapScriptExcuteLogService logService;
  @PostMapping("listScriptExecuteLog")
  @ApiOperation(nickname = "查看脚本执行日志", value = "根据id查看脚本执行日志")
  public RestResult<List<OnemapScriptExcuteLog>> listScriptExecuteLog(String scriptId, int page,
      int pageSize) {
    Page<OnemapScriptExcuteLog> page1 = new Page<>(page,pageSize);
    Page<OnemapScriptExcuteLog> p = logService.getPage(page1, scriptId);
    return RestResult.success(p.getRecords(), p.getTotal(), p.getPages(), p.getSize());
  }
}
