package com.hualu.highwaymaintenance.module.structLocation;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.module.bridge.entity.TBrdgBrdgrecog;
import com.hualu.highwaymaintenance.module.bridge.service.TBrdgBrdgrecogService;
import io.swagger.annotations.ApiOperation;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

@RestController
@CrossOrigin
@RequestMapping("/bridgeLocation2")
public class TunnelLocationController {
  @Resource
  private TBrdgBrdgrecogService service;
  @GetMapping("getBridgeListByOprtCode")
  @ApiOperation(nickname = "查询所属桥梁", value = "查询所属桥梁")
  public RestResult<List<TBrdgBrdgrecog>> getBridgeListByOprtCode(String oprtCode) {

    return RestResult.success(getBrdgListByOprtCode(oprtCode), "查询所属桥梁");
  }
  @PostMapping("/exportBridgeListByOprtCode")
  @ApiOperation(nickname = "exportBridgeListByOprtCode", value = "导出所属桥梁")
  public void exportBridgeListByOprtCode(
      String oprtCode,
      HttpServletResponse response
  ) throws Exception {
    getBrdgListByOprtCode(oprtCode);
    String fileName = URLEncoder.encode("桥梁采集位置.xlsx", "UTF-8");
    response.setContentType("application/x-download");
    response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
    try (
        InputStream inputStream = new ClassPathResource(
            "templates/bridge/桥梁采集位置模板.xlsx").getInputStream();
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
            .withTemplate(inputStream).build()
    ) {
      WriteSheet writeSheet = EasyExcel.writerSheet().build();
      FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
      excelWriter.fill(getBrdgListByOprtCode(oprtCode), fillConfig, writeSheet);
    }
  }
  private List<TBrdgBrdgrecog> getBrdgListByOprtCode(String oprtCode) {
    LambdaQueryWrapper<TBrdgBrdgrecog> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.eq(TBrdgBrdgrecog::getOprtOrgCode, oprtCode);
    lambdaQueryWrapper.eq(TBrdgBrdgrecog::getValidFlag, 1);
    lambdaQueryWrapper.orderByAsc(TBrdgBrdgrecog::getRoadNum);
    lambdaQueryWrapper.orderByAsc(TBrdgBrdgrecog::getLogicCntrStakeNum);
    List<TBrdgBrdgrecog> list = service.list(lambdaQueryWrapper);
    return list;
  }
}
