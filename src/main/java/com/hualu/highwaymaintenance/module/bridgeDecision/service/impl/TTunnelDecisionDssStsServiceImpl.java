package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TTunnelDecisionDssStsMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDecisionDssSts;
import com.hualu.highwaymaintenance.module.maintainbase.service.TTunnelDecisionDssStsService;

import java.util.List;

@Service
public class TTunnelDecisionDssStsServiceImpl extends ServiceImpl<TTunnelDecisionDssStsMapper,TTunnelDecisionDssSts> implements TTunnelDecisionDssStsService{

    @Resource
    private TTunnelDecisionDssStsMapper tTunnelDecisionDssStsMapper;

  @Override
  public List<TTunnelDecisionDssSts> getTunnelDssStsByTunnelId(String tunnelId) {
    return tTunnelDecisionDssStsMapper.getTunnelDssStsByTunnelId(tunnelId);
  }
}
