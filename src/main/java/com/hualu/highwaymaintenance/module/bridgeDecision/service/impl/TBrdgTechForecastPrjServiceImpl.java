package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.*;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeOrderModelParamMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.vo.*;
import com.hualu.highwaymaintenance.module.maintainbase.service.TStructMaintainSuggestService;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBrdgTechForecastPrjMapper;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBrdgTechForecastPrjService;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TBrdgTechForecastPrjServiceImpl extends ServiceImpl<TBrdgTechForecastPrjMapper,TBrdgTechForecastPrj> implements TBrdgTechForecastPrjService{

  @Resource
  private TBrdgTechForecastPrjMapper tBrdgTechForecastPrjMapper;

  @Resource
  private TBridgeOrderModelParamMapper orderModelParamMapper;

  @Override
  public int getBrdgSum(String prjId) {
    return tBrdgTechForecastPrjMapper.getBrdgSum(prjId);
  }

  @Override
  public List<BrdgMoneyVo> listBridgeMoneyList(String prjId) {
    return tBrdgTechForecastPrjMapper.listBridgeMoneyList(prjId);
  }

  @Override
  public ItemMoneyVo countMoney(String brdgId,  String modelId) {
      return tBrdgTechForecastPrjMapper.countMoney(brdgId,modelId);
  }


  @Override
  public double countWeight(String brdgId, String orderModelId) {
    LambdaQueryWrapper<TBridgeOrderModelParam> l = new LambdaQueryWrapper<>();
    l.eq(TBridgeOrderModelParam::getModelId,orderModelId);
    List<TBridgeOrderModelParam> tBridgeOrderModelParams = orderModelParamMapper.selectList(l);
    Map<Integer,Double> map = new HashMap<>();
    for(TBridgeOrderModelParam t:tBridgeOrderModelParams){
      map.put(t.getDicId().intValue(),t.getParamValue());
    }
    Double param1 = map.get(1);
    Double param2 = map.get(2);
    Double param3 = map.get(3);
    Double param4 = map.get(4);

    Double param5 = map.get(6);
    Double param6 = map.get(7);
    Double param7 = map.get(8);

    Double param8 = map.get(9);
    Double param9 = map.get(10);
    Double param10 = map.get(11);
    Double param11 = map.get(12);

    Double param12 = map.get(13);
    Double param13 = map.get(14);

    Double param14 = map.get(15);
    Double param15 = map.get(16);
    Double param16 = map.get(17);
    Double param18 = map.get(18);
    Double param19 = map.get(19);
    Double param105 = map.get(5);
    Double param100 = map.get(100);
    Double param101 = map.get(101);
    Double param102 = map.get(102);


    Double weight = 0d;
    Double x = 0d;
    Double y = 0d;
    Double z = 0d;
    Double v = 0d;

    BrdgWeight b = tBrdgTechForecastPrjMapper.queryBridgeWeight(brdgId);
    Double score = b.getScore();
    if(score==null){
      score = 100d;
    }
    if(score>=95){
      x = param5;
    } else if (score>=90) {
      x = param6;
    } else if (score>=85) {
      x = param7;
    } else if (score>=80) {
      x = param18;
    }else {
      x = param19;
    }
    String brdgType = b.getBrdgType();
    if(brdgType==null){
      brdgType = "梁式桥";
    }
    if(brdgType.contains("梁式桥")){
      y = param11;
    }
    if(brdgType.contains("拱桥")){
      y = param10;
    }
    if(brdgType.contains("斜拉桥")){
      y = param9;
    }
    if(brdgType.contains("悬索桥")){
      y = param8;
    }
    String maintainLvl = b.getMaintainLvl();
    if("1".equals(maintainLvl)){
      z = param12;
    }else{
      z = param13;
    }
    Calendar calendar = Calendar.getInstance();
    int currentYear = calendar.get(Calendar.YEAR)-1;
    Integer year = b.getInspectYear();
    if(year==null){
      year = currentYear;
    }
    int i = currentYear - year;
    if(i==0){
      v = param14;
    }else
    if(i==1){
      v = param15;
    }else {
      v = param16;
    }
    double w = 0d;
    Integer addt = b.getAddt();
    if(addt<=6000){
      w = param100;
    } else if (addt<=8000) {
      w = param101;
    }else {
      w = param102;
    }
    weight = param1*x+param2*y+param3*z+param4*v+param105*w;
    return weight;
  }

  @Override
  public int getTunnelSum(String prjId) {
    return tBrdgTechForecastPrjMapper.getTunnelSum(prjId);
  }

  @Override
  public List<BrdgMoneyVo> listTunnelMoneyList(String prjId) {
    return tBrdgTechForecastPrjMapper.listTunnelMoneyList(prjId);
  }

  @Override
  public double countSdWeight(String brdgId, String orderModelId) {
    LambdaQueryWrapper<TBridgeOrderModelParam> l = new LambdaQueryWrapper<>();
    l.eq(TBridgeOrderModelParam::getModelId,orderModelId);
    List<TBridgeOrderModelParam> tBridgeOrderModelParams = orderModelParamMapper.selectList(l);
    Map<Integer,Double> map = new HashMap<>();
    for(TBridgeOrderModelParam t:tBridgeOrderModelParams){
      map.put(t.getDicId().intValue()-19,t.getParamValue());
    }
    Double param1 = map.get(1);
    Double param2 = map.get(2);
    Double param3 = map.get(3);
    Double param4 = map.get(4);

    Double param5 = map.get(5);
    Double param6 = map.get(6);
    Double param7 = map.get(7);

    Double param8 = map.get(8);
    Double param9 = map.get(9);
    Double param10 = map.get(10);
    Double param11 = map.get(11);

    Double param12 = map.get(12);
    Double param13 = map.get(13);

    Double param14 = map.get(14);
    Double param15 = map.get(15);
    Double param16 = map.get(16);
    Double param18 = map.get(17);
    Double param19 = map.get(18);

    Double param105 = map.get(501-19);
    Double param100 = map.get(103-19);
    Double param101 = map.get(104-19);
    Double param102 = map.get(105-19);

    Double weight = 0d;
    Double x = 0d;
    Double y = 0d;
    Double z = 0d;
    Double v = 0d;

    TunnelWeight b = tBrdgTechForecastPrjMapper.queryTunnelWeight(brdgId);
    Double score = b.getScore();
    if(score==null){
      score = 100d;
    }
    if(score>=95){
      x = param5;
    } else if (score>=90) {
      x = param6;
    } else if (score>=85) {
      x = param7;
    } else if (score>=80) {
      x = param18;
    }else {
      x = param19;
    }
    String brdgType = b.getTunnelType();
    if(brdgType==null){
      brdgType = "中隧道";
    }
    if(brdgType.contains("4")){
      y = param11;
    }
    if(brdgType.contains("3")){
      y = param10;
    }
    if(brdgType.contains("2")){
      y = param9;
    }
    if(brdgType.contains("1")){
      y = param8;
    }
    String maintainLvl = b.getMaintainLvl();
    if("1".equals(maintainLvl)){
      z = param12;
    }else{
      z = param13;
    }
    Integer dssType = 3;
    Integer cq = b.getCq();
    if(cq>0){
      dssType = 1;
    } else if (b.getDk()>0) {
      dssType = 2;
    }


    if(dssType==3){
      v = param14;
    }else
    if(dssType==2){
      v = param15;
    }else {
      v = param16;
    }
    double w = 0d;
    Integer addt = b.getAddt();
    if(addt<=6000){
      w = param100;
    } else if (addt<=8000) {
      w = param101;
    }else {
      w = param102;
    }
    weight = param1*x+param2*y+param3*z+param4*v+param105*w;
    return weight;
  }

  @Override
  public ItemMoneyVo countTunnelMoney(String b, String maintainModel) {
      return tBrdgTechForecastPrjMapper.countTunnelMoney(b,maintainModel);

  }

  @Override
  public List<MaintainSummaryVo> listBridgeMaintainSummary(int prjYear) {
    int  curentYear = Calendar.getInstance().get(Calendar.YEAR)-1;
    return tBrdgTechForecastPrjMapper.tBrdgTechForecastPrjMapper(prjYear,curentYear);
  }

  @Override
  public List<MaintainSummaryVo> listTunnelMaintainSummary(int prjYear) {
    int  curentYear = Calendar.getInstance().get(Calendar.YEAR)-1;
    return tBrdgTechForecastPrjMapper.listTunnelMaintainSummary(prjYear,curentYear);
  }

  @Override
  public double getBrdgLenthByBrdgId(String brdgId) {
    return tBrdgTechForecastPrjMapper.getBrdgLenthByBrdgId(brdgId);
  }

  @Override
  public double getTunnelLenthByTunnelId(String structId) {
    return tBrdgTechForecastPrjMapper.getTunnelLenthByTunnelId(structId);
  }

  @Override
  public String getBrdgRouteCode(String structId) {
    return tBrdgTechForecastPrjMapper.getBrdgRouteCode(structId);
  }

  @Override
  public String getTunnelRouteCode(String structId) {
    return tBrdgTechForecastPrjMapper.getTunnelRouteCode(structId);
  }
  @Resource
  TStructMaintainSuggestService suggestService;
  @Override
  public void initHBridgeSuggest() {
    List<Map> map1 = tBrdgTechForecastPrjMapper.selectSummary();
    int p = 1;
    for(Map map:map1){
      p++;
      if(p>24){
        break;
      }
      String routeCode = map.get("ROUTE_CODE").toString();
      String year = map.get("YEAR").toString();
      int no = Integer.valueOf(map.get("NO").toString());

      List<String> brdgList = tBrdgTechForecastPrjMapper.getBrdgListByRouteCode(routeCode);
      String prjId = tBrdgTechForecastPrjMapper.getPrjByRouteCode(routeCode);
      String modelId = "a7b9b446-9c9a-4c89-b6c6-0e41eb1a2991";
      int f = Math.min(brdgList.size(), no);
      for(int j=0;j<f;j++){
        String s = brdgList.get(j);
        LambdaQueryWrapper<TStructMaintainSuggest> l = new LambdaQueryWrapper<>();
        l.eq(TStructMaintainSuggest::getStructId, s);
        l.eq(TStructMaintainSuggest::getPrjId,prjId);
        l.eq(TStructMaintainSuggest::getCat,"QL");
        suggestService.remove(l);

          TStructMaintainSuggest suggest = new TStructMaintainSuggest();
          suggest.setDataId(StringUtil.getUUID());
          suggest.setCat("QL");
          suggest.setPrjId(prjId);
          suggest.setStructId(s);
          //存储的为养护年份
          suggest.setMaintainType(year);
          suggest.setMaintainModelId(modelId);
          suggestService.save(suggest);

      }
    }
  }

  @Override
  public BridgeSuggestT findSuggestTByRouteCode(String brdgRouteCode, String year) {
    return tBrdgTechForecastPrjMapper.findSuggestTByRouteCode(brdgRouteCode,year);
  }

  @Override
  public TunnelSuggestT findSuggestTTunnelByRouteCode(String brdgRouteCode, String year) {
    return tBrdgTechForecastPrjMapper.findSuggestTTunnelByRouteCode(brdgRouteCode,year);
  }
}
