package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeDecisionDssStsMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeDecisionDssSts;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBridgeDecisionDssStsService;

import java.util.List;

@Service
public class TBridgeDecisionDssStsServiceImpl extends ServiceImpl<TBridgeDecisionDssStsMapper,TBridgeDecisionDssSts> implements TBridgeDecisionDssStsService{

    @Resource
    private TBridgeDecisionDssStsMapper tBridgeDecisionDssStsMapper;



  @Override
  public void stsDss(TBridgeDecisionDssSts sts) {
    if(sts.getDicId().intValue()==1){
      String sql = "select sum(d.dss_l) as su from BCTCMSDB.dss_info d where d.rel_task_code = '"+sts.getPrjId()+"' and d.struct_id='"+sts.getBridgeId()+"' and d.facility_cat='QL' and d.struct_part_id ='67' and exists (select 1 from memsdb.dss_type_old dt where dt.dss_type = d.dss_type and dt.dss_type_name like '裂缝') and d.dss_w<0.15";
      Object o = tBridgeDecisionDssStsMapper.selectBySql(sql);
      if(o!=null){
        sts.setAmount(Double.valueOf(o.toString()));
        tBridgeDecisionDssStsMapper.updateByBid(sts.getBridgeId(),sts.getDicId(),sts.getAmount());
      }
      return;
    }else if (sts.getDicId().intValue()==2){
      return;
    }
  }

  @Override
  public List<TBridgeDecisionDssSts> selectO() {
    return tBridgeDecisionDssStsMapper.selectO();
  }

  @Override
  public List<TBridgeDecisionDssSts> getBrdgDssStsByBrdgId(String brdgId) {
    return tBridgeDecisionDssStsMapper.getBrdgDssStsByBrdgId(brdgId);
  }

}
