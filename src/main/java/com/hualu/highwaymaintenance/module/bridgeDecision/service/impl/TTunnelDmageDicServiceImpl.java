package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDecisionDssSts;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TTunnelDecisionDssStsMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TTunnelDmageDicMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDmageDic;
import com.hualu.highwaymaintenance.module.maintainbase.service.TTunnelDmageDicService;
@Service
public class TTunnelDmageDicServiceImpl extends ServiceImpl<TTunnelDmageDicMapper, TTunnelDmageDic> implements TTunnelDmageDicService{



}
