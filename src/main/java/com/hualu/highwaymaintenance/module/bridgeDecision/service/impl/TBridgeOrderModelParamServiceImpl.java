package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.highwaymaintenance.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeOrderModelParamMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeOrderModelParam;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBridgeOrderModelParamService;

import java.math.BigDecimal;
import java.util.List;

@Service
public class TBridgeOrderModelParamServiceImpl extends ServiceImpl<TBridgeOrderModelParamMapper, TBridgeOrderModelParam> implements TBridgeOrderModelParamService {

  @Resource
  private TBridgeOrderModelParamMapper mapper;

  @Override
  public void delByModelId(String modelId) {
    LambdaQueryWrapper<TBridgeOrderModelParam> l = new LambdaQueryWrapper();
    l.eq(TBridgeOrderModelParam::getModelId, modelId);
    mapper.delete(l);
  }

  @Override
  public void saveModelParam(List<TBridgeOrderModelParam> list) {
    for (TBridgeOrderModelParam orderModelParam : list) {
      mapper.insert(orderModelParam);
    }
  }

  @Override
  public List<TBridgeOrderModelParam> getModelParamDetail(String modelId) {
    return mapper.getModelParamDetail(modelId);
  }

  @Override
  public void initParam(String modelId) {
    mapper.initParam(modelId);
  }

  @Override
  public void initSdParam(String modelId) {
    mapper.initSdParam(modelId);
  }


}



