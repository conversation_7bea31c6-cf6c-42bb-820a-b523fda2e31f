package com.hualu.highwaymaintenance.module.bridgeDecision.controller;

import cn.afterturn.easypoi.entity.ImageEntity;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.module.bridge.CulvertStsType;
import com.hualu.highwaymaintenance.module.bridge.entity.CulvertDssProjectSts;
import com.hualu.highwaymaintenance.module.bridge.entity.TBrdgBrdgrecog;
import com.hualu.highwaymaintenance.module.bridge.service.TBrdgBrdgrecogService;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.*;
import com.hualu.highwaymaintenance.module.bridgeDecision.vo.BrdgMoneyVo;
import com.hualu.highwaymaintenance.module.bridgeDecision.vo.ForecastResult;
import com.hualu.highwaymaintenance.module.elec.entity.ElecRepairPic;
import com.hualu.highwaymaintenance.module.elec.entity.ElecRepairRecord;
import com.hualu.highwaymaintenance.module.maintainbase.service.*;
import com.hualu.highwaymaintenance.module.scriptManagement.service.CulvertStsTypeService;
import com.hualu.highwaymaintenance.module.user.domain.FwRightOrg;
import com.hualu.highwaymaintenance.module.user.service.FwRightOrgService;
import com.hualu.highwaymaintenance.module.util.CommonUtil;
import com.hualu.highwaymaintenance.util.DateTimeUtil;
import com.hualu.highwaymaintenance.util.StringUtil;
import com.hualu.highwaymaintenance.util.WordUtil;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xwpf.usermodel.Document;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/bridgeDemand")
@ApiOperation(nickname = "桥隧需求分析", value = "桥隧需求分析")
public class BirdgeDemandController {
  @Resource
  private TBrdgTechForecastPrjService forecastPrjService;

  @PostMapping("listForecastModel")
  @ApiOperation(nickname = "获取马尔科夫模型", value = "获取马尔科夫模型,cat为QL，SD")
  public RestResult<List<TBrdgTechForecastPrj>> listForecastModel( String cat) {
    List<TBrdgTechForecastPrj> list = forecastPrjService.list();
    list.removeIf(next -> !cat.equals(next.getCat()));
    list.removeIf(next -> next.getType()==null||next.getType().intValue() == 2);
    return RestResult.success(list, "获取马尔科夫模型");
  }


  @PostMapping("listBridgeProject")
  @ApiOperation(nickname = "根据年份获取桥梁决策项目,TYPE值为QL，SD", value = "根据年份获取桥梁决策项目,TYPE值为QL，SD")
  public RestResult<List<TBrdgTechForecastPrj>> listBridgeProject(String type, int year) {
    List<TBrdgTechForecastPrj> list = forecastPrjService.list();
    list.removeIf(next -> !type.equals(next.getCat()));
    list.removeIf(next -> next.getType()==null||next.getType().intValue() == 1);
    list.removeIf(next -> next.getPrjYear()==null||next.getPrjYear().intValue() != year);
//    list = list.stream().sorted(Comparator.comparing(TBrdgTechForecastPrj::getPrjName)).collect(Collectors.toList());
    TBrdgTechForecastPrj x = null;
    for(TBrdgTechForecastPrj t:list){
      if(t.getPrjName().contains("汕头海湾")){
        x = t;
        break;
      }
    }
    list.removeIf(next -> next.getPrjName().contains("汕头海湾"));
    if(x!=null){
      list.add(x);
    }
    return RestResult.success(list, "根据年份获取桥梁决策项目");
  }

  @Resource
  private TBrdgTechForecastResultService resultService;

  @PostMapping("listBridgeForecastResult")
  @ApiOperation(nickname = "（桥梁）根据项目及模型获取项目预测结果", value = "（桥梁）根据项目及模型获取项目预测结果")
  public RestResult<List<ForecastResult>> listBridgeForecastResult(String prjId) {
    int qlSum = forecastPrjService.getBrdgSum(prjId);
    return getListRestResult(prjId, qlSum);
  }

  @PostMapping("listTunnelForecastResult")
  @ApiOperation(nickname = "（隧道）根据项目及模型获取项目预测结果", value = "（隧道）根据项目及模型获取项目预测结果")
  public RestResult<List<ForecastResult>> listTunnelForecastResult(String prjId) {
    int sdSum = forecastPrjService.getTunnelSum(prjId);
    return getListRestResult(prjId, sdSum);
  }

  @NotNull
  private RestResult<List<ForecastResult>> getListRestResult(String prjId, int sdSum) {
    LambdaQueryWrapper<TBrdgTechForecastResult> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(TBrdgTechForecastResult::getPrjId, prjId);
    queryWrapper.orderByAsc(TBrdgTechForecastResult::getYear);
    List<TBrdgTechForecastResult> list = resultService.list(queryWrapper);
    List<ForecastResult> x = new ArrayList<>();
    TBrdgTechForecastPrj prj = forecastPrjService.getById(prjId);
    int year = prj.getPrjYear().intValue();
    for (TBrdgTechForecastResult t : list) {
      ForecastResult f = new ForecastResult(t, sdSum, ++year);
      x.add(f);
    }
    return RestResult.success(x, "根据项目获取项目及模型获取项目预测结果");
  }

  @Resource
  private TBridgeOrderModelService modelService;
  @PostMapping("listBridgeMoneyList")
  @ApiOperation(nickname = "根据项目ID获取桥梁列表，如果传了养护方案id，就要展示金额，如果传了优先排序id，就要计算排序", value = "根据项目ID获取桥梁列表，如果传了养护方案id，就要展示金额，如果传了优先排序id，就要计算排序")
  public RestResult<List<BrdgMoneyVo>> listBridgeMoneyList(String prjId, @RequestParam(required = false) String  maintainModelId,@RequestParam(required = false)  String orderModelId
      ,@RequestParam(required = false)  Integer threeBridge,@RequestParam(required = false)  Integer threePart
  ) {
    List<BrdgMoneyVo> list = forecastPrjService.listBridgeMoneyList(prjId);
    if(threeBridge==1){
      list.removeIf(next -> !"3".equals(next.getGrade()));
    }
    if(threePart==1){
      list.removeIf(next -> next.getThreePart() == 0);
    }
    list = getBrdgMoneyVos(maintainModelId, orderModelId, list,"QL",threeBridge,threePart);
    for(BrdgMoneyVo v:list){
      if(v.getCustomized()==1){
        v.setIsCustomized("是");
      }else {
        v.setIsCustomized("否");
      }
    }
    return RestResult.success(list, "根据项目ID获取桥梁列表");
  }

  @PostMapping("listTunnelMoneyList")
  @ApiOperation(nickname = "根据项目ID获取隧道列表，如果传了养护方案id，就要展示金额，如果传了优先排序id，就要计算排序", value = "根据项目ID获取隧道列表，如果传了养护方案id，就要展示金额，如果传了优先排序id，就要计算排序")
  public RestResult<List<BrdgMoneyVo>> listTunnelMoneyList(String prjId, @RequestParam(required = false) String  maintainModelId,@RequestParam(required = false)  String orderModelId) {
    List<BrdgMoneyVo> list = forecastPrjService.listTunnelMoneyList(prjId);
    list = getBrdgMoneyVos(maintainModelId, orderModelId, list,"SD", null, null);
    return RestResult.success(list, "根据项目ID获取隧道列表");
  }

  @NotNull
  private List<BrdgMoneyVo> getBrdgMoneyVos(@RequestParam(required = false) String maintainModelId, @RequestParam(required = false) String orderModelId, List<BrdgMoneyVo> list, String cat, Integer threeBridge, Integer threePart) {
    int no = 1;

//    if(!StringUtil.isNullOrEmpty(maintainModelId)){
//      TBridgeOrderModel maintainModel = modelService.getById(maintainModelId);
//      for(BrdgMoneyVo b:list){
//        if("QL".equals(cat)){
//          ItemMoneyVo itemMoneyVo = forecastPrjService.countMoney(b.getBrdgId(), maintainModel.getModelId());
//          if(itemMoneyVo!=null&&itemMoneyVo.getMoneyPrevent()!=null&&itemMoneyVo.getMoneyRepair()!=null){
//            b.setMoney(String.valueOf(itemMoneyVo.getMoneyPrevent()+itemMoneyVo.getMoneyRepair()));
//          }
//        }
//        if("SD".equals(cat)){
//          ItemMoneyVo itemMoneyVo = forecastPrjService.countTunnelMoney(b.getBrdgId(), maintainModel.getModelId());
//          b.setMoney(String.valueOf(itemMoneyVo.getMoneyPrevent()+itemMoneyVo.getMoneyRepair()));
//        }
//      }
//    }
    if(!StringUtil.isNullOrEmpty(orderModelId)){
      for(BrdgMoneyVo b:list){
        if("QL".equals(cat)){
          b.setWeight(forecastPrjService.countWeight(b.getBrdgId(),orderModelId));
        }
        if("SD".equals(cat)){
          b.setWeight(forecastPrjService.countSdWeight(b.getBrdgId(),orderModelId));
        }
      }
      list = list.stream().sorted(Comparator.comparingDouble(BrdgMoneyVo::getWeight).reversed()).collect(Collectors.toList());
    }
    for(BrdgMoneyVo b:list) {
      b.setNo(no++);
    }
    return list;
  }


  @Resource
  private TStructMaintainSuggestService suggestService;
  @PostMapping("bringIntoSuggest")
  @ApiOperation(nickname = "纳入建议库,cat选项（QL,SD）", value = "纳入建议库,cat选项（QL,SD）")
  public RestResult<String> bringIntoSuggest(String prjId,String structId,String cat,String modelId,String maintainType) {
    String[] split = structId.split(",");
    LambdaQueryWrapper<TStructMaintainSuggest> l = new LambdaQueryWrapper<>();
//    l.in(TStructMaintainSuggest::getStructId,split);
    l.eq(TStructMaintainSuggest::getPrjId,prjId);
    l.eq(TStructMaintainSuggest::getCat,cat);
    List<TStructMaintainSuggest> list = suggestService.list(l);
    for(TStructMaintainSuggest suggest:list){
      suggestService.updateSuggestRepository(suggest,2);
    }
    suggestService.remove(l);

    for(String s:split) {
      TStructMaintainSuggest suggest = new TStructMaintainSuggest();
      suggest.setDataId(StringUtil.getUUID());
      suggest.setCat(cat);
      suggest.setPrjId(prjId);
      suggest.setStructId(s);
      //存储的为养护年份
      suggest.setMaintainType(maintainType);
      suggest.setMaintainModelId(modelId);
      suggestService.save(suggest);
      suggestService.updateSuggestRepository(suggest,1);
    }
    return RestResult.success("纳入建议库");
  }
  @Resource
  TBrdgBrdgrecogService brdgrecogService;

  /**
   *
   * @param structId
   * @param money
   * @param customized 1为添加自定义，其他为恢复默认初始分配值
   * @param prjId
   * @return
   */
  @PostMapping("updateBridgeMoney")
  @ApiOperation(nickname = "更新桥梁资金自定义", value = "更新桥梁资金自定义")
  public RestResult<String> updateBridgeMoney(String structId,Double money,Integer customized,String prjId) {
    if(customized==null){
      customized = 1;
    }
    if(structId.isEmpty()){
      return RestResult.success("请选择一座桥梁");
    }

    List<BrdgMoneyVo> list = forecastPrjService.listBridgeMoneyList(prjId);
    double sumLen = 0.0001;
    double sumMoney = 0.0001;
    for(BrdgMoneyVo vo:list){
      if(vo.getCustomized()!=1){
        sumLen+=vo.getLen();
        sumMoney+=Double.parseDouble(vo.getMoney());
      }else {
        if ( customized == 1) {
            if(!(vo.getBrdgId().equals(structId))){
              sumLen+=vo.getLen();
              sumMoney+=Double.parseDouble(vo.getMoney());
          }
        }else  {
          sumLen+=vo.getLen();
          sumMoney+=Double.parseDouble(vo.getMoney());
        }
      }

    }
    if(customized==1) {
      brdgrecogService.updateBridgeMoney(structId, money, customized);
      sumMoney -= money;
    }


    Iterator<BrdgMoneyVo> iterator = list.iterator();
    while (iterator.hasNext()){
      BrdgMoneyVo next = iterator.next();

      if(customized==1){
        if((next.getBrdgId().equals(structId))||next.getCustomized()==1){
          iterator.remove();
        }
      }else{
        if(!(next.getBrdgId().equals(structId))&&next.getCustomized()==1){
          iterator.remove();
        }
      }
    }

    for(BrdgMoneyVo vo:list){
      brdgrecogService.updateBridgeMoney(vo.getBrdgId(),vo.getLen()*sumMoney/sumLen, 0);
    }

    return RestResult.success("更新桥梁资金自定义成功");
  }

  @PostMapping("reCountMoney")
  @ApiOperation(nickname = "分配资金", value = "分配资金")
  public RestResult<String> reCountMoney(String prjId,String sumMoney) {
  Double sum = Double.parseDouble(sumMoney);
    List<BrdgMoneyVo> list = forecastPrjService.listBridgeMoneyList(prjId);
    double sumLen = 0.0001;
    for(BrdgMoneyVo vo:list){
      sumLen+=vo.getLen();
    }

    for(BrdgMoneyVo vo:list){
      brdgrecogService.updateBridgeMoney(vo.getBrdgId(),vo.getLen()*sum*10000/sumLen, 0);
    }


    return RestResult.success("分配资金成功");
  }


  @PostMapping("delFromSuggest")
  @ApiOperation(nickname = "移出建议库,cat选项（QL,SD）", value = "移出建议库,cat选项（QL,SD）")
  public RestResult<String> delFromSuggest(String prjId,String structId,String cat) {
    String[] split = structId.split(",");
    LambdaQueryWrapper<TStructMaintainSuggest> l = new LambdaQueryWrapper<>();
//    l.in(TStructMaintainSuggest::getStructId,split);
    l.eq(TStructMaintainSuggest::getPrjId,prjId);
    l.eq(TStructMaintainSuggest::getCat,cat);
    List<TStructMaintainSuggest> list = suggestService.list(l);
    for(TStructMaintainSuggest suggest:list){
      suggestService.updateSuggestRepository(suggest,2);
    }
    suggestService.remove(l);
    return RestResult.success("移出建议库");
  }


  @Resource
  private TunnelSuggestTService tunnelSuggestTService;
  @Resource
  private BridgeSuggestTService bridgeSuggestTService;
  @PostMapping("listBridgeMaintainSummary")
  @ApiOperation(nickname = "查看桥梁养护建议汇总表", value = "查看桥梁养护建议汇总表")
  public RestResult<List<BridgeSuggestT>> listBridgeMaintainSummary(int prjYear) {
    LambdaQueryWrapper<BridgeSuggestT> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.eq(BridgeSuggestT::getTarget,2024);
    lambdaQueryWrapper.eq(BridgeSuggestT::getYear,prjYear);
    List<BridgeSuggestT> list = bridgeSuggestTService.list(lambdaQueryWrapper);
    for(BridgeSuggestT t:list){
     t.setRepairFee(t.getRepairFee().setScale(3, RoundingMode.HALF_UP));
     t.setPreventFee(t.getPreventFee().setScale(3, RoundingMode.HALF_UP));
     t.setTotalFee(t.getTotalFee().setScale(3, RoundingMode.HALF_UP));
    }
    return RestResult.success(list, "查看桥梁养护建议汇总表");
  }
  @PostMapping("listTunnelMaintainSummary")
  @ApiOperation(nickname = "查看隧道养护建议汇总表", value = "查看隧道养护建议汇总表")
  public RestResult<List<TunnelSuggestT>> listTunnelMaintainSummary(int prjYear) {
    LambdaQueryWrapper<TunnelSuggestT> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.eq(TunnelSuggestT::getTarget,2024);
    lambdaQueryWrapper.eq(TunnelSuggestT::getYear,prjYear);
    List<TunnelSuggestT> list = tunnelSuggestTService.list(lambdaQueryWrapper);
    for(TunnelSuggestT t:list){
      t.setCivilPreventFee(t.getCivilPreventFee().setScale(3, RoundingMode.HALF_UP));
      t.setCivilRepairFee(t.getCivilRepairFee().setScale(3, RoundingMode.HALF_UP));
      t.setTotalFee(t.getTotalFee().setScale(3, RoundingMode.HALF_UP));
    }
    return RestResult.success(tunnelSuggestTService.list(lambdaQueryWrapper), "查看隧道养护建议汇总表");
  }
  @PostMapping("initHBridgeSuggest")
  @ApiOperation(nickname = "桥梁建议库初始化", value = "桥梁建议库初始化")
  public void initHBridgeSuggest() {
    forecastPrjService.initHBridgeSuggest();
  }

  @Resource
  FwRightOrgService fwRightOrgService;
  @PostMapping("initCulvertDssSts")
  @ApiOperation(nickname = "生成涵洞工程量模板", value = "生成涵洞工程量模板")
  public void initCulvertDssSts() throws IllegalAccessException {
    List<FwRightOrg> fwRightOrgs = fwRightOrgService.queryAllChilds("N000001");
    for(FwRightOrg fwRightOrg:fwRightOrgs){
      CulvertDssProjectSts culvertDssProjectSts = summaryClvrtDssCatByOprtCode(fwRightOrg.getOrgCode());
      String template = "templates/elec/culvertDssSts.docx";
      Class<CulvertDssProjectSts> aClass = CulvertDssProjectSts.class;
      Field[] declaredFields = aClass.getDeclaredFields();
      Map<String, Object> param = new HashMap<>();
      for (Field field:declaredFields){
        field.setAccessible(true);
        Object o = field.get(culvertDssProjectSts);
        if(o==null||o.toString().isEmpty()){
          param.put(field.getName(), "0.0");
        }else {
          param.put(field.getName(), o);
        }

      }
      WordUtil.exportWordLocal(template, fwRightOrg.getOrgCode()+".docx", param);
    }
  }


  private CulvertDssProjectSts summaryClvrtDssCatByOprtCode(String oprtOrgCode)  {
    List<com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo> totalDssInfos = brdgrecogService.findCulvertDssByOprtOrgCode(oprtOrgCode);
    totalDssInfos.forEach(dssInfo -> {
      if ("1".equals(dssInfo.getStructPartId()) || "2".equals(dssInfo.getStructPartId())) {
        dssInfo.setStructPartId("1,2");
      }
    });
    int pictureTypePng = Document.PICTURE_TYPE_PNG;
    return makeCulvertDssProjectSts(totalDssInfos);
  }
  @Resource
  CulvertStsTypeService culvertStsTypeService;
  private CulvertDssProjectSts makeCulvertDssProjectSts(List<com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo> totalDssInfos) {
    List<CulvertStsType> list = culvertStsTypeService.list();
    //部件名+病害类型 -> 需统计病害类型
    Map<Object, List<CulvertStsType>> stsMap = list.stream()
        .collect(Collectors.groupingBy(v -> v.getPartCode() + "@@" + v.getDssType()));

    CulvertDssProjectSts sts = new CulvertDssProjectSts();
    Field[] declaredFields = sts.getClass().getDeclaredFields();
    for (Field declaredField : declaredFields) {
      declaredField.setAccessible(true);
      try {
        declaredField.set(sts, "");
      } catch (IllegalAccessException e) {
      }
    }

    for (com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo totalDssInfo : totalDssInfos) {
      String structPartId = totalDssInfo.getStructPartId();
      String dssType = totalDssInfo.getDssType();

      //进水口，出水口的部件是1，2这种取出1就行
      if (structPartId != null && structPartId.contains(",")) {
        structPartId = structPartId.substring(0, structPartId.indexOf(","));
      }
      String key = structPartId + "@@" + dssType;

      List<CulvertStsType> stsList = stsMap.get(key);
      if (!CollectionUtils.isEmpty(stsList)) {
        CulvertStsType culvertStsType = stsList.get(0);
        totalDssInfo.setStsColumn(culvertStsType.getStsColumn());
        totalDssInfo.setStsDssTypeName(culvertStsType.getStsDssTypeName());
        totalDssInfo.setObjectField(culvertStsType.getObjField());
      }

    }

    Map<String, List<com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo>> mapList =
        totalDssInfos.stream()
            .filter(v -> StringUtils.hasText(v.getObjectField()))
            .collect(Collectors.groupingBy(com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo::getObjectField));
    mapList.forEach((objectField, dssInfos) -> {
      if (!CollectionUtils.isEmpty(dssInfos)) {
        String stsColumn = dssInfos.get(0).getStsColumn();

        double sqsl = 0;
        double sqslLfDy015 = 0;
        double sqslLfXy015 = 0;

        double sqycz = 0;
        double sqyczLfDy015 = 0;
        double sqyczLfXy015 = 0;

        double sqwcz = 0;
        double sqwczLfDy015 = 0;
        double sqwczLfXy015 = 0;

        //病害数量统计-本期-小计”将“上期-未处治”和“本期-新增”的工程量相加
        double bqxz = 0;
        double bqxzLfDy015 = 0;
        double bqxzLfXy015 = 0;

        for (com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssInfo s : dssInfos) {//0未修复, 1正在修复, 3历史修复, 4新增修复
          String repairStatus = s.getRepairStatus();
          //0新病害， 1旧病害
          String dssQuality = s.getDssQuality();

          boolean sqslHandle = "1".equals(dssQuality);

          //上期-已处治  旧病害-新增修复
          boolean sqHandled = "1".equals(dssQuality) && "4".equals(repairStatus);

          //上期-未处治  旧病害-待修复
          boolean sqWaitHandle = "1".equals(dssQuality) && "0".equals(repairStatus);

          //本期检查录入的病害中统计“新病害”
          boolean bqAddDisease = "0".equals(dssQuality);

          double dssL = CommonUtil.parseDouble(s.getDssL());
          double dssW = CommonUtil.parseDouble(s.getDssW());
          double dssA = CommonUtil.parseDouble(s.getDssA());
          double dssD = CommonUtil.parseDouble(s.getDssD());
          if (dssA == 0 && dssL > 0 && dssW > 0) {
            dssA = dssL * dssW;
          }
          double dssV = CommonUtil.parseDouble(s.getDssV());
          if (dssV == 0 && dssL > 0 && dssW > 0 && dssD > 0) {
            dssV = dssL * dssW * dssD;
          }
          double dssN = CommonUtil.parseDouble(s.getDssN());

          double stsNum = 0;
          double stsDy015Lf = 0;
          double stsXy015Lf = 0;
          switch (stsColumn) {
            case "dssL": {
              stsNum = dssL;
              //裂缝
              if (objectField.contains("_lf")) {
                if (dssW >= 0.15) {
                  stsDy015Lf += dssL;
                } else {
                  stsXy015Lf += dssL;
                }
              }
              break;
            }

            case "dssA": {
              stsNum = dssA;
              break;
            }

            case "dssV": {
              stsNum = dssV;
              break;
            }

            case "dssN": {
              if (dssN == 0) {
                dssN = 1;
              }
              stsNum = dssN;
              break;
            }
          }

          if (sqslHandle) {
            sqsl += stsNum;
            sqslLfDy015 += stsDy015Lf;
            sqslLfXy015 += stsXy015Lf;
          }

          if (sqHandled) {
            sqycz += stsNum;
            sqyczLfDy015 += stsDy015Lf;
            sqyczLfXy015 += stsXy015Lf;
          }

          if (sqWaitHandle) {
            sqwcz += stsNum;
            sqwczLfDy015 += stsDy015Lf;
            sqwczLfXy015 += stsXy015Lf;
          }

          if (bqAddDisease) {
            bqxz += stsNum;
            bqxzLfDy015 += stsDy015Lf;
            bqxzLfXy015 += stsXy015Lf;
          }
        }

        double bqxj = sqycz + bqxz;
        double bqxjLfDy015 = sqyczLfDy015 + bqxzLfDy015;
        double bqxjLfXy015 = sqyczLfXy015 + bqxzLfXy015;

        double[] dataList = {
            CommonUtil.parseDouble(String.format("%.3f", sqsl)),
            CommonUtil.parseDouble(String.format("%.3f", sqycz)),
            CommonUtil.parseDouble(String.format("%.3f", sqwcz)),
            CommonUtil.parseDouble(String.format("%.3f", bqxz)),
            CommonUtil.parseDouble(String.format("%.3f", bqxj))
        };
        for (int i = 1; i <= 5; i++) {
          try {
            Field declaredField = sts.getClass().getDeclaredField(objectField + i);
            declaredField.setAccessible(true);
            declaredField.set(sts, String.valueOf(dataList[i - 1]));
          } catch (Exception e) {
          }
        }

        if (objectField.contains("_lf")) {
          double[] dy015List = {
              CommonUtil.parseDouble(String.format("%.3f", sqslLfDy015)),
              CommonUtil.parseDouble(String.format("%.3f", sqyczLfDy015)),
              CommonUtil.parseDouble(String.format("%.3f", sqwczLfDy015)),
              CommonUtil.parseDouble(String.format("%.3f", bqxzLfDy015)),
              CommonUtil.parseDouble(String.format("%.3f", bqxjLfDy015))
          };
          for (int i = 1; i <= 5; i++) {
            try {
              Field declaredField = sts.getClass().getDeclaredField(objectField + "_ge015" + i);
              declaredField.setAccessible(true);
              declaredField.set(sts, String.valueOf(dy015List[i - 1]));
            } catch (Exception e) {
            }
          }

          double[] xy015List = {
              CommonUtil.parseDouble(String.format("%.3f", sqslLfXy015)),
              CommonUtil.parseDouble(String.format("%.3f", sqyczLfXy015)),
              CommonUtil.parseDouble(String.format("%.3f", sqwczLfXy015)),
              CommonUtil.parseDouble(String.format("%.3f", bqxzLfXy015)),
              CommonUtil.parseDouble(String.format("%.3f", bqxjLfXy015))
          };
          for (int i = 1; i <= 5; i++) {
            try {
              Field declaredField = sts.getClass().getDeclaredField(objectField + "_lt015" + i);
              declaredField.setAccessible(true);
              declaredField.set(sts, String.valueOf(xy015List[i - 1]));
            } catch (Exception e) {
            }
          }
        }
      }
    });
    return sts;
  }


  @GetMapping("/exportBridgeProject")
  @ApiOperation(nickname = "exportBridgeProject", value = "导出桥梁资金明细表")
  public void exportBridgeProject(HttpServletRequest request,  HttpServletResponse response) throws Exception {
     DecimalFormat DF = new DecimalFormat("######0.00");
    String prjId = request.getParameter("prjId");
    String fileName = "桥梁养护资金.docx";
    String template = "templates/elec/brdgList.docx";
    Map<String, Object> param = new HashMap<>();
    String reSql = "select ROWNUM as ORDERS, a.* from (select decode(br.MONEY_2025, null, 0, br.MONEY_2025) AMOUNT, decode(br.MONEY_2025, null, '预防养护', 0, '预防养护', '修复养护') TYPENAME, br.BRDG_NAME as BRIDGE_NAME, to_char(t.BRDG_LEN, 'FM9999.000') PROJECT_QUANTITY, (select WM_CONCAT(y.BRDG_TYPE_NAME) from BCTCMSDB.T_BRDG_TOPTYPE x inner join BCTCMSDB.T_BRDG_BRDGTYPE y on x.BRDG_TYPE = y.BRDGTYPE_ID and x.VALID_FLAG = 1 and y.VALID_FLAG = 1 where x.BRDGRECOG_ID = br.BRDGRECOG_ID) STRUCT_TYPE, decode(m.MAX_SPAN_KIND, 1, '特大桥', 2, '大桥', 3, '中桥', 4, '小桥') BRIDGE_TYPE, decode(f.GRADE, 1, '一类', 2, '二类', 3, '三类', 4, '四类') TECHNICAL_TYPE from BCTCMSDB.T_BRDG_BRDGRECOG br inner join BCTCMSDB.T_BRDG_TECHINDEX t on br.BRDGRECOG_ID = t.BRDGRECOG_ID inner join BCTCMSDB.EVAL_BRIDGE_NEW_F f on br.BRDGRECOG_ID = f.BRDGRECOG_ID inner join BCTCMSDB.T_BRDG_RECOGMANAGE m on m.MAIN_BRDGRECOG_ID = br.BRDGRECOG_ID and m.MAIN_BRDGRECOG_ID = m.BRDGRECOG_ID and m.VALID_FLAG = 1 where br.OPRT_ORG_CODE = (select x.ORG_ID from PMSDB.T_BRDG_TECH_FORECAST_PRJ x where x.PRJ_ID='"+prjId+"') and br.VALID_FLAG = 1 order by br.ROAD_NUM, br.LOGIC_CNTR_STAKE_NUM) a";
    List<Map> rList =   brdgrecogService.findMapBySql(reSql);
    double price = 0;
    for(Map c:rList){
      price+=Double.valueOf(c.get("AMOUNT").toString());
    }
    param.put("reList", rList);
    param.put("sumPrice",DF.format( price/10000));
    WordUtil.exportWord(template, fileName, param, response);
  }
}