package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBrdgTechForecastResult;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBrdgTechForecastResultMapper;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBrdgTechForecastResultService;
@Service
public class TBrdgTechForecastResultServiceImpl extends ServiceImpl<TBrdgTechForecastResultMapper,TBrdgTechForecastResult> implements TBrdgTechForecastResultService{

    @Resource
    private TBrdgTechForecastResultMapper tBrdgTechForecastResultMapper;


}
