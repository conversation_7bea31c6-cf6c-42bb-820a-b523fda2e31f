package com.hualu.highwaymaintenance.module.bridgeDecision.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgegStsType;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.DssInfo;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeDecisionDssSts;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TTunnelDecisionDssSts;
import com.hualu.highwaymaintenance.module.bridgeDecision.vo.DssProjectSts;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBridgeDecisionDssStsService;
import com.hualu.highwaymaintenance.module.maintainbase.service.TTunnelDecisionDssStsService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/sts")
public class BridgeDssStsController {
  @Resource
  private TBridgeDecisionDssStsService dssStsService;
  @Resource
  private TTunnelDecisionDssStsService tTunnelDecisionDssStsService;
  @PostMapping("/stsDss")
  @ApiOperation(nickname = "", value = "")
  public RestResult<String> stsDss(  ) {

    List<TBridgeDecisionDssSts> list = dssStsService.selectO();
    for(TBridgeDecisionDssSts sts:list){
      dssStsService.stsDss(sts);
    }

    return RestResult.success("ssss");
  }
  @PostMapping("/getBrdgDssStsByBrdgId")
  @ApiOperation(nickname = "查看单桥病害工程量", value = "查看单桥病害工程量")
  public RestResult<List<TBridgeDecisionDssSts>> getBrdgDssStsByBrdgId(String brdgId) {

    List<TBridgeDecisionDssSts> list = dssStsService.getBrdgDssStsByBrdgId(brdgId);

    return RestResult.success(list, "查看单桥病害工程量");
  }


  @PostMapping("/getTunnelDssStsByTunnelId")
  @ApiOperation(nickname = "查看隧道病害工程量", value = "查看隧道病害工程量")
  public RestResult<List<TTunnelDecisionDssSts>> getTunnelDssStsByTunnelId(String tunnelId) {

    List<TTunnelDecisionDssSts> list = tTunnelDecisionDssStsService.getTunnelDssStsByTunnelId(tunnelId);

    return RestResult.success(list, "查看隧道病害工程量");
  }



//  /**
//   * 桥梁工程量统计
//   * @param totalDssInfos
//   * @return
//   */
//  private DssProjectSts makeDssProjectSts(List<DssInfo> totalDssInfos) {
//    List<BridgegStsType> list = bridgegStsTypeService.list();
//
//    //部件名+病害类型 -> 需统计病害类型
//    Map<String, List<BridgegStsType>> stsMap = list.stream()
//        .collect(Collectors.groupingBy(v -> v.getPartCode() + "@@" + v.getDssType()));
//    DssProjectSts sts = new DssProjectSts();
//    Field[] declaredFields = sts.getClass().getDeclaredFields();
//    for (Field declaredField : declaredFields) {
//      declaredField.setAccessible(true);
//      try {
//        declaredField.set(sts, "");
//      } catch (IllegalAccessException e) {
//      }
//    }
//
//    for (DssInfo totalDssInfo : totalDssInfos) {
//      String structPartId = totalDssInfo.getStructPartId();
//      String dssType = totalDssInfo.getDssType();
//      String key = structPartId + "@@" + dssType;
//      //板式橡胶支座、盆式橡胶支座、钢板支座
//      String supportForm = totalDssInfo.getSupportForm() != null ? totalDssInfo.getSupportForm() : "";
//      List<BridgegStsType> stsList = stsMap.get(key);
//      if (!CollectionUtils.isEmpty(stsList)) {
//        if (!"11".equals(structPartId)) {
//          BridgegStsType bridgegStsType = stsList.get(0);
//          totalDssInfo.setStsColumn(bridgegStsType.getStsColumn());
//          totalDssInfo.setStsDssTypeName(bridgegStsType.getStsDssTypeName());
//          totalDssInfo.setObjectField(bridgegStsType.getObjField());
//        } else {
//          String zzType = "";
//          switch (supportForm) {
//            case "板式橡胶支座": {
//              zzType = "bszz";
//              break;
//            }
//
//            case "盆式橡胶支座":
//              zzType = "pszz";
//              break;
//
//            case "钢板支座":
//              zzType = "gbzz";
//              break;
//          }
//
//          String finalZzType = zzType;
//          BridgegStsType bszz = stsList.stream()
//              .filter(c -> c.getObjField().contains(finalZzType))
//              .collect(Collectors.toList())
//              .stream().findFirst().orElse(null);
//
//          if (bszz != null) {
//            totalDssInfo.setStsColumn(bszz.getStsColumn());
//            totalDssInfo.setStsDssTypeName(bszz.getStsDssTypeName());
//            totalDssInfo.setObjectField(bszz.getObjField());
//          }
//        }
//      }
//    }
//
//    Map<String, List<DssInfo>> mapList =
//        totalDssInfos.stream()
//            .filter(v -> StringUtils.hasText(v.getObjectField()))
//            .collect(Collectors.groupingBy(DssInfo::getObjectField));
//    mapList.forEach((objectField, dssInfos) -> {
//      if (!CollectionUtils.isEmpty(dssInfos)) {
//        String stsColumn = dssInfos.get(0).getStsColumn();
//
//        double sqsl = 0;
//        double sqslLfDy015 = 0;
//        double sqslLfXy015 = 0;
//
//        double sqycz = 0;
//        double sqyczLfDy015 = 0;
//        double sqyczLfXy015 = 0;
//
//        double sqwcz = 0;
//        double sqwczLfDy015 = 0;
//        double sqwczLfXy015 = 0;
//
//        //病害数量统计-本期-小计”将“上期-未处治”和“本期-新增”的工程量相加
//        double bqxz = 0;
//        double bqxzLfDy015 = 0;
//        double bqxzLfXy015 = 0;
//
//        for (DssInfo s : dssInfos) {//0未修复, 1正在修复, 3历史修复, 4新增修复
//          String repairStatus = s.getRepairStatus();
//          //0新病害， 1旧病害
//          String dssQuality = s.getDssQuality();
//
//          boolean sqslHandle = "1".equals(dssQuality);
//
//          //上期-已处治  旧病害-新增修复
//          boolean sqHandled = "1".equals(dssQuality) && "4".equals(repairStatus);
//
//          //上期-未处治  旧病害-待修复
//          boolean sqWaitHandle = "1".equals(dssQuality) && "0".equals(repairStatus);
//
//          //本期检查录入的病害中统计“新病害”
//          boolean bqAddDisease = "0".equals(dssQuality);
//
//          double dssL = CommonUtil.parseDouble(s.getDssL());
//          double dssW = CommonUtil.parseDouble(s.getDssW());
//          double dssA = CommonUtil.parseDouble(s.getDssA());
//          double dssD = CommonUtil.parseDouble(s.getDssD());
//          if (dssA == 0 && dssL > 0 && dssW > 0) {
//            dssA = dssL * dssW;
//          }
//          double dssV = CommonUtil.parseDouble(s.getDssV());
//          if (dssV == 0 && dssL > 0 && dssW > 0 && dssD > 0) {
//            dssV = dssL * dssW * dssD;
//          }
//          double dssN = CommonUtil.parseDouble(s.getDssN());
//
//          double stsNum = 0;
//          double stsDy015Lf = 0;
//          double stsXy015Lf = 0;
//          switch (stsColumn) {
//            case "dssL": {
//              stsNum = dssL;
//              //裂缝
//              if (objectField.contains("_lf")) {
//                if (dssW >= 0.15) {
//                  stsDy015Lf += dssL;
//                } else {
//                  stsXy015Lf += dssL;
//                }
//              }
//              break;
//            }
//
//            case "dssA": {
//              stsNum = dssA;
//              break;
//            }
//
//            case "dssV": {
//              stsNum = dssV;
//              break;
//            }
//
//            case "dssN": {
//              if (dssN == 0) {
//                dssN = 1;
//              }
//              stsNum = dssN;
//              break;
//            }
//          }
//
//          if (sqslHandle) {
//            sqsl += stsNum;
//            sqslLfDy015 += stsDy015Lf;
//            sqslLfXy015 += stsXy015Lf;
//          }
//
//          if (sqHandled) {
//            sqycz += stsNum;
//            sqyczLfDy015 += stsDy015Lf;
//            sqyczLfXy015 += stsXy015Lf;
//          }
//
//          if (sqWaitHandle) {
//            sqwcz += stsNum;
//            sqwczLfDy015 += stsDy015Lf;
//            sqwczLfXy015 += stsXy015Lf;
//          }
//
//          if (bqAddDisease) {
//            bqxz += stsNum;
//            bqxzLfDy015 += stsDy015Lf;
//            bqxzLfXy015 += stsXy015Lf;
//          }
//        }
//
//        double bqxj = sqwcz + bqxz;
//        double bqxjLfDy015 = sqwczLfDy015 + bqxzLfDy015;
//        double bqxjLfXy015 = sqwczLfXy015 + bqxzLfXy015;
//
//        double[] dataList = {
//            CommonUtil.parseDouble(String.format("%.3f", sqsl)),
//            CommonUtil.parseDouble(String.format("%.3f", sqycz)),
//            CommonUtil.parseDouble(String.format("%.3f", sqwcz)),
//            CommonUtil.parseDouble(String.format("%.3f", bqxz)),
//            CommonUtil.parseDouble(String.format("%.3f", bqxj))
//        };
//        for (int i = 1; i <= 5; i++) {
//          try {
//            Field declaredField = sts.getClass().getDeclaredField(objectField + i);
//            declaredField.setAccessible(true);
//            declaredField.set(sts, String.valueOf(dataList[i - 1]));
//          } catch (Exception e) {
//          }
//        }
//
//        if (objectField.contains("_lf")) {
//          double[] dy015List = {
//              CommonUtil.parseDouble(String.format("%.3f", sqslLfDy015)),
//              CommonUtil.parseDouble(String.format("%.3f", sqyczLfDy015)),
//              CommonUtil.parseDouble(String.format("%.3f", sqwczLfDy015)),
//              CommonUtil.parseDouble(String.format("%.3f", bqxzLfDy015)),
//              CommonUtil.parseDouble(String.format("%.3f", bqxjLfDy015))
//          };
//          for (int i = 1; i <= 5; i++) {
//            try {
//              Field declaredField = sts.getClass().getDeclaredField(objectField + "_dy015" + i);
//              declaredField.setAccessible(true);
//              declaredField.set(sts, String.valueOf(dy015List[i - 1]));
//            } catch (Exception e) {
//            }
//          }
//
//          double[] xy015List = {
//              CommonUtil.parseDouble(String.format("%.3f", sqslLfXy015)),
//              CommonUtil.parseDouble(String.format("%.3f", sqyczLfXy015)),
//              CommonUtil.parseDouble(String.format("%.3f", sqwczLfXy015)),
//              CommonUtil.parseDouble(String.format("%.3f", bqxzLfXy015)),
//              CommonUtil.parseDouble(String.format("%.3f", bqxjLfXy015))
//          };
//          for (int i = 1; i <= 5; i++) {
//            try {
//              Field declaredField = sts.getClass().getDeclaredField(objectField + "_xy015" + i);
//              declaredField.setAccessible(true);
//              declaredField.set(sts, String.valueOf(xy015List[i - 1]));
//            } catch (Exception e) {
//            }
//          }
//        }
//      }
//    });
//    return sts;
//  }

}
