package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.BridgegStsTypeMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgegStsType;
import com.hualu.highwaymaintenance.module.maintainbase.service.BridgegStsTypeService;
@Service
public class BridgegStsTypeServiceImpl implements BridgegStsTypeService{

    @Resource
    private BridgegStsTypeMapper bridgegStsTypeMapper;

    @Override
    public int insert(BridgegStsType record) {
        return bridgegStsTypeMapper.insert(record);
    }

    @Override
    public int insertSelective(BridgegStsType record) {
        return bridgegStsTypeMapper.insertSelective(record);
    }

}
