package com.hualu.highwaymaintenance.module.bridgeDecision.controller;

import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.*;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeMaintainModelParamMapper;
import com.hualu.highwaymaintenance.module.maintainbase.service.*;
import com.hualu.highwaymaintenance.util.StringUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/bridgeDecision")
public class BridgeDecisionController {
  @Resource
  private TBridgeMaintainStandardService standardService;

  @PostMapping("listBridgeStandard")
  @ApiOperation(nickname = "列出桥隧养护标准", value = "列出桥隧养护标准（1、2分别为桥梁标准及措施，3、4分别为隧道标准及措施）")
  public RestResult<List<TBridgeMaintainStandard>> listBridgeStandard(int type) {
    List<TBridgeMaintainStandard> list = standardService.list();
    list.removeIf(next -> next.getType().intValue() != type);
    return RestResult.success(list, "列出桥隧养护标准");
  }


  @Resource
  private TBridgeOrderModelService modelService;
  @Resource
  private TBridgeMaintainModelParamService maintainModelParamService;
  @Resource
  private TBridgeOrderModelParamService orderModelParamService;
  @PostMapping("/addModel")
  @ApiOperation(nickname = "新增或者更新模型", value = "新增或者更新模型（1为养护方案模型，2为优先排序模型,3为隧道养护方案模型，4为隧道优先排序模型，养护方案需填单价，优先排序不用）")
  public RestResult<String> addModel(
      String modelName,
      @RequestParam(required = false) String uid,
      @RequestParam(required = false) Double repairPrice,
      int modelType
  ) {
    boolean successful;
    TBridgeOrderModel model = null;
    if (uid != null) {
      model = modelService.getById(uid);
    }
    if (model == null) {
      model = new TBridgeOrderModel();
    }
    model.setModelName(modelName);
    model.setModelType(new BigDecimal(modelType));
    model.setPreventPrice(repairPrice);
    model.setRcPrice(repairPrice);
    try {
      if (uid == null){
        model.setModelId(StringUtil.getUUID());
      }
      modelService.saveOrUpdate(model);
      if (uid == null ) {
        if(modelType==1 && repairPrice==null) {
          //初始化养护方案模型维修细项参数
          maintainModelParamService.initParam(model.getModelId());
        }
        if(modelType==3 && repairPrice==null) {
          maintainModelParamService.initSdParam(model.getModelId());
        }
        if(modelType == 2){
          orderModelParamService.initParam(model.getModelId());
        }
        if(modelType == 4){
          orderModelParamService.initSdParam(model.getModelId());
        }
      }
      successful = true;
    } catch (Exception e) {
      successful = false;
      e.printStackTrace();
    }
    return successful ? RestResult.success(model.getModelId(), "新增或者更新优先排序模型完毕")
        : RestResult.error("新增或者更新优先排序模型失败");
  }

  @PostMapping("listBridgeOrder")
  @ApiOperation(nickname = "列出优先排序模型", value = "列出模型（1为桥梁养护方案模型，2为桥梁优先排序模型，3为隧道养护方案模型，4为隧道优先排序模型）")
  public RestResult<List<TBridgeOrderModel>> listBridgeOrder(int modelType) {
    List<TBridgeOrderModel> list = modelService.list();
    list.removeIf(next -> next.getModelType().intValue() != modelType);
    return RestResult.success(list, "列出优先排序模型完毕");
  }

  @Resource
  private TBridgeOrderDicService dicService;

  @PostMapping("listBridgeOrderDic")
  @ApiOperation(nickname = "列出优先排序字典", value = "列出优先排序字典,2为桥梁，3为隧道")
  public RestResult<List<TBridgeOrderDic>> listBridgeOrderDic(int dicType) {
    List<TBridgeOrderDic> list = dicService.list();
    list.removeIf(next -> next.getDicType().intValue() != dicType);
    return RestResult.success(list, "列出优先排序字典");
  }




  @PostMapping(value = "/saveModelParam")
  @ApiOperation(nickname = "新增或更新优先排序参数", value = "saveModelParam")
  public RestResult<String> saveModelParam(
      @RequestBody List<TBridgeOrderModelParam> list
  ) {
    if (list != null && !list.isEmpty()) {
      String modelId = list.get(0).getModelId();
      orderModelParamService.delByModelId(modelId);
      for (TBridgeOrderModelParam param : list) {
        param.setParamId(StringUtil.getUUID());
      }
    }
    orderModelParamService.saveModelParam(list);
    return RestResult.success("success", "新增或更新优先排序参数成功");
  }

  @PostMapping("/getModelParamDetail")
  @ApiOperation(nickname = "获取优先排序模型参数详情", value = "获取优先排序模型参数详情")
  public RestResult<List<TBridgeOrderModelParam>> getModelParamDetail(
      String modelId
  ) {
    return RestResult.success(orderModelParamService.getModelParamDetail(modelId));
  }


  @PostMapping("/getMaintainModelParamDetail")
  @ApiOperation(nickname = "获取养护方案模型参数详情（桥梁）", value = "获取养护方案模型参数详情（桥梁）")
  public RestResult<List<TBridgeMaintainModelParam>> getMaintainModelParamDetail(
      String modelId
  ) {
    return RestResult.success(maintainModelParamService.getModelParamDetail(modelId));
  }

  @PostMapping("/getTunnelMaintainModelParamDetail")
  @ApiOperation(nickname = "获取养护方案模型参数详情（隧道）", value = "获取养护方案模型参数详情（隧道）")
  public RestResult<List<TBridgeMaintainModelParam>> getTunnelMaintainModelParamDetail(
      String modelId
  ) {
    return RestResult.success(maintainModelParamService.getTunnelMaintainModelParamDetail(modelId));
  }

  @PostMapping("/getMeasureDicList")
  @ApiOperation(nickname = "获取养护措施列表,type为QL，SD", value = "获取养护措施列表，type为QL，SD")
  public RestResult<List<Map>> getMeasureDicList(
      String type
  ) {
    return RestResult.success(maintainModelParamService.getMeasureDicList(type));
  }


  @PostMapping(value = "/updateMaintainModelParam")
  @ApiOperation(nickname = "更新养护方案参数的养护措施", value = "更新养护方案参数的养护措施")
  public RestResult<String> updateMaintainModelParam(
     String paramId,String measureId
  ) {

    maintainModelParamService.updateMaintainModelParam(paramId,measureId);
    return RestResult.success("success", "更新养护方案参数的养护措施");
  }
}
