package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TBridgeMaintainModelParamMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeMaintainModelParam;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBridgeMaintainModelParamService;

import java.util.List;
import java.util.Map;

@Service
public class TBridgeMaintainModelParamServiceImpl extends ServiceImpl<TBridgeMaintainModelParamMapper,TBridgeMaintainModelParam> implements TBridgeMaintainModelParamService{

    @Resource
    private TBridgeMaintainModelParamMapper mapper;
    @Override
    public void initParam(String modelId) {
        mapper.initParam(modelId);
    }

    @Override
    public List<TBridgeMaintainModelParam> getModelParamDetail(String modelId) {
        return mapper.getModelParamDetail(modelId);
    }

    @Override
    public List<Map> getMeasureDicList(String type) {
        return mapper.getMeasureDicList(type);
    }

    @Override
    public void updateMaintainModelParam(String paramId, String measureId) {
        mapper.updateMaintainModelParam(paramId,measureId);
    }

    @Override
    public void initSdParam(String modelId) {
        mapper.initSdParam(modelId);
    }

    @Override
    public List<TBridgeMaintainModelParam> getTunnelMaintainModelParamDetail(String modelId) {
        return mapper.getTunnelMaintainModelParamDetail(modelId);
    }
}
