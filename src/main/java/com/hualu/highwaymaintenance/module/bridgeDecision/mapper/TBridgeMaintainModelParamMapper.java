package com.hualu.highwaymaintenance.module.bridgeDecision.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TBridgeMaintainModelParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface TBridgeMaintainModelParamMapper extends BaseMapper<TBridgeMaintainModelParam> {

  void initParam(String modelId);

  List<Map> getMeasureDicList(String type);

  void updateMaintainModelParam(@Param("paramId") String paramId, @Param("measureId") String measureId);

  List<TBridgeMaintainModelParam> getModelParamDetail(@Param("modelId") String modelId);

  void initSdParam(@Param("modelId") String modelId);

  List<TBridgeMaintainModelParam> getTunnelMaintainModelParamDetail(String modelId);
}