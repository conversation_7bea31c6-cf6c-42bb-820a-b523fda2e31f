package com.hualu.highwaymaintenance.module.bridgeDecision.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.BridgeSuggestT;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TStructMaintainSuggest;
import com.hualu.highwaymaintenance.module.bridgeDecision.entity.TunnelSuggestT;
import com.hualu.highwaymaintenance.module.bridgeDecision.mapper.TStructMaintainSuggestMapper;
import com.hualu.highwaymaintenance.module.bridgeDecision.vo.ItemMoneyVo;
import com.hualu.highwaymaintenance.module.maintainbase.service.BridgeSuggestTService;
import com.hualu.highwaymaintenance.module.maintainbase.service.TBrdgTechForecastPrjService;
import com.hualu.highwaymaintenance.module.maintainbase.service.TStructMaintainSuggestService;
import com.hualu.highwaymaintenance.module.maintainbase.service.TunnelSuggestTService;
import com.hualu.highwaymaintenance.module.national.entity.BridgeSuggest;
import com.hualu.highwaymaintenance.module.national.entity.BridgeSuggestO;
import com.hualu.highwaymaintenance.module.national.service.BridgeSuggestOService;
import com.hualu.highwaymaintenance.module.national.service.BridgeSuggestService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Service
public class TStructMaintainSuggestServiceImpl extends ServiceImpl<TStructMaintainSuggestMapper, TStructMaintainSuggest> implements TStructMaintainSuggestService {

  @Resource
  private TBrdgTechForecastPrjService forecastPrjService;

  @Resource
  private BridgeSuggestTService bridgeSuggestTService;
  @Resource
  private TunnelSuggestTService tunnelSuggestTService;
  @Override
  public void updateSuggestRepository(TStructMaintainSuggest suggest, int i) {
    //1增加，2减少
    String structId = suggest.getStructId();
    String cat = suggest.getCat();
    if (cat.equals("QL")) {
      //计算费用
      ItemMoneyVo itemMoneyVo = forecastPrjService.countMoney(structId, suggest.getMaintainModelId());
      Double moneyPrevent = itemMoneyVo.getMoneyPrevent();
      Double moneyRepair = itemMoneyVo.getMoneyRepair();
      double brdgLenthByBrdgId = forecastPrjService.getBrdgLenthByBrdgId(structId);
      String brdgRouteCode = forecastPrjService.getBrdgRouteCode(structId);
      BridgeSuggestT bridgeSuggest =  forecastPrjService.findSuggestTByRouteCode(brdgRouteCode,suggest.getMaintainType());
      if(bridgeSuggest!=null) {
        if (i == 1) {
          if (moneyPrevent > 0) {
            bridgeSuggest.setPreventCount(BigDecimal.valueOf(bridgeSuggest.getPreventCount().intValue() + 1));
            bridgeSuggest.setPreventFee(BigDecimal.valueOf(bridgeSuggest.getPreventFee().doubleValue() + moneyPrevent));
            bridgeSuggest.setPreventQuantity(BigDecimal.valueOf(bridgeSuggest.getPreventQuantity().doubleValue() + brdgLenthByBrdgId));
          }
          if (moneyRepair > 0) {
            bridgeSuggest.setRepairCount(BigDecimal.valueOf(bridgeSuggest.getRepairCount().intValue() + 1));
            bridgeSuggest.setRepairFee(BigDecimal.valueOf(bridgeSuggest.getRepairFee().doubleValue() + moneyRepair));
            bridgeSuggest.setRepairQuantity(BigDecimal.valueOf(bridgeSuggest.getRepairQuantity().doubleValue() + brdgLenthByBrdgId));
          }
        }
        if (i == 2) {
          if (moneyPrevent > 0) {
            bridgeSuggest.setPreventCount(BigDecimal.valueOf(bridgeSuggest.getPreventCount().intValue() - 1));
            if (bridgeSuggest.getPreventCount().intValue() < 0) {
              bridgeSuggest.setPreventCount(BigDecimal.valueOf(0));
            }
            bridgeSuggest.setPreventFee(BigDecimal.valueOf(bridgeSuggest.getPreventFee().doubleValue() - moneyPrevent));
            if (bridgeSuggest.getPreventFee().doubleValue() < 0) {
              bridgeSuggest.setPreventFee(BigDecimal.valueOf(0));
            }
            bridgeSuggest.setPreventQuantity(BigDecimal.valueOf(bridgeSuggest.getPreventQuantity().doubleValue() - brdgLenthByBrdgId));
            if (bridgeSuggest.getPreventQuantity().doubleValue() < 0) {
              bridgeSuggest.setPreventQuantity(BigDecimal.valueOf(0));
            }
          }
          if (moneyRepair > 0) {
            bridgeSuggest.setRepairCount(BigDecimal.valueOf(bridgeSuggest.getRepairCount().intValue() - 1));
            bridgeSuggest.setRepairFee(BigDecimal.valueOf(bridgeSuggest.getRepairFee().doubleValue() - moneyRepair));
            bridgeSuggest.setRepairQuantity(BigDecimal.valueOf(bridgeSuggest.getRepairQuantity().doubleValue() - brdgLenthByBrdgId));
            if (bridgeSuggest.getRepairCount().intValue() < 0) {
              bridgeSuggest.setRepairCount(BigDecimal.valueOf(0));
            }
            if (bridgeSuggest.getRepairFee().doubleValue() < 0) {
              bridgeSuggest.setRepairFee(BigDecimal.valueOf(0));
            }
            if (bridgeSuggest.getRepairQuantity().doubleValue() < 0) {
              bridgeSuggest.setRepairQuantity(BigDecimal.valueOf(0));
            }
          }
        }
        bridgeSuggest.setTotalFee(bridgeSuggest.getPreventFee().add(bridgeSuggest.getRepairFee()));
        bridgeSuggestTService.updateById(bridgeSuggest);
      }
    }
    if (cat.equals("SD")) {
      //计算费用
      ItemMoneyVo itemMoneyVo = forecastPrjService.countTunnelMoney(structId, suggest.getMaintainModelId());
      Double moneyPrevent = itemMoneyVo.getMoneyPrevent();
      Double moneyRepair = itemMoneyVo.getMoneyRepair();
      double brdgLenthByBrdgId = forecastPrjService.getTunnelLenthByTunnelId(structId);
      String brdgRouteCode = forecastPrjService.getTunnelRouteCode(structId);
      TunnelSuggestT bridgeSuggest =  forecastPrjService.findSuggestTTunnelByRouteCode(brdgRouteCode,suggest.getMaintainType());
      if(bridgeSuggest!=null) {
        if (i == 1) {
          if (moneyPrevent > 0) {
            bridgeSuggest.setPreventCount(BigDecimal.valueOf(bridgeSuggest.getPreventCount().intValue() + 1));
            bridgeSuggest.setCivilPreventFee(BigDecimal.valueOf(bridgeSuggest.getCivilPreventFee().doubleValue() + moneyPrevent));
            bridgeSuggest.setPreventQuantity(BigDecimal.valueOf(bridgeSuggest.getPreventQuantity().doubleValue() + brdgLenthByBrdgId));
          }
          if (moneyRepair > 0) {
            bridgeSuggest.setRepairCount(BigDecimal.valueOf(bridgeSuggest.getRepairCount().intValue() + 1));
            bridgeSuggest.setCivilRepairFee(BigDecimal.valueOf(bridgeSuggest.getCivilRepairFee().doubleValue() + moneyRepair));
            bridgeSuggest.setRepairQuantity(BigDecimal.valueOf(bridgeSuggest.getRepairQuantity().doubleValue() + brdgLenthByBrdgId));
          }
        }
        if (i == 2) {
          if (moneyPrevent > 0) {
            bridgeSuggest.setPreventCount(BigDecimal.valueOf(bridgeSuggest.getPreventCount().intValue() - 1));
            if (bridgeSuggest.getPreventCount().intValue() < 0) {
              bridgeSuggest.setPreventCount(BigDecimal.valueOf(0));
            }
            bridgeSuggest.setCivilPreventFee(BigDecimal.valueOf(bridgeSuggest.getCivilPreventFee().doubleValue() - moneyPrevent));
            if (bridgeSuggest.getCivilPreventFee().doubleValue() < 0) {
              bridgeSuggest.setCivilPreventFee(BigDecimal.valueOf(0));
            }
            bridgeSuggest.setPreventQuantity(BigDecimal.valueOf(bridgeSuggest.getPreventQuantity().doubleValue() - brdgLenthByBrdgId));
            if (bridgeSuggest.getPreventQuantity().doubleValue() < 0) {
              bridgeSuggest.setPreventQuantity(BigDecimal.valueOf(0));
            }
          }
          if (moneyRepair > 0) {
            bridgeSuggest.setRepairCount(BigDecimal.valueOf(bridgeSuggest.getRepairCount().intValue() - 1));
            bridgeSuggest.setCivilRepairFee(BigDecimal.valueOf(bridgeSuggest.getCivilRepairFee().doubleValue() - moneyRepair));
            bridgeSuggest.setRepairQuantity(BigDecimal.valueOf(bridgeSuggest.getRepairQuantity().doubleValue() - brdgLenthByBrdgId));
            if (bridgeSuggest.getRepairCount().intValue() < 0) {
              bridgeSuggest.setRepairCount(BigDecimal.valueOf(0));
            }
            if (bridgeSuggest.getCivilRepairFee().doubleValue() < 0) {
              bridgeSuggest.setCivilRepairFee(BigDecimal.valueOf(0));
            }
            if (bridgeSuggest.getRepairQuantity().doubleValue() < 0) {
              bridgeSuggest.setRepairQuantity(BigDecimal.valueOf(0));
            }
          }
        }
        bridgeSuggest.setCivilTotalFee(bridgeSuggest.getCivilRepairFee().add(bridgeSuggest.getCivilPreventFee()));
        bridgeSuggest.setTotalFee(bridgeSuggest.getCivilTotalFee().add(bridgeSuggest.getElecTotalFee()));
        tunnelSuggestTService.updateById(bridgeSuggest);
      }
    }
  }
}



