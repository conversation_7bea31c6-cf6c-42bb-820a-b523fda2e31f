package com.hualu.highwaymaintenance;

import com.hualu.highwaymaintenance.common.exception.BusinessException;
import com.hualu.highwaymaintenance.common.vo.RestResult;
import com.hualu.highwaymaintenance.module.inspect.domain.CoreApiKey;
import com.hualu.highwaymaintenance.module.inspect.service.CoreApiKeyService;
import com.hualu.highwaymaintenance.module.inspect.service.RedisService;
import com.hualu.highwaymaintenance.util.MD5Util;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.TreeMap;

@Component
public class InspectInterceptor extends HandlerInterceptorAdapter {

    private Logger logger = LoggerFactory.getLogger(InspectInterceptor.class);

    @Resource
    private RedisService redisService;

    @Resource
    private CoreApiKeyService coreApiKeyService;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if(request.getRequestURI().contains("/api/inspect")){
            String appid = request.getHeader("appid");
            String timestamp = request.getHeader("timestamp");
            String nonce = request.getHeader("nonce");
            String sign = request.getHeader("sign");
            if (StringUtils.isEmpty(appid) || StringUtils.isEmpty(timestamp) || StringUtils.isEmpty(nonce) || StringUtils.isEmpty(sign)) {
                throw new BusinessException("缺少请求头参数", RestResult.ERROR);
            }
            // 限制为（含）60秒以内发送的请求
            long time = 60;
            long now = System.currentTimeMillis() / 1000;
            if (now - Long.valueOf(timestamp) > time) {
                throw new BusinessException("请求发起时间超过服务器限制时间", RestResult.ERROR);
            }
            // 查询appid是否正确
            CoreApiKey apiKey = coreApiKeyService.selectByAppid(appid);
            if (apiKey == null) {
                throw new BusinessException("appid参数错误", RestResult.ERROR);
            }
            // 验证请求是否重复
            if (redisService.hasKeyHashItem("third_party_key:" + apiKey.getAppid() + nonce)) {
                throw new BusinessException("请不要发送重复的请求", RestResult.WARN);
            } else {
                // 如果nonce没有存在缓存中，则加入，并设置失效时间（秒）
                redisService.set("third_party_key:" +apiKey.getAppid() + nonce, nonce, time);
            }
            TreeMap<String,String> signObj = new TreeMap<>();
            signObj.put("appid", appid);
            signObj.put("timestamp", timestamp);
            signObj.put("nonce", nonce);
            String mySign = MD5Util.getDigest(signObj, apiKey.getSecret(),"UTF-8");
            // 验证签名
            if (!mySign.equals(sign)) {
                throw new BusinessException("签名信息错误", RestResult.ERROR);
            }
            String keyAppid = apiKey.getAppid();
            String userKey = "USER:" + keyAppid;
            String orgKey = "ORG:" + keyAppid;
            String enKey = "EN:" + keyAppid;
            String nameKey = "NAME:" + keyAppid;
            redisService.set(userKey,apiKey.getUserCode());
            redisService.set(orgKey,apiKey.getOrgCode());
            redisService.set(enKey,apiKey.getEn());
            redisService.set(nameKey,apiKey.getUserName());
            return true;
        }
        throw new BusinessException("无权限访问", RestResult.WARN);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        super.afterCompletion(request, response, handler, ex);
    }
}
