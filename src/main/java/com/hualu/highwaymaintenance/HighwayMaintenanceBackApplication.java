package com.hualu.highwaymaintenance;

import com.hualu.highwaymaintenance.module.birdgeMonitor.interceptor.ApiAuthInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.WebApplicationInitializer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.annotation.Resource;

@Configuration
@SpringBootApplication
@EnableScheduling
@ServletComponentScan
@EnableTransactionManagement
@EnableSwagger2
@EnableAsync
@EnableFeignClients(basePackages = "com.hualu.highwaymaintenance.module.inspect.service")
@MapperScan("com.hualu.highwaymaintenance.**.mapper")
public class HighwayMaintenanceBackApplication extends SpringBootServletInitializer implements
    WebApplicationInitializer, WebMvcConfigurer {
  /**
   * Add Spring MVC lifecycle interceptors for pre- and post-processing of
   * controller method invocations and resource handler requests.
   * Interceptors can be registered to apply to all requests or be limited
   * to a subset of URL patterns.
   *
   * @param registry
   */
  @Resource
  private InspectInterceptor inspectInterceptor;

  @Resource
  private ApiAuthInterceptor apiAuthInterceptor;

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(new AppInterceptor()) //拦截器注册对象
        .excludePathPatterns("/mbapp/login")
        .excludePathPatterns("/static_resource/**")
        .excludePathPatterns("/swagger-ui/*")
        .excludePathPatterns("/decision/*")
        .excludePathPatterns("/api/inspect/**")
        .excludePathPatterns("/signature/**")
        .excludePathPatterns("/national/**").excludePathPatterns("/monitor/*");

    registry.addInterceptor(inspectInterceptor)
            .addPathPatterns("/api/inspect/**").excludePathPatterns("/api/inspect/synDisease").excludePathPatterns("/monitor/*");

    registry.addInterceptor(apiAuthInterceptor)
            .addPathPatterns("/monitor/*");
  }

  public static void main(String[] args) {
    SpringApplication.run(HighwayMaintenanceBackApplication.class, args);
  }

  @Override protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
    return builder.sources(HighwayMaintenanceBackApplication.class);
  }

}
