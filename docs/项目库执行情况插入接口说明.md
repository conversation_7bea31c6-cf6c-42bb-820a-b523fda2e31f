# 项目库执行情况插入接口说明

## 概述

在 `NationalController.java` 中新增了三个项目库执行情况的插入接口，分别对应路面、桥梁和隧道的项目库执行情况数据插入功能。

## 新增接口列表

### 1. 路面项目库执行情况插入接口

**接口地址**: `POST /national/addPavementRepositoryImp`

**请求参数**:
- `orgCode` (必填): 组织编码，通过查询参数传递
- `year` (可选): 年份，默认值为2025，通过查询参数传递
- 请求体: `PavementRepositoryImp` 实体对象

**示例请求**:
```
POST /national/addPavementRepositoryImp?orgCode=N000001&year=2025
Content-Type: application/json

{
  "orders": 1,
  "orgName": "广东省高速公路有限公司",
  "lineCode": "G0001",
  "lineName": "广深高速",
  "stake": "K100+000-K200+000",
  "lineDirect": "上行",
  "laneNo": 3,
  "preventQuantity": 10.5,
  "repairQuantity": 5.2,
  "preventFee": 1000000,
  "repairFee": 500000,
  "totalFee": 1500000,
  "isPlan": "是",
  "planInvest": 1500000,
  "projectEvolve": "进行中",
  "startTime": "2025-01-01",
  "finishTime": "2025-12-31",
  "finishQuantity": 8.0,
  "finishInvest": 1200000,
  "remark": "路面养护项目"
}
```

### 2. 桥梁项目库执行情况插入接口

**接口地址**: `POST /national/addBridgeRepositoryImp`

**请求参数**:
- `orgCode` (必填): 组织编码，通过查询参数传递
- `year` (可选): 年份，默认值为2025，通过查询参数传递
- 请求体: `BridgeRepositoryImp` 实体对象

**示例请求**:
```
POST /national/addBridgeRepositoryImp?orgCode=N000001&year=2025
Content-Type: application/json

{
  "orders": 1,
  "orgName": "广东省高速公路有限公司",
  "lineCode": "G0001",
  "lineName": "广深高速",
  "stake": "K100+000",
  "repairCount": 2,
  "preventCount": 3,
  "preventQuantity": 100.0,
  "repairQuantity": 50.0,
  "preventFee": 2000000,
  "repairFee": 1000000,
  "totalFee": 3000000,
  "isPlan": "是",
  "planInvest": 3000000,
  "projectEvolve": "进行中",
  "startTime": "2025-01-01",
  "finishTime": "2025-12-31",
  "finishQuantity": 80.0,
  "finishInvest": 2500000,
  "remark": "桥梁养护项目"
}
```

### 3. 隧道项目库执行情况插入接口

**接口地址**: `POST /national/addTunnelRepositoryImp`

**请求参数**:
- `orgCode` (必填): 组织编码，通过查询参数传递
- `year` (可选): 年份，默认值为2025，通过查询参数传递
- 请求体: `TunnelRepositoryImp` 实体对象

**示例请求**:
```
POST /national/addTunnelRepositoryImp?orgCode=N000001&year=2025
Content-Type: application/json

{
  "orders": 1,
  "orgName": "广东省高速公路有限公司",
  "lineCode": "G0001",
  "lineName": "广深高速",
  "stake": "K100+000",
  "repairCount": 1,
  "preventCount": 2,
  "preventQuantity": 200.0,
  "repairQuantity": 100.0,
  "civilPreventFee": 1500000,
  "civilRepairFee": 800000,
  "civilTotalFee": 2300000,
  "elecTotalFee": 500000,
  "totalFee": 2800000,
  "isPlan": "是",
  "planInvest": 2800000,
  "projectEvolve": "进行中",
  "startTime": "2025-01-01",
  "finishTime": "2025-12-31",
  "finishQuantity": 150.0,
  "finishInvest": 2300000,
  "remark": "隧道养护项目"
}
```

## 接口特性

### 自动设置字段

所有接口都会自动设置以下字段：

1. **orgCode**: 从查询参数获取并设置到实体对象
2. **year**: 从查询参数获取（默认2025）并设置到实体对象
3. **target**: 自动设置为year的值（默认当前年份）
4. **importTime**: 自动设置为当前时间
5. **id**: 如果未提供或为空，自动生成UUID

### 参数验证

- `orgCode`: 必填参数，通过查询参数传递
- `year`: 可选参数，默认值为2025
- 请求体: 对应的实体对象，包含具体的项目库执行情况数据

### 响应格式

**成功响应**:
```json
{
  "code": 200,
  "message": "新增成功",
  "data": null
}
```

**失败响应**:
```json
{
  "code": 500,
  "message": "新增失败",
  "data": null
}
```

## 数据库表对应关系

| 接口 | 数据库表 | 实体类 |
|------|----------|--------|
| addPavementRepositoryImp | MEMSDB.PAVEMENT_REPOSITORY_IMP | PavementRepositoryImp |
| addBridgeRepositoryImp | MEMSDB.BRIDGE_REPOSITORY_IMP | BridgeRepositoryImp |
| addTunnelRepositoryImp | MEMSDB.TUNNEL_REPOSITORY_IMP | TunnelRepositoryImp |

## 实体类主要字段说明

### PavementRepositoryImp (路面)
- `lineDirect`: 上/下行
- `laneNo`: 车道号
- `preventQuantity`: 预防性养护工程量
- `repairQuantity`: 修复性养护工程量
- `planPreventQuantity`: 计划预防性养护工程量
- `planRepairQuantity`: 计划修复性养护工程量

### BridgeRepositoryImp (桥梁)
- `repairCount`: 修复养护（座）
- `preventCount`: 预防养护（座）
- `preventQuantity`: 预防性养护工程量
- `repairQuantity`: 修复性养护工程量
- `planRepairCount`: 计划修复养护座数
- `planPreventCount`: 计划预防养护座数

### TunnelRepositoryImp (隧道)
- `repairCount`: 修复养护（座）
- `preventCount`: 预防养护（座）
- `civilPreventFee`: 土建预防性养护费用
- `civilRepairFee`: 土建修复性养护费用
- `civilTotalFee`: 土建总费用
- `elecTotalFee`: 机电总费用
- `planCivilPreventFee`: 计划土建预防性养护费用
- `planCivilRepairFee`: 计划土建修复性养护费用

## 使用注意事项

1. **权限控制**: 建议在实际使用中添加权限验证，确保只有有权限的用户才能插入数据
2. **数据验证**: 建议在Service层添加业务逻辑验证，确保数据的完整性和合理性
3. **事务管理**: 插入操作已经使用了MyBatis-Plus的save方法，具有事务保护
4. **ID生成**: 系统会自动生成UUID作为主键，无需手动指定
5. **时间字段**: importTime会自动设置为当前时间
6. **组织编码**: 确保传入的orgCode在系统中存在且有效

## 扩展建议

1. **批量插入**: 可以考虑添加批量插入接口提高效率
2. **数据校验**: 可以添加更严格的数据校验规则
3. **审计日志**: 可以添加操作日志记录，便于追踪数据变更
4. **异步处理**: 对于大量数据插入，可以考虑异步处理机制

---

**创建日期**: 2025-07-29  
**创建人**: 系统管理员  
**文档版本**: 1.0
